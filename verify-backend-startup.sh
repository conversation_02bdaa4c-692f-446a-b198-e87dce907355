#!/bin/bash

# 智慧农业平台后端启动验证脚本
# 验证所有修复是否生效

echo "=========================================="
echo "智慧农业平台后端启动验证脚本"
echo "=========================================="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 验证计数器
total_checks=0
passed_checks=0
failed_checks=0

# 检查函数
check_step() {
    local step_name=$1
    local command=$2
    local expected_result=$3
    
    total_checks=$((total_checks + 1))
    echo -n "[$total_checks] $step_name ... "
    
    if eval "$command" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 通过${NC}"
        passed_checks=$((passed_checks + 1))
        return 0
    else
        echo -e "${RED}❌ 失败${NC}"
        failed_checks=$((failed_checks + 1))
        return 1
    fi
}

# 检查文件存在
check_file() {
    local file_name=$1
    local file_path=$2
    
    total_checks=$((total_checks + 1))
    echo -n "[$total_checks] 检查$file_name ... "
    
    if [ -f "$file_path" ]; then
        echo -e "${GREEN}✅ 存在${NC}"
        passed_checks=$((passed_checks + 1))
        return 0
    else
        echo -e "${RED}❌ 不存在${NC}"
        failed_checks=$((failed_checks + 1))
        return 1
    fi
}

echo -e "${BLUE}🔍 1. 文件完整性检查${NC}"
echo "=================================="

# 检查关键文件
check_file "主类文件" "backend/main/src/main/java/com/agriculture/AgricultureMallApplication.java"
check_file "ProductMapper.xml" "backend/main/src/main/resources/mapper/ProductMapper.xml"
check_file "pom.xml" "backend/main/pom.xml"
check_file "application.yml" "backend/main/src/main/resources/application.yml"

echo ""
echo -e "${BLUE}🔧 2. XML语法验证${NC}"
echo "=================================="

# 验证ProductMapper.xml语法
total_checks=$((total_checks + 1))
echo -n "[$total_checks] ProductMapper.xml语法检查 ... "

if xmllint --noout backend/main/src/main/resources/mapper/ProductMapper.xml 2>/dev/null; then
    echo -e "${GREEN}✅ XML语法正确${NC}"
    passed_checks=$((passed_checks + 1))
else
    echo -e "${RED}❌ XML语法错误${NC}"
    failed_checks=$((failed_checks + 1))
fi

# 检查CDATA使用
total_checks=$((total_checks + 1))
echo -n "[$total_checks] 检查CDATA使用 ... "

if grep -q "CDATA" backend/main/src/main/resources/mapper/ProductMapper.xml; then
    echo -e "${GREEN}✅ 使用了CDATA${NC}"
    passed_checks=$((passed_checks + 1))
else
    echo -e "${YELLOW}⚠️ 未使用CDATA${NC}"
fi

echo ""
echo -e "${BLUE}📦 3. Maven配置检查${NC}"
echo "=================================="

# 检查pom.xml语法
total_checks=$((total_checks + 1))
echo -n "[$total_checks] pom.xml语法检查 ... "

cd backend/main
if mvn help:effective-pom > /dev/null 2>&1; then
    echo -e "${GREEN}✅ pom.xml语法正确${NC}"
    passed_checks=$((passed_checks + 1))
else
    echo -e "${RED}❌ pom.xml语法错误${NC}"
    failed_checks=$((failed_checks + 1))
fi

# 检查slf4j排除配置
total_checks=$((total_checks + 1))
echo -n "[$total_checks] 检查SLF4J排除配置 ... "

if grep -A 5 "dashscope-sdk-java" pom.xml | grep -q "slf4j-simple"; then
    echo -e "${GREEN}✅ SLF4J排除配置正确${NC}"
    passed_checks=$((passed_checks + 1))
else
    echo -e "${RED}❌ SLF4J排除配置缺失${NC}"
    failed_checks=$((failed_checks + 1))
fi

echo ""
echo -e "${BLUE}🏗️ 4. 编译验证${NC}"
echo "=================================="

# Maven编译测试
total_checks=$((total_checks + 1))
echo -n "[$total_checks] Maven编译测试 ... "

if mvn clean compile -q > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 编译成功${NC}"
    passed_checks=$((passed_checks + 1))
else
    echo -e "${RED}❌ 编译失败${NC}"
    failed_checks=$((failed_checks + 1))
    echo "编译错误详情："
    mvn clean compile | tail -20
fi

echo ""
echo -e "${BLUE}🚀 5. 启动测试${NC}"
echo "=================================="

# 启动测试（超时30秒）
total_checks=$((total_checks + 1))
echo -n "[$total_checks] 应用启动测试 ... "

# 启动应用并等待
timeout 30s mvn spring-boot:run > startup.log 2>&1 &
startup_pid=$!

# 等待启动完成或超时
sleep 15

# 检查是否启动成功
if curl -s http://localhost:8081/health > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 启动成功${NC}"
    passed_checks=$((passed_checks + 1))
    
    # 停止应用
    kill $startup_pid 2>/dev/null
    
    # 检查启动日志
    echo ""
    echo -e "${BLUE}📋 启动日志分析${NC}"
    echo "=================================="
    
    if grep -q "智慧农业辅助平台启动成功" startup.log; then
        echo -e "${GREEN}✅ 启动成功日志正常${NC}"
    else
        echo -e "${YELLOW}⚠️ 未找到启动成功日志${NC}"
    fi
    
    if grep -q "SAXParseException" startup.log; then
        echo -e "${RED}❌ 仍有XML解析错误${NC}"
        failed_checks=$((failed_checks + 1))
    else
        echo -e "${GREEN}✅ 无XML解析错误${NC}"
    fi
    
    if grep -q "SLF4J.*multiple.*bindings" startup.log; then
        echo -e "${YELLOW}⚠️ 仍有SLF4J多重绑定警告${NC}"
    else
        echo -e "${GREEN}✅ 无SLF4J多重绑定警告${NC}"
    fi
    
else
    echo -e "${RED}❌ 启动失败${NC}"
    failed_checks=$((failed_checks + 1))
    
    echo ""
    echo "启动错误日志："
    tail -20 startup.log
fi

# 清理
rm -f startup.log

echo ""
echo -e "${BLUE}📊 验证结果汇总${NC}"
echo "=================================="

echo "总检查项: $total_checks"
echo -e "通过检查: ${GREEN}$passed_checks${NC}"
echo -e "失败检查: ${RED}$failed_checks${NC}"

success_rate=$(( passed_checks * 100 / total_checks ))
echo "成功率: $success_rate%"

echo ""
if [ $failed_checks -eq 0 ]; then
    echo -e "${GREEN}🎉 所有检查通过！后端修复成功！${NC}"
    echo ""
    echo -e "${BLUE}✅ 修复完成确认：${NC}"
    echo "- XML解析错误已修复"
    echo "- SLF4J多重绑定已解决"
    echo "- MyBatis配置已优化"
    echo "- 应用可以正常启动"
    echo ""
    echo -e "${BLUE}🚀 下一步：${NC}"
    echo "1. 重启生产环境应用"
    echo "2. 验证前后端通信"
    echo "3. 测试关键业务功能"
    
    exit 0
elif [ $success_rate -ge 80 ]; then
    echo -e "${YELLOW}⚠️ 大部分检查通过，但仍有问题需要解决${NC}"
    exit 1
else
    echo -e "${RED}❌ 多个检查失败，需要进一步修复${NC}"
    exit 2
fi
