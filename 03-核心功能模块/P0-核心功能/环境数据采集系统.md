# 环境数据采集系统 (P0级核心功能)

> **优先级**: P0 (最高优先级)  
> **预算**: 300元硬件 + 50元软件  
> **开发时间**: 2周  
> **技术难度**: ⭐⭐⭐☆☆  

## 📋 功能概述

### 🎯 核心目标
实现农业环境的实时数据采集，包括温度、湿度、土壤湿度、光照强度等关键参数的监测，为智慧农业决策提供基础数据支撑。

### 📊 功能特性
- **实时监测**: 每分钟采集一次环境数据
- **多参数采集**: 支持4-6种环境参数同时监测
- **数据上传**: 自动上传到云端数据库
- **异常告警**: 参数超出阈值时自动告警
- **历史查询**: 支持历史数据查询和导出

## 🛠️ 硬件实现方案

### 📱 硬件架构
```
┌─────────────────────────────────────────────────────────────────┐
│                    环境数据采集硬件架构                          │
├─────────────────────────────────────────────────────────────────┤
│  主控制器                                                       │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │ 树莓派4B (主控) + Ard<PERSON>o Uno (传感器控制)                 │ │
│  │ • WiFi通信  • 数据处理  • 系统管理  • 远程控制             │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                              ↕                                  │
├─────────────────────────────────────────────────────────────────┤
│  传感器层                                                       │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐               │
│  │ DHT22       │ │ 土壤湿度    │ │ BH1750      │               │
│  │ 温湿度传感器│ │ 传感器      │ │ 光照传感器  │               │
│  └─────────────┘ └─────────────┘ └─────────────┘               │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐               │
│  │ pH传感器    │ │ 水位传感器  │ │ 状态指示灯  │               │
│  │ (可选)      │ │ (可选)      │ │ LED显示     │               │
│  └─────────────┘ └─────────────┘ └─────────────┘               │
└─────────────────────────────────────────────────────────────────┘
```

### 🔧 硬件清单和成本
```yaml
必备硬件 (250元):
  Arduino Uno R3: 30元
  DHT22温湿度传感器: 20元
  土壤湿度传感器: 15元
  BH1750光照传感器: 12元
  面包板和连接线: 25元
  电阻套装: 10元
  LED指示灯: 5元
  防水外壳: 30元
  电源模块: 25元
  安装支架: 15元
  其他配件: 63元

可选硬件 (50元):
  pH传感器: 35元
  水位传感器: 15元

总成本: 300元
```

### ⚡ 电路连接图
```
Arduino Uno R3 引脚连接:
┌─────────────────────────────────────────┐
│ 传感器        Arduino引脚    电源       │
├─────────────────────────────────────────┤
│ DHT22         数字引脚2      5V/GND     │
│ 土壤湿度      模拟引脚A0     5V/GND     │
│ BH1750        I2C (A4/A5)   3.3V/GND   │
│ pH传感器      模拟引脚A1     5V/GND     │
│ 水位传感器    数字引脚3      5V/GND     │
│ 状态LED       数字引脚13     内置       │
│ 蜂鸣器        数字引脚8      5V/GND     │
└─────────────────────────────────────────┘

注意事项:
- BH1750使用3.3V供电，避免烧毁
- 土壤湿度传感器需要防水处理
- pH传感器需要定期校准
```

## 💻 软件实现方案

### 🔧 Arduino代码实现
```cpp
// 环境数据采集Arduino代码
#include <DHT.h>
#include <Wire.h>
#include <BH1750.h>
#include <SoftwareSerial.h>

// 传感器定义
#define DHT_PIN 2
#define DHT_TYPE DHT22
#define SOIL_PIN A0
#define PH_PIN A1
#define WATER_PIN 3
#define LED_PIN 13
#define BUZZER_PIN 8

// 传感器对象
DHT dht(DHT_PIN, DHT_TYPE);
BH1750 lightMeter;

// 数据结构
struct SensorData {
  float temperature;
  float humidity;
  int soilMoisture;
  float lightIntensity;
  float phValue;
  bool waterLevel;
  unsigned long timestamp;
};

void setup() {
  Serial.begin(9600);
  
  // 初始化传感器
  dht.begin();
  Wire.begin();
  lightMeter.begin();
  
  // 初始化引脚
  pinMode(WATER_PIN, INPUT);
  pinMode(LED_PIN, OUTPUT);
  pinMode(BUZZER_PIN, OUTPUT);
  
  Serial.println("环境监测系统启动完成");
}

void loop() {
  SensorData data = collectSensorData();
  
  // 数据验证
  if (validateData(data)) {
    // 发送数据到树莓派
    sendDataToRaspberryPi(data);
    
    // 检查告警条件
    checkAlerts(data);
    
    // 状态指示
    blinkLED();
  } else {
    Serial.println("数据采集异常");
    errorAlert();
  }
  
  delay(60000); // 1分钟采集一次
}

SensorData collectSensorData() {
  SensorData data;
  
  // 读取温湿度
  data.temperature = dht.readTemperature();
  data.humidity = dht.readHumidity();
  
  // 读取土壤湿度 (转换为百分比)
  int soilRaw = analogRead(SOIL_PIN);
  data.soilMoisture = map(soilRaw, 0, 1023, 100, 0);
  
  // 读取光照强度
  data.lightIntensity = lightMeter.readLightLevel();
  
  // 读取pH值 (需要校准)
  int phRaw = analogRead(PH_PIN);
  data.phValue = map(phRaw, 0, 1023, 0, 14);
  
  // 读取水位
  data.waterLevel = digitalRead(WATER_PIN);
  
  // 时间戳
  data.timestamp = millis();
  
  return data;
}

bool validateData(SensorData data) {
  // 数据合理性检查
  if (isnan(data.temperature) || isnan(data.humidity)) {
    return false;
  }
  
  if (data.temperature < -40 || data.temperature > 80) {
    return false;
  }
  
  if (data.humidity < 0 || data.humidity > 100) {
    return false;
  }
  
  return true;
}

void sendDataToRaspberryPi(SensorData data) {
  // JSON格式发送数据
  Serial.print("{");
  Serial.print("\"temperature\":");
  Serial.print(data.temperature);
  Serial.print(",\"humidity\":");
  Serial.print(data.humidity);
  Serial.print(",\"soilMoisture\":");
  Serial.print(data.soilMoisture);
  Serial.print(",\"lightIntensity\":");
  Serial.print(data.lightIntensity);
  Serial.print(",\"phValue\":");
  Serial.print(data.phValue);
  Serial.print(",\"waterLevel\":");
  Serial.print(data.waterLevel);
  Serial.print(",\"timestamp\":");
  Serial.print(data.timestamp);
  Serial.println("}");
}

void checkAlerts(SensorData data) {
  bool alertTriggered = false;
  
  // 温度告警 (0-40°C正常范围)
  if (data.temperature < 0 || data.temperature > 40) {
    Serial.println("ALERT: 温度异常");
    alertTriggered = true;
  }
  
  // 湿度告警 (30-80%正常范围)
  if (data.humidity < 30 || data.humidity > 80) {
    Serial.println("ALERT: 湿度异常");
    alertTriggered = true;
  }
  
  // 土壤湿度告警 (20-80%正常范围)
  if (data.soilMoisture < 20 || data.soilMoisture > 80) {
    Serial.println("ALERT: 土壤湿度异常");
    alertTriggered = true;
  }
  
  if (alertTriggered) {
    soundAlert();
  }
}

void blinkLED() {
  digitalWrite(LED_PIN, HIGH);
  delay(100);
  digitalWrite(LED_PIN, LOW);
}

void soundAlert() {
  for (int i = 0; i < 3; i++) {
    digitalWrite(BUZZER_PIN, HIGH);
    delay(200);
    digitalWrite(BUZZER_PIN, LOW);
    delay(200);
  }
}

void errorAlert() {
  // 错误指示 - 快速闪烁
  for (int i = 0; i < 10; i++) {
    digitalWrite(LED_PIN, HIGH);
    delay(50);
    digitalWrite(LED_PIN, LOW);
    delay(50);
  }
}
```

### 🐍 树莓派Python代码
```python
# 树莓派数据处理和上传服务
import serial
import json
import time
import requests
import sqlite3
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnvironmentDataCollector:
    def __init__(self):
        # 串口配置
        self.serial_port = '/dev/ttyACM0'
        self.baud_rate = 9600
        self.ser = None
        
        # API配置
        self.api_base_url = 'https://your-domain.com/api'
        self.device_id = 'ENV_001'
        
        # 本地数据库
        self.init_local_database()
        
    def init_local_database(self):
        """初始化本地SQLite数据库"""
        self.conn = sqlite3.connect('sensor_data.db')
        cursor = self.conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sensor_readings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                device_id TEXT,
                temperature REAL,
                humidity REAL,
                soil_moisture INTEGER,
                light_intensity REAL,
                ph_value REAL,
                water_level BOOLEAN,
                timestamp DATETIME,
                uploaded BOOLEAN DEFAULT FALSE
            )
        ''')
        
        self.conn.commit()
        logger.info("本地数据库初始化完成")
    
    def connect_serial(self):
        """连接Arduino串口"""
        try:
            self.ser = serial.Serial(self.serial_port, self.baud_rate, timeout=1)
            logger.info(f"串口连接成功: {self.serial_port}")
            return True
        except Exception as e:
            logger.error(f"串口连接失败: {e}")
            return False
    
    def read_sensor_data(self):
        """读取传感器数据"""
        if not self.ser:
            return None
            
        try:
            line = self.ser.readline().decode('utf-8').strip()
            if line.startswith('{') and line.endswith('}'):
                data = json.loads(line)
                data['device_id'] = self.device_id
                data['timestamp'] = datetime.now().isoformat()
                return data
        except Exception as e:
            logger.error(f"数据读取错误: {e}")
        
        return None
    
    def save_to_local(self, data):
        """保存数据到本地数据库"""
        try:
            cursor = self.conn.cursor()
            cursor.execute('''
                INSERT INTO sensor_readings 
                (device_id, temperature, humidity, soil_moisture, 
                 light_intensity, ph_value, water_level, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                data['device_id'],
                data['temperature'],
                data['humidity'],
                data['soilMoisture'],
                data['lightIntensity'],
                data['phValue'],
                data['waterLevel'],
                data['timestamp']
            ))
            
            self.conn.commit()
            logger.info("数据保存到本地数据库")
            return cursor.lastrowid
        except Exception as e:
            logger.error(f"本地保存失败: {e}")
            return None
    
    def upload_to_cloud(self, data):
        """上传数据到云端"""
        try:
            response = requests.post(
                f"{self.api_base_url}/sensor-data",
                json=data,
                timeout=10
            )
            
            if response.status_code == 200:
                logger.info("数据上传成功")
                return True
            else:
                logger.error(f"上传失败: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"网络上传错误: {e}")
            return False
    
    def sync_offline_data(self):
        """同步离线数据"""
        try:
            cursor = self.conn.cursor()
            cursor.execute('''
                SELECT * FROM sensor_readings 
                WHERE uploaded = FALSE 
                ORDER BY timestamp ASC 
                LIMIT 100
            ''')
            
            offline_data = cursor.fetchall()
            
            for row in offline_data:
                data = {
                    'device_id': row[1],
                    'temperature': row[2],
                    'humidity': row[3],
                    'soilMoisture': row[4],
                    'lightIntensity': row[5],
                    'phValue': row[6],
                    'waterLevel': row[7],
                    'timestamp': row[8]
                }
                
                if self.upload_to_cloud(data):
                    # 标记为已上传
                    cursor.execute(
                        'UPDATE sensor_readings SET uploaded = TRUE WHERE id = ?',
                        (row[0],)
                    )
                    self.conn.commit()
                    
            logger.info(f"同步了 {len(offline_data)} 条离线数据")
            
        except Exception as e:
            logger.error(f"离线数据同步失败: {e}")
    
    def run(self):
        """主运行循环"""
        logger.info("环境数据采集服务启动")
        
        if not self.connect_serial():
            logger.error("无法连接Arduino，退出程序")
            return
        
        while True:
            try:
                # 读取传感器数据
                data = self.read_sensor_data()
                
                if data:
                    # 保存到本地
                    local_id = self.save_to_local(data)
                    
                    # 尝试上传到云端
                    if self.upload_to_cloud(data):
                        # 标记为已上传
                        cursor = self.conn.cursor()
                        cursor.execute(
                            'UPDATE sensor_readings SET uploaded = TRUE WHERE id = ?',
                            (local_id,)
                        )
                        self.conn.commit()
                    
                    # 定期同步离线数据
                    if int(time.time()) % 300 == 0:  # 每5分钟同步一次
                        self.sync_offline_data()
                
                time.sleep(1)  # 1秒检查一次
                
            except KeyboardInterrupt:
                logger.info("程序被用户中断")
                break
            except Exception as e:
                logger.error(f"运行时错误: {e}")
                time.sleep(5)
        
        # 清理资源
        if self.ser:
            self.ser.close()
        self.conn.close()
        logger.info("程序退出")

if __name__ == "__main__":
    collector = EnvironmentDataCollector()
    collector.run()
```

## 🌐 跨端实现方案

### 📱 移动端APP (uni-app)
```vue
<!-- 环境监测页面 -->
<template>
  <view class="container">
    <view class="header">
      <text class="title">环境监测</text>
      <view class="status" :class="deviceStatus">
        {{ deviceStatusText }}
      </view>
    </view>
    
    <!-- 实时数据卡片 -->
    <view class="data-cards">
      <view class="card" v-for="(item, index) in sensorData" :key="index">
        <view class="card-header">
          <text class="card-title">{{ item.name }}</text>
          <text class="card-unit">{{ item.unit }}</text>
        </view>
        <view class="card-value" :class="getValueClass(item)">
          {{ item.value }}
        </view>
        <view class="card-time">
          {{ formatTime(item.updateTime) }}
        </view>
      </view>
    </view>
    
    <!-- 历史趋势图表 -->
    <view class="chart-container">
      <text class="chart-title">24小时趋势</text>
      <canvas canvas-id="trendChart" class="chart"></canvas>
    </view>
    
    <!-- 告警信息 -->
    <view class="alerts" v-if="alerts.length > 0">
      <text class="alerts-title">告警信息</text>
      <view class="alert-item" v-for="alert in alerts" :key="alert.id">
        <view class="alert-level" :class="alert.level">{{ alert.level }}</view>
        <view class="alert-content">
          <text class="alert-message">{{ alert.message }}</text>
          <text class="alert-time">{{ formatTime(alert.time) }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      deviceStatus: 'online',
      sensorData: [
        { name: '温度', value: '--', unit: '°C', type: 'temperature', updateTime: null },
        { name: '湿度', value: '--', unit: '%', type: 'humidity', updateTime: null },
        { name: '土壤湿度', value: '--', unit: '%', type: 'soilMoisture', updateTime: null },
        { name: '光照强度', value: '--', unit: 'lx', type: 'lightIntensity', updateTime: null }
      ],
      alerts: [],
      chartData: []
    }
  },
  
  computed: {
    deviceStatusText() {
      return this.deviceStatus === 'online' ? '在线' : '离线';
    }
  },
  
  onLoad() {
    this.initData();
    this.startRealTimeUpdate();
  },
  
  methods: {
    async initData() {
      try {
        // 获取最新数据
        const response = await this.$http.get('/api/sensor-data/latest');
        this.updateSensorData(response.data);
        
        // 获取历史数据
        const historyResponse = await this.$http.get('/api/sensor-data/history?hours=24');
        this.chartData = historyResponse.data;
        this.drawChart();
        
        // 获取告警信息
        const alertsResponse = await this.$http.get('/api/alerts/recent');
        this.alerts = alertsResponse.data;
        
      } catch (error) {
        console.error('数据加载失败:', error);
        uni.showToast({
          title: '数据加载失败',
          icon: 'none'
        });
      }
    },
    
    updateSensorData(data) {
      this.sensorData.forEach(item => {
        if (data[item.type] !== undefined) {
          item.value = data[item.type];
          item.updateTime = data.timestamp;
        }
      });
      
      this.deviceStatus = 'online';
    },
    
    getValueClass(item) {
      // 根据数值范围返回样式类
      const value = parseFloat(item.value);
      
      switch (item.type) {
        case 'temperature':
          if (value < 0 || value > 40) return 'value-danger';
          if (value < 10 || value > 35) return 'value-warning';
          return 'value-normal';
          
        case 'humidity':
          if (value < 30 || value > 80) return 'value-danger';
          if (value < 40 || value > 70) return 'value-warning';
          return 'value-normal';
          
        case 'soilMoisture':
          if (value < 20 || value > 80) return 'value-danger';
          if (value < 30 || value > 70) return 'value-warning';
          return 'value-normal';
          
        default:
          return 'value-normal';
      }
    },
    
    startRealTimeUpdate() {
      // 每30秒更新一次数据
      setInterval(() => {
        this.initData();
      }, 30000);
    },
    
    drawChart() {
      // 使用uCharts绘制趋势图
      // 这里简化实现，实际项目中需要引入图表库
      console.log('绘制趋势图:', this.chartData);
    },
    
    formatTime(timestamp) {
      if (!timestamp) return '--';
      const date = new Date(timestamp);
      return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
    }
  }
}
</script>

<style scoped>
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.status {
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: white;
}

.status.online {
  background-color: #67c23a;
}

.status.offline {
  background-color: #f56c6c;
}

.data-cards {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.card-title {
  font-size: 28rpx;
  color: #666;
}

.card-unit {
  font-size: 24rpx;
  color: #999;
}

.card-value {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.value-normal { color: #67c23a; }
.value-warning { color: #e6a23c; }
.value-danger { color: #f56c6c; }

.card-time {
  font-size: 20rpx;
  color: #ccc;
}

.chart-container {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.chart-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.chart {
  width: 100%;
  height: 400rpx;
}

.alerts {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
}

.alerts-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.alert-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.alert-level {
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
  color: white;
  margin-right: 20rpx;
}

.alert-level.warning {
  background-color: #e6a23c;
}

.alert-level.danger {
  background-color: #f56c6c;
}

.alert-content {
  flex: 1;
}

.alert-message {
  font-size: 28rpx;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.alert-time {
  font-size: 24rpx;
  color: #999;
}
</style>
```

## 📊 测试和验证

### 🧪 功能测试清单
```yaml
硬件测试:
  - [ ] 传感器数据准确性测试
  - [ ] 电路连接稳定性测试
  - [ ] 防水防尘性能测试
  - [ ] 长时间运行稳定性测试

软件测试:
  - [ ] 数据采集功能测试
  - [ ] 数据上传功能测试
  - [ ] 异常处理测试
  - [ ] 离线数据同步测试

系统集成测试:
  - [ ] 硬件软件联调测试
  - [ ] 网络通信测试
  - [ ] 告警功能测试
  - [ ] 用户界面测试
```

### 📈 性能指标
```yaml
数据采集:
  - 采集频率: 1次/分钟
  - 数据准确率: >95%
  - 响应时间: <5秒
  - 稳定运行: >24小时

通信性能:
  - 数据上传成功率: >98%
  - 网络重连时间: <30秒
  - 离线数据缓存: >1000条
  - 数据同步延迟: <1分钟
```

---

**文档版本**: v1.0  
**创建时间**: 2025-01-31  
**开发状态**: 设计完成  
**测试状态**: 待测试
