@echo off
echo ========================================
echo SFAP后端快速编译测试
echo ========================================

cd backend\main

echo.
echo 正在编译项目...
call mvn compile -q

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ 编译成功！
    echo.
    echo 🎉 所有编译错误已修复！
    echo.
    echo 现在可以启动应用程序：
    echo mvn spring-boot:run
    echo.
    echo 或者运行完整测试：
    echo mvn clean package
) else (
    echo.
    echo ❌ 编译失败！
    echo 请检查错误信息并修复代码问题。
)

echo.
echo 按任意键退出...
pause >nul
