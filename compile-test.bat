@echo off
echo ========================================
echo SFAP后端编译测试
echo ========================================

cd backend\main

echo.
echo 正在清理项目...
call mvn clean

echo.
echo 正在编译项目...
call mvn compile

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ 编译成功！
    echo.
    echo 正在运行测试...
    call mvn test -Dtest=*Controller*Test
    
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo ✅ 测试通过！
        echo.
        echo 正在打包项目...
        call mvn package -DskipTests
        
        if %ERRORLEVEL% EQU 0 (
            echo.
            echo ✅ 打包成功！
            echo.
            echo 🎉 所有编译和测试都通过了！
            echo.
            echo 可以启动应用程序：
            echo mvn spring-boot:run
        ) else (
            echo.
            echo ❌ 打包失败！
        )
    ) else (
        echo.
        echo ⚠️ 测试失败，但编译成功
        echo 可以尝试启动应用程序：
        echo mvn spring-boot:run
    )
) else (
    echo.
    echo ❌ 编译失败！
    echo 请检查错误信息并修复代码问题。
)

echo.
echo 按任意键退出...
pause >nul
