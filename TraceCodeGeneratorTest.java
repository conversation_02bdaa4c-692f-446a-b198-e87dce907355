package com.agriculture.utils;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * 溯源码生成器测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class TraceCodeGeneratorTest {

    @Test
    public void testTraceCodeLength() {
        TraceCodeGenerator generator = new TraceCodeGenerator();
        
        // 测试销售者溯源码生成
        String sellerTraceCode = generator.generateTraceCode(6020L, "seller_upload");
        System.out.println("销售者溯源码: " + sellerTraceCode);
        System.out.println("长度: " + sellerTraceCode.length());
        assert sellerTraceCode.length() == 24 : "销售者溯源码长度应该是24位，实际是" + sellerTraceCode.length() + "位";
        
        // 测试管理员溯源码生成
        String adminTraceCode = generator.generateTraceCode(6020L, "admin_direct");
        System.out.println("管理员溯源码: " + adminTraceCode);
        System.out.println("长度: " + adminTraceCode.length());
        assert adminTraceCode.length() == 24 : "管理员溯源码长度应该是24位，实际是" + adminTraceCode.length() + "位";
        
        // 测试null productId的情况
        String nullProductTraceCode = generator.generateTraceCode(null, "seller_upload");
        System.out.println("null产品ID溯源码: " + nullProductTraceCode);
        System.out.println("长度: " + nullProductTraceCode.length());
        assert nullProductTraceCode.length() == 24 : "null产品ID溯源码长度应该是24位，实际是" + nullProductTraceCode.length() + "位";
        
        // 验证格式
        assert sellerTraceCode.startsWith("SFAPS") : "销售者溯源码应该以SFAPS开头";
        assert adminTraceCode.startsWith("SFAPA") : "管理员溯源码应该以SFAPA开头";
        assert nullProductTraceCode.startsWith("SFAPS") : "null产品ID溯源码应该以SFAPS开头";
        
        System.out.println("✅ 所有测试通过！溯源码长度正确为24位");
    }
    
    @Test
    public void testTraceCodeValidation() {
        TraceCodeGenerator generator = new TraceCodeGenerator();
        
        // 测试有效的24位溯源码
        String validCode24 = "SFAPS25072318516018UX9C0";
        assert generator.isValidTraceCode(validCode24) : "24位溯源码应该有效";
        
        // 测试有效的22位溯源码
        String validCode22 = "SFAP25071410001001A1B2";
        assert generator.isValidTraceCode(validCode22) : "22位溯源码应该有效";
        
        // 测试无效的25位溯源码
        String invalidCode25 = "SFAP175327731836760200007";
        assert !generator.isValidTraceCode(invalidCode25) : "25位溯源码应该无效";
        
        // 测试无效的23位溯源码
        String invalidCode23 = "SFAPS25072318346017070R";
        assert !generator.isValidTraceCode(invalidCode23) : "23位溯源码应该无效";
        
        System.out.println("✅ 溯源码验证测试通过！");
    }
}
