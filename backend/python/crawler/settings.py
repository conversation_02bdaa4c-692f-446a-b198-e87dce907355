# Scrapy settings for agriculture_news_crawler project

BOT_NAME = 'agriculture_news_crawler'

SPIDER_MODULES = ['crawler.spiders']
NEWSPIDER_MODULE = 'crawler.spiders'

# 爬虫设置
USER_AGENT = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
ROBOTSTXT_OBEY = True
CONCURRENT_REQUESTS = 4  # 并发请求数
DOWNLOAD_DELAY = 2  # 下载延迟，避免请求过于频繁
COOKIES_ENABLED = False  # 禁用cookies

# 配置日志
LOG_LEVEL = 'INFO'
LOG_FORMAT = '%(asctime)s [%(name)s] %(levelname)s: %(message)s'

# 配置管道
ITEM_PIPELINES = {
    'crawler.pipelines.AgricultureNewsPipeline': 300,
}

# 配置下载中间件
DOWNLOADER_MIDDLEWARES = {
    'scrapy.downloadermiddlewares.useragent.UserAgentMiddleware': None,
    'scrapy.downloadermiddlewares.retry.RetryMiddleware': 500,
}

# 重试设置
RETRY_ENABLED = True
RETRY_TIMES = 3  # 重试次数
RETRY_HTTP_CODES = [500, 502, 503, 504, 408, 429]  # 重试的HTTP状态码

# 超时设置
DOWNLOAD_TIMEOUT = 15  # 下载超时时间 