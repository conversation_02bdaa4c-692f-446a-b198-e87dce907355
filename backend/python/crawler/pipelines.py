import os
import logging
import requests
from datetime import datetime
import sys
import io
from PIL import Image

# 添加父目录到路径，以便导入db_manager
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from db_manager import get_db_manager

logger = logging.getLogger(__name__)

class AgricultureNewsPipeline:
    """处理农业新闻数据的管道"""
    
    def __init__(self):
        self.db_manager = get_db_manager()
    
    def process_item(self, item, spider):
        """处理爬取到的新闻数据"""
        try:
            # 准备数据
            title = item.get('title')
            content = item.get('content')
            summary = item.get('summary')
            source = item.get('source')
            publish_time = item.get('publish_time')
            url = item.get('url')
            category = item.get('category', '农业要闻')
            views = item.get('views', 0)
            
            # 如果没有发布时间，使用当前时间
            if not publish_time:
                publish_time = datetime.now()
            
            # 保存新闻到数据库
            news_id = self.db_manager.save_news(
                title=title,
                content=content,
                summary=summary,
                source=source,
                publish_time=publish_time,
                url=url,
                category=category,
                views=views
            )
            
            if not news_id:
                logger.error(f"保存新闻失败: {title}")
                return item
            
            # 处理图片
            image_urls = item.get('image_urls', [])
            for image_data in image_urls:
                image_url = image_data.get('url')
                is_cover = image_data.get('is_cover', False)
                
                if not image_url:
                    continue
                
                try:
                    # 下载图片
                    response = requests.get(image_url, timeout=10)
                    if response.status_code == 200:
                        # 保存图片
                        self.db_manager.save_image(
                            news_id=news_id,
                            image_url=image_url,
                            image_data=response.content,
                            is_cover=is_cover
                        )
                    else:
                        logger.warning(f"下载图片失败: {image_url}, 状态码: {response.status_code}")
                        # 仍然保存图片URL到数据库，但不保存图片数据
                        self.db_manager.save_image(
                            news_id=news_id,
                            image_url=image_url,
                            is_cover=is_cover
                        )
                except Exception as e:
                    logger.error(f"处理图片失败: {image_url}, 错误: {str(e)}")
                    # 仍然保存图片URL到数据库，但不保存图片数据
                    self.db_manager.save_image(
                        news_id=news_id,
                        image_url=image_url,
                        is_cover=is_cover
                    )
            
            return item
        except Exception as e:
            logger.error(f"处理新闻数据失败: {str(e)}")
            return item 