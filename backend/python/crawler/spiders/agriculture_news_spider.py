import scrapy
import re
from datetime import datetime
import logging
from urllib.parse import urljoin

logger = logging.getLogger(__name__)

class AgricultureNewsSpider(scrapy.Spider):
    name = 'agriculture_news'
    allowed_domains = ['agri.cn']
    start_urls = ['https://www.agri.cn/zx/nyyw/']
    
    def parse(self, response):
        """解析新闻列表页"""
        # 调试输出，查看页面结构
        self.logger.info(f"开始解析列表页: {response.url}")
        
        # 使用新发现的选择器
        news_items = response.css('div.list_li_box')
        
        # 如果上面的选择器没有找到结果，尝试其他选择器
        if not news_items:
            news_items = response.css('div.news-list ul li')
        if not news_items:
            news_items = response.css('ul li.news-item')
        if not news_items:
            news_items = response.css('div.news-list li')
        if not news_items:
            news_items = response.css('div.content-list li')
        if not news_items:
            # 最后尝试更通用的选择器
            news_items = response.css('li a[href*="nyyw"]').xpath('./parent::li')
        
        self.logger.info(f"找到 {len(news_items)} 条新闻")
        
        for item in news_items:
            # 提取标题和链接 - 使用新发现的选择器
            title = None
            url = None
            
            # 尝试新的选择器
            title_link = item.css('div.list_li_con a')
            if title_link:
                title = title_link.css('::text').get()
                url = title_link.css('::attr(href)').get()
            
            # 如果新选择器没有找到，尝试旧的选择器
            if not title or not url:
                title_link = item.css('a')
                if title_link:
                    title = title_link.css('::text').get()
                    url = title_link.css('::attr(href)').get()
            
            # 尝试方法2
            if not title or not url:
                title = item.css('h3::text, h4::text, div.title::text').get()
                url = item.css('a::attr(href)').get()
            
            # 如果仍然找不到，则跳过
            if not title or not url:
                self.logger.warning(f"无法提取标题或URL: {item.get()}")
                continue
                
            title = title.strip()
            
            # 处理相对URL
            if not url.startswith('http'):
                url = urljoin(response.url, url)
            
            # 提取日期 - 使用新发现的选择器
            date_text = item.css('div.con_date::text').get()
            if not date_text:
                date_text = item.css('span.time::text, span.date::text, div.date::text').get()
            
            if not date_text:
                # 尝试查找包含日期格式的文本
                all_texts = item.css('*::text').getall()
                for text in all_texts:
                    if re.search(r'\d{4}-\d{2}-\d{2}|\d{4}年\d{1,2}月\d{1,2}日', text):
                        date_text = text.strip()
                        break
            
            self.logger.info(f"提取到新闻: {title}, URL: {url}, 日期: {date_text}")
            
            # 构建请求详情页
            yield scrapy.Request(
                url=url,
                callback=self.parse_detail,
                meta={
                    'title': title,
                    'url': url,
                    'publish_date': date_text
                }
            )
        
        # 处理分页 - 尝试多种选择器
        next_page = None
        next_selectors = [
            'div.page a:contains("下一页")::attr(href)',
            'div.pagination a:contains("下一页")::attr(href)',
            'a.next::attr(href)',
            'a[rel="next"]::attr(href)',
            'a:contains("下一页")::attr(href)'
        ]
        
        for selector in next_selectors:
            next_page = response.css(selector).get()
            if next_page:
                break
        
        if next_page:
            if not next_page.startswith('http'):
                next_page = urljoin(response.url, next_page)
            self.logger.info(f"找到下一页: {next_page}")
            yield scrapy.Request(url=next_page, callback=self.parse)
    
    def parse_detail(self, response):
        """解析新闻详情页"""
        self.logger.info(f"开始解析详情页: {response.url}")
        
        title = response.meta.get('title')
        url = response.meta.get('url')
        publish_date = response.meta.get('publish_date')
        
        # 提取文章内容 - 尝试多种选择器
        content = ""
        content_selectors = [
            'div.article-content p',
            'div.content p',
            'div.article p',
            'div.TRS_Editor p',
            'div.con_con p'  # 新增选择器
        ]
        
        for selector in content_selectors:
            content_parts = response.css(selector)
            if content_parts:
                content = '\n'.join([p.get().strip() for p in content_parts if p.get() and p.get().strip()])
                break
        
        # 如果仍然找不到内容，尝试更通用的选择器
        if not content:
            content_parts = response.css('div[class*="content"] p, div[class*="con"] p')
            content = '\n'.join([p.get().strip() for p in content_parts if p.get() and p.get().strip()])
        
        # 清理HTML标签
        content = re.sub(r'<[^>]+>', '', content)
        
        # 提取摘要（取内容前100个字符）
        summary = content[:100] + '...' if len(content) > 100 else content
        
        # 提取来源 - 尝试多种选择器
        source = None
        source_selectors = [
            'div.article-meta span.source::text',
            'div.info span.source::text',
            'div.meta span.source::text',
            'span:contains("来源")::text'
        ]
        
        for selector in source_selectors:
            source = response.css(selector).get()
            if source:
                source = source.strip()
                if source.startswith('来源：'):
                    source = source[3:].strip()
                break
        
        if not source:
            source = '中国农业农村部'
        
        # 处理发布时间
        publish_time = None
        if publish_date:
            # 尝试多种日期格式
            date_formats = ['%Y-%m-%d', '%Y年%m月%d日', '%Y/%m/%d']
            for fmt in date_formats:
                try:
                    publish_time = datetime.strptime(publish_date.strip(), fmt)
                    break
                except ValueError:
                    continue
        
        # 如果从列表页没有获取到日期，尝试从详情页获取
        if not publish_time:
            date_selectors = [
                'div.article-meta span.time::text',
                'div.info span.date::text',
                'div.meta span.date::text',
                'span:contains("时间")::text'
            ]
            
            for selector in date_selectors:
                date_text = response.css(selector).get()
                if date_text:
                    date_match = re.search(r'\d{4}-\d{2}-\d{2}|\d{4}年\d{1,2}月\d{1,2}日|\d{4}/\d{1,2}/\d{1,2}', date_text)
                    if date_match:
                        date_text = date_match.group()
                        for fmt in date_formats:
                            try:
                                publish_time = datetime.strptime(date_text.strip(), fmt)
                                break
                            except ValueError:
                                continue
                    if publish_time:
                        break
        
        # 如果仍然没有日期，使用当前日期
        if not publish_time:
            publish_time = datetime.now()
        
        # 提取阅读量
        views = 0
        views_selectors = [
            'div.article-meta span.views::text',
            'div.info span.views::text',
            'span:contains("阅读")::text'
        ]
        
        for selector in views_selectors:
            views_text = response.css(selector).get()
            if views_text:
                views_match = re.search(r'\d+', views_text)
                if views_match:
                    views = int(views_match.group())
                    break
        
        # 提取图片
        image_urls = []
        
        # 先尝试获取封面图
        cover_selectors = [
            'div.article-cover img::attr(src)',
            'div.cover img::attr(src)',
            'div.banner img::attr(src)'
        ]
        
        cover_img = None
        for selector in cover_selectors:
            cover_img = response.css(selector).get()
            if cover_img:
                break
        
        if cover_img:
            if not cover_img.startswith('http'):
                cover_img = urljoin(response.url, cover_img)
            image_urls.append({'url': cover_img, 'is_cover': True})
        
        # 获取正文中的图片
        content_img_selectors = [
            'div.article-content img::attr(src)',
            'div.content img::attr(src)',
            'div.article img::attr(src)',
            'div.TRS_Editor img::attr(src)',
            'div.con_con img::attr(src)'  # 新增选择器
        ]
        
        content_images = []
        for selector in content_img_selectors:
            content_images = response.css(selector).getall()
            if content_images:
                break
        
        for img_url in content_images:
            if img_url:
                if not img_url.startswith('http'):
                    img_url = urljoin(response.url, img_url)
                # 避免重复添加封面图
                if img_url != cover_img:
                    image_urls.append({'url': img_url, 'is_cover': False})
        
        self.logger.info(f"解析完成: {title}, 内容长度: {len(content)}, 图片数: {len(image_urls)}")
        
        # 返回结果
        return {
            'title': title,
            'url': url,
            'content': content,
            'summary': summary,
            'source': source,
            'publish_time': publish_time,
            'views': views,
            'image_urls': image_urls,
            'category': '农业要闻'
        } 