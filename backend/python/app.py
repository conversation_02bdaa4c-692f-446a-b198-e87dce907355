from flask import Flask, request, jsonify
from flask_cors import CORS
import logging
import os
import subprocess
import threading
import time
from datetime import datetime, timedelta
import json
from db_manager import get_db_manager
import sys
import importlib.util
import traceback

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# 初始化数据库管理器
db_manager = get_db_manager()

# 爬虫运行状态
crawler_status = {
    'is_running': False,
    'last_run': None,
    'next_run': None,
    'status': 'idle',
    'error': None
}

def run_crawler():
    """运行爬虫的函数"""
    global crawler_status
    
    if crawler_status['is_running']:
        logger.warning("爬虫已在运行中")
        return
    
    try:
        crawler_status['is_running'] = True
        crawler_status['status'] = 'running'
        crawler_status['error'] = None
        crawler_status['last_run'] = datetime.now()
        
        # 使用Python代码直接运行爬虫，而不是通过命令行
        try:
            # 添加当前目录到sys.path
            current_dir = os.path.dirname(os.path.abspath(__file__))
            if current_dir not in sys.path:
                sys.path.append(current_dir)
            
            # 导入并运行爬虫
            from scrapy.crawler import CrawlerProcess
            from scrapy.utils.project import get_project_settings
            from crawler.spiders.agriculture_news_spider import AgricultureNewsSpider
            
            # 设置工作目录
            os.chdir(os.path.join(current_dir, 'crawler'))
            
            # 获取设置
            settings = get_project_settings()
            settings.set('LOG_FILE', os.path.join(current_dir, 'crawler.log'))
            
            # 创建爬虫进程
            process = CrawlerProcess(settings)
            process.crawl(AgricultureNewsSpider)
            
            # 运行爬虫（非阻塞方式）
            def run_crawler_process():
                process.start()
                process.stop()
            
            # 在新线程中运行爬虫进程
            crawler_thread = threading.Thread(target=run_crawler_process)
            crawler_thread.start()
            crawler_thread.join(timeout=300)  # 最多等待5分钟
            
            logger.info("爬虫运行成功")
            crawler_status['status'] = 'completed'
        except Exception as e:
            error_msg = f"爬虫运行失败: {str(e)}\n{traceback.format_exc()}"
            logger.error(error_msg)
            crawler_status['status'] = 'failed'
            crawler_status['error'] = error_msg
    except Exception as e:
        error_msg = f"启动爬虫失败: {str(e)}\n{traceback.format_exc()}"
        logger.error(error_msg)
        crawler_status['status'] = 'failed'
        crawler_status['error'] = error_msg
    finally:
        crawler_status['is_running'] = False
        # 设置下次运行时间（12小时后）
        crawler_status['next_run'] = datetime.now() + timedelta(hours=12)

def schedule_crawler():
    """定时调度爬虫"""
    while True:
        now = datetime.now()
        next_run = crawler_status.get('next_run')
        
        if next_run and now >= next_run and not crawler_status['is_running']:
            logger.info("定时任务触发爬虫运行")
            threading.Thread(target=run_crawler).start()
        
        # 如果从未运行过，立即运行一次
        if crawler_status.get('last_run') is None and not crawler_status['is_running']:
            logger.info("首次启动爬虫")
            threading.Thread(target=run_crawler).start()
        
        # 每分钟检查一次
        time.sleep(60)

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查端点"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'crawler_status': crawler_status
    })

@app.route('/api/news/list', methods=['GET'])
def get_news_list():
    """获取新闻列表API"""
    try:
        category = request.args.get('category')
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('pageSize', 10))
        
        # 计算偏移量
        offset = (page - 1) * page_size
        
        # 获取新闻列表
        news_list = db_manager.get_news_list(
            category=category,
            limit=page_size,
            offset=offset
        )
        
        # 格式化日期时间
        for news in news_list:
            if 'publish_time' in news and news['publish_time']:
                news['publish_time'] = news['publish_time'].isoformat()
            if 'crawl_time' in news and news['crawl_time']:
                news['crawl_time'] = news['crawl_time'].isoformat()
            if 'update_time' in news and news['update_time']:
                news['update_time'] = news['update_time'].isoformat()
        
        return jsonify({
            'code': 0,
            'msg': 'success',
            'data': {
                'total': len(news_list),  # 这里应该从数据库获取总数，但为简化先使用列表长度
                'items': news_list
            }
        })
    except Exception as e:
        logger.error(f"获取新闻列表失败: {str(e)}")
        return jsonify({
            'code': 500,
            'msg': f"获取新闻列表失败: {str(e)}",
            'data': None
        }), 500

@app.route('/api/news/detail/<int:news_id>', methods=['GET'])
def get_news_detail(news_id):
    """获取新闻详情API"""
    try:
        news = db_manager.get_news_detail(news_id)
        
        if not news:
            return jsonify({
                'code': 404,
                'msg': '新闻不存在',
                'data': None
            }), 404
        
        # 格式化日期时间
        if 'publish_time' in news and news['publish_time']:
            news['publish_time'] = news['publish_time'].isoformat()
        if 'crawl_time' in news and news['crawl_time']:
            news['crawl_time'] = news['crawl_time'].isoformat()
        if 'update_time' in news and news['update_time']:
            news['update_time'] = news['update_time'].isoformat()
        
        # 处理图片列表
        for img in news.get('images', []):
            if 'crawl_time' in img and img['crawl_time']:
                img['crawl_time'] = img['crawl_time'].isoformat()
        
        return jsonify({
            'code': 0,
            'msg': 'success',
            'data': news
        })
    except Exception as e:
        logger.error(f"获取新闻详情失败: {str(e)}")
        return jsonify({
            'code': 500,
            'msg': f"获取新闻详情失败: {str(e)}",
            'data': None
        }), 500

@app.route('/api/crawler/run', methods=['POST'])
def trigger_crawler():
    """手动触发爬虫运行"""
    if crawler_status['is_running']:
        return jsonify({
            'code': 400,
            'msg': '爬虫已在运行中',
            'data': crawler_status
        }), 400
    
    # 在新线程中运行爬虫
    threading.Thread(target=run_crawler).start()
    
    return jsonify({
        'code': 0,
        'msg': '爬虫启动成功',
        'data': crawler_status
    })

@app.route('/api/crawler/status', methods=['GET'])
def get_crawler_status():
    """获取爬虫状态"""
    status_copy = crawler_status.copy()
    
    # 格式化日期时间
    if status_copy.get('last_run'):
        status_copy['last_run'] = status_copy['last_run'].isoformat()
    if status_copy.get('next_run'):
        status_copy['next_run'] = status_copy['next_run'].isoformat()
    
    return jsonify({
        'code': 0,
        'msg': 'success',
        'data': status_copy
    })

if __name__ == '__main__':
    # 启动定时任务线程
    scheduler_thread = threading.Thread(target=schedule_crawler, daemon=True)
    scheduler_thread.start()

    # 启动Flask应用 - 修改端口为5001避免与AI服务冲突
    app.run(host='0.0.0.0', port=5001)