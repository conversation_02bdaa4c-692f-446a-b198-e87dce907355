# 农业新闻爬虫系统

这是一个用于爬取农业农村部网站新闻的系统，包括爬虫模块和API接口。

## 功能特点

- 自动爬取农业农村部网站（https://www.agri.cn/zx/nyyw/）的新闻内容
- 支持新闻内容、图片的自动下载和存储
- 提供RESTful API接口，方便前端调用
- 支持定时爬取，默认每12小时爬取一次
- 支持手动触发爬虫运行

## 系统要求

- Python 3.8+
- MySQL 5.7+

## 安装依赖

```bash
pip install -r requirements.txt
```

## 数据库配置

在 `db_manager.py` 文件中配置数据库连接信息：

```python
self.config = {
    'host': 'localhost',
    'user': 'root',
    'password': 'your_password',
    'database': 'agriculture_mall',
    'port': 3306
}
```

## 运行系统

```bash
python app.py
```

## API接口

### 1. 获取新闻列表

- **URL**: `/api/news/list`
- **方法**: GET
- **参数**:
  - `category`: 新闻分类（可选）
  - `page`: 页码，默认为1
  - `pageSize`: 每页数量，默认为10
- **响应示例**:
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "total": 10,
    "items": [
      {
        "id": 1,
        "title": "农业农村部召开推进部社会组织高质量发展工作会",
        "summary": "本网讯 6月27日，农业农村部召开推进部社会组织高质量发展工作会...",
        "source": "中国农业农村部",
        "publish_time": "2025-06-27T00:00:00",
        "url": "https://www.agri.cn/zx/nyyw/202506/t20250627_7345678.htm",
        "category": "农业要闻",
        "views": 123,
        "cover_image": "https://www.agri.cn/images/202506/t20250627_7345678.jpg"
      },
      // 更多新闻...
    ]
  }
}
```

### 2. 获取新闻详情

- **URL**: `/api/news/detail/{news_id}`
- **方法**: GET
- **响应示例**:
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "id": 1,
    "title": "农业农村部召开推进部社会组织高质量发展工作会",
    "content": "本网讯 6月27日，农业农村部召开推进部社会组织高质量发展工作会，农业农村部党组成员、副部长张治礼出席会议并讲话...",
    "summary": "本网讯 6月27日，农业农村部召开推进部社会组织高质量发展工作会...",
    "source": "中国农业农村部",
    "publish_time": "2025-06-27T00:00:00",
    "url": "https://www.agri.cn/zx/nyyw/202506/t20250627_7345678.htm",
    "category": "农业要闻",
    "views": 124,
    "images": [
      {
        "id": 1,
        "news_id": 1,
        "image_url": "https://www.agri.cn/images/202506/t20250627_7345678.jpg",
        "image_path": "backend/python/images/news/1/abcdef1234567890.jpg",
        "is_cover": 1,
        "width": 800,
        "height": 600,
        "file_size": 123
      },
      // 更多图片...
    ]
  }
}
```

### 3. 手动触发爬虫

- **URL**: `/api/crawler/run`
- **方法**: POST
- **响应示例**:
```json
{
  "code": 0,
  "msg": "爬虫启动成功",
  "data": {
    "is_running": true,
    "last_run": "2023-06-28T12:34:56",
    "next_run": "2023-06-28T24:34:56",
    "status": "running",
    "error": null
  }
}
```

### 4. 获取爬虫状态

- **URL**: `/api/crawler/status`
- **方法**: GET
- **响应示例**:
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "is_running": false,
    "last_run": "2023-06-28T12:34:56",
    "next_run": "2023-06-28T24:34:56",
    "status": "completed",
    "error": null
  }
}
```

## 目录结构

```
backend/python/
├── app.py                  # 主应用程序，提供API接口
├── db_manager.py           # 数据库管理模块
├── requirements.txt        # 依赖列表
├── README.md               # 说明文档
├── crawler/                # 爬虫模块
│   ├── __init__.py
│   ├── settings.py         # 爬虫配置
│   ├── pipelines.py        # 数据处理管道
│   └── spiders/            # 爬虫实现
│       ├── __init__.py
│       └── agriculture_news_spider.py  # 农业新闻爬虫
└── images/                 # 图片存储目录
    └── news/               # 新闻图片目录
```

## 注意事项

1. 爬虫仅用于学习和研究，请勿用于商业用途
2. 爬取频率设置为较低，避免对目标网站造成压力
3. 遵守网站的robots.txt规则
4. 图片存储在本地，但数据库中同时保存了原始URL，可以根据需要选择使用本地图片或原始URL 