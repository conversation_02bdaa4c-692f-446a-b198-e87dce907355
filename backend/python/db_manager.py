import mysql.connector
import logging
import time
from datetime import datetime
import os
from PIL import Image
import io
import hashlib

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DBManager:
    """数据库管理类，用于新闻数据的存储和检索"""
    
    def __init__(self, config=None):
        """初始化数据库连接
        
        Args:
            config (dict): 数据库连接配置，包含host, user, password, database等信息
        """
        self.config = config or {
            'host': 'localhost',
            'user': 'root',
            'password': 'fan13965711955',
            'database': 'agriculture_mall',
            'port': 3306
        }
        self.conn = None
        self.max_retries = 3
        self.retry_interval = 2  # 秒
    
    def connect(self):
        """建立数据库连接"""
        retries = 0
        while retries < self.max_retries:
            try:
                self.conn = mysql.connector.connect(**self.config)
                logger.info("成功连接到数据库")
                return True
            except mysql.connector.Error as err:
                retries += 1
                logger.error(f"数据库连接失败 (尝试 {retries}/{self.max_retries}): {err}")
                if retries < self.max_retries:
                    time.sleep(self.retry_interval)
                else:
                    logger.error("数据库连接失败，已达到最大重试次数")
                    return False
    
    def close(self):
        """关闭数据库连接"""
        if self.conn and self.conn.is_connected():
            self.conn.close()
            logger.info("数据库连接已关闭")
    
    def ensure_tables_exist(self):
        """确保新闻和图片表存在"""
        if not self.conn or not self.conn.is_connected():
            if not self.connect():
                return False
        
        try:
            cursor = self.conn.cursor()
            
            # 创建新闻表
            create_news_table_query = """
            CREATE TABLE IF NOT EXISTS agriculture_news (
                id INT AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(255) NOT NULL COMMENT '新闻标题',
                content TEXT COMMENT '新闻正文内容',
                summary VARCHAR(500) DEFAULT NULL COMMENT '新闻摘要',
                source VARCHAR(100) DEFAULT NULL COMMENT '新闻来源',
                publish_time DATETIME DEFAULT NULL COMMENT '发布时间',
                url VARCHAR(255) NOT NULL COMMENT '原文链接',
                category VARCHAR(50) DEFAULT NULL COMMENT '新闻分类',
                views INT DEFAULT 0 COMMENT '阅读量',
                crawl_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '爬取时间',
                update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                UNIQUE KEY idx_url (url),
                KEY idx_publish_time (publish_time),
                KEY idx_category (category)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='农业新闻表';
            """
            cursor.execute(create_news_table_query)
            
            # 创建图片表
            create_images_table_query = """
            CREATE TABLE IF NOT EXISTS news_images (
                id INT AUTO_INCREMENT PRIMARY KEY,
                news_id INT NOT NULL COMMENT '关联的新闻ID',
                image_url VARCHAR(255) NOT NULL COMMENT '原始图片URL',
                image_path VARCHAR(255) DEFAULT NULL COMMENT '本地存储路径',
                is_cover TINYINT(1) DEFAULT 0 COMMENT '是否为封面图',
                width INT DEFAULT NULL COMMENT '图片宽度',
                height INT DEFAULT NULL COMMENT '图片高度',
                file_size INT DEFAULT NULL COMMENT '文件大小(KB)',
                crawl_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '爬取时间',
                KEY idx_news_id (news_id),
                CONSTRAINT fk_news_images_news FOREIGN KEY (news_id) REFERENCES agriculture_news (id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='新闻图片表';
            """
            cursor.execute(create_images_table_query)
            
            self.conn.commit()
            logger.info("新闻相关表创建或已存在")
            return True
        except mysql.connector.Error as err:
            logger.error(f"创建表失败: {err}")
            return False
        finally:
            if cursor:
                cursor.close()
    
    def save_news(self, title, content, summary, source, publish_time, url, category="农业要闻", views=0):
        """保存新闻到数据库
        
        Args:
            title (str): 新闻标题
            content (str): 新闻正文内容
            summary (str): 新闻摘要
            source (str): 新闻来源
            publish_time (datetime): 发布时间
            url (str): 原文链接
            category (str): 新闻分类
            views (int): 阅读量
        
        Returns:
            int: 新闻ID，如果保存失败则返回None
        """
        if not self.ensure_tables_exist():
            return None
        
        try:
            cursor = self.conn.cursor()
            
            # 检查是否已存在相同URL的新闻
            check_query = "SELECT id FROM agriculture_news WHERE url = %s"
            cursor.execute(check_query, (url,))
            existing_news = cursor.fetchone()
            
            if existing_news:
                # 更新现有新闻
                update_query = """
                UPDATE agriculture_news 
                SET title = %s, content = %s, summary = %s, source = %s, 
                    publish_time = %s, category = %s, views = %s, update_time = NOW()
                WHERE url = %s
                """
                cursor.execute(update_query, (
                    title, content, summary, source, 
                    publish_time, category, views, url
                ))
                news_id = existing_news[0]
                logger.info(f"更新新闻: {title} (ID: {news_id})")
            else:
                # 插入新新闻
                insert_query = """
                INSERT INTO agriculture_news 
                (title, content, summary, source, publish_time, url, category, views) 
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                """
                cursor.execute(insert_query, (
                    title, content, summary, source, 
                    publish_time, url, category, views
                ))
                news_id = cursor.lastrowid
                logger.info(f"保存新闻: {title} (ID: {news_id})")
            
            self.conn.commit()
            return news_id
        except Exception as e:
            self.conn.rollback()
            logger.error(f"保存新闻失败: {str(e)}")
            return None
        finally:
            if cursor:
                cursor.close()
    
    def save_image(self, news_id, image_url, image_data=None, is_cover=False):
        """保存新闻图片
        
        Args:
            news_id (int): 关联的新闻ID
            image_url (str): 原始图片URL
            image_data (bytes): 图片二进制数据，如果为None则不保存本地文件
            is_cover (bool): 是否为封面图
        
        Returns:
            int: 图片ID，如果保存失败则返回None
        """
        if not self.conn or not self.conn.is_connected():
            if not self.connect():
                return None
        
        try:
            cursor = self.conn.cursor()
            
            # 检查是否已存在相同URL的图片
            check_query = "SELECT id FROM news_images WHERE news_id = %s AND image_url = %s"
            cursor.execute(check_query, (news_id, image_url))
            existing_image = cursor.fetchone()
            
            # 处理图片数据
            image_path = None
            width = None
            height = None
            file_size = None
            
            if image_data:
                # 生成文件名
                image_hash = hashlib.md5(image_url.encode()).hexdigest()
                # 确保目录存在
                image_dir = os.path.join('backend', 'python', 'images', 'news', str(news_id))
                os.makedirs(image_dir, exist_ok=True)
                
                image_path = os.path.join(image_dir, f"{image_hash}.jpg")
                
                # 保存图片文件
                try:
                    # 使用PIL处理图片
                    img = Image.open(io.BytesIO(image_data))
                    width, height = img.size
                    img.save(image_path)
                    file_size = len(image_data) // 1024  # KB
                except Exception as e:
                    logger.error(f"处理图片失败: {str(e)}")
            
            if existing_image:
                # 更新现有图片
                update_query = """
                UPDATE news_images 
                SET image_path = %s, is_cover = %s, width = %s, height = %s, file_size = %s
                WHERE id = %s
                """
                cursor.execute(update_query, (
                    image_path, is_cover, width, height, file_size, existing_image[0]
                ))
                image_id = existing_image[0]
            else:
                # 插入新图片
                insert_query = """
                INSERT INTO news_images 
                (news_id, image_url, image_path, is_cover, width, height, file_size) 
                VALUES (%s, %s, %s, %s, %s, %s, %s)
                """
                cursor.execute(insert_query, (
                    news_id, image_url, image_path, is_cover, width, height, file_size
                ))
                image_id = cursor.lastrowid
            
            self.conn.commit()
            return image_id
        except Exception as e:
            self.conn.rollback()
            logger.error(f"保存图片失败: {str(e)}")
            return None
        finally:
            if cursor:
                cursor.close()
    
    def get_news_list(self, category=None, limit=20, offset=0):
        """获取新闻列表
        
        Args:
            category (str): 新闻分类，为None则获取所有分类
            limit (int): 返回的最大记录数
            offset (int): 起始偏移量
        
        Returns:
            list: 新闻列表
        """
        if not self.conn or not self.conn.is_connected():
            if not self.connect():
                return []
        
        try:
            cursor = self.conn.cursor(dictionary=True)
            
            if category:
                query = """
                SELECT n.*, 
                       (SELECT image_url FROM news_images WHERE news_id = n.id AND is_cover = 1 LIMIT 1) as cover_image 
                FROM agriculture_news n 
                WHERE n.category = %s 
                ORDER BY n.publish_time DESC 
                LIMIT %s OFFSET %s
                """
                cursor.execute(query, (category, limit, offset))
            else:
                query = """
                SELECT n.*, 
                       (SELECT image_url FROM news_images WHERE news_id = n.id AND is_cover = 1 LIMIT 1) as cover_image 
                FROM agriculture_news n 
                ORDER BY n.publish_time DESC 
                LIMIT %s OFFSET %s
                """
                cursor.execute(query, (limit, offset))
            
            news_list = cursor.fetchall()
            return news_list
        except Exception as e:
            logger.error(f"获取新闻列表失败: {str(e)}")
            return []
        finally:
            if cursor:
                cursor.close()
    
    def get_news_detail(self, news_id):
        """获取新闻详情
        
        Args:
            news_id (int): 新闻ID
        
        Returns:
            dict: 新闻详情，包括图片列表
        """
        if not self.conn or not self.conn.is_connected():
            if not self.connect():
                return None
        
        try:
            cursor = self.conn.cursor(dictionary=True)
            
            # 获取新闻基本信息
            query = "SELECT * FROM agriculture_news WHERE id = %s"
            cursor.execute(query, (news_id,))
            news = cursor.fetchone()
            
            if not news:
                return None
            
            # 获取新闻图片
            query = "SELECT * FROM news_images WHERE news_id = %s ORDER BY is_cover DESC"
            cursor.execute(query, (news_id,))
            images = cursor.fetchall()
            
            news['images'] = images
            
            # 更新阅读量
            update_query = "UPDATE agriculture_news SET views = views + 1 WHERE id = %s"
            cursor.execute(update_query, (news_id,))
            self.conn.commit()
            
            return news
        except Exception as e:
            logger.error(f"获取新闻详情失败: {str(e)}")
            return None
        finally:
            if cursor:
                cursor.close()

# 单例模式获取数据库管理器
def get_db_manager(config=None):
    """获取数据库管理器实例"""
    if not hasattr(get_db_manager, 'instance'):
        get_db_manager.instance = DBManager(config)
    return get_db_manager.instance 