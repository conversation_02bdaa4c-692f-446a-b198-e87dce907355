import requests
from bs4 import BeautifulSoup
import re

def analyze_site():
    url = 'https://www.agri.cn/zx/nyyw/'
    print(f"分析网站: {url}")
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    r = requests.get(url, headers=headers)
    r.encoding = 'utf-8'  # 确保正确的编码
    soup = BeautifulSoup(r.text, 'html.parser')
    
    # 保存HTML以便检查
    with open('page.html', 'w', encoding='utf-8') as f:
        f.write(r.text)
    print("已保存HTML到page.html")
    
    # 查找页面上的所有链接，特别是新闻链接
    print("\n查找页面上所有可能的新闻链接:")
    news_links = []
    for a in soup.select('a'):
        href = a.get('href', '')
        text = a.text.strip()
        # 过滤可能的新闻链接
        if href and text and len(text) > 10:
            if href.endswith('.html') or '/zx/' in href or '/news/' in href:
                news_links.append((text, href))
    
    print(f"找到 {len(news_links)} 个可能的新闻链接")
    for i, (text, href) in enumerate(news_links[:10]):
        print(f"{i+1}. {text[:50]}... -> {href}")
    
    # 查找页面上的所有列表元素
    print("\n查找页面上的列表结构:")
    for tag in ['ul', 'ol', 'div.list', '.list', '.news-list']:
        items = soup.select(tag)
        print(f"{tag}: {len(items)}")
    
    # 查找具有特定类的div
    print("\n查找具有特定类的div:")
    divs_with_class = soup.find_all('div', class_=True)
    class_counts = {}
    for div in divs_with_class:
        for class_name in div.get('class', []):
            if class_name in class_counts:
                class_counts[class_name] += 1
            else:
                class_counts[class_name] = 1
    
    # 按出现次数排序
    sorted_classes = sorted(class_counts.items(), key=lambda x: x[1], reverse=True)
    for class_name, count in sorted_classes[:10]:
        print(f".{class_name}: {count}")
        
        # 对于前几个最常见的类，检查它们的子元素是否包含链接
        if count > 2:
            elements = soup.select(f'.{class_name}')
            links_in_class = sum(1 for e in elements if e.find('a'))
            print(f"  - 包含链接的元素: {links_in_class}/{count}")
            
            if links_in_class > 0:
                # 显示第一个包含链接的元素的文本
                for e in elements:
                    if e.find('a'):
                        print(f"  - 示例文本: {e.text.strip()[:100]}...")
                        break

if __name__ == "__main__":
    analyze_site() 