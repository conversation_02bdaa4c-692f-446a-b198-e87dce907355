-- 测试销售者产品溯源同步机制
-- 此脚本用于验证新创建的销售者产品是否能正确同步到溯源相关表

-- 1. 查看当前数据状态
SELECT 
    'Before Test' as test_phase,
    (SELECT COUNT(*) FROM product WHERE seller_id IS NOT NULL AND source_type = 'seller_upload') as total_seller_products,
    (SELECT COUNT(*) FROM traceability_record WHERE source_type = 'seller_upload') as total_trace_records,
    (SELECT COUNT(*) FROM trace_codes tc JOIN traceability_record tr ON tc.trace_record_id = tr.id WHERE tr.source_type = 'seller_upload') as total_trace_codes;

-- 2. 模拟插入一个新的销售者产品（仅用于测试，实际应通过API创建）
-- INSERT INTO product (
--     name, description, price, stock, category_id, seller_id, 
--     source_type, has_traceability, status, sales_count, view_count, 
--     favorite_count, review_count, is_featured, is_hot, is_new, 
--     sort_order, deleted, created_at, updated_at
-- ) VALUES (
--     'Test Product for Sync', 'Test product to verify traceability sync', 
--     99.99, 100, 1, 7, 'seller_upload', 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 
--     NOW(), NOW()
-- );

-- 3. 验证同步状态的查询
SELECT 
    p.id as product_id,
    p.name as product_name,
    p.trace_code as product_trace_code,
    p.has_traceability,
    tr.id as trace_record_id,
    tr.trace_code as record_trace_code,
    tr.status as record_status,
    tc.code as trace_codes_code,
    tc.status as trace_code_status
FROM product p
LEFT JOIN traceability_record tr ON p.id = tr.product_id
LEFT JOIN trace_codes tc ON tr.id = tc.trace_record_id
WHERE p.seller_id IS NOT NULL 
    AND p.source_type = 'seller_upload'
ORDER BY p.created_at DESC
LIMIT 5;

-- 4. 检查是否有未同步的数据
SELECT 
    'Data Integrity Check' as check_type,
    COUNT(*) as count,
    'Products without trace records' as description
FROM product p
LEFT JOIN traceability_record tr ON p.id = tr.product_id
WHERE p.seller_id IS NOT NULL 
    AND p.source_type = 'seller_upload'
    AND p.trace_code IS NOT NULL
    AND p.trace_code != ''
    AND tr.id IS NULL

UNION ALL

SELECT 
    'Data Integrity Check' as check_type,
    COUNT(*) as count,
    'Trace records without trace codes' as description
FROM traceability_record tr
LEFT JOIN trace_codes tc ON tr.id = tc.trace_record_id
WHERE tr.source_type = 'seller_upload'
    AND tr.trace_code IS NOT NULL
    AND tr.trace_code != ''
    AND tc.code IS NULL;