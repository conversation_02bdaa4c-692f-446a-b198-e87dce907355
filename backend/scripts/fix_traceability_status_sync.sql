-- 修复溯源记录状态同步问题
-- 当商品上架时，对应的溯源记录也应该发布

-- 1. 查看当前状态不匹配的情况
SELECT 
    p.id as product_id,
    p.name as product_name,
    p.status as product_status,
    p.has_traceability,
    tr.id as trace_record_id,
    tr.status as trace_status,
    tr.trace_code,
    CASE 
        WHEN p.status = 1 AND tr.status != 2 THEN '需要同步：商品已上架但溯源未发布'
        WHEN p.status = 0 AND tr.status = 2 THEN '需要同步：商品已下架但溯源仍发布'
        ELSE '状态一致'
    END as sync_status
FROM product p 
LEFT JOIN traceability_record tr ON p.id = tr.product_id 
WHERE p.seller_id IS NOT NULL 
  AND p.source_type = 'seller_upload' 
  AND p.deleted = 0 
  AND tr.deleted = 0
  AND (
    (p.status = 1 AND tr.status != 2) OR 
    (p.status = 0 AND tr.status = 2)
  )
ORDER BY p.created_at DESC;

-- 2. 修复状态不匹配的记录
-- 将已上架商品对应的溯源记录设置为已发布状态
UPDATE traceability_record tr
INNER JOIN product p ON tr.product_id = p.id
SET tr.status = 2, tr.updated_at = NOW()
WHERE p.status = 1 
  AND tr.status != 2
  AND p.seller_id IS NOT NULL 
  AND p.source_type = 'seller_upload'
  AND p.deleted = 0 
  AND tr.deleted = 0;

-- 3. 将已下架商品对应的溯源记录设置为草稿状态（可选）
-- UPDATE traceability_record tr
-- INNER JOIN product p ON tr.product_id = p.id
-- SET tr.status = 0, tr.updated_at = NOW()
-- WHERE p.status = 0 
--   AND tr.status = 2
--   AND p.seller_id IS NOT NULL 
--   AND p.source_type = 'seller_upload'
--   AND p.deleted = 0 
--   AND tr.deleted = 0;

-- 4. 验证修复结果
SELECT 
    '修复后状态统计' as description,
    COUNT(*) as total_products,
    SUM(CASE WHEN p.status = 1 THEN 1 ELSE 0 END) as products_on_shelf,
    SUM(CASE WHEN tr.status = 2 THEN 1 ELSE 0 END) as published_traces,
    SUM(CASE WHEN p.status = 1 AND tr.status = 2 THEN 1 ELSE 0 END) as synced_records
FROM product p 
LEFT JOIN traceability_record tr ON p.id = tr.product_id 
WHERE p.seller_id IS NOT NULL 
  AND p.source_type = 'seller_upload' 
  AND p.deleted = 0 
  AND tr.deleted = 0;