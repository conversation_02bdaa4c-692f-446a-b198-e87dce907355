-- 同步二维码URL脚本
-- 将traceability_record表中的qr_code_url同步到product表

-- 1. 为销售者产品同步二维码URL
UPDATE product p 
INNER JOIN traceability_record tr ON p.id = tr.product_id 
SET p.qr_code_url = tr.qr_code_url 
WHERE tr.qr_code_url IS NOT NULL 
  AND tr.qr_code_url != '' 
  AND (p.qr_code_url IS NULL OR p.qr_code_url = '')
  AND p.seller_id IS NOT NULL 
  AND p.source_type = 'seller_upload';

-- 2. 检查同步结果
SELECT 
    '同步后统计' as description,
    COUNT(*) as total_products,
    SUM(CASE WHEN p.qr_code_url IS NOT NULL AND p.qr_code_url != '' THEN 1 ELSE 0 END) as products_with_qr,
    SUM(CASE WHEN tr.qr_code_url IS NOT NULL AND tr.qr_code_url != '' THEN 1 ELSE 0 END) as records_with_qr
FROM product p 
LEFT JOIN traceability_record tr ON p.id = tr.product_id 
WHERE p.seller_id IS NOT NULL 
  AND p.source_type = 'seller_upload';

-- 3. 显示具体的同步情况
SELECT 
    p.id as product_id,
    p.name as product_name,
    p.qr_code_url as product_qr_url,
    tr.qr_code_url as record_qr_url,
    CASE 
        WHEN p.qr_code_url = tr.qr_code_url THEN '已同步'
        WHEN p.qr_code_url IS NULL OR p.qr_code_url = '' THEN '产品缺失'
        WHEN tr.qr_code_url IS NULL OR tr.qr_code_url = '' THEN '记录缺失'
        ELSE '不一致'
    END as sync_status
FROM product p 
LEFT JOIN traceability_record tr ON p.id = tr.product_id 
WHERE p.seller_id IS NOT NULL 
  AND p.source_type = 'seller_upload'
ORDER BY p.id DESC;