-- SFAP销售者销售中心测试数据生成脚本
-- 执行时间: 2025年7月22日
-- 目标: 为销售者生成完整的测试订单数据

-- 设置安全模式
SET SQL_SAFE_UPDATES = 0;

-- 1. 为销售者生成测试订单数据
-- 销售者ID: 7, 29, 30, 33

-- 为销售者ID=7生成订单
INSERT INTO `order` (
    order_no, user_id, seller_id, total_amount, discount_amount, shipping_fee, actual_amount,
    payment_method, payment_status, order_status, shipping_address, buyer_note, seller_note,
    remark, delivery_company, tracking_number, payment_time, shipping_time, delivery_time,
    completion_time, receive_time, cancel_time, cancel_reason, ip_address, device_info,
    deleted, created_at, updated_at
) VALUES
-- 已完成订单
('ORD202507220001', 1, 7, 89.70, 0.00, 10.00, 99.70, 'wechat', 1, 3, 
 '北京市朝阳区建国路88号SOHO现代城', '请尽快发货', '优质客户', '感谢购买', 
 '顺丰速运', 'SF1234567890', '2025-07-20 10:30:00', '2025-07-20 14:00:00', 
 '2025-07-21 09:00:00', '2025-07-21 16:30:00', '2025-07-21 16:30:00', NULL, NULL,
 '*************', 'iPhone 14 Pro', 0, '2025-07-20 09:15:00', '2025-07-21 16:30:00'),

('ORD202507220002', 2, 7, 156.80, 5.00, 15.00, 166.80, 'alipay', 1, 3,
 '上海市浦东新区陆家嘴环路1000号', '包装要仔细', '回头客', '包装精美',
 '中通快递', 'ZTO9876543210', '2025-07-19 15:20:00', '2025-07-19 18:00:00',
 '2025-07-20 10:30:00', '2025-07-20 17:45:00', '2025-07-20 17:45:00', NULL, NULL,
 '*************', 'Android 13', 0, '2025-07-19 14:30:00', '2025-07-20 17:45:00'),

-- 待发货订单
('ORD202507220003', 3, 7, 234.50, 10.00, 12.00, 236.50, 'wechat', 1, 1,
 '广州市天河区珠江新城花城大道85号', '急需，请加急处理', '', '已安排发货',
 '', '', '2025-07-22 08:45:00', NULL, NULL, NULL, NULL, NULL, NULL,
 '*************', 'iPad Air', 0, '2025-07-22 08:30:00', '2025-07-22 08:45:00'),

-- 待支付订单
('ORD202507220004', 4, 7, 78.90, 0.00, 8.00, 86.90, '', 0, 0,
 '深圳市南山区科技园南区深南大道9988号', '', '', '',
 '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL,
 '*************', 'MacBook Pro', 0, '2025-07-22 11:20:00', '2025-07-22 11:20:00'),

-- 已取消订单
('ORD202507220005', 5, 7, 145.60, 0.00, 10.00, 155.60, '', 0, 4,
 '杭州市西湖区文三路259号', '', '', '客户主动取消',
 '', '', NULL, NULL, NULL, NULL, NULL, '2025-07-22 12:00:00', '客户临时改变主意', 
 '*************', 'Huawei P50', 0, '2025-07-22 10:15:00', '2025-07-22 12:00:00');

-- 为销售者ID=29生成订单
INSERT INTO `order` (
    order_no, user_id, seller_id, total_amount, discount_amount, shipping_fee, actual_amount,
    payment_method, payment_status, order_status, shipping_address, buyer_note, seller_note,
    remark, delivery_company, tracking_number, payment_time, shipping_time, delivery_time,
    completion_time, receive_time, cancel_time, cancel_reason, ip_address, device_info,
    deleted, created_at, updated_at
) VALUES
('ORD202507220006', 6, 29, 198.50, 8.50, 15.00, 205.00, 'wechat', 1, 2,
 '成都市锦江区红星路三段1号IFS国际金融中心', '新鲜度要保证', '有机产品', '已发货，请注意查收',
 '京东物流', 'JD2024072201', '2025-07-21 16:30:00', '2025-07-22 09:00:00', NULL,
 NULL, NULL, NULL, NULL, '*************', 'Xiaomi 13', 0, '2025-07-21 15:45:00', '2025-07-22 09:00:00'),

('ORD202507220007', 7, 29, 89.90, 0.00, 12.00, 101.90, 'alipay', 1, 3,
 '重庆市渝中区解放碑步行街88号', '包装要密封', '绿色食品', '感谢支持',
 '圆通速递', 'YTO5678901234', '2025-07-18 14:20:00', '2025-07-18 17:30:00',
 '2025-07-19 11:15:00', '2025-07-19 18:20:00', '2025-07-19 18:20:00', NULL, NULL,
 '*************', 'OPPO Find X5', 0, '2025-07-18 13:40:00', '2025-07-19 18:20:00');

-- 为销售者ID=30生成订单
INSERT INTO `order` (
    order_no, user_id, seller_id, total_amount, discount_amount, shipping_fee, actual_amount,
    payment_method, payment_status, order_status, shipping_address, buyer_note, seller_note,
    remark, delivery_company, tracking_number, payment_time, shipping_time, delivery_time,
    completion_time, receive_time, cancel_time, cancel_reason, ip_address, device_info,
    deleted, created_at, updated_at
) VALUES
('ORD202507220008', 8, 30, 267.80, 12.80, 18.00, 273.00, 'wechat', 1, 1,
 '西安市雁塔区高新路88号尚品国际', '质量要好', '精品推荐', '正在备货',
 '', '', '2025-07-22 07:15:00', NULL, NULL, NULL, NULL, NULL, NULL,
 '*************', 'vivo X90', 0, '2025-07-22 07:00:00', '2025-07-22 07:15:00'),

('ORD202507220009', 9, 30, 156.70, 6.70, 10.00, 160.00, 'alipay', 1, 3,
 '南京市鼓楼区中山路1号德基广场', '要新鲜的', '当季特产', '已送达',
 '申通快递', 'STO3456789012', '2025-07-17 11:30:00', '2025-07-17 15:45:00',
 '2025-07-18 08:30:00', '2025-07-18 14:50:00', '2025-07-18 14:50:00', NULL, NULL,
 '*************', 'Samsung S23', 0, '2025-07-17 10:20:00', '2025-07-18 14:50:00');

-- 为销售者ID=33生成订单
INSERT INTO `order` (
    order_no, user_id, seller_id, total_amount, discount_amount, shipping_fee, actual_amount,
    payment_method, payment_status, order_status, shipping_address, buyer_note, seller_note,
    remark, delivery_company, tracking_number, payment_time, shipping_time, delivery_time,
    completion_time, receive_time, cancel_time, cancel_reason, ip_address, device_info,
    deleted, created_at, updated_at
) VALUES
('ORD202507220010', 10, 33, 345.60, 15.60, 20.00, 350.00, 'wechat', 1, 2,
 '武汉市江汉区中山大道818号平安大厦', '大件商品注意包装', '热销产品', '物流已安排',
 '德邦物流', 'DBL7890123456', '2025-07-21 13:45:00', '2025-07-22 08:30:00', NULL,
 NULL, NULL, NULL, NULL, '*************', 'iPhone 15', 0, '2025-07-21 13:20:00', '2025-07-22 08:30:00');

-- 2. 生成对应的订单项数据
INSERT INTO order_item (
    order_id, product_id, product_name, product_image, price, quantity, total_price,
    created_at, updated_at
) VALUES
-- 订单1的商品项
(1, 6012, '有机苹果', '/uploads/products/apple_organic.jpg', 29.90, 3, 89.70, '2025-07-20 09:15:00', '2025-07-20 09:15:00'),

-- 订单2的商品项  
(2, 6013, '新鲜橙子', '/uploads/products/orange_fresh.jpg', 35.60, 2, 71.20, '2025-07-19 14:30:00', '2025-07-19 14:30:00'),
(3, 6014, '精品香蕉', '/uploads/products/banana_premium.jpg', 28.50, 3, 85.50, '2025-07-19 14:30:00', '2025-07-19 14:30:00'),

-- 订单3的商品项
(4, 6012, '有机苹果', '/uploads/products/apple_organic.jpg', 29.90, 5, 149.50, '2025-07-22 08:30:00', '2025-07-22 08:30:00'),
(5, 6013, '新鲜橙子', '/uploads/products/orange_fresh.jpg', 35.60, 2, 71.20, '2025-07-22 08:30:00', '2025-07-22 08:30:00'),

-- 订单4的商品项
(6, 6014, '精品香蕉', '/uploads/products/banana_premium.jpg', 28.50, 2, 57.00, '2025-07-22 11:20:00', '2025-07-22 11:20:00'),

-- 订单5的商品项
(7, 6012, '有机苹果', '/uploads/products/apple_organic.jpg', 29.90, 3, 89.70, '2025-07-22 10:15:00', '2025-07-22 10:15:00'),
(8, 6013, '新鲜橙子', '/uploads/products/orange_fresh.jpg', 35.60, 1, 35.60, '2025-07-22 10:15:00', '2025-07-22 10:15:00'),

-- 其他销售者的订单项
(9, 6015, '农家土豆', '/uploads/products/potato_farm.jpg', 18.90, 5, 94.50, '2025-07-21 15:45:00', '2025-07-21 15:45:00'),
(10, 6016, '绿色蔬菜', '/uploads/products/vegetables_green.jpg', 25.80, 4, 103.20, '2025-07-21 15:45:00', '2025-07-21 15:45:00'),

(11, 6017, '有机大米', '/uploads/products/rice_organic.jpg', 45.90, 2, 91.80, '2025-07-18 13:40:00', '2025-07-18 13:40:00'),

(12, 6018, '山地玉米', '/uploads/products/corn_mountain.jpg', 32.50, 4, 130.00, '2025-07-22 07:00:00', '2025-07-22 07:00:00'),
(13, 6019, '野生蘑菇', '/uploads/products/mushroom_wild.jpg', 68.90, 2, 137.80, '2025-07-22 07:00:00', '2025-07-22 07:00:00'),

(14, 6020, '纯天然蜂蜜', '/uploads/products/honey_natural.jpg', 78.50, 2, 157.00, '2025-07-17 10:20:00', '2025-07-17 10:20:00'),

(15, 6021, '农场鸡蛋', '/uploads/products/eggs_farm.jpg', 28.90, 6, 173.40, '2025-07-21 13:20:00', '2025-07-21 13:20:00'),
(16, 6022, '新鲜牛奶', '/uploads/products/milk_fresh.jpg', 15.80, 10, 158.00, '2025-07-21 13:20:00', '2025-07-21 13:20:00');

-- 3. 生成销售者统计数据
INSERT INTO seller_statistics (
    seller_id, date, total_sales, total_orders, total_products, avg_order_value,
    conversion_rate, customer_count, return_rate, satisfaction_score,
    created_at, updated_at
) VALUES
-- 销售者7的统计数据
(7, '2025-07-22', 756.50, 5, 3, 151.30, 0.85, 5, 0.02, 4.8, NOW(), NOW()),
(7, '2025-07-21', 623.40, 4, 3, 155.85, 0.82, 4, 0.01, 4.9, NOW(), NOW()),
(7, '2025-07-20', 445.20, 3, 3, 148.40, 0.78, 3, 0.00, 4.7, NOW(), NOW()),

-- 销售者29的统计数据  
(29, '2025-07-22', 306.90, 2, 4, 153.45, 0.75, 2, 0.00, 4.6, NOW(), NOW()),
(29, '2025-07-21', 278.50, 2, 4, 139.25, 0.73, 2, 0.00, 4.5, NOW(), NOW()),

-- 销售者30的统计数据
(30, '2025-07-22', 433.00, 2, 5, 216.50, 0.88, 2, 0.00, 4.9, NOW(), NOW()),
(30, '2025-07-21', 389.70, 2, 5, 194.85, 0.85, 2, 0.00, 4.8, NOW(), NOW()),

-- 销售者33的统计数据
(33, '2025-07-22', 350.00, 1, 6, 350.00, 0.90, 1, 0.00, 5.0, NOW(), NOW()),
(33, '2025-07-21', 315.80, 1, 6, 315.80, 0.87, 1, 0.00, 4.9, NOW(), NOW());

-- 恢复安全模式
SET SQL_SAFE_UPDATES = 1;

-- 验证数据生成结果
SELECT 
    s.seller_id,
    u.username as seller_name,
    COUNT(o.id) as order_count,
    SUM(o.actual_amount) as total_sales,
    AVG(o.actual_amount) as avg_order_value
FROM `order` o
JOIN user u ON o.seller_id = u.id
LEFT JOIN seller_statistics s ON o.seller_id = s.seller_id
WHERE o.seller_id IN (7, 29, 30, 33)
GROUP BY s.seller_id, u.username
ORDER BY total_sales DESC;

-- 显示订单状态分布
SELECT 
    seller_id,
    order_status,
    COUNT(*) as count,
    CASE order_status
        WHEN 0 THEN '待支付'
        WHEN 1 THEN '待发货' 
        WHEN 2 THEN '已发货'
        WHEN 3 THEN '已完成'
        WHEN 4 THEN '已取消'
    END as status_name
FROM `order` 
WHERE seller_id IN (7, 29, 30, 33)
GROUP BY seller_id, order_status
ORDER BY seller_id, order_status;
