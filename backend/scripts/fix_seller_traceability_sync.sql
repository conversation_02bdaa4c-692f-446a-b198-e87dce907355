-- 修复销售者产品溯源数据同步问题
-- 为现有的销售者产品创建对应的溯源记录和溯源码记录

-- 1. 为没有溯源记录的销售者产品创建溯源记录
INSERT INTO traceability_record (
    product_id, 
    product_name, 
    producer_id, 
    trace_code, 
    source_type, 
    status, 
    deleted, 
    created_at, 
    updated_at
)
SELECT 
    p.id as product_id,
    p.name as product_name,
    p.seller_id as producer_id,
    p.trace_code,
    'seller_upload' as source_type,
    0 as status, -- 草稿状态
    0 as deleted,
    p.created_at,
    NOW() as updated_at
FROM product p
LEFT JOIN traceability_record tr ON p.id = tr.product_id
WHERE p.seller_id IS NOT NULL 
    AND p.source_type = 'seller_upload'
    AND p.trace_code IS NOT NULL
    AND p.trace_code != ''
    AND tr.id IS NULL;

-- 2. 为没有trace_codes记录的溯源记录创建trace_codes记录
INSERT INTO trace_codes (
    code,
    trace_record_id,
    generated_time,
    status,
    scan_count,
    daily_scan_count,
    weekly_scan_count,
    monthly_scan_count
)
SELECT 
    tr.trace_code as code,
    tr.id as trace_record_id,
    tr.created_at as generated_time,
    1 as status, -- 激活状态
    0 as scan_count,
    0 as daily_scan_count,
    0 as weekly_scan_count,
    0 as monthly_scan_count
FROM traceability_record tr
LEFT JOIN trace_codes tc ON tr.id = tc.trace_record_id
WHERE tr.source_type = 'seller_upload'
    AND tr.trace_code IS NOT NULL
    AND tr.trace_code != ''
    AND tc.code IS NULL;

-- 3. 更新产品表的has_traceability字段
UPDATE product p
SET has_traceability = 1
WHERE p.seller_id IS NOT NULL 
    AND p.source_type = 'seller_upload'
    AND p.trace_code IS NOT NULL
    AND p.trace_code != ''
    AND p.has_traceability != 1;

-- 4. 验证修复结果的查询语句
-- 查看修复后的数据同步情况
SELECT 
    '修复后数据统计' as description,
    (SELECT COUNT(*) FROM product WHERE seller_id IS NOT NULL AND source_type = 'seller_upload') as total_seller_products,
    (SELECT COUNT(*) FROM traceability_record WHERE source_type = 'seller_upload') as total_trace_records,
    (SELECT COUNT(*) FROM trace_codes tc JOIN traceability_record tr ON tc.trace_record_id = tr.id WHERE tr.source_type = 'seller_upload') as total_trace_codes,
    (SELECT COUNT(*) FROM product WHERE seller_id IS NOT NULL AND source_type = 'seller_upload' AND has_traceability = 1) as products_with_traceability;