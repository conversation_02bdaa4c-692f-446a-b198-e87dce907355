# 惠农网价格数据爬虫项目配置文件
# 复制此文件为 settings.yaml 并修改相应配置

# 数据库配置
database:
  # 数据库连接信息
  host: localhost
  port: 3306
  username: root
  password: ${DB_PASSWORD}  # 从环境变量读取
  database: agriculture_mall
  charset: utf8mb4
  
  # 连接池配置
  pool_size: 10
  max_overflow: 20
  pool_timeout: 30
  pool_recycle: 3600
  
  # 连接选项
  connect_timeout: 10
  read_timeout: 30
  write_timeout: 30

# 爬虫配置
spider:
  # 基础设置
  download_delay: 15                   # 请求间隔时间(秒)
  randomize_download_delay: 0.5        # 随机延迟倍数
  concurrent_requests: 1               # 全局并发请求数
  concurrent_requests_per_domain: 1    # 每域名并发请求数
  
  # 重试设置
  retry_times: 3                       # 重试次数
  retry_http_codes: [500, 502, 503, 504, 408, 429]
  retry_priority_adjust: -1
  
  # 超时设置
  download_timeout: 30                 # 下载超时时间(秒)
  
  # User-Agent设置
  user_agents:
    - "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    - "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    - "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
  
  # 代理设置（可选）
  proxy_enabled: false
  proxy_pool:
    - "http://proxy1:port"
    - "http://proxy2:port"
  
  # 目标网站设置
  target_domain: "www.cnhnb.com"
  base_url: "https://www.cnhnb.com/hangqing/"
  
  # 采集范围设置
  categories:
    - name: "水果"
      pinyin: "sgzw"
      category_id: 2003191
      enabled: true
    - name: "蔬菜"
      pinyin: "sczw"
      category_id: 2003192
      enabled: true
    - name: "禽畜肉蛋"
      pinyin: "qcrd"
      category_id: 2003193
      enabled: true
    - name: "水产"
      pinyin: "shuic"
      category_id: 2003194
      enabled: true
    - name: "农副加工"
      pinyin: "nfjg"
      category_id: 2003195
      enabled: false
    - name: "粮油米面"
      pinyin: "lymm"
      category_id: 2003196
      enabled: false
    - name: "种子种苗"
      pinyin: "zzzm"
      category_id: 2003197
      enabled: false
    - name: "苗木花草"
      pinyin: "mmhc"
      category_id: 2003198
      enabled: false
    - name: "农资农机"
      pinyin: "nznj"
      category_id: 2003199
      enabled: false
    - name: "中药材"
      pinyin: "zyc"
      category_id: 2003200
      enabled: false

# 数据处理配置
data_processing:
  # 数据清洗设置
  cleaning:
    remove_duplicates: true
    validate_price_range: true
    min_price: 0.01
    max_price: 10000.00
    standardize_units: true
    
  # 数据验证设置
  validation:
    required_fields: ["product_name", "region", "price", "data_date"]
    price_precision: 2
    date_format: "%Y-%m-%d"
    
  # 数据去重设置
  deduplication:
    enabled: true
    key_fields: ["product_id", "region_id", "data_date"]
    strategy: "keep_latest"  # keep_first, keep_latest, merge

# 任务调度配置
scheduler:
  # 定时任务设置
  enabled: true
  timezone: "Asia/Shanghai"
  
  # 任务配置
  jobs:
    - name: "daily_full_crawl"
      description: "每日全量数据采集"
      cron: "0 2 * * *"  # 每天凌晨2点执行
      spider: "full_spider"
      enabled: true
      
    - name: "hourly_incremental_crawl"
      description: "每小时增量数据采集"
      cron: "0 * * * *"  # 每小时执行
      spider: "incremental_spider"
      enabled: false
      
    - name: "weekly_data_cleanup"
      description: "每周数据清理"
      cron: "0 3 * * 0"  # 每周日凌晨3点执行
      task: "cleanup_old_data"
      enabled: true

# 监控配置
monitoring:
  # 基础监控设置
  enabled: true
  check_interval: 60  # 检查间隔(秒)
  
  # 邮件告警设置
  email_alert:
    enabled: true
    smtp_server: "smtp.gmail.com"
    smtp_port: 587
    username: ${EMAIL_USERNAME}
    password: ${EMAIL_PASSWORD}
    from_email: "<EMAIL>"
    to_emails:
      - "<EMAIL>"
      - "<EMAIL>"
    
  # 告警阈值设置
  thresholds:
    error_rate: 0.05        # 错误率阈值
    response_time: 10       # 响应时间阈值(秒)
    memory_usage: 0.8       # 内存使用率阈值
    disk_usage: 0.9         # 磁盘使用率阈值
    
  # 监控指标
  metrics:
    - name: "requests_total"
      description: "总请求数"
      type: "counter"
    - name: "requests_success"
      description: "成功请求数"
      type: "counter"
    - name: "requests_failed"
      description: "失败请求数"
      type: "counter"
    - name: "response_time"
      description: "响应时间"
      type: "histogram"
    - name: "items_scraped"
      description: "采集条目数"
      type: "counter"

# 日志配置
logging:
  # 基础设置
  level: INFO
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} - {message}"
  
  # 文件设置
  file:
    enabled: true
    path: "logs/spider.log"
    rotation: "1 day"      # 日志轮转
    retention: "30 days"   # 保留时间
    compression: "gz"      # 压缩格式
    
  # 控制台设置
  console:
    enabled: true
    colorize: true
    
  # 特定模块日志级别
  loggers:
    scrapy: WARNING
    urllib3: WARNING
    requests: WARNING
    
# 缓存配置
cache:
  # Redis缓存
  redis:
    enabled: false
    host: localhost
    port: 6379
    db: 0
    password: ${REDIS_PASSWORD}
    
  # 磁盘缓存
  disk:
    enabled: true
    directory: "cache"
    size_limit: "1GB"
    eviction_policy: "least-recently-stored"

# API配置
api:
  # 基础设置
  enabled: false
  host: "0.0.0.0"
  port: 8002
  debug: false
  
  # 认证设置
  authentication:
    enabled: false
    secret_key: ${API_SECRET_KEY}
    algorithm: "HS256"
    access_token_expire_minutes: 30
    
  # 限流设置
  rate_limiting:
    enabled: true
    requests_per_minute: 100
    requests_per_hour: 1000

# 数据导出配置
export:
  # 支持的格式
  formats: ["csv", "excel", "json"]
  
  # 默认设置
  default_format: "csv"
  max_records: 100000
  
  # 文件设置
  output_directory: "exports"
  filename_template: "huinong_data_{timestamp}.{format}"
  
  # CSV设置
  csv:
    encoding: "utf-8-sig"
    delimiter: ","
    quote_char: '"'
    
  # Excel设置
  excel:
    sheet_name: "价格数据"
    include_index: false

# 安全配置
security:
  # 数据加密
  encryption:
    enabled: true
    algorithm: "AES-256-CBC"
    key: ${ENCRYPTION_KEY}
    
  # 访问控制
  access_control:
    enabled: false
    allowed_ips: ["127.0.0.1", "***********/24"]
    
  # 审计日志
  audit:
    enabled: true
    log_file: "logs/audit.log"

# 性能优化配置
performance:
  # 内存管理
  memory:
    max_memory_usage: "2GB"
    gc_threshold: 0.8
    
  # 并发控制
  concurrency:
    max_workers: 4
    queue_size: 1000
    
  # 数据库优化
  database_optimization:
    batch_size: 1000
    bulk_insert: true
    connection_pooling: true

# 开发配置
development:
  # 调试设置
  debug: false
  verbose: false
  
  # 测试设置
  test_mode: false
  mock_responses: false
  
  # 开发工具
  profiling: false
  memory_profiling: false
