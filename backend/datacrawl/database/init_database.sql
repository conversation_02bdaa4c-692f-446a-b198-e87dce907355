-- 惠农网价格数据爬虫项目 - 数据库初始化脚本
-- 创建时间: 2025-07-24
-- 版本: 1.0

-- 创建数据库
CREATE DATABASE IF NOT EXISTS huinong_spider 
DEFAULT CHARACTER SET utf8mb4 
DEFAULT COLLATE utf8mb4_unicode_ci;

USE huinong_spider;

-- 1. 创建产品分类表
DROP TABLE IF EXISTS categories;
CREATE TABLE categories (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '分类ID',
    name VARCHAR(50) NOT NULL COMMENT '分类名称',
    pinyin VARCHAR(50) NOT NULL COMMENT '分类拼音',
    category_id INT NOT NULL COMMENT '惠农网分类ID',
    parent_id INT DEFAULT NULL COMMENT '父分类ID',
    level TINYINT NOT NULL DEFAULT 1 COMMENT '分类层级',
    sort_order INT DEFAULT 0 COMMENT '排序',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_category_id (category_id),
    UNIQUE KEY uk_pinyin (pinyin),
    INDEX idx_parent_id (parent_id),
    INDEX idx_level (level),
    INDEX idx_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='产品分类表';

-- 2. 创建地区表
DROP TABLE IF EXISTS regions;
CREATE TABLE regions (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '地区ID',
    name VARCHAR(100) NOT NULL COMMENT '地区名称',
    region_code INT NOT NULL COMMENT '惠农网地区编码',
    parent_id INT DEFAULT NULL COMMENT '父地区ID',
    level TINYINT NOT NULL COMMENT '地区层级(1省2市3县)',
    full_name VARCHAR(200) COMMENT '完整地区名称',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_region_code (region_code),
    INDEX idx_parent_id (parent_id),
    INDEX idx_level (level),
    INDEX idx_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='地区表';

-- 3. 创建产品表
DROP TABLE IF EXISTS products;
CREATE TABLE products (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '产品ID',
    name VARCHAR(100) NOT NULL COMMENT '产品名称',
    category_id INT NOT NULL COMMENT '分类ID',
    subcategory VARCHAR(50) COMMENT '子分类名称',
    product_code VARCHAR(50) COMMENT '产品编码',
    unit VARCHAR(20) DEFAULT '斤' COMMENT '计量单位',
    description TEXT COMMENT '产品描述',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_category_id (category_id),
    INDEX idx_name (name),
    INDEX idx_subcategory (subcategory),
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='产品表';

-- 4. 创建价格数据表
DROP TABLE IF EXISTS price_data;
CREATE TABLE price_data (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '价格数据ID',
    product_id INT NOT NULL COMMENT '产品ID',
    region_id INT NOT NULL COMMENT '地区ID',
    price DECIMAL(10,2) NOT NULL COMMENT '价格',
    unit VARCHAR(20) NOT NULL DEFAULT '斤' COMMENT '计量单位',
    price_change VARCHAR(20) COMMENT '价格变化',
    price_trend TINYINT COMMENT '价格趋势(1上涨0持平-1下跌)',
    source_url VARCHAR(500) COMMENT '数据源URL',
    source_id VARCHAR(100) COMMENT '源数据ID',
    data_date DATE NOT NULL COMMENT '数据日期',
    crawl_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '采集时间',
    
    UNIQUE KEY uk_product_region_date (product_id, region_id, data_date),
    INDEX idx_data_date (data_date),
    INDEX idx_crawl_time (crawl_time),
    INDEX idx_price (price),
    INDEX idx_source_id (source_id),
    INDEX idx_product_date (product_id, data_date DESC),
    INDEX idx_region_date (region_id, data_date DESC),
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE RESTRICT ON UPDATE CASCADE,
    FOREIGN KEY (region_id) REFERENCES regions(id) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='价格数据表';

-- 5. 创建爬取日志表
DROP TABLE IF EXISTS crawl_logs;
CREATE TABLE crawl_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
    task_id VARCHAR(100) NOT NULL COMMENT '任务ID',
    spider_name VARCHAR(50) NOT NULL COMMENT '爬虫名称',
    start_time TIMESTAMP NOT NULL COMMENT '开始时间',
    end_time TIMESTAMP COMMENT '结束时间',
    status ENUM('running', 'success', 'failed', 'stopped') NOT NULL COMMENT '状态',
    total_requests INT DEFAULT 0 COMMENT '总请求数',
    success_requests INT DEFAULT 0 COMMENT '成功请求数',
    failed_requests INT DEFAULT 0 COMMENT '失败请求数',
    items_scraped INT DEFAULT 0 COMMENT '采集条目数',
    items_saved INT DEFAULT 0 COMMENT '保存条目数',
    error_message TEXT COMMENT '错误信息',
    config_snapshot JSON COMMENT '配置快照',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_task_id (task_id),
    INDEX idx_spider_name (spider_name),
    INDEX idx_start_time (start_time),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='爬取日志表';

-- 6. 创建数据质量监控表
DROP TABLE IF EXISTS data_quality_logs;
CREATE TABLE data_quality_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '质量日志ID',
    check_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '检查时间',
    check_type ENUM('completeness', 'accuracy', 'consistency', 'timeliness') NOT NULL COMMENT '检查类型',
    table_name VARCHAR(50) NOT NULL COMMENT '表名',
    total_records INT NOT NULL COMMENT '总记录数',
    valid_records INT NOT NULL COMMENT '有效记录数',
    invalid_records INT NOT NULL COMMENT '无效记录数',
    quality_score DECIMAL(5,2) NOT NULL COMMENT '质量分数',
    details JSON COMMENT '详细信息',
    
    INDEX idx_check_time (check_time),
    INDEX idx_check_type (check_type),
    INDEX idx_table_name (table_name),
    INDEX idx_quality_score (quality_score)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据质量监控表';

-- 7. 创建系统配置表
DROP TABLE IF EXISTS system_configs;
CREATE TABLE system_configs (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '配置ID',
    config_key VARCHAR(100) NOT NULL COMMENT '配置键',
    config_value TEXT NOT NULL COMMENT '配置值',
    config_type ENUM('string', 'int', 'float', 'boolean', 'json') DEFAULT 'string' COMMENT '配置类型',
    description VARCHAR(200) COMMENT '配置描述',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_config_key (config_key),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 插入初始分类数据
INSERT INTO categories (name, pinyin, category_id, parent_id, level, sort_order) VALUES
('水果', 'sgzw', 2003191, NULL, 1, 1),
('蔬菜', 'sczw', 2003192, NULL, 1, 2),
('禽畜肉蛋', 'qcrd', 2003193, NULL, 1, 3),
('水产', 'shuic', 2003194, NULL, 1, 4),
('农副加工', 'nfjg', 2003195, NULL, 1, 5),
('粮油米面', 'lymm', 2003196, NULL, 1, 6),
('种子种苗', 'zzzm', 2003197, NULL, 1, 7),
('苗木花草', 'mmhc', 2003198, NULL, 1, 8),
('农资农机', 'nznj', 2003199, NULL, 1, 9),
('中药材', 'zyc', 2003200, NULL, 1, 10),
('日用百货', 'rybh', 2003201, NULL, 1, 11),
('土地流转', 'tudi', 2003202, NULL, 1, 12),
('包装', 'package', 2003203, NULL, 1, 13),
('农业服务', 'nyfw', 2003204, NULL, 1, 14),
('农工服务', 'ngfw', 2003205, NULL, 1, 15),
('租赁服务', 'zlfw', 2003206, NULL, 1, 16),
('农技服务', 'nongjifuwu', 2003207, NULL, 1, 17),
('经济作物', 'jingjizw', 2003208, NULL, 1, 18);

-- 插入省级地区数据
INSERT INTO regions (name, region_code, parent_id, level, full_name) VALUES
('河北', 3, NULL, 1, '河北省'),
('山西', 4, NULL, 1, '山西省'),
('内蒙古', 5, NULL, 1, '内蒙古自治区'),
('辽宁', 6, NULL, 1, '辽宁省'),
('吉林', 7, NULL, 1, '吉林省'),
('黑龙江', 8, NULL, 1, '黑龙江省'),
('江苏', 10, NULL, 1, '江苏省'),
('浙江', 11, NULL, 1, '浙江省'),
('安徽', 12, NULL, 1, '安徽省'),
('福建', 13, NULL, 1, '福建省'),
('江西', 14, NULL, 1, '江西省'),
('山东', 15, NULL, 1, '山东省'),
('河南', 16, NULL, 1, '河南省'),
('湖北', 17, NULL, 1, '湖北省'),
('湖南', 18, NULL, 1, '湖南省'),
('广东', 19, NULL, 1, '广东省'),
('广西', 20, NULL, 1, '广西壮族自治区'),
('海南', 21, NULL, 1, '海南省'),
('四川', 23, NULL, 1, '四川省'),
('贵州', 24, NULL, 1, '贵州省'),
('云南', 25, NULL, 1, '云南省'),
('西藏', 26, NULL, 1, '西藏自治区'),
('陕西', 27, NULL, 1, '陕西省'),
('甘肃', 28, NULL, 1, '甘肃省'),
('青海', 29, NULL, 1, '青海省'),
('宁夏', 30, NULL, 1, '宁夏回族自治区'),
('新疆', 31, NULL, 1, '新疆维吾尔自治区'),
('北京', 45081, NULL, 1, '北京市'),
('天津', 45082, NULL, 1, '天津市'),
('上海', 45083, NULL, 1, '上海市'),
('重庆', 45084, NULL, 1, '重庆市'),
('台湾省', 45085, NULL, 1, '台湾省'),
('香港特别行政区', 45086, NULL, 1, '香港特别行政区'),
('澳门特别行政区', 45087, NULL, 1, '澳门特别行政区'),
('境外', 45114, NULL, 1, '境外');

-- 插入系统配置数据
INSERT INTO system_configs (config_key, config_value, config_type, description) VALUES
('spider.download_delay', '2', 'int', '爬虫请求间隔时间(秒)'),
('spider.concurrent_requests', '1', 'int', '并发请求数'),
('spider.retry_times', '3', 'int', '重试次数'),
('spider.timeout', '30', 'int', '请求超时时间(秒)'),
('database.connection_pool_size', '10', 'int', '数据库连接池大小'),
('monitoring.enable_email_alert', 'true', 'boolean', '是否启用邮件告警'),
('data_retention.price_data_days', '365', 'int', '价格数据保留天数'),
('data_retention.log_data_days', '90', 'int', '日志数据保留天数');

-- 创建视图：最新价格数据
CREATE OR REPLACE VIEW v_latest_prices AS
SELECT 
    pd.id,
    p.name AS product_name,
    c.name AS category_name,
    r.full_name AS region_name,
    pd.price,
    pd.unit,
    pd.price_change,
    pd.price_trend,
    pd.data_date,
    pd.crawl_time
FROM price_data pd
JOIN products p ON pd.product_id = p.id
JOIN categories c ON p.category_id = c.id
JOIN regions r ON pd.region_id = r.id
WHERE pd.data_date = (
    SELECT MAX(data_date) 
    FROM price_data pd2 
    WHERE pd2.product_id = pd.product_id 
    AND pd2.region_id = pd.region_id
);

-- 创建存储过程：数据清理
DELIMITER //
CREATE PROCEDURE CleanOldData()
BEGIN
    DECLARE price_retention_days INT DEFAULT 365;
    DECLARE log_retention_days INT DEFAULT 90;
    
    -- 获取配置的保留天数
    SELECT CAST(config_value AS UNSIGNED) INTO price_retention_days 
    FROM system_configs 
    WHERE config_key = 'data_retention.price_data_days' AND is_active = TRUE;
    
    SELECT CAST(config_value AS UNSIGNED) INTO log_retention_days 
    FROM system_configs 
    WHERE config_key = 'data_retention.log_data_days' AND is_active = TRUE;
    
    -- 清理过期的价格数据
    DELETE FROM price_data 
    WHERE data_date < DATE_SUB(CURDATE(), INTERVAL price_retention_days DAY);
    
    -- 清理过期的日志数据
    DELETE FROM crawl_logs 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL log_retention_days DAY);
    
    DELETE FROM data_quality_logs 
    WHERE check_time < DATE_SUB(NOW(), INTERVAL log_retention_days DAY);
    
END //
DELIMITER ;

-- 创建定时任务清理数据（需要开启事件调度器）
-- SET GLOBAL event_scheduler = ON;
-- CREATE EVENT IF NOT EXISTS clean_old_data_event
-- ON SCHEDULE EVERY 1 DAY
-- STARTS TIMESTAMP(CURRENT_DATE + INTERVAL 1 DAY, '02:00:00')
-- DO CALL CleanOldData();

COMMIT;
