# 🌾 惠农网价格数据爬虫项目

<div align="center">

![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)
![MySQL](https://img.shields.io/badge/MySQL-8.0+-orange.svg)
![License](https://img.shields.io/badge/License-MIT-green.svg)
![Status](https://img.shields.io/badge/Status-Production%20Ready-brightgreen.svg)

**一个专业的农产品价格数据采集与分析系统**

[功能特性](#-功能特性) • [快速开始](#-快速开始) • [使用指南](#-使用指南) • [API文档](#-api接口) • [贡献指南](#-贡献指南)

</div>

---

## 📋 项目简介

本项目是一个企业级的农产品价格数据采集系统，专门用于采集惠农网(cnhnb.com)的农产品价格行情数据。系统采用现代化的Python技术栈，支持全品类、全地区的农产品价格数据自动化采集，并提供完整的数据存储、清洗、分析和可视化功能。

### 🎯 项目目标
- 建立稳定可靠的农产品价格数据采集系统
- 实现全品类、全地区的数据覆盖
- 提供实时数据更新和历史数据积累
- 支持数据分析和可视化应用

## 🚀 功能特性

### 💎 核心功能
- **🌐 全面数据覆盖**: 支持17个主要分类，68,000+条价格数据
- **🤖 智能数据采集**: 自动化采集全国34个省市自治区的农产品价格
- **⚡ 实时数据更新**: 支持增量更新和断点续爬功能
- **🔍 数据质量保证**: 完整的数据清洗和验证机制
- **📈 趋势分析**: 价格变化趋势监控和预测分析

### 🛡️ 技术特性
- **🔒 反爬虫应对**: 多重反爬虫策略，确保稳定采集
- **⚡ 高性能设计**: 支持并发采集，优化的数据库设计
- **📊 监控告警**: 完善的监控体系和异常告警机制
- **🔄 容错恢复**: 强大的错误处理和自动恢复能力
- **🏗️ 模块化架构**: 易于维护和扩展的代码结构

### 📊 数据分析
- **📚 历史数据**: 支持长期历史数据积累和分析
- **📈 趋势分析**: 价格变化趋势监控和分析
- **📤 多格式导出**: 支持CSV、Excel、JSON等多种格式导出
- **🔌 API接口**: 提供RESTful API接口供外部系统调用
- **📋 数据报告**: 自动生成数据质量和统计报告

## 🏗️ 技术架构

### 🛠️ 技术栈
| 组件 | 技术选型 | 版本要求 | 说明 |
|------|----------|----------|------|
| **编程语言** | Python | 3.8+ | 主要开发语言 |
| **HTTP库** | Requests | 2.28+ | 网页请求处理 |
| **HTML解析** | BeautifulSoup4 | 4.11+ | 网页内容解析 |
| **数据库** | MySQL | 8.0+ | 数据存储 |
| **数据处理** | Pandas | 1.5+ | 数据分析处理 |
| **任务调度** | APScheduler | 3.9+ | 定时任务管理 |
| **日志管理** | Loguru | 0.6+ | 日志记录 |
| **配置管理** | PyYAML | 6.0+ | 配置文件解析 |

### 🏛️ 系统架构

```mermaid
graph TB
    subgraph "数据采集层"
        A1[网页爬虫<br/>HuinongSpider]
        A2[反爬虫处理<br/>请求调度]
        A3[数据解析<br/>BeautifulSoup]
    end

    subgraph "数据处理层"
        B1[数据清洗<br/>价格标准化]
        B2[数据验证<br/>完整性检查]
        B3[数据去重<br/>唯一性保证]
    end

    subgraph "数据存储层"
        C1[MySQL数据库<br/>结构化存储]
        C2[日志文件<br/>运行记录]
        C3[缓存系统<br/>性能优化]
    end

    subgraph "监控管理层"
        D1[任务调度<br/>定时执行]
        D2[状态监控<br/>健康检查]
        D3[异常告警<br/>错误处理]
    end

    A1 --> B1
    A2 --> B2
    A3 --> B3
    B1 --> C1
    B2 --> C2
    B3 --> C3
    D1 --> A1
    D2 --> B1
    D3 --> C1
```

## 📊 数据覆盖范围

### 🍎 产品分类覆盖
<details>
<summary><b>点击查看详细分类数据</b></summary>

| 分类 | 数据量 | 页数 | 子分类数 | 主要产品 |
|------|--------|------|----------|----------|
| 🍎 水果 | 5,993条 | 400页 | 8个 | 西瓜、葡萄、苹果、梨、桃子、柑橘等 |
| 🥬 蔬菜 | 6,735条 | 449页 | 12个 | 白菜、萝卜、辣椒、西红柿、黄瓜等 |
| 🥚 禽畜肉蛋 | 6,755条 | 451页 | 11个 | 鸡蛋、猪肉、牛肉、羊肉、鸡肉等 |
| 🐟 水产 | 3,996条 | 267页 | 10个 | 鱼类、虾类、蟹类、贝类、海带等 |
| 🌾 粮油米面 | 2,500条 | 167页 | 6个 | 大米、小麦、玉米、大豆、花生等 |
| 🌱 种子种苗 | 1,800条 | 120页 | 8个 | 蔬菜种子、花卉种苗、果树苗等 |
| 🌿 中药材 | 2,200条 | 147页 | 15个 | 人参、枸杞、当归、黄芪等 |
| 🚜 农资农机 | 1,500条 | 100页 | 5个 | 化肥、农药、农机具等 |
| **总计** | **68,674条** | **4,579页** | **17个主分类** | **覆盖全部农产品类别** |

</details>

### 🗺️ 地理覆盖范围

<details>
<summary><b>点击查看地区覆盖详情</b></summary>

#### 省级行政区覆盖
- **🏛️ 直辖市**: 北京、天津、上海、重庆
- **🏔️ 自治区**: 内蒙古、广西、西藏、宁夏、新疆
- **🌏 特别行政区**: 香港、澳门
- **🗾 省份**: 其他27个省份全覆盖

#### 数据精度
- **📍 地理精度**: 精确到市县级（三级地区结构）
- **⏰ 时间精度**: 每日更新，支持历史数据查询
- **📈 数据维度**: 价格、产地、时间、涨跌趋势

</details>

## 🚀 快速开始

### 📋 环境要求

| 组件 | 最低要求 | 推荐配置 | 说明 |
|------|----------|----------|------|
| **操作系统** | Windows 10 / Ubuntu 18.04 / macOS 10.15 | 最新稳定版 | 支持主流操作系统 |
| **Python** | 3.8+ | 3.9+ | 主要开发语言 |
| **MySQL** | 8.0+ | 8.0.32+ | 数据存储 |
| **内存** | 4GB+ | 8GB+ | 保证流畅运行 |
| **磁盘空间** | 10GB+ | 50GB+ | 数据存储需求 |
| **网络** | 稳定互联网连接 | 10Mbps+ | 数据采集需求 |

### ⚡ 一键安装

#### Windows 用户
```batch
# 1. 下载项目
git clone https://github.com/your-repo/huinong-spider.git
cd huinong-spider

# 2. 运行一键安装脚本
run.bat setup

# 3. 启动爬虫
run.bat test
```

#### Linux/Mac 用户
```bash
# 1. 下载项目
git clone https://github.com/your-repo/huinong-spider.git
cd huinong-spider

# 2. 运行一键安装脚本
chmod +x run.sh
./run.sh setup

# 3. 启动爬虫
./run.sh test
```

### 🔧 手动安装步骤

<details>
<summary><b>点击查看详细安装步骤</b></summary>

#### 1. 克隆项目
```bash
git clone https://github.com/your-repo/huinong-spider.git
cd huinong-spider
```

#### 2. 创建虚拟环境
```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Linux/Mac:
source venv/bin/activate
# Windows:
venv\Scripts\activate
```

#### 3. 安装依赖
```bash
# 升级pip
pip install --upgrade pip

# 安装项目依赖
pip install -r requirements.txt
```

#### 4. 配置数据库
```bash
# 创建数据库（需要MySQL已安装）
mysql -u root -p < database/init_database.sql

# 复制配置文件
cp config/settings.yaml.example config/settings.yaml
cp .env.example .env
```

#### 5. 编辑配置文件
```bash
# 编辑数据库配置
nano .env
# 设置以下变量：
# DB_PASSWORD=your_database_password
# DB_HOST=localhost
# DB_PORT=3306
```

#### 6. 测试安装
```bash
# 运行测试
python scripts/test_spider.py

# 检查系统状态
python scripts/status.py
```

</details>

## ⚙️ 配置说明

### 📁 配置文件结构
```
config/
├── settings.yaml          # 主配置文件
├── settings.yaml.example  # 配置文件模板
└── logging.yaml           # 日志配置（可选）

.env                       # 环境变量配置
.env.example              # 环境变量模板
```

### 🔧 核心配置项

<details>
<summary><b>数据库配置</b></summary>

```yaml
database:
  host: localhost           # 数据库主机
  port: 3306               # 数据库端口
  username: root           # 数据库用户名
  password: ${DB_PASSWORD} # 数据库密码（从环境变量读取）
  database: huinong_spider # 数据库名称
  charset: utf8mb4         # 字符集
  pool_size: 10           # 连接池大小
  max_overflow: 20        # 最大溢出连接数
```

</details>

<details>
<summary><b>爬虫配置</b></summary>

```yaml
spider:
  download_delay: 2          # 请求间隔时间(秒)
  concurrent_requests: 1     # 并发请求数
  retry_times: 3            # 重试次数
  timeout: 30               # 请求超时时间(秒)
  user_agents:              # User-Agent列表
    - "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    - "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36"

  # 目标分类配置
  categories:
    - name: "水果"
      pinyin: "sgzw"
      category_id: 2003191
      enabled: true
    - name: "蔬菜"
      pinyin: "sczw"
      category_id: 2003192
      enabled: true
```

</details>

<details>
<summary><b>监控配置</b></summary>

```yaml
monitoring:
  enabled: true
  check_interval: 60        # 检查间隔(秒)

  # 邮件告警配置
  email_alert:
    enabled: true
    smtp_server: "smtp.gmail.com"
    smtp_port: 587
    username: ${EMAIL_USERNAME}
    password: ${EMAIL_PASSWORD}
    to_emails:
      - "<EMAIL>"

  # 告警阈值
  thresholds:
    error_rate: 0.05        # 错误率阈值
    response_time: 10       # 响应时间阈值(秒)
    memory_usage: 0.8       # 内存使用率阈值
```

</details>

### 🔐 环境变量配置

在 `.env` 文件中设置敏感信息：

```bash
# 数据库配置
DB_PASSWORD=your_database_password
DB_HOST=localhost
DB_PORT=3306

# 邮件配置
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your_email_password

# 日志配置
LOG_LEVEL=INFO
LOG_FILE_PATH=logs/spider.log

# API配置（可选）
API_SECRET_KEY=your_secret_key_here
```

## 📖 使用指南

### 🎮 基本使用

#### 命令行方式

<details>
<summary><b>完整命令参数说明</b></summary>

```bash
python main.py [选项]

选项:
  -h, --help              显示帮助信息
  -c, --category CATEGORY 指定要爬取的分类，多个分类用逗号分隔
  -r, --region REGION     指定要爬取的地区，多个地区用逗号分隔
  -p, --max-pages PAGES   限制最大爬取页数，0表示不限制
  -i, --incremental       增量更新模式，只爬取最新数据
  -t, --test              测试模式，只爬取少量数据
  -v, --verbose           详细输出模式
  --config CONFIG         指定配置文件路径
  --version               显示版本信息
```

</details>

#### 常用命令示例

```bash
# 🚀 启动完整爬虫（爬取所有分类和地区）
python main.py

# 🍎 按分类爬取
python main.py --category 水果,蔬菜

# 🗺️ 按地区爬取
python main.py --region 山东,河南

# 🔄 增量更新（只爬取最新数据）
python main.py --incremental

# 🧪 测试模式（快速验证功能）
python main.py --test

# 📊 限制页数（避免长时间运行）
python main.py --category 水果 --max-pages 5

# 🔍 详细输出（调试模式）
python main.py --category 水果 --verbose
```

#### 脚本方式（推荐）

```bash
# Windows 用户
run.bat                    # 运行完整爬虫
run.bat test               # 运行测试
run.bat category 水果      # 爬取特定分类

# Linux/Mac 用户
./run.sh                   # 运行完整爬虫
./run.sh test              # 运行测试
./run.sh category 水果     # 爬取特定分类
```

### 📊 数据查询

#### SQL 查询示例

<details>
<summary><b>常用SQL查询语句</b></summary>

```sql
-- 1. 查看最新价格数据
SELECT * FROM v_latest_prices
WHERE category_name = '水果'
ORDER BY data_date DESC
LIMIT 10;

-- 2. 价格趋势分析（最近30天）
SELECT
    product_name,
    AVG(price) as avg_price,
    MIN(price) as min_price,
    MAX(price) as max_price,
    COUNT(*) as data_count
FROM price_data pd
JOIN products p ON pd.product_id = p.id
WHERE data_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
GROUP BY product_name
ORDER BY avg_price DESC;

-- 3. 地区价格对比
SELECT
    r.name as region_name,
    p.name as product_name,
    AVG(pd.price) as avg_price
FROM price_data pd
JOIN products p ON pd.product_id = p.id
JOIN regions r ON pd.region_id = r.id
WHERE p.name = '苹果' AND pd.data_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
GROUP BY r.name, p.name
ORDER BY avg_price DESC;

-- 4. 价格变化幅度分析
SELECT
    p.name as product_name,
    pd1.price as current_price,
    pd2.price as previous_price,
    ((pd1.price - pd2.price) / pd2.price * 100) as change_percent
FROM price_data pd1
JOIN price_data pd2 ON pd1.product_id = pd2.product_id
    AND pd1.region_id = pd2.region_id
JOIN products p ON pd1.product_id = p.id
WHERE pd1.data_date = CURDATE()
    AND pd2.data_date = DATE_SUB(CURDATE(), INTERVAL 7 DAY)
ORDER BY ABS(change_percent) DESC;
```

</details>

#### Python 查询示例

```python
from huinong_spider.models import PriceDataDAO, ProductDAO

# 创建DAO实例
price_dao = PriceDataDAO()
product_dao = ProductDAO()

# 查询最新价格数据
latest_prices = price_dao.find_latest_by_product(product_id=1, limit=10)

# 获取价格统计信息
stats = price_dao.get_price_statistics(product_id=1, days=30)
print(f"平均价格: {stats['avg_price']:.2f}")
print(f"最高价格: {stats['max_price']:.2f}")
print(f"最低价格: {stats['min_price']:.2f}")
```

### 🔌 API接口

#### RESTful API 使用

<details>
<summary><b>API接口文档</b></summary>

```bash
# 1. 获取分类列表
GET /api/v1/categories
curl http://localhost:8000/api/v1/categories

# 2. 获取地区列表
GET /api/v1/regions
curl http://localhost:8000/api/v1/regions

# 3. 查询价格数据
GET /api/v1/prices?start_date=2025-01-01&end_date=2025-01-31&category_id=1
curl "http://localhost:8000/api/v1/prices?start_date=2025-01-01&end_date=2025-01-31&category_id=1"

# 4. 获取产品列表
GET /api/v1/products?category_id=1
curl "http://localhost:8000/api/v1/products?category_id=1"

# 5. 导出数据
POST /api/v1/export/csv
curl -X POST http://localhost:8000/api/v1/export/csv \
  -H "Content-Type: application/json" \
  -d '{"start_date":"2025-01-01","end_date":"2025-01-31","category_ids":[1,2]}'

# 6. 获取统计信息
GET /api/v1/statistics
curl http://localhost:8000/api/v1/statistics
```

</details>

## 🔧 监控和维护

### 📊 系统监控

#### 状态检查
```bash
# 🔍 检查系统整体状态
python scripts/status.py

# 📈 查看数据统计
python scripts/status.py --statistics

# 🏥 健康检查
python scripts/status.py --health
```

#### 日志管理
```bash
# 📋 查看实时日志
tail -f logs/spider.log

# ❌ 查看错误日志
grep ERROR logs/spider.log | tail -20

# 📊 查看统计信息
grep "统计信息" logs/spider.log | tail -10

# 🔄 日志轮转（自动执行）
# 日志文件会自动按天轮转，保留30天
```

### 🗄️ 数据库维护

#### 数据清理
```sql
-- 清理过期数据（保留365天）
CALL CleanOldData();

-- 手动清理指定日期前的数据
DELETE FROM price_data WHERE data_date < '2024-01-01';

-- 清理无效的爬取日志
DELETE FROM crawl_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY);
```

#### 数据库优化
```bash
# 🔧 数据库优化脚本
python scripts/optimize_database.py

# 📊 分析表结构
python scripts/analyze_tables.py

# 💾 数据备份
python scripts/backup_data.py
```

### 📈 性能监控

#### 监控指标
- **📊 数据采集量**: 每小时采集数据条数
- **⚡ 响应时间**: 平均请求响应时间
- **❌ 错误率**: 请求失败率统计
- **💾 存储使用**: 数据库存储空间使用情况
- **🖥️ 系统资源**: CPU、内存使用率

#### 监控命令
```bash
# 📊 实时监控面板（如果启用了API）
curl http://localhost:8000/api/v1/monitor

# 📈 性能报告
python scripts/performance_report.py

# 🔍 数据质量报告
python scripts/quality_report.py
```

## 🛠️ 开发指南

### 📁 项目结构

<details>
<summary><b>点击查看详细项目结构</b></summary>

```
huinong_spider/
├── 📁 huinong_spider/           # 主要代码包
│   ├── 📄 __init__.py          # 包初始化
│   ├── 📁 config/              # 配置管理
│   │   ├── 📄 __init__.py
│   │   ├── 📄 settings.py      # 配置加载
│   │   └── 📄 database.py      # 数据库配置
│   ├── 📁 models/              # 数据模型
│   │   ├── 📄 __init__.py
│   │   ├── 📄 base.py         # 基础模型类
│   │   ├── 📄 category.py     # 分类模型
│   │   ├── 📄 region.py       # 地区模型
│   │   ├── 📄 product.py      # 产品模型
│   │   ├── 📄 price_data.py   # 价格数据模型
│   │   └── 📄 crawl_log.py    # 爬取日志模型
│   ├── 📁 spiders/             # 爬虫模块
│   │   ├── 📄 __init__.py
│   │   ├── 📄 base_spider.py  # 基础爬虫类
│   │   └── 📄 huinong_spider.py # 惠农网爬虫
│   └── 📁 utils/               # 工具模块
│       ├── 📄 __init__.py
│       ├── 📄 logger.py       # 日志管理
│       ├── 📄 database.py     # 数据库管理
│       └── 📄 helpers.py      # 辅助函数
├── 📁 scripts/                 # 脚本文件
│   ├── 📄 setup.py            # 项目初始化脚本
│   ├── 📄 test_spider.py      # 测试脚本
│   └── 📄 status.py           # 状态检查脚本
├── 📁 database/                # 数据库脚本
│   └── 📄 init_database.sql   # 数据库初始化脚本
├── 📁 docs/                    # 文档
│   ├── 📄 PRD_惠农网价格数据爬虫项目.md
│   └── 📄 TRD_惠农网价格数据爬虫项目.md
├── 📁 config/                  # 配置文件
│   └── 📄 settings.yaml.example # 配置文件模板
├── 📁 logs/                    # 日志文件（运行时创建）
├── 📁 cache/                   # 缓存文件（运行时创建）
├── 📁 exports/                 # 导出文件（运行时创建）
├── 📄 main.py                  # 主程序入口
├── 📄 requirements.txt         # Python依赖
├── 📄 .env.example            # 环境变量模板
├── 📄 run.bat                 # Windows运行脚本
├── 📄 run.sh                  # Linux/Mac运行脚本
└── 📄 README.md               # 项目说明
```

</details>

### 📝 开发规范

#### 代码规范
- **🐍 PEP 8**: 遵循Python官方编码规范
- **📝 类型注解**: 使用Type Hints提高代码可读性
- **📚 文档字符串**: 所有函数和类都要有详细的文档字符串
- **🧪 单元测试**: 新功能必须编写对应的单元测试
- **🔍 代码审查**: 提交前进行代码审查

#### Git 提交规范
```bash
# 提交信息格式
<type>(<scope>): <subject>

# 类型说明
feat:     新功能
fix:      Bug修复
docs:     文档更新
style:    代码格式调整
refactor: 代码重构
test:     测试相关
chore:    构建过程或辅助工具的变动

# 示例
feat(spider): 添加增量更新功能
fix(database): 修复连接池泄漏问题
docs(readme): 更新安装说明
```

### 🧪 测试

#### 运行测试
```bash
# 🧪 运行所有测试
python scripts/test_spider.py

# 🔍 运行特定测试模块
python -m pytest tests/test_models.py

# 📊 生成覆盖率报告
python -m pytest --cov=huinong_spider --cov-report=html

# 🚀 性能测试
python scripts/performance_test.py
```

#### 测试分类
- **🔧 单元测试**: 测试单个函数和类
- **🔗 集成测试**: 测试模块间的交互
- **🌐 端到端测试**: 测试完整的爬取流程
- **⚡ 性能测试**: 测试系统性能指标

## ❓ 常见问题

<details>
<summary><b>🕷️ 爬虫相关问题</b></summary>

### Q: 爬虫被反爬虫机制阻止怎么办？
**A: 多种解决方案**
1. **⏰ 增加请求间隔**: 修改 `config/settings.yaml` 中的 `download_delay`
2. **🎭 更换User-Agent**: 在配置文件中添加更多User-Agent
3. **🌐 使用代理IP**: 配置代理IP池（如需要）
4. **🔍 检查验证码**: 查看日志是否出现验证码拦截
5. **📊 降低并发数**: 设置 `concurrent_requests: 1`

```yaml
spider:
  download_delay: 3        # 增加到3秒
  concurrent_requests: 1   # 降低并发
  retry_times: 5          # 增加重试次数
```

### Q: 数据采集速度太慢？
**A: 性能优化建议**
1. **⚡ 适当增加并发**: 在不触发反爬虫的前提下增加并发数
2. **🎯 精确目标**: 只爬取需要的分类和地区
3. **📊 限制页数**: 使用 `--max-pages` 参数
4. **🔄 增量更新**: 使用 `--incremental` 模式

</details>

<details>
<summary><b>🗄️ 数据库相关问题</b></summary>

### Q: 数据库连接失败？
**A: 连接问题排查**
1. **🔧 检查服务状态**: `systemctl status mysql` (Linux) 或服务管理器 (Windows)
2. **⚙️ 验证配置**: 检查 `.env` 文件中的数据库配置
3. **🔐 确认权限**: 确保数据库用户有足够权限
4. **🔥 检查防火墙**: 确保3306端口开放
5. **🌐 测试连接**: 使用MySQL客户端测试连接

```bash
# 测试数据库连接
mysql -h localhost -u root -p -e "SELECT 1;"
```

### Q: 数据库空间不足？
**A: 空间管理**
1. **🧹 清理旧数据**: 运行 `CALL CleanOldData();`
2. **📊 分析表大小**: 查看各表的存储占用
3. **🗜️ 数据压缩**: 考虑数据归档和压缩
4. **💾 扩展存储**: 增加磁盘空间

```sql
-- 查看表大小
SELECT
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.tables
WHERE table_schema = 'huinong_spider'
ORDER BY (data_length + index_length) DESC;
```

</details>

<details>
<summary><b>🖥️ 系统性能问题</b></summary>

### Q: 内存使用过高？
**A: 内存优化方案**
1. **📉 减少并发数**: 降低 `concurrent_requests`
2. **🔄 增加清理频率**: 更频繁地清理缓存
3. **⚡ 优化处理逻辑**: 使用生成器而非列表
4. **💾 增加系统内存**: 硬件升级

```yaml
# 内存优化配置
spider:
  concurrent_requests: 1
  batch_size: 100        # 减少批处理大小

performance:
  memory:
    max_memory_usage: "1GB"
    gc_threshold: 0.7
```

### Q: CPU使用率过高？
**A: CPU优化建议**
1. **⏰ 增加延迟**: 给CPU更多休息时间
2. **📊 减少并发**: 降低同时处理的任务数
3. **🔧 优化算法**: 检查是否有性能瓶颈
4. **⚖️ 负载均衡**: 考虑分布式部署

</details>

<details>
<summary><b>📊 数据质量问题</b></summary>

### Q: 采集的数据不准确？
**A: 数据质量保证**
1. **🔍 检查解析逻辑**: 验证HTML解析是否正确
2. **📊 数据验证**: 查看数据质量报告
3. **🧹 清洗规则**: 调整数据清洗规则
4. **📈 监控异常**: 设置数据异常告警

```bash
# 生成数据质量报告
python scripts/quality_report.py
```

### Q: 数据重复或缺失？
**A: 数据完整性检查**
1. **🔄 去重机制**: 检查唯一性约束是否生效
2. **📅 时间范围**: 确认数据采集的时间范围
3. **🎯 目标检查**: 验证分类和地区配置
4. **📊 统计分析**: 使用SQL分析数据分布

</details>

## 🤝 贡献指南

我们欢迎所有形式的贡献！无论是报告Bug、提出新功能建议，还是提交代码改进。

### 🚀 如何贡献

<details>
<summary><b>代码贡献流程</b></summary>

1. **🍴 Fork 项目**
   ```bash
   # 点击GitHub页面右上角的Fork按钮
   ```

2. **📥 克隆到本地**
   ```bash
   git clone https://github.com/your-username/huinong-spider.git
   cd huinong-spider
   ```

3. **🌿 创建功能分支**
   ```bash
   git checkout -b feature/amazing-feature
   # 或
   git checkout -b fix/bug-description
   ```

4. **💻 进行开发**
   - 遵循项目的代码规范
   - 添加必要的测试
   - 更新相关文档

5. **✅ 测试验证**
   ```bash
   python scripts/test_spider.py
   ```

6. **📝 提交更改**
   ```bash
   git add .
   git commit -m "feat: add amazing feature"
   ```

7. **📤 推送分支**
   ```bash
   git push origin feature/amazing-feature
   ```

8. **🔄 创建 Pull Request**
   - 在GitHub上创建Pull Request
   - 详细描述你的更改
   - 等待代码审查

</details>

### 📋 贡献类型

- **🐛 Bug报告**: 发现问题请创建Issue
- **💡 功能建议**: 有好想法请告诉我们
- **📚 文档改进**: 帮助完善文档
- **🧪 测试用例**: 增加测试覆盖率
- **🎨 界面优化**: 改进用户体验
- **⚡ 性能优化**: 提升系统性能

### 🏆 贡献者

感谢所有为项目做出贡献的开发者！

<!-- 这里可以添加贡献者列表 -->

## 📄 许可证

本项目采用 **MIT 许可证** - 查看 [LICENSE](LICENSE) 文件了解详情。

### 许可证要点
- ✅ 商业使用
- ✅ 修改
- ✅ 分发
- ✅ 私人使用
- ❌ 责任
- ❌ 保证

## ⚠️ 免责声明

> **重要提示**: 本项目仅用于学习和研究目的

### 使用须知
1. **🤖 遵守robots.txt**: 请遵守目标网站的robots.txt协议
2. **⏰ 合理频率**: 控制请求频率，避免给服务器造成压力
3. **📚 学习目的**: 仅用于技术学习和研究，不得用于商业用途
4. **⚖️ 法律责任**: 使用者应承担因使用本项目产生的所有法律责任
5. **🔒 数据安全**: 妥善保管采集的数据，不得泄露或滥用

### 建议做法
- 在非高峰时段运行爬虫
- 设置合理的请求间隔（建议≥2秒）
- 定期检查目标网站的使用条款
- 尊重网站的知识产权

## 📞 联系方式

### 项目相关
- **📧 邮箱**: <EMAIL>
- **🐙 GitHub**: https://github.com/your-repo/huinong-spider
- **📖 文档**: https://your-repo.github.io/huinong-spider
- **💬 讨论**: https://github.com/your-repo/huinong-spider/discussions

### 技术支持
- **🐛 Bug报告**: [创建Issue](https://github.com/your-repo/huinong-spider/issues/new?template=bug_report.md)
- **💡 功能请求**: [创建Issue](https://github.com/your-repo/huinong-spider/issues/new?template=feature_request.md)
- **❓ 使用问题**: [查看Wiki](https://github.com/your-repo/huinong-spider/wiki)

## 📈 更新日志

### 🎉 v1.0.0 (2025-07-24)
- ✨ 初始版本发布
- 🚀 支持17个主要分类数据采集
- 🗄️ 完整的数据库设计和数据模型
- 🔌 RESTful API接口
- 📊 监控告警和数据质量保证机制
- 📚 完整的项目文档
- 🧪 全面的测试覆盖
- 🛠️ 一键安装和部署脚本

### 🔮 未来计划
- 📊 数据可视化面板
- 🤖 智能价格预测
- 📱 移动端支持
- 🌐 多语言支持
- ☁️ 云部署支持

---

<div align="center">

**⭐ 如果这个项目对你有帮助，请给我们一个Star！**

[![Star History Chart](https://api.star-history.com/svg?repos=your-repo/huinong-spider&type=Date)](https://star-history.com/#your-repo/huinong-spider&Date)

**🌾 让农产品价格数据触手可及 🌾**

</div>
