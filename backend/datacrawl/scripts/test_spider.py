#!/usr/bin/env python3
"""
爬虫测试脚本

用于测试爬虫的各个功能模块。
"""

import sys
import unittest
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from huinong_spider.utils.database import get_db_manager
from huinong_spider.utils.helpers import clean_price, standardize_region, parse_date
from huinong_spider.models import CategoryDAO, RegionDAO, ProductDAO, PriceDataDAO
from huinong_spider.spiders import HuinongSpider
from huinong_spider.config.settings import settings


class TestDatabase(unittest.TestCase):
    """测试数据库连接"""
    
    def setUp(self):
        self.db = get_db_manager()
    
    def test_connection(self):
        """测试数据库连接"""
        with self.db.get_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                self.assertIsNotNone(result)
    
    def test_table_exists(self):
        """测试表是否存在"""
        tables = ['categories', 'regions', 'products', 'price_data', 'crawl_logs']
        
        for table in tables:
            exists = self.db.table_exists(table)
            self.assertTrue(exists, f"表 {table} 不存在")


class TestHelpers(unittest.TestCase):
    """测试辅助函数"""
    
    def test_clean_price(self):
        """测试价格清洗函数"""
        test_cases = [
            ("1.50", 1.50),
            ("￥2.30", 2.30),
            ("3.5元/斤", 3.5),
            ("无效价格", None),
            ("", None),
            ("0", 0.0)
        ]
        
        for input_price, expected in test_cases:
            result = clean_price(input_price)
            self.assertEqual(result, expected, f"价格清洗失败: {input_price}")
    
    def test_standardize_region(self):
        """测试地区标准化函数"""
        test_cases = [
            ("北京市", "北京"),
            ("山东省", "山东"),
            ("内蒙古自治区", "内蒙古"),
            ("香港特别行政区", "香港"),
            ("普通地区", "普通地区")
        ]
        
        for input_region, expected in test_cases:
            result = standardize_region(input_region)
            self.assertEqual(result, expected, f"地区标准化失败: {input_region}")
    
    def test_parse_date(self):
        """测试日期解析函数"""
        test_cases = [
            "2025-01-15",
            "2025/01/15",
            "2025.01.15",
            "2025年01月15日"
        ]
        
        for date_str in test_cases:
            result = parse_date(date_str)
            self.assertIsNotNone(result, f"日期解析失败: {date_str}")


class TestModels(unittest.TestCase):
    """测试数据模型"""
    
    def setUp(self):
        self.category_dao = CategoryDAO()
        self.region_dao = RegionDAO()
        self.product_dao = ProductDAO()
        self.price_data_dao = PriceDataDAO()
    
    def test_category_dao(self):
        """测试分类DAO"""
        # 测试查找分类
        categories = self.category_dao.find_active_categories()
        self.assertGreater(len(categories), 0, "没有找到启用的分类")
        
        # 测试根据名称查找
        category = self.category_dao.find_by_name("水果")
        if category:
            self.assertEqual(category.name, "水果")
    
    def test_region_dao(self):
        """测试地区DAO"""
        # 测试查找省份
        provinces = self.region_dao.find_provinces()
        self.assertGreater(len(provinces), 0, "没有找到省份")
        
        # 测试根据名称查找
        region = self.region_dao.find_by_name("山东")
        if region:
            self.assertEqual(region.name, "山东")
    
    def test_product_dao(self):
        """测试产品DAO"""
        # 测试查找产品
        products = self.product_dao.find_active_products()
        self.assertIsInstance(products, list)
    
    def test_price_data_dao(self):
        """测试价格数据DAO"""
        # 测试统计功能
        count = self.price_data_dao.count()
        self.assertIsInstance(count, int)


class TestSpider(unittest.TestCase):
    """测试爬虫"""
    
    def test_spider_init(self):
        """测试爬虫初始化"""
        spider = HuinongSpider()
        self.assertEqual(spider.name, "huinong_spider")
        self.assertIsNotNone(spider.session)
        spider.session.close()
    
    def test_fetch_page(self):
        """测试页面获取"""
        spider = HuinongSpider()
        
        try:
            # 测试获取首页
            response = spider.fetch_page(spider.base_url)
            if response:
                self.assertEqual(response.status_code, 200)
                self.assertIn("惠农网", response.text)
        except Exception as e:
            self.skipTest(f"网络请求失败: {e}")
        finally:
            spider.session.close()


class TestConfig(unittest.TestCase):
    """测试配置"""
    
    def test_settings_load(self):
        """测试配置加载"""
        self.assertIsNotNone(settings)
        
        # 测试数据库配置
        db_config = settings.database
        self.assertIsInstance(db_config, dict)
        self.assertIn('host', db_config)
        
        # 测试爬虫配置
        spider_config = settings.spider
        self.assertIsInstance(spider_config, dict)


def run_basic_tests():
    """运行基础测试"""
    print("🧪 运行基础功能测试...")
    
    # 创建测试套件
    suite = unittest.TestSuite()
    
    # 添加测试用例
    suite.addTest(unittest.makeSuite(TestHelpers))
    suite.addTest(unittest.makeSuite(TestConfig))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()


def run_database_tests():
    """运行数据库测试"""
    print("🗄️ 运行数据库测试...")
    
    try:
        # 创建测试套件
        suite = unittest.TestSuite()
        suite.addTest(unittest.makeSuite(TestDatabase))
        suite.addTest(unittest.makeSuite(TestModels))
        
        # 运行测试
        runner = unittest.TextTestRunner(verbosity=2)
        result = runner.run(suite)
        
        return result.wasSuccessful()
        
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        return False


def run_spider_tests():
    """运行爬虫测试"""
    print("🕷️ 运行爬虫测试...")
    
    try:
        # 创建测试套件
        suite = unittest.TestSuite()
        suite.addTest(unittest.makeSuite(TestSpider))
        
        # 运行测试
        runner = unittest.TextTestRunner(verbosity=2)
        result = runner.run(suite)
        
        return result.wasSuccessful()
        
    except Exception as e:
        print(f"❌ 爬虫测试失败: {e}")
        return False


def run_integration_test():
    """运行集成测试"""
    print("🔗 运行集成测试...")
    
    try:
        # 创建爬虫实例
        spider = HuinongSpider()
        
        # 运行测试爬取
        print("  测试爬取水果分类数据...")
        success = spider.run(
            categories=['水果'],
            regions=['山东'],
            max_pages=1
        )
        
        spider.session.close()
        
        if success:
            print("✅ 集成测试通过")
            return True
        else:
            print("❌ 集成测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 开始运行爬虫测试...")
    
    all_passed = True
    
    # 运行基础测试
    if not run_basic_tests():
        print("❌ 基础测试失败")
        all_passed = False
    else:
        print("✅ 基础测试通过")
    
    # 运行数据库测试
    if not run_database_tests():
        print("❌ 数据库测试失败")
        all_passed = False
    else:
        print("✅ 数据库测试通过")
    
    # 运行爬虫测试
    if not run_spider_tests():
        print("❌ 爬虫测试失败")
        all_passed = False
    else:
        print("✅ 爬虫测试通过")
    
    # 运行集成测试
    if not run_integration_test():
        print("❌ 集成测试失败")
        all_passed = False
    else:
        print("✅ 集成测试通过")
    
    # 总结
    if all_passed:
        print("\n🎉 所有测试通过！")
        return 0
    else:
        print("\n💥 部分测试失败，请检查错误信息")
        return 1


if __name__ == "__main__":
    sys.exit(main())
