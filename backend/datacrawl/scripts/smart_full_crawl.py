#!/usr/bin/env python3
"""
智能全量数据爬取脚本

实现更智能的反爬虫策略和错误处理机制
"""

import sys
import time
import random
import signal
from pathlib import Path
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from huinong_spider.spiders.huinong_spider import HuinongSpider
from huinong_spider.models import CategoryDAO, RegionDAO
from huinong_spider.utils.logger import get_logger
from huinong_spider.utils.database import get_db_manager


class SmartCrawlManager:
    """智能爬取管理器"""
    
    def __init__(self):
        self.logger = get_logger("smart_crawl")
        self.spider = HuinongSpider()
        self.category_dao = CategoryDAO()
        self.region_dao = RegionDAO()
        
        # 爬取状态
        self.is_running = True
        self.current_task = None
        self.completed_tasks = []
        self.failed_tasks = []
        self.start_time = None
        
        # 智能配置
        self.max_pages_per_combination = 2  # 减少页数
        self.base_pause = 60  # 基础暂停时间（1分钟）
        self.max_pause = 1800  # 最大暂停时间（30分钟）
        self.consecutive_failure_threshold = 3  # 连续失败阈值
        self.success_reset_factor = 0.8  # 成功后暂停时间减少因子
        
        # 动态暂停时间
        self.current_pause = self.base_pause
        self.consecutive_failures = 0
        self.last_success_time = None
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        self.logger.info(f"收到信号 {signum}，准备优雅退出...")
        self.is_running = False
    
    def get_prioritized_tasks(self) -> List[Dict[str, Any]]:
        """获取优先级排序的任务列表"""
        tasks = []
        
        # 获取所有启用的分类和地区
        categories = self.category_dao.find_active_categories()
        regions = self.region_dao.find_active_regions()
        
        # 优先级策略：先爬取之前成功过的分类
        successful_categories = ['水果', '蔬菜', '农副加工', '种子种苗', '苗木花草', '中药材']
        successful_regions = ['山东', '河南', '河北']
        
        # 生成高优先级任务
        high_priority_tasks = []
        for category in categories:
            if category.name in successful_categories:
                for region in regions:
                    if region.name in successful_regions:
                        task = self._create_task(category, region, priority='high')
                        high_priority_tasks.append(task)
        
        # 生成中优先级任务
        medium_priority_tasks = []
        for category in categories:
            if category.name in successful_categories:
                for region in regions:
                    if region.name not in successful_regions:
                        task = self._create_task(category, region, priority='medium')
                        medium_priority_tasks.append(task)
        
        # 生成低优先级任务
        low_priority_tasks = []
        for category in categories:
            if category.name not in successful_categories:
                for region in regions:
                    task = self._create_task(category, region, priority='low')
                    low_priority_tasks.append(task)
        
        # 随机打乱每个优先级内的任务顺序
        random.shuffle(high_priority_tasks)
        random.shuffle(medium_priority_tasks)
        random.shuffle(low_priority_tasks)
        
        # 合并任务列表
        tasks = high_priority_tasks + medium_priority_tasks + low_priority_tasks
        
        self.logger.info(f"生成任务: 高优先级 {len(high_priority_tasks)}, 中优先级 {len(medium_priority_tasks)}, 低优先级 {len(low_priority_tasks)}")
        return tasks
    
    def _create_task(self, category, region, priority='medium'):
        """创建任务"""
        return {
            'category': category,
            'region': region,
            'category_name': category.name,
            'region_name': region.name,
            'priority': priority,
            'max_pages': self.max_pages_per_combination,
            'status': 'pending',
            'attempts': 0,
            'last_attempt': None,
            'error_message': None,
            'data_count': 0
        }
    
    def calculate_dynamic_pause(self) -> int:
        """计算动态暂停时间"""
        if self.consecutive_failures == 0:
            # 没有连续失败，使用基础暂停时间
            pause = self.base_pause
        else:
            # 有连续失败，指数增长暂停时间
            pause = min(
                self.base_pause * (2 ** self.consecutive_failures),
                self.max_pause
            )
        
        # 添加随机抖动（±20%）
        jitter = pause * 0.2
        pause = pause + random.uniform(-jitter, jitter)
        
        return max(int(pause), 30)  # 最少30秒
    
    def execute_task(self, task: Dict[str, Any]) -> bool:
        """执行单个爬取任务"""
        category = task['category']
        region = task['region']
        max_pages = task['max_pages']
        
        self.current_task = task
        task['attempts'] += 1
        task['last_attempt'] = datetime.now()
        task['status'] = 'running'
        
        self.logger.info(f"执行任务 [{task['priority']}]: {category.name} - {region.name}")
        
        try:
            # 执行爬取
            result = self.spider.crawl_category_region(category, region, max_pages)
            
            if result and result > 0:
                task['status'] = 'completed'
                task['data_count'] = result
                self.consecutive_failures = 0
                self.last_success_time = datetime.now()
                
                # 成功后减少暂停时间
                self.current_pause = max(
                    int(self.current_pause * self.success_reset_factor),
                    self.base_pause
                )
                
                self.logger.info(f"✅ 任务成功: {category.name} - {region.name}，采集 {result} 条数据")
                return True
            else:
                task['status'] = 'no_data'
                task['data_count'] = 0
                self.consecutive_failures = 0  # 无数据不算失败
                self.logger.info(f"⚪ 任务完成但无数据: {category.name} - {region.name}")
                return True
                
        except Exception as e:
            task['status'] = 'failed'
            task['error_message'] = str(e)
            self.consecutive_failures += 1
            
            self.logger.error(f"❌ 任务失败: {category.name} - {region.name}，错误: {e}")
            self.logger.warning(f"连续失败次数: {self.consecutive_failures}")
            
            return False
        
        finally:
            self.current_task = None
    
    def should_take_long_break(self) -> bool:
        """判断是否需要长时间休息"""
        return self.consecutive_failures >= self.consecutive_failure_threshold
    
    def run_smart_crawl(self):
        """运行智能爬取"""
        self.start_time = datetime.now()
        self.logger.info("🚀 开始智能全量数据爬取")
        
        # 获取优先级排序的任务
        tasks = self.get_prioritized_tasks()
        total_tasks = len(tasks)
        
        self.logger.info(f"📋 总任务数: {total_tasks}")
        
        # 执行任务
        for i, task in enumerate(tasks):
            if not self.is_running:
                self.logger.info("⏹️ 收到停止信号，退出爬取")
                break
            
            # 显示进度
            progress = (i + 1) / total_tasks * 100
            self.logger.info(f"📊 进度: {i+1}/{total_tasks} ({progress:.1f}%)")
            
            # 检查是否需要长时间休息
            if self.should_take_long_break():
                long_break = self.calculate_dynamic_pause()
                self.logger.warning(f"😴 连续失败 {self.consecutive_failures} 次，长时间休息 {long_break} 秒")
                
                # 分段等待，允许中途中断
                for _ in range(long_break // 10):
                    if not self.is_running:
                        break
                    time.sleep(10)
                
                # 重置反爬虫状态
                self.spider.anti_spider.reset_after_long_pause()
                self.logger.info("🔄 反爬虫状态已重置")
            
            # 执行任务
            success = self.execute_task(task)
            
            if success:
                self.completed_tasks.append(task)
            else:
                self.failed_tasks.append(task)
            
            # 动态暂停
            if i < total_tasks - 1:  # 最后一个任务不需要暂停
                pause_time = self.calculate_dynamic_pause()
                self.logger.info(f"⏱️ 任务间暂停 {pause_time} 秒")
                
                # 分段等待，允许中途中断
                for _ in range(pause_time // 5):
                    if not self.is_running:
                        break
                    time.sleep(5)
        
        # 生成最终报告
        self.generate_final_report(tasks)
    
    def generate_final_report(self, tasks: List[Dict[str, Any]]):
        """生成最终报告"""
        end_time = datetime.now()
        total_time = end_time - self.start_time
        
        # 统计信息
        total_tasks = len(tasks)
        completed_tasks = len([t for t in tasks if t['status'] == 'completed'])
        no_data_tasks = len([t for t in tasks if t['status'] == 'no_data'])
        failed_tasks = len([t for t in tasks if t['status'] == 'failed'])
        pending_tasks = len([t for t in tasks if t['status'] == 'pending'])
        
        total_data = sum(t.get('data_count', 0) for t in tasks if t.get('data_count'))
        
        # 按优先级统计
        high_completed = len([t for t in tasks if t['priority'] == 'high' and t['status'] == 'completed'])
        medium_completed = len([t for t in tasks if t['priority'] == 'medium' and t['status'] == 'completed'])
        low_completed = len([t for t in tasks if t['priority'] == 'low' and t['status'] == 'completed'])
        
        self.logger.info("="*60)
        self.logger.info("🎯 智能全量爬取完成报告")
        self.logger.info("="*60)
        self.logger.info(f"⏰ 开始时间: {self.start_time}")
        self.logger.info(f"⏰ 结束时间: {end_time}")
        self.logger.info(f"⏱️ 总耗时: {total_time}")
        self.logger.info("")
        self.logger.info("📊 任务统计:")
        self.logger.info(f"  📋 总任务数: {total_tasks}")
        self.logger.info(f"  ✅ 已完成: {completed_tasks}")
        self.logger.info(f"  ⚪ 无数据: {no_data_tasks}")
        self.logger.info(f"  ❌ 失败: {failed_tasks}")
        self.logger.info(f"  ⏸️ 未执行: {pending_tasks}")
        self.logger.info(f"  📈 成功率: {(completed_tasks + no_data_tasks) / total_tasks:.2%}")
        self.logger.info("")
        self.logger.info("🎯 优先级完成情况:")
        self.logger.info(f"  🔴 高优先级: {high_completed}")
        self.logger.info(f"  🟡 中优先级: {medium_completed}")
        self.logger.info(f"  🟢 低优先级: {low_completed}")
        self.logger.info("")
        self.logger.info("📈 数据统计:")
        self.logger.info(f"  📊 总采集数据: {total_data} 条")
        self.logger.info(f"  📊 平均每任务: {total_data / max(completed_tasks, 1):.1f} 条")
        
        self.logger.info("="*60)


def main():
    """主函数"""
    manager = SmartCrawlManager()
    
    try:
        manager.run_smart_crawl()
        return 0
    except KeyboardInterrupt:
        manager.logger.info("⏹️ 用户中断爬取")
        return 1
    except Exception as e:
        manager.logger.error(f"❌ 爬取过程中发生错误: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
