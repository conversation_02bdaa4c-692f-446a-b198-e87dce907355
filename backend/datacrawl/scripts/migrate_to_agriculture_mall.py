#!/usr/bin/env python3
"""
数据库迁移脚本

将数据从 huinong_spider 数据库迁移到 agriculture_mall 数据库
"""

import sys
import mysql.connector
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from huinong_spider.utils.logger import get_logger


class DatabaseMigrator:
    """数据库迁移器"""
    
    def __init__(self):
        self.logger = get_logger("db_migrator")
        
        # 数据库连接配置
        self.source_config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': 'fan13965711955',
            'database': 'huinong_spider',
            'charset': 'utf8mb4'
        }
        
        self.target_config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': 'fan13965711955',
            'database': 'agriculture_mall',
            'charset': 'utf8mb4'
        }
        
        self.source_conn = None
        self.target_conn = None
        
        # 迁移统计
        self.migration_stats = {
            'categories': {'migrated': 0, 'skipped': 0, 'errors': 0},
            'regions': {'migrated': 0, 'skipped': 0, 'errors': 0},
            'products': {'migrated': 0, 'skipped': 0, 'errors': 0},
            'price_data': {'migrated': 0, 'skipped': 0, 'errors': 0},
            'crawl_logs': {'migrated': 0, 'skipped': 0, 'errors': 0}
        }
    
    def connect_databases(self):
        """连接源数据库和目标数据库"""
        try:
            self.logger.info("连接源数据库 (huinong_spider)...")
            self.source_conn = mysql.connector.connect(**self.source_config)
            
            self.logger.info("连接目标数据库 (agriculture_mall)...")
            self.target_conn = mysql.connector.connect(**self.target_config)
            
            self.logger.info("数据库连接成功")
            return True
            
        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            return False
    
    def close_connections(self):
        """关闭数据库连接"""
        if self.source_conn:
            self.source_conn.close()
        if self.target_conn:
            self.target_conn.close()
        self.logger.info("数据库连接已关闭")
    
    def check_table_compatibility(self) -> bool:
        """检查表结构兼容性"""
        self.logger.info("检查表结构兼容性...")
        
        try:
            source_cursor = self.source_conn.cursor()
            target_cursor = self.target_conn.cursor()
            
            # 检查关键表是否存在
            tables_to_check = ['categories', 'regions', 'products', 'price_data', 'crawl_logs']
            
            for table in tables_to_check:
                # 检查源表
                source_cursor.execute(f"SHOW TABLES LIKE '{table}'")
                if not source_cursor.fetchone():
                    self.logger.warning(f"源数据库中不存在表: {table}")
                    continue
                
                # 检查目标表
                target_cursor.execute(f"SHOW TABLES LIKE '{table}'")
                if not target_cursor.fetchone():
                    self.logger.error(f"目标数据库中不存在表: {table}")
                    return False
                
                self.logger.info(f"✅ 表 {table} 在两个数据库中都存在")
            
            source_cursor.close()
            target_cursor.close()
            
            self.logger.info("表结构兼容性检查通过")
            return True
            
        except Exception as e:
            self.logger.error(f"表结构兼容性检查失败: {e}")
            return False
    
    def migrate_categories(self):
        """迁移分类数据"""
        self.logger.info("开始迁移分类数据...")
        
        try:
            source_cursor = self.source_conn.cursor(dictionary=True)
            target_cursor = self.target_conn.cursor()
            
            # 获取源数据
            source_cursor.execute("SELECT * FROM categories")
            categories = source_cursor.fetchall()
            
            for category in categories:
                try:
                    # 检查目标数据库中是否已存在
                    target_cursor.execute(
                        "SELECT id FROM categories WHERE category_id = %s",
                        (category['category_id'],)
                    )
                    
                    if target_cursor.fetchone():
                        self.logger.debug(f"分类 {category['name']} 已存在，跳过")
                        self.migration_stats['categories']['skipped'] += 1
                        continue
                    
                    # 插入数据
                    insert_sql = """
                        INSERT INTO categories (name, pinyin, category_id, parent_id, level, sort_order, is_active)
                        VALUES (%s, %s, %s, %s, %s, %s, %s)
                    """
                    
                    target_cursor.execute(insert_sql, (
                        category['name'],
                        category['pinyin'],
                        category['category_id'],
                        category.get('parent_id'),
                        category.get('level', 1),
                        category.get('sort_order', 0),
                        category.get('is_active', 1)
                    ))
                    
                    self.migration_stats['categories']['migrated'] += 1
                    self.logger.debug(f"迁移分类: {category['name']}")
                    
                except Exception as e:
                    self.logger.error(f"迁移分类 {category['name']} 失败: {e}")
                    self.migration_stats['categories']['errors'] += 1
            
            self.target_conn.commit()
            source_cursor.close()
            target_cursor.close()
            
            self.logger.info(f"分类数据迁移完成: 迁移 {self.migration_stats['categories']['migrated']} 条，跳过 {self.migration_stats['categories']['skipped']} 条")
            
        except Exception as e:
            self.logger.error(f"分类数据迁移失败: {e}")
            self.target_conn.rollback()
    
    def migrate_regions(self):
        """迁移地区数据"""
        self.logger.info("开始迁移地区数据...")
        
        try:
            source_cursor = self.source_conn.cursor(dictionary=True)
            target_cursor = self.target_conn.cursor()
            
            # 获取源数据
            source_cursor.execute("SELECT * FROM regions")
            regions = source_cursor.fetchall()
            
            for region in regions:
                try:
                    # 检查目标数据库中是否已存在
                    target_cursor.execute(
                        "SELECT id FROM regions WHERE region_code = %s",
                        (region['region_code'],)
                    )
                    
                    if target_cursor.fetchone():
                        self.logger.debug(f"地区 {region['name']} 已存在，跳过")
                        self.migration_stats['regions']['skipped'] += 1
                        continue
                    
                    # 插入数据
                    insert_sql = """
                        INSERT INTO regions (name, region_code, parent_id, level, full_name, is_active)
                        VALUES (%s, %s, %s, %s, %s, %s)
                    """
                    
                    target_cursor.execute(insert_sql, (
                        region['name'],
                        region['region_code'],
                        region.get('parent_id'),
                        region.get('level', 1),
                        region.get('full_name'),
                        region.get('is_active', 1)
                    ))
                    
                    self.migration_stats['regions']['migrated'] += 1
                    self.logger.debug(f"迁移地区: {region['name']}")
                    
                except Exception as e:
                    self.logger.error(f"迁移地区 {region['name']} 失败: {e}")
                    self.migration_stats['regions']['errors'] += 1
            
            self.target_conn.commit()
            source_cursor.close()
            target_cursor.close()
            
            self.logger.info(f"地区数据迁移完成: 迁移 {self.migration_stats['regions']['migrated']} 条，跳过 {self.migration_stats['regions']['skipped']} 条")
            
        except Exception as e:
            self.logger.error(f"地区数据迁移失败: {e}")
            self.target_conn.rollback()
    
    def migrate_products(self):
        """迁移产品数据"""
        self.logger.info("开始迁移产品数据...")
        
        try:
            source_cursor = self.source_conn.cursor(dictionary=True)
            target_cursor = self.target_conn.cursor()
            
            # 获取源数据
            source_cursor.execute("SELECT * FROM products")
            products = source_cursor.fetchall()
            
            for product in products:
                try:
                    # 检查目标数据库中是否已存在相同名称和分类的产品
                    target_cursor.execute(
                        "SELECT id FROM products WHERE name = %s AND category_id = %s",
                        (product['name'], product['category_id'])
                    )
                    
                    if target_cursor.fetchone():
                        self.logger.debug(f"产品 {product['name']} 已存在，跳过")
                        self.migration_stats['products']['skipped'] += 1
                        continue
                    
                    # 插入数据
                    insert_sql = """
                        INSERT INTO products (name, category_id, subcategory, product_code, unit, description, is_active)
                        VALUES (%s, %s, %s, %s, %s, %s, %s)
                    """
                    
                    target_cursor.execute(insert_sql, (
                        product['name'],
                        product['category_id'],
                        product.get('subcategory'),
                        product.get('product_code'),
                        product.get('unit', '斤'),
                        product.get('description'),
                        product.get('is_active', 1)
                    ))
                    
                    self.migration_stats['products']['migrated'] += 1
                    self.logger.debug(f"迁移产品: {product['name']}")
                    
                except Exception as e:
                    self.logger.error(f"迁移产品 {product['name']} 失败: {e}")
                    self.migration_stats['products']['errors'] += 1
            
            self.target_conn.commit()
            source_cursor.close()
            target_cursor.close()
            
            self.logger.info(f"产品数据迁移完成: 迁移 {self.migration_stats['products']['migrated']} 条，跳过 {self.migration_stats['products']['skipped']} 条")
            
        except Exception as e:
            self.logger.error(f"产品数据迁移失败: {e}")
            self.target_conn.rollback()
    
    def run_migration(self):
        """运行完整迁移"""
        self.logger.info("开始数据库迁移...")
        start_time = datetime.now()
        
        try:
            # 连接数据库
            if not self.connect_databases():
                return False
            
            # 检查兼容性
            if not self.check_table_compatibility():
                return False
            
            # 执行迁移
            self.migrate_categories()
            self.migrate_regions()
            self.migrate_products()
            # 注意：price_data 和 crawl_logs 包含大量数据，可能需要分批迁移
            
            # 生成迁移报告
            self.generate_migration_report(start_time)
            
            return True
            
        except Exception as e:
            self.logger.error(f"迁移过程中发生错误: {e}")
            return False
        
        finally:
            self.close_connections()
    
    def generate_migration_report(self, start_time: datetime):
        """生成迁移报告"""
        end_time = datetime.now()
        duration = end_time - start_time
        
        self.logger.info("="*60)
        self.logger.info("数据库迁移完成报告")
        self.logger.info("="*60)
        self.logger.info(f"开始时间: {start_time}")
        self.logger.info(f"结束时间: {end_time}")
        self.logger.info(f"总耗时: {duration}")
        self.logger.info("")
        
        for table, stats in self.migration_stats.items():
            if stats['migrated'] > 0 or stats['skipped'] > 0 or stats['errors'] > 0:
                self.logger.info(f"{table}:")
                self.logger.info(f"  迁移: {stats['migrated']} 条")
                self.logger.info(f"  跳过: {stats['skipped']} 条")
                self.logger.info(f"  错误: {stats['errors']} 条")
        
        self.logger.info("="*60)


def main():
    """主函数"""
    migrator = DatabaseMigrator()
    
    try:
        success = migrator.run_migration()
        return 0 if success else 1
    except KeyboardInterrupt:
        migrator.logger.info("用户中断迁移")
        return 1
    except Exception as e:
        migrator.logger.error(f"迁移过程中发生错误: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
