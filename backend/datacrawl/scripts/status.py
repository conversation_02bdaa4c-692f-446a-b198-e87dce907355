#!/usr/bin/env python3
"""
项目状态检查脚本

检查项目的运行状态、数据统计和系统健康状况。
"""

import sys
import os
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from huinong_spider import get_project_info
from huinong_spider.utils.database import get_db_manager
from huinong_spider.models import (
    CategoryDAO, RegionDAO, ProductDAO, 
    PriceDataDAO, CrawlLogDAO, CrawlStatus
)


def print_header(title):
    """打印标题"""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")


def print_section(title):
    """打印章节标题"""
    print(f"\n{'-'*40}")
    print(f" {title}")
    print(f"{'-'*40}")


def check_project_info():
    """检查项目信息"""
    print_header("项目信息")
    
    info = get_project_info()
    print(f"项目名称: {info['name']}")
    print(f"版本: {info['version']}")
    print(f"作者: {info['author']}")
    print(f"描述: {info['description']}")
    print(f"目标网站: {info['target_site']}")
    print(f"支持分类数: {len(info['supported_categories'])}")


def check_database_status():
    """检查数据库状态"""
    print_header("数据库状态")
    
    try:
        db = get_db_manager()
        
        # 测试连接
        with db.get_connection() as conn:
            print("✅ 数据库连接正常")
        
        # 连接池状态
        stats = db.get_connection_stats()
        print(f"连接池大小: {stats['pool_size']}")
        print(f"当前连接数: {stats['current_connections']}")
        print(f"最大连接数: {stats['max_connections']}")
        
        # 检查表
        tables = ['categories', 'regions', 'products', 'price_data', 'crawl_logs']
        print("\n表状态:")
        for table in tables:
            exists = db.table_exists(table)
            status = "✅ 存在" if exists else "❌ 不存在"
            print(f"  {table}: {status}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False


def check_data_statistics():
    """检查数据统计"""
    print_header("数据统计")
    
    try:
        # 分类统计
        category_dao = CategoryDAO()
        category_stats = category_dao.get_category_stats()
        
        print_section("分类统计")
        print(f"总分类数: {category_stats.get('total', 0)}")
        print(f"启用分类数: {category_stats.get('active', 0)}")
        for level in range(1, 4):
            count = category_stats.get(f'level_{level}', 0)
            print(f"第{level}级分类: {count}")
        
        # 地区统计
        region_dao = RegionDAO()
        region_stats = region_dao.get_region_stats()
        
        print_section("地区统计")
        print(f"总地区数: {region_stats.get('total', 0)}")
        print(f"启用地区数: {region_stats.get('active', 0)}")
        print(f"省级地区: {region_stats.get('provinces', 0)}")
        print(f"市级地区: {region_stats.get('cities', 0)}")
        print(f"县级地区: {region_stats.get('counties', 0)}")
        
        # 产品统计
        product_dao = ProductDAO()
        product_stats = product_dao.get_product_stats()
        
        print_section("产品统计")
        print(f"总产品数: {product_stats.get('total', 0)}")
        print(f"启用产品数: {product_stats.get('active', 0)}")
        
        # 按分类统计产品
        by_category = product_stats.get('by_category', {})
        if by_category:
            print("\n按分类统计:")
            for category, count in list(by_category.items())[:10]:
                print(f"  {category}: {count}")
        
        # 价格数据统计
        price_data_dao = PriceDataDAO()
        
        print_section("价格数据统计")
        total_price_data = price_data_dao.count()
        print(f"总价格数据: {total_price_data}")
        
        # 最近7天的数据
        recent_data = price_data_dao.count(
            "data_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)"
        )
        print(f"最近7天数据: {recent_data}")
        
        # 今日数据
        today_data = price_data_dao.count("data_date = CURDATE()")
        print(f"今日数据: {today_data}")
        
        # 数据质量报告
        quality_report = price_data_dao.get_data_quality_report()
        print(f"最新数据日期: {quality_report.get('latest_date', '无')}")
        print(f"缺失价格记录: {quality_report.get('missing_price', 0)}")
        print(f"异常价格记录: {quality_report.get('abnormal_price', 0)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据统计失败: {e}")
        return False


def check_crawl_logs():
    """检查爬取日志"""
    print_header("爬取日志")
    
    try:
        crawl_log_dao = CrawlLogDAO()
        
        # 最近7天统计
        stats = crawl_log_dao.get_statistics(days=7)
        
        print_section("最近7天统计")
        print(f"总任务数: {stats.get('total_tasks', 0)}")
        print(f"成功任务: {stats.get('success_tasks', 0)}")
        print(f"失败任务: {stats.get('failed_tasks', 0)}")
        print(f"运行中任务: {stats.get('running_tasks', 0)}")
        print(f"成功率: {stats.get('success_rate', 0):.2%}")
        print(f"平均执行时间: {stats.get('avg_duration', 0):.1f}秒")
        print(f"总采集数据: {stats.get('total_items', 0)}")
        
        # 正在运行的任务
        running_tasks = crawl_log_dao.find_running_tasks()
        if running_tasks:
            print_section("正在运行的任务")
            for task in running_tasks:
                duration = (datetime.now() - task.start_time).total_seconds()
                print(f"  任务ID: {task.task_id}")
                print(f"  爬虫: {task.spider_name}")
                print(f"  开始时间: {task.start_time}")
                print(f"  运行时长: {duration:.1f}秒")
        
        # 最近的任务
        recent_logs = crawl_log_dao.find_recent_logs(hours=24, limit=5)
        if recent_logs:
            print_section("最近任务")
            for log in recent_logs:
                status_icon = "✅" if log.status == CrawlStatus.SUCCESS.value else "❌"
                print(f"  {status_icon} {log.spider_name} - {log.start_time} - {log.status}")
                if log.items_saved > 0:
                    print(f"    采集数据: {log.items_saved}")
        
        # 错误摘要
        errors = crawl_log_dao.get_error_summary(days=7)
        if errors:
            print_section("错误摘要")
            for error in errors[:5]:
                print(f"  {error['error_message']}: {error['count']}次")
        
        return True
        
    except Exception as e:
        print(f"❌ 爬取日志检查失败: {e}")
        return False


def check_system_health():
    """检查系统健康状况"""
    print_header("系统健康状况")
    
    try:
        # 检查目录
        print_section("目录检查")
        directories = ['logs', 'cache', 'exports', 'backups', 'config']
        for directory in directories:
            dir_path = project_root / directory
            exists = dir_path.exists()
            status = "✅ 存在" if exists else "❌ 不存在"
            print(f"  {directory}: {status}")
        
        # 检查配置文件
        print_section("配置文件检查")
        config_files = [
            'config/settings.yaml',
            '.env',
            'requirements.txt'
        ]
        for config_file in config_files:
            file_path = project_root / config_file
            exists = file_path.exists()
            status = "✅ 存在" if exists else "❌ 不存在"
            print(f"  {config_file}: {status}")
        
        # 检查日志文件
        print_section("日志文件检查")
        log_dir = project_root / 'logs'
        if log_dir.exists():
            log_files = list(log_dir.glob('*.log'))
            print(f"日志文件数量: {len(log_files)}")
            
            if log_files:
                # 最新日志文件
                latest_log = max(log_files, key=lambda f: f.stat().st_mtime)
                size = latest_log.stat().st_size
                print(f"最新日志: {latest_log.name} ({size} bytes)")
        else:
            print("日志目录不存在")
        
        # 检查磁盘空间
        print_section("磁盘空间检查")
        import shutil
        total, used, free = shutil.disk_usage(project_root)
        
        print(f"总空间: {total // (1024**3)} GB")
        print(f"已使用: {used // (1024**3)} GB")
        print(f"可用空间: {free // (1024**3)} GB")
        print(f"使用率: {used/total:.1%}")
        
        if used/total > 0.9:
            print("⚠️ 磁盘空间不足")
        
        return True
        
    except Exception as e:
        print(f"❌ 系统健康检查失败: {e}")
        return False


def generate_summary():
    """生成状态摘要"""
    print_header("状态摘要")
    
    # 检查各个组件
    components = {
        "数据库": check_database_status,
        "数据统计": check_data_statistics,
        "爬取日志": check_crawl_logs,
        "系统健康": check_system_health
    }
    
    results = {}
    for name, check_func in components.items():
        try:
            results[name] = check_func()
        except:
            results[name] = False
    
    print("\n组件状态:")
    for name, status in results.items():
        icon = "✅" if status else "❌"
        print(f"  {icon} {name}")
    
    # 总体状态
    all_good = all(results.values())
    overall_status = "✅ 良好" if all_good else "⚠️ 需要注意"
    print(f"\n总体状态: {overall_status}")
    
    return all_good


def main():
    """主函数"""
    print("🔍 惠农网价格数据爬虫项目状态检查")
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查项目信息
    check_project_info()
    
    # 生成状态摘要
    all_good = generate_summary()
    
    print(f"\n{'='*60}")
    print("状态检查完成")
    
    return 0 if all_good else 1


if __name__ == "__main__":
    sys.exit(main())
