#!/usr/bin/env python3
"""
项目初始化脚本

用于初始化项目环境，包括：
1. 创建必要的目录
2. 复制配置文件
3. 初始化数据库
4. 安装依赖
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def create_directories():
    """创建必要的目录"""
    directories = [
        'logs',
        'cache',
        'exports',
        'backups',
        'config'
    ]
    
    for directory in directories:
        dir_path = project_root / directory
        dir_path.mkdir(exist_ok=True)
        print(f"✓ 创建目录: {directory}")


def copy_config_files():
    """复制配置文件"""
    config_files = [
        ('config/settings.yaml.example', 'config/settings.yaml'),
        ('.env.example', '.env')
    ]
    
    for src, dst in config_files:
        src_path = project_root / src
        dst_path = project_root / dst
        
        if src_path.exists() and not dst_path.exists():
            shutil.copy2(src_path, dst_path)
            print(f"✓ 复制配置文件: {src} -> {dst}")
        elif dst_path.exists():
            print(f"⚠ 配置文件已存在: {dst}")
        else:
            print(f"✗ 源文件不存在: {src}")


def install_dependencies():
    """安装Python依赖"""
    requirements_file = project_root / 'requirements.txt'
    
    if not requirements_file.exists():
        print("✗ requirements.txt 文件不存在")
        return False
    
    try:
        print("📦 安装Python依赖...")
        subprocess.run([
            sys.executable, '-m', 'pip', 'install', '-r', str(requirements_file)
        ], check=True)
        print("✓ Python依赖安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Python依赖安装失败: {e}")
        return False


def init_database():
    """初始化数据库"""
    sql_file = project_root / 'database' / 'init_database.sql'
    
    if not sql_file.exists():
        print("✗ 数据库初始化脚本不存在")
        return False
    
    print("🗄️ 初始化数据库...")
    print("请手动执行以下命令来初始化数据库:")
    print(f"mysql -u root -p < {sql_file}")
    print("或者使用MySQL客户端执行该SQL文件")
    
    return True


def check_environment():
    """检查环境"""
    print("🔍 检查环境...")
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version < (3, 8):
        print(f"✗ Python版本过低: {python_version.major}.{python_version.minor}")
        print("  需要Python 3.8或更高版本")
        return False
    else:
        print(f"✓ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 检查pip
    try:
        import pip
        print(f"✓ pip可用")
    except ImportError:
        print("✗ pip不可用")
        return False
    
    return True


def show_next_steps():
    """显示后续步骤"""
    print("\n🎉 项目初始化完成！")
    print("\n📋 后续步骤:")
    print("1. 编辑 .env 文件，设置数据库密码等敏感信息")
    print("2. 编辑 config/settings.yaml 文件，调整爬虫配置")
    print("3. 执行数据库初始化脚本: mysql -u root -p < database/init_database.sql")
    print("4. 运行测试: python main.py --test")
    print("5. 运行完整爬虫: python main.py")
    
    print("\n📚 更多信息请查看 README.md 文件")


def main():
    """主函数"""
    print("🚀 开始初始化惠农网价格数据爬虫项目...")
    print(f"📁 项目根目录: {project_root}")
    
    # 检查环境
    if not check_environment():
        print("❌ 环境检查失败，请解决上述问题后重试")
        return 1
    
    # 创建目录
    print("\n📁 创建项目目录...")
    create_directories()
    
    # 复制配置文件
    print("\n⚙️ 设置配置文件...")
    copy_config_files()
    
    # 安装依赖
    print("\n📦 安装依赖...")
    if not install_dependencies():
        print("⚠️ 依赖安装失败，请手动安装")
    
    # 初始化数据库
    print("\n🗄️ 数据库设置...")
    init_database()
    
    # 显示后续步骤
    show_next_steps()
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
