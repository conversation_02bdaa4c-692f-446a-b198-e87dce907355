#!/usr/bin/env python3
"""
简单测试脚本

验证项目的核心功能是否正常工作
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_database_connection():
    """测试数据库连接"""
    try:
        from huinong_spider.utils.database import get_db_manager
        
        print("🔍 测试数据库连接...")
        db = get_db_manager()
        
        with db.get_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                if result:
                    print("✅ 数据库连接正常")
                    return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def test_models():
    """测试数据模型"""
    try:
        from huinong_spider.models import CategoryDAO, RegionDAO
        
        print("🔍 测试数据模型...")
        
        # 测试分类DAO
        category_dao = CategoryDAO()
        categories = category_dao.find_active_categories()
        print(f"✅ 找到 {len(categories)} 个启用的分类")
        
        # 测试地区DAO
        region_dao = RegionDAO()
        regions = region_dao.find_provinces()
        print(f"✅ 找到 {len(regions)} 个省级地区")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据模型测试失败: {e}")
        return False

def test_config():
    """测试配置加载"""
    try:
        from huinong_spider.config.settings import settings
        
        print("🔍 测试配置加载...")
        
        # 测试数据库配置
        db_config = settings.database
        print(f"✅ 数据库配置加载成功: {db_config.get('host', 'unknown')}")
        
        # 测试爬虫配置
        spider_config = settings.spider
        print(f"✅ 爬虫配置加载成功: 延迟={spider_config.get('download_delay', 'unknown')}秒")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置加载测试失败: {e}")
        return False

def test_helpers():
    """测试辅助函数"""
    try:
        from huinong_spider.utils.helpers import clean_price, standardize_region, parse_date
        
        print("🔍 测试辅助函数...")
        
        # 测试价格清洗
        price = clean_price("￥12.50元/斤")
        if price == 12.50:
            print("✅ 价格清洗功能正常")
        else:
            print(f"⚠️ 价格清洗结果异常: {price}")
        
        # 测试地区标准化
        region = standardize_region("山东省")
        if region == "山东":
            print("✅ 地区标准化功能正常")
        else:
            print(f"⚠️ 地区标准化结果异常: {region}")
        
        # 测试日期解析
        date_obj = parse_date("2025-01-15")
        if date_obj:
            print("✅ 日期解析功能正常")
        else:
            print("⚠️ 日期解析功能异常")
        
        return True
        
    except Exception as e:
        print(f"❌ 辅助函数测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始简单功能测试...\n")
    
    tests = [
        ("数据库连接", test_database_connection),
        ("配置加载", test_config),
        ("数据模型", test_models),
        ("辅助函数", test_helpers)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 发生异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    print(f"\n{'='*50}")
    print("测试结果汇总")
    print('='*50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("\n🎉 所有测试通过！系统配置正常。")
        return True
    else:
        print(f"\n⚠️ {len(results) - passed} 个测试失败，请检查配置。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
