#!/usr/bin/env python3
"""
网页诊断脚本

分析惠农网页面结构，找出数据解析问题
"""

import sys
import requests
from pathlib import Path
from bs4 import BeautifulSoup

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def analyze_webpage(url):
    """分析网页内容"""
    print(f"🔍 分析网页: {url}")
    
    try:
        # 发送请求
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Connection': 'keep-alive'
        }
        
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        
        print(f"✅ 请求成功，状态码: {response.status_code}")
        print(f"📄 响应长度: {len(response.text)} 字符")
        
        # 解析HTML
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 分析页面结构
        print("\n📊 页面结构分析:")
        
        # 查找标题
        title = soup.find('title')
        if title:
            print(f"📝 页面标题: {title.get_text().strip()}")
        
        # 查找各种可能的表格
        tables = soup.find_all('table')
        print(f"📋 找到 {len(tables)} 个表格")
        
        for i, table in enumerate(tables):
            print(f"  表格 {i+1}:")
            print(f"    - class: {table.get('class', '无')}")
            print(f"    - id: {table.get('id', '无')}")
            rows = table.find_all('tr')
            print(f"    - 行数: {len(rows)}")
            if rows:
                cols = rows[0].find_all(['td', 'th'])
                print(f"    - 列数: {len(cols)}")
        
        # 查找div容器
        divs_with_class = soup.find_all('div', class_=True)
        print(f"\n📦 找到 {len(divs_with_class)} 个带class的div")
        
        # 查找可能包含数据的div
        data_divs = []
        for div in divs_with_class:
            class_names = ' '.join(div.get('class', []))
            if any(keyword in class_names.lower() for keyword in ['list', 'data', 'price', 'table', 'content']):
                data_divs.append((div, class_names))
        
        print(f"🎯 找到 {len(data_divs)} 个可能包含数据的div:")
        for div, class_names in data_divs[:5]:  # 只显示前5个
            print(f"  - class: {class_names}")
            text_preview = div.get_text().strip()[:100]
            print(f"    内容预览: {text_preview}...")
        
        # 查找列表项
        lis = soup.find_all('li')
        print(f"\n📝 找到 {len(lis)} 个列表项")
        
        # 查找可能的价格信息
        price_patterns = ['价格', '元', '斤', '公斤', '￥', '¥']
        price_elements = []
        
        for pattern in price_patterns:
            elements = soup.find_all(text=lambda text: text and pattern in text)
            price_elements.extend(elements)
        
        print(f"💰 找到 {len(price_elements)} 个可能包含价格的文本")
        
        # 保存HTML到文件用于进一步分析
        output_file = project_root / "logs" / "webpage_sample.html"
        output_file.parent.mkdir(exist_ok=True)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(response.text)
        
        print(f"\n💾 HTML内容已保存到: {output_file}")
        
        # 分析特定的表格结构
        print("\n🔍 详细表格分析:")
        target_table = soup.find('table', class_='table')
        if target_table:
            print("✅ 找到class='table'的表格")
            analyze_table_structure(target_table)
        else:
            print("❌ 未找到class='table'的表格")
            
            # 尝试其他可能的表格选择器
            alternative_selectors = [
                'table',
                '.price-table',
                '.data-table',
                '.list-table',
                '[class*="table"]',
                '[class*="list"]'
            ]
            
            for selector in alternative_selectors:
                elements = soup.select(selector)
                if elements:
                    print(f"🎯 找到选择器 '{selector}' 匹配的 {len(elements)} 个元素")
                    if selector == 'table' and elements:
                        analyze_table_structure(elements[0])
                        break
        
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False

def analyze_table_structure(table):
    """分析表格结构"""
    print("📋 表格结构详细分析:")
    
    # 查找表头
    thead = table.find('thead')
    if thead:
        print("✅ 找到表头 (thead)")
        headers = thead.find_all(['th', 'td'])
        print(f"  表头列数: {len(headers)}")
        for i, header in enumerate(headers):
            print(f"    列 {i+1}: {header.get_text().strip()}")
    
    # 查找表体
    tbody = table.find('tbody')
    if tbody:
        print("✅ 找到表体 (tbody)")
        rows = tbody.find_all('tr')
        print(f"  数据行数: {len(rows)}")
        
        if rows:
            # 分析第一行数据
            first_row = rows[0]
            cells = first_row.find_all(['td', 'th'])
            print(f"  第一行列数: {len(cells)}")
            for i, cell in enumerate(cells):
                print(f"    列 {i+1}: {cell.get_text().strip()}")
    else:
        print("❌ 未找到表体 (tbody)")
        # 直接查找tr
        rows = table.find_all('tr')
        print(f"  直接找到 {len(rows)} 行")
        if rows:
            first_row = rows[0]
            cells = first_row.find_all(['td', 'th'])
            print(f"  第一行列数: {len(cells)}")

def main():
    """主函数"""
    print("🔍 惠农网页面结构诊断")
    print("="*50)
    
    # 测试几个不同的URL
    test_urls = [
        "https://www.cnhnb.com/hangqing/cdlist-2003191-0-15-0-0-1/",  # 水果-山东
        "https://www.cnhnb.com/hangqing/cdlist-2003192-0-15-0-0-1/",  # 蔬菜-山东
        "https://www.cnhnb.com/hangqing/sgzw/",  # 水果分类页
        "https://www.cnhnb.com/hangqing/"  # 主页
    ]
    
    for i, url in enumerate(test_urls):
        print(f"\n{'='*60}")
        print(f"测试 {i+1}/{len(test_urls)}: {url}")
        print('='*60)
        
        success = analyze_webpage(url)
        if not success:
            print(f"⚠️ URL {url} 分析失败，跳过")
            continue
        
        # 只分析第一个成功的URL，避免过多输出
        if success:
            break
    
    print(f"\n{'='*60}")
    print("诊断完成")
    print('='*60)

if __name__ == "__main__":
    main()
