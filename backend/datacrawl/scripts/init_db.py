#!/usr/bin/env python3
"""
数据库初始化脚本

使用Python连接MySQL数据库并执行初始化SQL脚本
"""

import os
import sys
import pymysql
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def connect_mysql():
    """连接MySQL数据库"""
    try:
        # 从环境变量读取数据库配置
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='fan13965711955',
            charset='utf8mb4',
            autocommit=True
        )
        print("✅ MySQL连接成功")
        return connection
    except Exception as e:
        print(f"❌ MySQL连接失败: {e}")
        return None

def execute_sql_file(connection, sql_file_path):
    """执行SQL文件"""
    try:
        with open(sql_file_path, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # 分割SQL语句（以分号分隔）
        sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
        
        cursor = connection.cursor()
        
        for i, statement in enumerate(sql_statements):
            try:
                print(f"执行SQL语句 {i+1}/{len(sql_statements)}")
                cursor.execute(statement)
                print(f"✅ 语句执行成功")
            except Exception as e:
                print(f"⚠️ 语句执行警告: {e}")
                # 继续执行下一条语句
                continue
        
        cursor.close()
        print("✅ SQL文件执行完成")
        return True
        
    except Exception as e:
        print(f"❌ SQL文件执行失败: {e}")
        return False

def verify_database():
    """验证数据库和表是否创建成功"""
    try:
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='fan13965711955',
            database='huinong_spider',
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        # 检查表是否存在
        tables = ['categories', 'regions', 'products', 'price_data', 'crawl_logs']
        
        print("\n📊 验证数据库表:")
        for table in tables:
            cursor.execute(f"SHOW TABLES LIKE '{table}'")
            result = cursor.fetchone()
            if result:
                # 检查表中的数据量
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"✅ {table}: 存在 ({count} 条记录)")
            else:
                print(f"❌ {table}: 不存在")
        
        cursor.close()
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始数据库初始化...")
    
    # 连接MySQL
    connection = connect_mysql()
    if not connection:
        return False
    
    # 执行初始化SQL脚本
    sql_file = project_root / "database" / "init_database.sql"
    if not sql_file.exists():
        print(f"❌ SQL文件不存在: {sql_file}")
        return False
    
    print(f"📄 执行SQL文件: {sql_file}")
    success = execute_sql_file(connection, sql_file)
    
    connection.close()
    
    if success:
        print("\n🔍 验证数据库初始化结果...")
        verify_database()
        print("\n🎉 数据库初始化完成！")
        return True
    else:
        print("\n❌ 数据库初始化失败！")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
