#!/usr/bin/env python3
"""
全量数据爬取脚本

实现无人值守的全量数据爬取，包含智能反爬虫策略和自动恢复机制
"""

import sys
import time
import signal
from pathlib import Path
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from huinong_spider.spiders.huinong_spider import HuinongSpider
from huinong_spider.models import CategoryDAO, RegionDAO
from huinong_spider.utils.logger import get_logger
from huinong_spider.utils.database import get_db_manager


class FullCrawlManager:
    """全量爬取管理器"""
    
    def __init__(self):
        self.logger = get_logger("full_crawl")
        self.spider = HuinongSpider()
        self.category_dao = CategoryDAO()
        self.region_dao = RegionDAO()
        
        # 爬取状态
        self.is_running = True
        self.current_task = None
        self.completed_tasks = []
        self.failed_tasks = []
        self.start_time = None
        
        # 配置
        self.max_pages_per_combination = 3  # 每个分类-地区组合的最大页数
        self.pause_between_combinations = 120  # 组合间暂停时间（2分钟）
        self.long_pause_threshold = 3  # 连续失败阈值
        self.long_pause_duration = 600  # 长暂停时间（10分钟）
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        self.logger.info(f"收到信号 {signum}，准备优雅退出...")
        self.is_running = False
    
    def get_all_tasks(self) -> List[Dict[str, Any]]:
        """获取所有爬取任务"""
        tasks = []
        
        # 获取所有启用的分类和地区
        categories = self.category_dao.find_active_categories()
        regions = self.region_dao.find_active_regions()
        
        self.logger.info(f"找到 {len(categories)} 个分类，{len(regions)} 个地区")
        
        # 生成所有分类-地区组合
        for category in categories:
            for region in regions:
                task = {
                    'category': category,
                    'region': region,
                    'category_name': category.name,
                    'region_name': region.name,
                    'max_pages': self.max_pages_per_combination,
                    'status': 'pending',
                    'attempts': 0,
                    'last_attempt': None,
                    'error_message': None
                }
                tasks.append(task)
        
        self.logger.info(f"生成 {len(tasks)} 个爬取任务")
        return tasks
    
    def execute_task(self, task: Dict[str, Any]) -> bool:
        """执行单个爬取任务"""
        category = task['category']
        region = task['region']
        max_pages = task['max_pages']
        
        self.current_task = task
        task['attempts'] += 1
        task['last_attempt'] = datetime.now()
        task['status'] = 'running'
        
        self.logger.info(f"开始执行任务: {category.name} - {region.name} (最大 {max_pages} 页)")
        
        try:
            # 执行爬取
            result = self.spider.crawl_category_region(category, region, max_pages)
            
            if result and result > 0:
                task['status'] = 'completed'
                task['data_count'] = result
                self.logger.info(f"任务完成: {category.name} - {region.name}，采集 {result} 条数据")
                return True
            else:
                task['status'] = 'no_data'
                task['data_count'] = 0
                self.logger.warning(f"任务完成但无数据: {category.name} - {region.name}")
                return True  # 无数据也算成功
                
        except Exception as e:
            task['status'] = 'failed'
            task['error_message'] = str(e)
            self.logger.error(f"任务失败: {category.name} - {region.name}，错误: {e}")
            return False
        
        finally:
            self.current_task = None
    
    def should_pause_long(self, recent_failures: int) -> bool:
        """判断是否需要长时间暂停"""
        return recent_failures >= self.long_pause_threshold
    
    def get_recent_failure_count(self, tasks: List[Dict[str, Any]], window_minutes: int = 30) -> int:
        """获取最近的失败次数"""
        cutoff_time = datetime.now() - timedelta(minutes=window_minutes)
        
        recent_failures = 0
        for task in tasks:
            if (task['status'] == 'failed' and 
                task['last_attempt'] and 
                task['last_attempt'] > cutoff_time):
                recent_failures += 1
        
        return recent_failures
    
    def run_full_crawl(self):
        """运行全量爬取"""
        self.start_time = datetime.now()
        self.logger.info("开始全量数据爬取")
        
        # 获取所有任务
        tasks = self.get_all_tasks()
        total_tasks = len(tasks)
        
        # 执行任务
        for i, task in enumerate(tasks):
            if not self.is_running:
                self.logger.info("收到停止信号，退出爬取")
                break
            
            # 显示进度
            progress = (i + 1) / total_tasks * 100
            self.logger.info(f"进度: {i+1}/{total_tasks} ({progress:.1f}%)")
            
            # 检查是否需要长时间暂停
            recent_failures = self.get_recent_failure_count(tasks)
            if self.should_pause_long(recent_failures):
                self.logger.warning(f"最近 {recent_failures} 次失败，暂停 {self.long_pause_duration} 秒")
                time.sleep(self.long_pause_duration)
                
                # 重置反爬虫状态
                self.spider.anti_spider.reset_after_long_pause()
            
            # 执行任务
            success = self.execute_task(task)
            
            if success:
                self.completed_tasks.append(task)
            else:
                self.failed_tasks.append(task)
            
            # 任务间暂停
            if i < total_tasks - 1:  # 最后一个任务不需要暂停
                self.logger.debug(f"任务间暂停 {self.pause_between_combinations} 秒")
                time.sleep(self.pause_between_combinations)
        
        # 生成最终报告
        self.generate_final_report(tasks)
    
    def generate_final_report(self, tasks: List[Dict[str, Any]]):
        """生成最终报告"""
        end_time = datetime.now()
        total_time = end_time - self.start_time
        
        # 统计信息
        total_tasks = len(tasks)
        completed_tasks = len([t for t in tasks if t['status'] == 'completed'])
        no_data_tasks = len([t for t in tasks if t['status'] == 'no_data'])
        failed_tasks = len([t for t in tasks if t['status'] == 'failed'])
        pending_tasks = len([t for t in tasks if t['status'] == 'pending'])
        
        total_data = sum(t.get('data_count', 0) for t in tasks if t.get('data_count'))
        
        # 反爬虫统计
        anti_spider_stats = self.spider.anti_spider.get_statistics()
        
        self.logger.info("="*60)
        self.logger.info("全量爬取完成报告")
        self.logger.info("="*60)
        self.logger.info(f"开始时间: {self.start_time}")
        self.logger.info(f"结束时间: {end_time}")
        self.logger.info(f"总耗时: {total_time}")
        self.logger.info("")
        self.logger.info("任务统计:")
        self.logger.info(f"  总任务数: {total_tasks}")
        self.logger.info(f"  已完成: {completed_tasks}")
        self.logger.info(f"  无数据: {no_data_tasks}")
        self.logger.info(f"  失败: {failed_tasks}")
        self.logger.info(f"  未执行: {pending_tasks}")
        self.logger.info(f"  成功率: {(completed_tasks + no_data_tasks) / total_tasks:.2%}")
        self.logger.info("")
        self.logger.info("数据统计:")
        self.logger.info(f"  总采集数据: {total_data} 条")
        self.logger.info(f"  平均每任务: {total_data / max(completed_tasks, 1):.1f} 条")
        self.logger.info("")
        self.logger.info("反爬虫统计:")
        self.logger.info(f"  总请求数: {anti_spider_stats['total_requests']}")
        self.logger.info(f"  成功率: {anti_spider_stats['success_rate']:.2%}")
        self.logger.info(f"  最终延迟: {anti_spider_stats['current_delay']:.2f}秒")
        self.logger.info(f"  连续成功: {anti_spider_stats['consecutive_successes']}")
        self.logger.info(f"  连续错误: {anti_spider_stats['consecutive_errors']}")
        
        # 失败任务详情
        if failed_tasks > 0:
            self.logger.info("")
            self.logger.info("失败任务详情:")
            for task in tasks:
                if task['status'] == 'failed':
                    self.logger.info(f"  {task['category_name']} - {task['region_name']}: {task['error_message']}")
        
        self.logger.info("="*60)


def main():
    """主函数"""
    manager = FullCrawlManager()
    
    try:
        manager.run_full_crawl()
        return 0
    except KeyboardInterrupt:
        manager.logger.info("用户中断爬取")
        return 1
    except Exception as e:
        manager.logger.error(f"爬取过程中发生错误: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
