#!/usr/bin/env python3
"""
反爬虫机制测试脚本

分析惠农网的反爬虫策略，找出最佳的请求间隔和策略
"""

import sys
import time
import requests
import random
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from huinong_spider.utils.logger import get_logger

class AntiSpiderTester:
    """反爬虫测试器"""
    
    def __init__(self):
        self.logger = get_logger("anti_spider_test")
        self.session = requests.Session()
        self.base_url = "https://www.cnhnb.com/hangqing/"
        
        # 测试URL列表
        self.test_urls = [
            "https://www.cnhnb.com/hangqing/cdlist-2003191-0-15-0-0-1/",  # 水果-山东
            "https://www.cnhnb.com/hangqing/cdlist-2003192-0-15-0-0-1/",  # 蔬菜-山东
            "https://www.cnhnb.com/hangqing/cdlist-2003193-0-15-0-0-1/",  # 禽畜肉蛋-山东
            "https://www.cnhnb.com/hangqing/cdlist-2003194-0-15-0-0-1/",  # 水产-山东
        ]
        
        # User-Agent池
        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        ]
        
        # 测试结果
        self.test_results = []
    
    def get_random_headers(self):
        """获取随机请求头"""
        return {
            'User-Agent': random.choice(self.user_agents),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
    
    def test_request_interval(self, interval_seconds, test_count=10):
        """测试特定间隔的请求成功率"""
        self.logger.info(f"测试请求间隔: {interval_seconds}秒, 测试次数: {test_count}")
        
        success_count = 0
        error_503_count = 0
        other_error_count = 0
        response_times = []
        
        start_time = datetime.now()
        
        for i in range(test_count):
            url = random.choice(self.test_urls)
            headers = self.get_random_headers()
            
            try:
                request_start = time.time()
                response = self.session.get(url, headers=headers, timeout=30)
                request_time = time.time() - request_start
                response_times.append(request_time)
                
                if response.status_code == 200:
                    success_count += 1
                    self.logger.debug(f"请求 {i+1}/{test_count} 成功: {response.status_code}, 响应时间: {request_time:.2f}s")
                elif response.status_code == 503:
                    error_503_count += 1
                    self.logger.warning(f"请求 {i+1}/{test_count} 遇到503错误")
                else:
                    other_error_count += 1
                    self.logger.warning(f"请求 {i+1}/{test_count} 其他错误: {response.status_code}")
                    
            except Exception as e:
                other_error_count += 1
                self.logger.error(f"请求 {i+1}/{test_count} 异常: {e}")
            
            # 等待间隔时间
            if i < test_count - 1:  # 最后一次请求不需要等待
                time.sleep(interval_seconds)
        
        end_time = datetime.now()
        total_time = (end_time - start_time).total_seconds()
        
        # 计算统计信息
        success_rate = success_count / test_count
        avg_response_time = sum(response_times) / len(response_times) if response_times else 0
        
        result = {
            'interval': interval_seconds,
            'test_count': test_count,
            'success_count': success_count,
            'error_503_count': error_503_count,
            'other_error_count': other_error_count,
            'success_rate': success_rate,
            'avg_response_time': avg_response_time,
            'total_time': total_time
        }
        
        self.test_results.append(result)
        
        self.logger.info(f"间隔 {interval_seconds}s 测试结果:")
        self.logger.info(f"  成功率: {success_rate:.2%}")
        self.logger.info(f"  503错误: {error_503_count}")
        self.logger.info(f"  其他错误: {other_error_count}")
        self.logger.info(f"  平均响应时间: {avg_response_time:.2f}s")
        self.logger.info(f"  总耗时: {total_time:.2f}s")
        
        return result
    
    def test_continuous_requests(self, max_requests=20):
        """测试连续请求的阈值"""
        self.logger.info(f"测试连续请求阈值，最大请求数: {max_requests}")
        
        url = self.test_urls[0]
        headers = self.get_random_headers()
        
        for i in range(max_requests):
            try:
                response = self.session.get(url, headers=headers, timeout=30)
                
                if response.status_code == 200:
                    self.logger.debug(f"连续请求 {i+1} 成功")
                elif response.status_code == 503:
                    self.logger.warning(f"连续请求 {i+1} 触发503错误，阈值为: {i+1}")
                    return i + 1
                else:
                    self.logger.warning(f"连续请求 {i+1} 其他错误: {response.status_code}")
                    
            except Exception as e:
                self.logger.error(f"连续请求 {i+1} 异常: {e}")
                return i + 1
            
            # 短暂间隔
            time.sleep(0.5)
        
        self.logger.info(f"连续 {max_requests} 次请求都成功，未触发反爬虫")
        return max_requests
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        self.logger.info("开始反爬虫机制综合测试")
        
        # 测试不同的请求间隔
        intervals = [1, 2, 3, 5, 8, 10, 15, 20, 30]
        
        for interval in intervals:
            self.test_request_interval(interval, test_count=8)
            
            # 如果成功率达到100%，可以考虑减少后续测试
            if self.test_results[-1]['success_rate'] >= 1.0:
                self.logger.info(f"间隔 {interval}s 达到100%成功率")
            
            # 测试间隔等待
            time.sleep(10)
        
        # 测试连续请求阈值
        threshold = self.test_continuous_requests()
        
        # 分析结果
        self.analyze_results(threshold)
    
    def analyze_results(self, continuous_threshold):
        """分析测试结果"""
        self.logger.info("分析测试结果...")
        
        # 找出最佳间隔
        best_result = None
        for result in self.test_results:
            if result['success_rate'] >= 0.9:  # 90%以上成功率
                if best_result is None or result['interval'] < best_result['interval']:
                    best_result = result
        
        if best_result:
            recommended_interval = best_result['interval']
            self.logger.info(f"推荐的最小安全间隔: {recommended_interval}秒")
        else:
            # 如果没有找到90%成功率的间隔，选择成功率最高的
            best_result = max(self.test_results, key=lambda x: x['success_rate'])
            recommended_interval = best_result['interval'] * 1.5  # 增加50%作为安全边际
            self.logger.warning(f"未找到90%成功率的间隔，推荐使用: {recommended_interval}秒")
        
        # 生成配置建议
        config_suggestions = {
            'download_delay': recommended_interval,
            'retry_delay_base': recommended_interval * 2,
            'max_retry_delay': 300,  # 5分钟
            'continuous_request_threshold': continuous_threshold,
            'backoff_factor': 2.0
        }
        
        self.logger.info("配置建议:")
        for key, value in config_suggestions.items():
            self.logger.info(f"  {key}: {value}")
        
        return config_suggestions

def main():
    """主函数"""
    tester = AntiSpiderTester()
    
    try:
        suggestions = tester.run_comprehensive_test()
        
        print("\n" + "="*60)
        print("反爬虫测试完成")
        print("="*60)
        print("配置建议:")
        for key, value in suggestions.items():
            print(f"  {key}: {value}")
        
        return suggestions
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        return None
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        return None

if __name__ == "__main__":
    main()
