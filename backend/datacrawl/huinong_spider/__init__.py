"""
惠农网价格数据爬虫项目

一个专业的农产品价格数据采集系统，用于采集惠农网的农产品价格行情数据。

Author: Your Name
Version: 1.0.0
Date: 2025-07-24
"""

__version__ = "1.0.0"
__author__ = "Your Name"
__email__ = "<EMAIL>"
__description__ = "惠农网价格数据爬虫系统"

# 导入主要模块
from .config import settings
from .utils.logger import get_logger
from .utils.database import DatabaseManager

# 创建全局日志器
logger = get_logger(__name__)

# 项目信息
PROJECT_INFO = {
    "name": "huinong_spider",
    "version": __version__,
    "author": __author__,
    "description": __description__,
    "target_site": "https://www.cnhnb.com/hangqing/",
    "supported_categories": [
        "水果", "蔬菜", "禽畜肉蛋", "水产", "农副加工", 
        "粮油米面", "种子种苗", "苗木花草", "农资农机", 
        "中药材", "日用百货", "土地流转", "包装", 
        "农业服务", "农工服务", "租赁服务", "农技服务", "经济作物"
    ]
}

def get_project_info():
    """获取项目信息"""
    return PROJECT_INFO

def initialize():
    """初始化项目"""
    logger.info(f"初始化 {PROJECT_INFO['name']} v{PROJECT_INFO['version']}")
    logger.info(f"目标网站: {PROJECT_INFO['target_site']}")
    logger.info(f"支持分类数: {len(PROJECT_INFO['supported_categories'])}")
