"""
价格数据模型

定义价格数据的数据模型和数据访问对象。
"""

from dataclasses import dataclass
from datetime import date, datetime
from typing import Optional, List, Dict, Any, Tuple
from decimal import Decimal
from .base import BaseModel, BaseDAO


@dataclass
class PriceData(BaseModel):
    """价格数据模型"""
    
    product_id: int = 0
    region_id: int = 0
    price: float = 0.0
    unit: str = "斤"
    price_change: str = ""
    price_trend: Optional[int] = None  # 1上涨, 0持平, -1下跌
    source_url: str = ""
    source_id: str = ""
    data_date: Optional[date] = None
    crawl_time: Optional[datetime] = None
    
    def __post_init__(self):
        """初始化后处理"""
        if self.product_id <= 0:
            raise ValueError("产品ID必须大于0")
        if self.region_id <= 0:
            raise ValueError("地区ID必须大于0")
        if self.price < 0:
            raise ValueError("价格不能为负数")
        if self.data_date is None:
            self.data_date = date.today()


class PriceDataDAO(BaseDAO):
    """价格数据访问对象"""
    
    @property
    def table_name(self) -> str:
        return "price_data"
    
    @property
    def model_class(self):
        return PriceData
    
    def find_by_product_and_region(self, product_id: int, region_id: int, 
                                  start_date: Optional[date] = None,
                                  end_date: Optional[date] = None) -> List[PriceData]:
        """
        根据产品和地区查找价格数据
        
        Args:
            product_id: 产品ID
            region_id: 地区ID
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            价格数据列表
        """
        where_clause = "product_id = %s AND region_id = %s"
        params = [product_id, region_id]
        
        if start_date:
            where_clause += " AND data_date >= %s"
            params.append(start_date)
        
        if end_date:
            where_clause += " AND data_date <= %s"
            params.append(end_date)
        
        where_clause += " ORDER BY data_date DESC"
        
        return self.find_by_condition(where_clause, tuple(params))
    
    def find_latest_by_product(self, product_id: int, limit: int = 10) -> List[PriceData]:
        """
        查找产品的最新价格数据
        
        Args:
            product_id: 产品ID
            limit: 限制数量
            
        Returns:
            最新价格数据列表
        """
        sql = f"""
        SELECT *
        FROM {self.table_name}
        WHERE product_id = %s
        ORDER BY data_date DESC, crawl_time DESC
        LIMIT %s
        """
        
        results = self.db.execute_query(sql, (product_id, limit))
        return [PriceData.from_dict(row) for row in results]
    
    def find_latest_by_region(self, region_id: int, limit: int = 10) -> List[PriceData]:
        """
        查找地区的最新价格数据
        
        Args:
            region_id: 地区ID
            limit: 限制数量
            
        Returns:
            最新价格数据列表
        """
        sql = f"""
        SELECT *
        FROM {self.table_name}
        WHERE region_id = %s
        ORDER BY data_date DESC, crawl_time DESC
        LIMIT %s
        """
        
        results = self.db.execute_query(sql, (region_id, limit))
        return [PriceData.from_dict(row) for row in results]
    
    def find_by_date_range(self, start_date: date, end_date: date) -> List[PriceData]:
        """
        根据日期范围查找价格数据
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            价格数据列表
        """
        return self.find_by_condition(
            "data_date BETWEEN %s AND %s ORDER BY data_date DESC",
            (start_date, end_date)
        )
    
    def get_price_statistics(self, product_id: int, region_id: Optional[int] = None,
                           days: int = 30) -> Dict[str, Any]:
        """
        获取价格统计信息
        
        Args:
            product_id: 产品ID
            region_id: 地区ID（可选）
            days: 统计天数
            
        Returns:
            价格统计信息
        """
        where_clause = "product_id = %s AND data_date >= DATE_SUB(CURDATE(), INTERVAL %s DAY)"
        params = [product_id, days]
        
        if region_id:
            where_clause += " AND region_id = %s"
            params.append(region_id)
        
        sql = f"""
        SELECT 
            COUNT(*) as count,
            AVG(price) as avg_price,
            MIN(price) as min_price,
            MAX(price) as max_price,
            STDDEV(price) as std_price
        FROM {self.table_name}
        WHERE {where_clause}
        """
        
        result = self.db.execute_query(sql, tuple(params))
        return result[0] if result else {}
    
    def get_price_trend(self, product_id: int, region_id: int, days: int = 30) -> List[Dict[str, Any]]:
        """
        获取价格趋势数据
        
        Args:
            product_id: 产品ID
            region_id: 地区ID
            days: 天数
            
        Returns:
            价格趋势数据
        """
        sql = f"""
        SELECT data_date, AVG(price) as avg_price, COUNT(*) as count
        FROM {self.table_name}
        WHERE product_id = %s AND region_id = %s 
        AND data_date >= DATE_SUB(CURDATE(), INTERVAL %s DAY)
        GROUP BY data_date
        ORDER BY data_date
        """
        
        return self.db.execute_query(sql, (product_id, region_id, days))
    
    def find_price_changes(self, threshold: float = 10.0, days: int = 7) -> List[Dict[str, Any]]:
        """
        查找价格变化较大的数据
        
        Args:
            threshold: 变化阈值（百分比）
            days: 天数
            
        Returns:
            价格变化数据
        """
        sql = f"""
        SELECT 
            p1.product_id,
            p1.region_id,
            p1.price as current_price,
            p2.price as previous_price,
            ((p1.price - p2.price) / p2.price * 100) as change_percent,
            p1.data_date
        FROM {self.table_name} p1
        JOIN {self.table_name} p2 ON p1.product_id = p2.product_id 
        AND p1.region_id = p2.region_id
        WHERE p1.data_date = CURDATE()
        AND p2.data_date = DATE_SUB(CURDATE(), INTERVAL %s DAY)
        AND ABS((p1.price - p2.price) / p2.price * 100) >= %s
        ORDER BY ABS((p1.price - p2.price) / p2.price * 100) DESC
        """
        
        return self.db.execute_query(sql, (days, threshold))
    
    def get_latest_prices_by_category(self, category_id: int) -> List[Dict[str, Any]]:
        """
        获取分类下所有产品的最新价格
        
        Args:
            category_id: 分类ID
            
        Returns:
            最新价格数据
        """
        sql = f"""
        SELECT 
            pd.*,
            p.name as product_name,
            r.name as region_name
        FROM {self.table_name} pd
        JOIN products p ON pd.product_id = p.id
        JOIN regions r ON pd.region_id = r.id
        WHERE p.category_id = %s
        AND pd.data_date = (
            SELECT MAX(data_date)
            FROM {self.table_name} pd2
            WHERE pd2.product_id = pd.product_id 
            AND pd2.region_id = pd.region_id
        )
        ORDER BY p.name, r.name
        """
        
        return self.db.execute_query(sql, (category_id,))
    
    def insert_or_update_price(self, price_data: PriceData) -> int:
        """
        插入或更新价格数据
        
        Args:
            price_data: 价格数据实例
            
        Returns:
            影响的行数
        """
        unique_keys = ['product_id', 'region_id', 'data_date']
        return self.insert_or_update(price_data, unique_keys)
    
    def batch_insert_prices(self, price_data_list: List[PriceData]) -> int:
        """
        批量插入价格数据
        
        Args:
            price_data_list: 价格数据列表
            
        Returns:
            影响的行数
        """
        if not price_data_list:
            return 0
        
        # 准备批量插入的SQL
        sql = f"""
        INSERT INTO {self.table_name} 
        (product_id, region_id, price, unit, price_change, price_trend, 
         source_url, source_id, data_date)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
        price = VALUES(price),
        unit = VALUES(unit),
        price_change = VALUES(price_change),
        price_trend = VALUES(price_trend),
        source_url = VALUES(source_url),
        source_id = VALUES(source_id),
        crawl_time = CURRENT_TIMESTAMP
        """
        
        # 准备参数列表
        params_list = []
        for price_data in price_data_list:
            params = (
                price_data.product_id,
                price_data.region_id,
                price_data.price,
                price_data.unit,
                price_data.price_change,
                price_data.price_trend,
                price_data.source_url,
                price_data.source_id,
                price_data.data_date
            )
            params_list.append(params)
        
        return self.db.execute_batch(sql, params_list)
    
    def delete_old_data(self, days: int = 365) -> int:
        """
        删除过期数据
        
        Args:
            days: 保留天数
            
        Returns:
            删除的行数
        """
        sql = f"""
        DELETE FROM {self.table_name}
        WHERE data_date < DATE_SUB(CURDATE(), INTERVAL %s DAY)
        """
        
        return self.db.execute_delete(sql, (days,))
    
    def get_data_quality_report(self) -> Dict[str, Any]:
        """
        获取数据质量报告
        
        Returns:
            数据质量报告
        """
        report = {}
        
        # 总数据量
        report['total_records'] = self.count()
        
        # 最新数据日期
        sql = f"SELECT MAX(data_date) as latest_date FROM {self.table_name}"
        result = self.db.execute_query(sql)
        report['latest_date'] = result[0]['latest_date'] if result else None
        
        # 缺失价格的记录
        report['missing_price'] = self.count("price IS NULL OR price = 0")
        
        # 异常价格的记录（价格过高或过低）
        report['abnormal_price'] = self.count("price < 0.01 OR price > 10000")
        
        # 缺失日期的记录
        report['missing_date'] = self.count("data_date IS NULL")
        
        # 按日期统计数据量
        sql = f"""
        SELECT data_date, COUNT(*) as count
        FROM {self.table_name}
        WHERE data_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
        GROUP BY data_date
        ORDER BY data_date DESC
        """
        
        report['daily_counts'] = self.db.execute_query(sql)
        
        return report
