"""
分类数据模型

定义产品分类的数据模型和数据访问对象。
"""

from dataclasses import dataclass
from typing import Optional, List, Dict, Any
from .base import BaseModel, BaseDAO


@dataclass
class Category(BaseModel):
    """产品分类模型"""
    
    name: str = ""
    pinyin: str = ""
    category_id: int = 0
    parent_id: Optional[int] = None
    level: int = 1
    sort_order: int = 0
    is_active: bool = True
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.name:
            raise ValueError("分类名称不能为空")
        if not self.pinyin:
            raise ValueError("分类拼音不能为空")
        if self.category_id <= 0:
            raise ValueError("分类ID必须大于0")


class CategoryDAO(BaseDAO):
    """分类数据访问对象"""
    
    @property
    def table_name(self) -> str:
        return "categories"
    
    @property
    def model_class(self):
        return Category
    
    def find_by_category_id(self, category_id: int) -> Optional[Category]:
        """
        根据分类ID查找分类
        
        Args:
            category_id: 惠农网分类ID
            
        Returns:
            分类实例或None
        """
        return self.find_one_by_condition("category_id = %s", (category_id,))
    
    def find_by_pinyin(self, pinyin: str) -> Optional[Category]:
        """
        根据拼音查找分类

        Args:
            pinyin: 分类拼音

        Returns:
            分类实例或None
        """
        return self.find_one_by_condition("pinyin = %s", (pinyin,))

    def find_by_name(self, name: str) -> Optional[Category]:
        """
        根据分类名称查找分类

        Args:
            name: 分类名称

        Returns:
            分类实例或None
        """
        return self.find_one_by_condition("name = %s", (name,))

    def find_by_parent_id(self, parent_id: Optional[int] = None) -> List[Category]:
        """
        根据父分类ID查找子分类
        
        Args:
            parent_id: 父分类ID，None表示查找顶级分类
            
        Returns:
            分类列表
        """
        if parent_id is None:
            return self.find_by_condition("parent_id IS NULL ORDER BY sort_order, id")
        else:
            return self.find_by_condition("parent_id = %s ORDER BY sort_order, id", (parent_id,))
    
    def find_by_level(self, level: int) -> List[Category]:
        """
        根据层级查找分类
        
        Args:
            level: 分类层级
            
        Returns:
            分类列表
        """
        return self.find_by_condition("level = %s ORDER BY sort_order, id", (level,))
    
    def find_active_categories(self) -> List[Category]:
        """
        查找所有启用的分类
        
        Returns:
            启用的分类列表
        """
        return self.find_by_condition("is_active = 1 ORDER BY level, sort_order, id")
    
    def find_top_level_categories(self) -> List[Category]:
        """
        查找顶级分类
        
        Returns:
            顶级分类列表
        """
        return self.find_by_condition(
            "parent_id IS NULL AND is_active = 1 ORDER BY sort_order, id"
        )
    
    def get_category_tree(self) -> List[Dict[str, Any]]:
        """
        获取分类树结构
        
        Returns:
            分类树列表
        """
        # 获取所有分类
        all_categories = self.find_active_categories()
        
        # 构建分类字典
        category_dict = {cat.id: cat.to_dict() for cat in all_categories}
        
        # 为每个分类添加children字段
        for cat_data in category_dict.values():
            cat_data['children'] = []
        
        # 构建树结构
        root_categories = []
        
        for category in all_categories:
            cat_data = category_dict[category.id]
            
            if category.parent_id is None:
                # 顶级分类
                root_categories.append(cat_data)
            else:
                # 子分类
                parent = category_dict.get(category.parent_id)
                if parent:
                    parent['children'].append(cat_data)
        
        return root_categories
    
    def get_category_path(self, category_id: int) -> List[Category]:
        """
        获取分类路径（从根到当前分类）
        
        Args:
            category_id: 分类ID
            
        Returns:
            分类路径列表
        """
        path = []
        current_category = self.find_by_id(category_id)
        
        while current_category:
            path.insert(0, current_category)
            if current_category.parent_id:
                current_category = self.find_by_id(current_category.parent_id)
            else:
                break
        
        return path
    
    def get_subcategories(self, category_id: int, include_self: bool = False) -> List[Category]:
        """
        获取所有子分类（递归）
        
        Args:
            category_id: 分类ID
            include_self: 是否包含自身
            
        Returns:
            子分类列表
        """
        subcategories = []
        
        if include_self:
            category = self.find_by_id(category_id)
            if category:
                subcategories.append(category)
        
        # 获取直接子分类
        direct_children = self.find_by_parent_id(category_id)
        
        for child in direct_children:
            subcategories.append(child)
            # 递归获取子分类的子分类
            subcategories.extend(self.get_subcategories(child.id, False))
        
        return subcategories
    
    def update_sort_order(self, category_id: int, sort_order: int) -> int:
        """
        更新分类排序
        
        Args:
            category_id: 分类ID
            sort_order: 排序值
            
        Returns:
            影响的行数
        """
        sql = f"UPDATE {self.table_name} SET sort_order = %s WHERE id = %s"
        return self.db.execute_update(sql, (sort_order, category_id))
    
    def toggle_active_status(self, category_id: int) -> int:
        """
        切换分类启用状态
        
        Args:
            category_id: 分类ID
            
        Returns:
            影响的行数
        """
        sql = f"UPDATE {self.table_name} SET is_active = NOT is_active WHERE id = %s"
        return self.db.execute_update(sql, (category_id,))
    
    def get_category_stats(self) -> Dict[str, int]:
        """
        获取分类统计信息
        
        Returns:
            统计信息字典
        """
        stats = {}
        
        # 总分类数
        stats['total'] = self.count()
        
        # 启用分类数
        stats['active'] = self.count("is_active = 1")
        
        # 各层级分类数
        for level in range(1, 4):
            stats[f'level_{level}'] = self.count("level = %s", (level,))
        
        return stats
    
    def search_categories(self, keyword: str) -> List[Category]:
        """
        搜索分类
        
        Args:
            keyword: 搜索关键词
            
        Returns:
            匹配的分类列表
        """
        return self.find_by_condition(
            "name LIKE %s OR pinyin LIKE %s",
            (f"%{keyword}%", f"%{keyword}%")
        )
