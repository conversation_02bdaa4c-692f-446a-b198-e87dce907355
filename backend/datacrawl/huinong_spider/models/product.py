"""
产品数据模型

定义产品的数据模型和数据访问对象。
"""

from dataclasses import dataclass
from typing import Optional, List, Dict, Any
from .base import BaseModel, BaseDAO


@dataclass
class Product(BaseModel):
    """产品模型"""
    
    name: str = ""
    category_id: int = 0
    subcategory: str = ""
    product_code: str = ""
    unit: str = "斤"
    description: str = ""
    is_active: bool = True
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.name:
            raise ValueError("产品名称不能为空")
        if self.category_id <= 0:
            raise ValueError("分类ID必须大于0")


class ProductDAO(BaseDAO):
    """产品数据访问对象"""
    
    @property
    def table_name(self) -> str:
        return "products"
    
    @property
    def model_class(self):
        return Product
    
    def find_by_name(self, name: str) -> Optional[Product]:
        """
        根据产品名称查找产品
        
        Args:
            name: 产品名称
            
        Returns:
            产品实例或None
        """
        return self.find_one_by_condition("name = %s", (name,))
    
    def find_by_category_id(self, category_id: int) -> List[Product]:
        """
        根据分类ID查找产品
        
        Args:
            category_id: 分类ID
            
        Returns:
            产品列表
        """
        return self.find_by_condition(
            "category_id = %s AND is_active = 1 ORDER BY name",
            (category_id,)
        )
    
    def find_by_subcategory(self, subcategory: str) -> List[Product]:
        """
        根据子分类查找产品
        
        Args:
            subcategory: 子分类名称
            
        Returns:
            产品列表
        """
        return self.find_by_condition(
            "subcategory = %s AND is_active = 1 ORDER BY name",
            (subcategory,)
        )
    
    def find_by_product_code(self, product_code: str) -> Optional[Product]:
        """
        根据产品编码查找产品
        
        Args:
            product_code: 产品编码
            
        Returns:
            产品实例或None
        """
        return self.find_one_by_condition("product_code = %s", (product_code,))
    
    def search_products(self, keyword: str) -> List[Product]:
        """
        搜索产品
        
        Args:
            keyword: 搜索关键词
            
        Returns:
            匹配的产品列表
        """
        return self.find_by_condition(
            "name LIKE %s OR subcategory LIKE %s OR description LIKE %s",
            (f"%{keyword}%", f"%{keyword}%", f"%{keyword}%")
        )
    
    def find_active_products(self) -> List[Product]:
        """
        查找所有启用的产品
        
        Returns:
            启用的产品列表
        """
        return self.find_by_condition("is_active = 1 ORDER BY category_id, name")
    
    def get_products_by_category(self, category_id: int, include_subcategory: bool = True) -> Dict[str, List[Product]]:
        """
        按分类获取产品，可选择是否按子分类分组
        
        Args:
            category_id: 分类ID
            include_subcategory: 是否按子分类分组
            
        Returns:
            产品字典，键为子分类名称（如果include_subcategory为True）
        """
        products = self.find_by_category_id(category_id)
        
        if not include_subcategory:
            return {"all": products}
        
        # 按子分类分组
        grouped_products = {}
        for product in products:
            subcategory = product.subcategory or "其他"
            if subcategory not in grouped_products:
                grouped_products[subcategory] = []
            grouped_products[subcategory].append(product)
        
        return grouped_products
    
    def get_subcategories(self, category_id: int) -> List[str]:
        """
        获取分类下的所有子分类
        
        Args:
            category_id: 分类ID
            
        Returns:
            子分类名称列表
        """
        sql = f"""
        SELECT DISTINCT subcategory
        FROM {self.table_name}
        WHERE category_id = %s AND subcategory IS NOT NULL AND subcategory != ''
        ORDER BY subcategory
        """
        
        results = self.db.execute_query(sql, (category_id,))
        return [row['subcategory'] for row in results]
    
    def get_product_stats(self) -> Dict[str, Any]:
        """
        获取产品统计信息
        
        Returns:
            统计信息字典
        """
        stats = {}
        
        # 总产品数
        stats['total'] = self.count()
        
        # 启用产品数
        stats['active'] = self.count("is_active = 1")
        
        # 按分类统计
        sql = f"""
        SELECT c.name as category_name, COUNT(p.id) as product_count
        FROM {self.table_name} p
        JOIN categories c ON p.category_id = c.id
        WHERE p.is_active = 1
        GROUP BY c.id, c.name
        ORDER BY product_count DESC
        """
        
        category_stats = self.db.execute_query(sql)
        stats['by_category'] = {row['category_name']: row['product_count'] for row in category_stats}
        
        # 按子分类统计
        sql = f"""
        SELECT subcategory, COUNT(*) as count
        FROM {self.table_name}
        WHERE is_active = 1 AND subcategory IS NOT NULL AND subcategory != ''
        GROUP BY subcategory
        ORDER BY count DESC
        LIMIT 10
        """
        
        subcategory_stats = self.db.execute_query(sql)
        stats['top_subcategories'] = {row['subcategory']: row['count'] for row in subcategory_stats}
        
        return stats
    
    def find_or_create_product(self, name: str, category_id: int, **kwargs) -> Product:
        """
        查找或创建产品
        
        Args:
            name: 产品名称
            category_id: 分类ID
            **kwargs: 其他产品属性
            
        Returns:
            产品实例
        """
        # 先尝试查找
        product = self.find_by_name(name)
        
        if product:
            return product
        
        # 创建新产品
        product_data = {
            'name': name,
            'category_id': category_id,
            **kwargs
        }
        
        product = Product(**product_data)
        product_id = self.insert(product)
        product.id = product_id
        
        return product
    
    def update_product_info(self, product_id: int, **kwargs) -> int:
        """
        更新产品信息
        
        Args:
            product_id: 产品ID
            **kwargs: 要更新的字段
            
        Returns:
            影响的行数
        """
        if not kwargs:
            return 0
        
        set_clauses = [f"{field} = %s" for field in kwargs.keys()]
        sql = f"UPDATE {self.table_name} SET {', '.join(set_clauses)} WHERE id = %s"
        
        params = tuple(kwargs.values()) + (product_id,)
        return self.db.execute_update(sql, params)
    
    def toggle_active_status(self, product_id: int) -> int:
        """
        切换产品启用状态
        
        Args:
            product_id: 产品ID
            
        Returns:
            影响的行数
        """
        sql = f"UPDATE {self.table_name} SET is_active = NOT is_active WHERE id = %s"
        return self.db.execute_update(sql, (product_id,))
    
    def get_popular_products(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取热门产品（基于价格数据数量）
        
        Args:
            limit: 限制数量
            
        Returns:
            热门产品列表
        """
        sql = f"""
        SELECT p.*, COUNT(pd.id) as price_count
        FROM {self.table_name} p
        LEFT JOIN price_data pd ON p.id = pd.product_id
        WHERE p.is_active = 1
        GROUP BY p.id
        ORDER BY price_count DESC
        LIMIT %s
        """
        
        results = self.db.execute_query(sql, (limit,))
        return results
    
    def find_products_without_prices(self) -> List[Product]:
        """
        查找没有价格数据的产品
        
        Returns:
            没有价格数据的产品列表
        """
        sql = f"""
        SELECT p.*
        FROM {self.table_name} p
        LEFT JOIN price_data pd ON p.id = pd.product_id
        WHERE p.is_active = 1 AND pd.id IS NULL
        ORDER BY p.name
        """
        
        results = self.db.execute_query(sql)
        return [Product.from_dict(row) for row in results]
