"""
基础数据模型

提供数据模型的基础类和数据访问对象基类。
"""

from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, field
from ..utils.database import get_db_manager
from ..utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class BaseModel:
    """基础数据模型"""
    
    id: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = {}
        for key, value in self.__dict__.items():
            if value is not None:
                if isinstance(value, datetime):
                    result[key] = value.isoformat()
                else:
                    result[key] = value
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]):
        """从字典创建实例"""
        # 过滤掉不存在的字段
        valid_fields = {k: v for k, v in data.items() if hasattr(cls, k)}
        return cls(**valid_fields)


class BaseDAO(ABC):
    """基础数据访问对象"""
    
    def __init__(self):
        """初始化DAO"""
        self.db = get_db_manager()
        self.logger = get_logger(self.__class__.__name__)
    
    @property
    @abstractmethod
    def table_name(self) -> str:
        """表名"""
        pass
    
    @property
    @abstractmethod
    def model_class(self):
        """模型类"""
        pass
    
    def find_by_id(self, id: int) -> Optional[BaseModel]:
        """
        根据ID查找记录
        
        Args:
            id: 记录ID
            
        Returns:
            模型实例或None
        """
        sql = f"SELECT * FROM {self.table_name} WHERE id = %s"
        results = self.db.execute_query(sql, (id,))
        
        if results:
            return self.model_class.from_dict(results[0])
        return None
    
    def find_all(self, limit: Optional[int] = None, offset: int = 0) -> List[BaseModel]:
        """
        查找所有记录
        
        Args:
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            模型实例列表
        """
        sql = f"SELECT * FROM {self.table_name}"
        
        if limit is not None:
            sql += f" LIMIT {limit} OFFSET {offset}"
        
        results = self.db.execute_query(sql)
        return [self.model_class.from_dict(row) for row in results]
    
    def count(self, where_clause: str = "", params: tuple = ()) -> int:
        """
        统计记录数
        
        Args:
            where_clause: WHERE子句
            params: 查询参数
            
        Returns:
            记录数
        """
        sql = f"SELECT COUNT(*) as count FROM {self.table_name}"
        
        if where_clause:
            sql += f" WHERE {where_clause}"
        
        result = self.db.execute_query(sql, params)
        return result[0]['count'] if result else 0
    
    def insert(self, model: BaseModel) -> int:
        """
        插入记录
        
        Args:
            model: 模型实例
            
        Returns:
            插入的记录ID
        """
        data = model.to_dict()
        
        # 移除ID和时间戳字段
        data.pop('id', None)
        data.pop('created_at', None)
        data.pop('updated_at', None)
        
        if not data:
            raise ValueError("没有可插入的数据")
        
        columns = list(data.keys())
        placeholders = ', '.join(['%s'] * len(columns))
        
        sql = f"""
        INSERT INTO {self.table_name} ({', '.join(columns)})
        VALUES ({placeholders})
        """
        
        params = tuple(data.values())
        return self.db.execute_insert(sql, params)
    
    def update(self, model: BaseModel) -> int:
        """
        更新记录
        
        Args:
            model: 模型实例
            
        Returns:
            影响的行数
        """
        if not model.id:
            raise ValueError("模型ID不能为空")
        
        data = model.to_dict()
        
        # 移除ID和创建时间
        data.pop('id', None)
        data.pop('created_at', None)
        
        if not data:
            raise ValueError("没有可更新的数据")
        
        set_clauses = [f"{col} = %s" for col in data.keys()]
        
        sql = f"""
        UPDATE {self.table_name}
        SET {', '.join(set_clauses)}
        WHERE id = %s
        """
        
        params = tuple(data.values()) + (model.id,)
        return self.db.execute_update(sql, params)
    
    def delete(self, id: int) -> int:
        """
        删除记录
        
        Args:
            id: 记录ID
            
        Returns:
            删除的行数
        """
        sql = f"DELETE FROM {self.table_name} WHERE id = %s"
        return self.db.execute_delete(sql, (id,))
    
    def find_by_condition(self, where_clause: str, params: tuple = ()) -> List[BaseModel]:
        """
        根据条件查找记录
        
        Args:
            where_clause: WHERE子句
            params: 查询参数
            
        Returns:
            模型实例列表
        """
        sql = f"SELECT * FROM {self.table_name} WHERE {where_clause}"
        results = self.db.execute_query(sql, params)
        return [self.model_class.from_dict(row) for row in results]
    
    def find_one_by_condition(self, where_clause: str, params: tuple = ()) -> Optional[BaseModel]:
        """
        根据条件查找单个记录
        
        Args:
            where_clause: WHERE子句
            params: 查询参数
            
        Returns:
            模型实例或None
        """
        results = self.find_by_condition(where_clause, params)
        return results[0] if results else None
    
    def exists(self, where_clause: str, params: tuple = ()) -> bool:
        """
        检查记录是否存在
        
        Args:
            where_clause: WHERE子句
            params: 查询参数
            
        Returns:
            是否存在
        """
        return self.count(where_clause, params) > 0
    
    def batch_insert(self, models: List[BaseModel]) -> int:
        """
        批量插入记录
        
        Args:
            models: 模型实例列表
            
        Returns:
            影响的行数
        """
        if not models:
            return 0
        
        # 获取第一个模型的字段
        first_model = models[0]
        data = first_model.to_dict()
        
        # 移除ID和时间戳字段
        data.pop('id', None)
        data.pop('created_at', None)
        data.pop('updated_at', None)
        
        columns = list(data.keys())
        placeholders = ', '.join(['%s'] * len(columns))
        
        sql = f"""
        INSERT INTO {self.table_name} ({', '.join(columns)})
        VALUES ({placeholders})
        """
        
        # 准备参数列表
        params_list = []
        for model in models:
            model_data = model.to_dict()
            params = tuple(model_data.get(col) for col in columns)
            params_list.append(params)
        
        return self.db.execute_batch(sql, params_list)
    
    def insert_or_update(self, model: BaseModel, unique_keys: List[str]) -> int:
        """
        插入或更新记录
        
        Args:
            model: 模型实例
            unique_keys: 唯一键列表
            
        Returns:
            影响的行数
        """
        data = model.to_dict()
        
        # 移除ID和时间戳字段
        data.pop('id', None)
        data.pop('created_at', None)
        data.pop('updated_at', None)
        
        return self.db.insert_or_update(self.table_name, data, unique_keys)
