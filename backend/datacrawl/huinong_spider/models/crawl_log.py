"""
爬取日志模型

定义爬取日志的数据模型和数据访问对象。
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Optional, List, Dict, Any
from enum import Enum
from .base import BaseModel, BaseDAO


class CrawlStatus(Enum):
    """爬取状态枚举"""
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    STOPPED = "stopped"


@dataclass
class CrawlLog(BaseModel):
    """爬取日志模型"""
    
    task_id: str = ""
    spider_name: str = ""
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    status: str = CrawlStatus.RUNNING.value
    total_requests: int = 0
    success_requests: int = 0
    failed_requests: int = 0
    items_scraped: int = 0
    items_saved: int = 0
    error_message: str = ""
    config_snapshot: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.task_id:
            raise ValueError("任务ID不能为空")
        if not self.spider_name:
            raise ValueError("爬虫名称不能为空")
        if self.start_time is None:
            self.start_time = datetime.now()
    
    @property
    def duration(self) -> Optional[float]:
        """获取执行时长（秒）"""
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        return None
    
    @property
    def success_rate(self) -> float:
        """获取成功率"""
        if self.total_requests == 0:
            return 0.0
        return self.success_requests / self.total_requests
    
    @property
    def save_rate(self) -> float:
        """获取保存率"""
        if self.items_scraped == 0:
            return 0.0
        return self.items_saved / self.items_scraped


class CrawlLogDAO(BaseDAO):
    """爬取日志数据访问对象"""

    @property
    def table_name(self) -> str:
        return "crawl_logs"

    @property
    def model_class(self):
        return CrawlLog

    def insert(self, model: CrawlLog) -> int:
        """
        插入记录（重写以处理JSON字段）

        Args:
            model: 模型实例

        Returns:
            插入的记录ID
        """
        import json

        data = model.to_dict()

        # 移除ID和时间戳字段
        data.pop('id', None)
        data.pop('created_at', None)
        data.pop('updated_at', None)

        # 处理JSON字段
        if 'config_snapshot' in data and data['config_snapshot'] is not None:
            if isinstance(data['config_snapshot'], dict):
                data['config_snapshot'] = json.dumps(data['config_snapshot'])
            elif data['config_snapshot'] == "":
                data['config_snapshot'] = None

        if not data:
            raise ValueError("没有可插入的数据")

        columns = list(data.keys())
        placeholders = ', '.join(['%s'] * len(columns))

        sql = f"""
        INSERT INTO {self.table_name} ({', '.join(columns)})
        VALUES ({placeholders})
        """

        params = tuple(data.values())
        return self.db.execute_insert(sql, params)
    
    def find_by_task_id(self, task_id: str) -> Optional[CrawlLog]:
        """
        根据任务ID查找日志
        
        Args:
            task_id: 任务ID
            
        Returns:
            日志实例或None
        """
        return self.find_one_by_condition("task_id = %s", (task_id,))
    
    def find_by_spider_name(self, spider_name: str, limit: int = 10) -> List[CrawlLog]:
        """
        根据爬虫名称查找日志
        
        Args:
            spider_name: 爬虫名称
            limit: 限制数量
            
        Returns:
            日志列表
        """
        sql = f"""
        SELECT * FROM {self.table_name}
        WHERE spider_name = %s
        ORDER BY start_time DESC
        LIMIT %s
        """
        
        results = self.db.execute_query(sql, (spider_name, limit))
        return [CrawlLog.from_dict(row) for row in results]
    
    def find_by_status(self, status: CrawlStatus, limit: int = 10) -> List[CrawlLog]:
        """
        根据状态查找日志
        
        Args:
            status: 爬取状态
            limit: 限制数量
            
        Returns:
            日志列表
        """
        return self.find_by_condition(
            "status = %s ORDER BY start_time DESC LIMIT %s",
            (status.value, limit)
        )
    
    def find_running_tasks(self) -> List[CrawlLog]:
        """
        查找正在运行的任务
        
        Returns:
            正在运行的任务列表
        """
        return self.find_by_condition("status = %s", (CrawlStatus.RUNNING.value,))
    
    def find_recent_logs(self, hours: int = 24, limit: int = 50) -> List[CrawlLog]:
        """
        查找最近的日志
        
        Args:
            hours: 小时数
            limit: 限制数量
            
        Returns:
            最近的日志列表
        """
        sql = f"""
        SELECT * FROM {self.table_name}
        WHERE start_time >= DATE_SUB(NOW(), INTERVAL %s HOUR)
        ORDER BY start_time DESC
        LIMIT %s
        """
        
        results = self.db.execute_query(sql, (hours, limit))
        return [CrawlLog.from_dict(row) for row in results]
    
    def update_status(self, task_id: str, status: CrawlStatus, 
                     error_message: str = "") -> int:
        """
        更新任务状态
        
        Args:
            task_id: 任务ID
            status: 新状态
            error_message: 错误信息
            
        Returns:
            影响的行数
        """
        sql = f"""
        UPDATE {self.table_name}
        SET status = %s, error_message = %s
        WHERE task_id = %s
        """
        
        return self.db.execute_update(sql, (status.value, error_message, task_id))
    
    def update_progress(self, task_id: str, **kwargs) -> int:
        """
        更新任务进度
        
        Args:
            task_id: 任务ID
            **kwargs: 要更新的字段
            
        Returns:
            影响的行数
        """
        if not kwargs:
            return 0
        
        set_clauses = [f"{field} = %s" for field in kwargs.keys()]
        sql = f"""
        UPDATE {self.table_name}
        SET {', '.join(set_clauses)}
        WHERE task_id = %s
        """
        
        params = tuple(kwargs.values()) + (task_id,)
        return self.db.execute_update(sql, params)
    
    def finish_task(self, task_id: str, status: CrawlStatus, 
                   items_saved: int = 0, error_message: str = "") -> int:
        """
        完成任务
        
        Args:
            task_id: 任务ID
            status: 最终状态
            items_saved: 保存的条目数
            error_message: 错误信息
            
        Returns:
            影响的行数
        """
        sql = f"""
        UPDATE {self.table_name}
        SET status = %s, end_time = NOW(), items_saved = %s, error_message = %s
        WHERE task_id = %s
        """
        
        return self.db.execute_update(sql, (status.value, items_saved, error_message, task_id))
    
    def get_statistics(self, days: int = 7) -> Dict[str, Any]:
        """
        获取爬取统计信息
        
        Args:
            days: 统计天数
            
        Returns:
            统计信息字典
        """
        stats = {}
        
        # 总任务数
        stats['total_tasks'] = self.count(
            "start_time >= DATE_SUB(NOW(), INTERVAL %s DAY)",
            (days,)
        )
        
        # 按状态统计
        for status in CrawlStatus:
            stats[f'{status.value}_tasks'] = self.count(
                "status = %s AND start_time >= DATE_SUB(NOW(), INTERVAL %s DAY)",
                (status.value, days)
            )
        
        # 成功率
        if stats['total_tasks'] > 0:
            stats['success_rate'] = stats['success_tasks'] / stats['total_tasks']
        else:
            stats['success_rate'] = 0.0
        
        # 平均执行时间
        sql = f"""
        SELECT AVG(TIMESTAMPDIFF(SECOND, start_time, end_time)) as avg_duration
        FROM {self.table_name}
        WHERE status = %s AND end_time IS NOT NULL
        AND start_time >= DATE_SUB(NOW(), INTERVAL %s DAY)
        """
        
        result = self.db.execute_query(sql, (CrawlStatus.SUCCESS.value, days))
        stats['avg_duration'] = result[0]['avg_duration'] if result and result[0]['avg_duration'] else 0
        
        # 总采集数据量
        sql = f"""
        SELECT SUM(items_saved) as total_items
        FROM {self.table_name}
        WHERE start_time >= DATE_SUB(NOW(), INTERVAL %s DAY)
        """
        
        result = self.db.execute_query(sql, (days,))
        stats['total_items'] = result[0]['total_items'] if result and result[0]['total_items'] else 0
        
        return stats
    
    def get_spider_performance(self, spider_name: str, days: int = 7) -> Dict[str, Any]:
        """
        获取特定爬虫的性能统计
        
        Args:
            spider_name: 爬虫名称
            days: 统计天数
            
        Returns:
            性能统计信息
        """
        stats = {}
        
        # 任务数量
        stats['total_tasks'] = self.count(
            "spider_name = %s AND start_time >= DATE_SUB(NOW(), INTERVAL %s DAY)",
            (spider_name, days)
        )
        
        # 成功任务数
        stats['success_tasks'] = self.count(
            "spider_name = %s AND status = %s AND start_time >= DATE_SUB(NOW(), INTERVAL %s DAY)",
            (spider_name, CrawlStatus.SUCCESS.value, days)
        )
        
        # 平均请求数
        sql = f"""
        SELECT AVG(total_requests) as avg_requests,
               AVG(success_requests) as avg_success_requests,
               AVG(items_scraped) as avg_items_scraped,
               AVG(items_saved) as avg_items_saved
        FROM {self.table_name}
        WHERE spider_name = %s AND start_time >= DATE_SUB(NOW(), INTERVAL %s DAY)
        """
        
        result = self.db.execute_query(sql, (spider_name, days))
        if result:
            stats.update(result[0])
        
        return stats
    
    def get_error_summary(self, days: int = 7) -> List[Dict[str, Any]]:
        """
        获取错误摘要
        
        Args:
            days: 统计天数
            
        Returns:
            错误摘要列表
        """
        sql = f"""
        SELECT error_message, COUNT(*) as count
        FROM {self.table_name}
        WHERE status = %s AND error_message != ''
        AND start_time >= DATE_SUB(NOW(), INTERVAL %s DAY)
        GROUP BY error_message
        ORDER BY count DESC
        LIMIT 10
        """
        
        return self.db.execute_query(sql, (CrawlStatus.FAILED.value, days))
    
    def cleanup_old_logs(self, days: int = 90) -> int:
        """
        清理旧日志
        
        Args:
            days: 保留天数
            
        Returns:
            删除的行数
        """
        sql = f"""
        DELETE FROM {self.table_name}
        WHERE start_time < DATE_SUB(NOW(), INTERVAL %s DAY)
        """
        
        return self.db.execute_delete(sql, (days,))
