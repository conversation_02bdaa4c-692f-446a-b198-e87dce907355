"""
地区数据模型

定义地区的数据模型和数据访问对象。
"""

from dataclasses import dataclass
from typing import Optional, List, Dict, Any
from .base import BaseModel, BaseDAO


@dataclass
class Region(BaseModel):
    """地区模型"""
    
    name: str = ""
    region_code: int = 0
    parent_id: Optional[int] = None
    level: int = 1
    full_name: str = ""
    is_active: bool = True
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.name:
            raise ValueError("地区名称不能为空")
        if self.region_code <= 0:
            raise ValueError("地区编码必须大于0")
        if not self.full_name:
            self.full_name = self.name


class RegionDAO(BaseDAO):
    """地区数据访问对象"""
    
    @property
    def table_name(self) -> str:
        return "regions"
    
    @property
    def model_class(self):
        return Region
    
    def find_by_region_code(self, region_code: int) -> Optional[Region]:
        """
        根据地区编码查找地区
        
        Args:
            region_code: 惠农网地区编码
            
        Returns:
            地区实例或None
        """
        return self.find_one_by_condition("region_code = %s", (region_code,))
    
    def find_by_name(self, name: str) -> Optional[Region]:
        """
        根据地区名称查找地区
        
        Args:
            name: 地区名称
            
        Returns:
            地区实例或None
        """
        return self.find_one_by_condition("name = %s", (name,))
    
    def find_by_parent_id(self, parent_id: Optional[int] = None) -> List[Region]:
        """
        根据父地区ID查找子地区
        
        Args:
            parent_id: 父地区ID，None表示查找省级地区
            
        Returns:
            地区列表
        """
        if parent_id is None:
            return self.find_by_condition("parent_id IS NULL ORDER BY region_code")
        else:
            return self.find_by_condition("parent_id = %s ORDER BY region_code", (parent_id,))
    
    def find_by_level(self, level: int) -> List[Region]:
        """
        根据层级查找地区
        
        Args:
            level: 地区层级 (1=省, 2=市, 3=县)
            
        Returns:
            地区列表
        """
        return self.find_by_condition("level = %s ORDER BY region_code", (level,))
    
    def find_provinces(self) -> List[Region]:
        """
        查找所有省级地区

        Returns:
            省级地区列表
        """
        return self.find_by_condition(
            "level = 1 AND is_active = 1 ORDER BY region_code"
        )

    def find_active_regions(self) -> List[Region]:
        """
        查找所有启用的地区

        Returns:
            启用的地区列表
        """
        return self.find_by_condition(
            "is_active = 1 ORDER BY level, region_code"
        )
    
    def find_cities(self, province_id: int) -> List[Region]:
        """
        查找省份下的所有城市
        
        Args:
            province_id: 省份ID
            
        Returns:
            城市列表
        """
        return self.find_by_condition(
            "parent_id = %s AND level = 2 AND is_active = 1 ORDER BY region_code",
            (province_id,)
        )
    
    def find_counties(self, city_id: int) -> List[Region]:
        """
        查找城市下的所有县区
        
        Args:
            city_id: 城市ID
            
        Returns:
            县区列表
        """
        return self.find_by_condition(
            "parent_id = %s AND level = 3 AND is_active = 1 ORDER BY region_code",
            (city_id,)
        )
    
    def get_region_tree(self) -> List[Dict[str, Any]]:
        """
        获取地区树结构
        
        Returns:
            地区树列表
        """
        # 获取所有地区
        all_regions = self.find_by_condition("is_active = 1 ORDER BY level, region_code")
        
        # 构建地区字典
        region_dict = {region.id: region.to_dict() for region in all_regions}
        
        # 为每个地区添加children字段
        for region_data in region_dict.values():
            region_data['children'] = []
        
        # 构建树结构
        root_regions = []
        
        for region in all_regions:
            region_data = region_dict[region.id]
            
            if region.parent_id is None:
                # 省级地区
                root_regions.append(region_data)
            else:
                # 子地区
                parent = region_dict.get(region.parent_id)
                if parent:
                    parent['children'].append(region_data)
        
        return root_regions
    
    def get_region_path(self, region_id: int) -> List[Region]:
        """
        获取地区路径（从省到当前地区）
        
        Args:
            region_id: 地区ID
            
        Returns:
            地区路径列表
        """
        path = []
        current_region = self.find_by_id(region_id)
        
        while current_region:
            path.insert(0, current_region)
            if current_region.parent_id:
                current_region = self.find_by_id(current_region.parent_id)
            else:
                break
        
        return path
    
    def get_full_name(self, region_id: int) -> str:
        """
        获取地区完整名称
        
        Args:
            region_id: 地区ID
            
        Returns:
            完整地区名称
        """
        path = self.get_region_path(region_id)
        return " ".join([region.name for region in path])
    
    def search_regions(self, keyword: str) -> List[Region]:
        """
        搜索地区
        
        Args:
            keyword: 搜索关键词
            
        Returns:
            匹配的地区列表
        """
        return self.find_by_condition(
            "name LIKE %s OR full_name LIKE %s",
            (f"%{keyword}%", f"%{keyword}%")
        )
    
    def get_region_stats(self) -> Dict[str, int]:
        """
        获取地区统计信息
        
        Returns:
            统计信息字典
        """
        stats = {}
        
        # 总地区数
        stats['total'] = self.count()
        
        # 启用地区数
        stats['active'] = self.count("is_active = 1")
        
        # 各层级地区数
        stats['provinces'] = self.count("level = 1")
        stats['cities'] = self.count("level = 2")
        stats['counties'] = self.count("level = 3")
        
        return stats
    
    def find_by_name_fuzzy(self, name: str) -> List[Region]:
        """
        模糊匹配地区名称
        
        Args:
            name: 地区名称
            
        Returns:
            匹配的地区列表
        """
        # 先尝试精确匹配
        exact_match = self.find_by_name(name)
        if exact_match:
            return [exact_match]
        
        # 模糊匹配
        return self.find_by_condition("name LIKE %s", (f"%{name}%",))
    
    def get_major_cities(self) -> List[Region]:
        """
        获取主要城市（直辖市和省会城市）
        
        Returns:
            主要城市列表
        """
        # 直辖市的region_code
        municipality_codes = [45081, 45082, 45083, 45084]  # 北京、天津、上海、重庆
        
        return self.find_by_condition(
            "region_code IN (%s, %s, %s, %s) AND is_active = 1",
            tuple(municipality_codes)
        )
    
    def toggle_active_status(self, region_id: int) -> int:
        """
        切换地区启用状态
        
        Args:
            region_id: 地区ID
            
        Returns:
            影响的行数
        """
        sql = f"UPDATE {self.table_name} SET is_active = NOT is_active WHERE id = %s"
        return self.db.execute_update(sql, (region_id,))
    
    def update_full_name(self, region_id: int) -> int:
        """
        更新地区完整名称
        
        Args:
            region_id: 地区ID
            
        Returns:
            影响的行数
        """
        full_name = self.get_full_name(region_id)
        sql = f"UPDATE {self.table_name} SET full_name = %s WHERE id = %s"
        return self.db.execute_update(sql, (full_name, region_id))
