"""
数据库配置管理

负责数据库连接配置和连接池管理。
"""

import os
from typing import Dict, Any, Optional
from urllib.parse import quote_plus


class DatabaseConfig:
    """数据库配置类"""
    
    def __init__(self):
        """初始化数据库配置"""
        self._config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载数据库配置"""
        return {
            'host': os.getenv('DB_HOST', 'localhost'),
            'port': int(os.getenv('DB_PORT', 3306)),
            'username': os.getenv('DB_USERNAME', 'root'),
            'password': os.getenv('DB_PASSWORD', ''),
            'database': os.getenv('DB_DATABASE', 'huinong_spider'),
            'charset': os.getenv('DB_CHARSET', 'utf8mb4'),
            'pool_size': int(os.getenv('DB_POOL_SIZE', 10)),
            'max_overflow': int(os.getenv('DB_MAX_OVERFLOW', 20)),
            'pool_timeout': int(os.getenv('DB_POOL_TIMEOUT', 30)),
            'pool_recycle': int(os.getenv('DB_POOL_RECYCLE', 3600)),
            'connect_timeout': int(os.getenv('DB_CONNECT_TIMEOUT', 10)),
            'read_timeout': int(os.getenv('DB_READ_TIMEOUT', 30)),
            'write_timeout': int(os.getenv('DB_WRITE_TIMEOUT', 30))
        }
    
    @property
    def host(self) -> str:
        """数据库主机"""
        return self._config['host']
    
    @property
    def port(self) -> int:
        """数据库端口"""
        return self._config['port']
    
    @property
    def username(self) -> str:
        """数据库用户名"""
        return self._config['username']
    
    @property
    def password(self) -> str:
        """数据库密码"""
        return self._config['password']
    
    @property
    def database(self) -> str:
        """数据库名"""
        return self._config['database']
    
    @property
    def charset(self) -> str:
        """字符集"""
        return self._config['charset']
    
    @property
    def pool_size(self) -> int:
        """连接池大小"""
        return self._config['pool_size']
    
    @property
    def max_overflow(self) -> int:
        """最大溢出连接数"""
        return self._config['max_overflow']
    
    @property
    def pool_timeout(self) -> int:
        """连接池超时时间"""
        return self._config['pool_timeout']
    
    @property
    def pool_recycle(self) -> int:
        """连接回收时间"""
        return self._config['pool_recycle']
    
    def get_connection_url(self, driver: str = 'pymysql') -> str:
        """
        获取数据库连接URL
        
        Args:
            driver: 数据库驱动，默认为 pymysql
            
        Returns:
            数据库连接URL
        """
        password = quote_plus(self.password) if self.password else ''
        
        if password:
            auth = f"{self.username}:{password}"
        else:
            auth = self.username
        
        return (f"mysql+{driver}://{auth}@{self.host}:{self.port}/"
                f"{self.database}?charset={self.charset}")
    
    def get_pymysql_config(self) -> Dict[str, Any]:
        """
        获取PyMySQL连接配置
        
        Returns:
            PyMySQL连接配置字典
        """
        return {
            'host': self.host,
            'port': self.port,
            'user': self.username,
            'password': self.password,
            'database': self.database,
            'charset': self.charset,
            'connect_timeout': self._config['connect_timeout'],
            'read_timeout': self._config['read_timeout'],
            'write_timeout': self._config['write_timeout'],
            'autocommit': True,
            'cursorclass': 'DictCursor'  # 使用字典游标
        }
    
    def get_sqlalchemy_config(self) -> Dict[str, Any]:
        """
        获取SQLAlchemy连接配置
        
        Returns:
            SQLAlchemy连接配置字典
        """
        return {
            'url': self.get_connection_url(),
            'pool_size': self.pool_size,
            'max_overflow': self.max_overflow,
            'pool_timeout': self.pool_timeout,
            'pool_recycle': self.pool_recycle,
            'pool_pre_ping': True,  # 连接前检查
            'echo': False  # 不输出SQL语句
        }
    
    def update_config(self, **kwargs):
        """
        更新配置
        
        Args:
            **kwargs: 配置参数
        """
        for key, value in kwargs.items():
            if key in self._config:
                self._config[key] = value
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"DatabaseConfig(host={self.host}, database={self.database})"
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        return self.__str__()
