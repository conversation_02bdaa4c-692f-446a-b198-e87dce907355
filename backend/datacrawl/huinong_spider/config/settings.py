"""
项目配置管理

负责加载和管理项目的各种配置信息。
"""

import os
import yaml
from pathlib import Path
from typing import Dict, List, Any, Optional
from dotenv import load_dotenv

class Settings:
    """项目配置管理类"""
    
    def __init__(self, config_file: Optional[str] = None):
        """
        初始化配置
        
        Args:
            config_file: 配置文件路径，默认为 config/settings.yaml
        """
        # 加载环境变量
        load_dotenv()
        
        # 确定配置文件路径
        if config_file is None:
            project_root = Path(__file__).parent.parent.parent
            config_file = project_root / "config" / "settings.yaml"
        
        self.config_file = Path(config_file)
        self._config = {}
        self._load_config()
    
    def _load_config(self):
        """加载配置文件"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    # 替换环境变量
                    content = self._replace_env_vars(content)
                    self._config = yaml.safe_load(content)
            else:
                print(f"警告: 配置文件 {self.config_file} 不存在，使用默认配置")
                self._config = self._get_default_config()
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            self._config = self._get_default_config()
    
    def _replace_env_vars(self, content: str) -> str:
        """替换配置文件中的环境变量"""
        import re
        
        def replace_var(match):
            var_name = match.group(1)
            default_value = match.group(2) if match.group(2) else ""
            return os.getenv(var_name, default_value)
        
        # 匹配 ${VAR_NAME} 或 ${VAR_NAME:default_value} 格式
        pattern = r'\$\{([^}:]+)(?::([^}]*))?\}'
        return re.sub(pattern, replace_var, content)
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'database': {
                'host': 'localhost',
                'port': 3306,
                'username': 'root',
                'password': '',
                'database': 'huinong_spider',
                'charset': 'utf8mb4'
            },
            'spider': {
                'download_delay': 2,
                'concurrent_requests': 1,
                'retry_times': 3,
                'timeout': 30,
                'user_agents': [
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                ]
            },
            'logging': {
                'level': 'INFO',
                'file': {
                    'enabled': True,
                    'path': 'logs/spider.log'
                }
            }
        }
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键，如 'database.host'
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key.split('.')
        value = self._config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any):
        """
        设置配置值
        
        Args:
            key: 配置键
            value: 配置值
        """
        keys = key.split('.')
        config = self._config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    @property
    def database(self) -> Dict[str, Any]:
        """数据库配置"""
        return self.get('database', {})
    
    @property
    def spider(self) -> Dict[str, Any]:
        """爬虫配置"""
        return self.get('spider', {})
    
    @property
    def logging(self) -> Dict[str, Any]:
        """日志配置"""
        return self.get('logging', {})
    
    @property
    def monitoring(self) -> Dict[str, Any]:
        """监控配置"""
        return self.get('monitoring', {})
    
    @property
    def categories(self) -> List[Dict[str, Any]]:
        """分类配置"""
        return self.get('spider.categories', [])
    
    @property
    def enabled_categories(self) -> List[Dict[str, Any]]:
        """启用的分类"""
        return [cat for cat in self.categories if cat.get('enabled', True)]
    
    def reload(self):
        """重新加载配置"""
        self._load_config()
    
    def save(self, file_path: Optional[str] = None):
        """
        保存配置到文件
        
        Args:
            file_path: 保存路径，默认为当前配置文件路径
        """
        if file_path is None:
            file_path = self.config_file
        
        with open(file_path, 'w', encoding='utf-8') as f:
            yaml.dump(self._config, f, default_flow_style=False, 
                     allow_unicode=True, indent=2)
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"Settings(config_file={self.config_file})"
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        return self.__str__()


# 全局配置实例
settings = Settings()
