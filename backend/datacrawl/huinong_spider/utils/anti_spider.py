#!/usr/bin/env python3
"""
智能反爬虫管理器

实现自动化的反爬虫策略，包括智能退避、自动重试等
"""

import time
import random
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from .logger import get_logger


class AntiSpiderManager:
    """智能反爬虫管理器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.logger = get_logger("anti_spider")
        
        # 默认配置
        default_config = {
            'base_delay': 8.0,           # 基础延迟时间（秒）
            'max_delay': 600.0,          # 最大延迟时间（秒，10分钟）
            'backoff_factor': 2.0,       # 退避因子
            'jitter_range': 0.2,         # 随机抖动范围（20%）
            'max_retries': 5,            # 最大重试次数
            'success_reset_threshold': 10, # 连续成功后重置延迟的阈值
            'error_window_size': 100,    # 错误统计窗口大小
            'error_rate_threshold': 0.3, # 错误率阈值
        }
        
        # 合并配置
        self.config = {**default_config, **(config or {})}
        
        # 状态变量
        self.current_delay = self.config['base_delay']
        self.consecutive_successes = 0
        self.consecutive_errors = 0
        self.last_request_time = 0
        self.error_history = []
        self.request_count = 0
        self.success_count = 0
        
        # 用户代理池
        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15"
        ]
        
        self.logger.info(f"反爬虫管理器初始化完成，基础延迟: {self.current_delay}秒")
    
    def get_random_headers(self) -> Dict[str, str]:
        """获取随机请求头"""
        return {
            'User-Agent': random.choice(self.user_agents),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
        }
    
    def wait_before_request(self):
        """请求前等待"""
        current_time = time.time()
        
        # 计算需要等待的时间
        time_since_last = current_time - self.last_request_time
        delay_with_jitter = self._add_jitter(self.current_delay)
        
        if time_since_last < delay_with_jitter:
            wait_time = delay_with_jitter - time_since_last
            self.logger.debug(f"等待 {wait_time:.2f}秒后发送请求")
            time.sleep(wait_time)
        
        self.last_request_time = time.time()
    
    def _add_jitter(self, delay: float) -> float:
        """添加随机抖动"""
        jitter = delay * self.config['jitter_range']
        return delay + random.uniform(-jitter, jitter)
    
    def record_success(self):
        """记录成功请求"""
        self.request_count += 1
        self.success_count += 1
        self.consecutive_successes += 1
        self.consecutive_errors = 0
        
        # 如果连续成功达到阈值，逐步降低延迟
        if self.consecutive_successes >= self.config['success_reset_threshold']:
            old_delay = self.current_delay
            self.current_delay = max(
                self.config['base_delay'],
                self.current_delay * 0.8  # 降低20%
            )
            
            if old_delay != self.current_delay:
                self.logger.info(f"连续成功 {self.consecutive_successes} 次，降低延迟: {old_delay:.2f}s -> {self.current_delay:.2f}s")
            
            self.consecutive_successes = 0
        
        self.logger.debug(f"记录成功请求，当前延迟: {self.current_delay:.2f}s")
    
    def record_error(self, error_type: str = "unknown", status_code: Optional[int] = None):
        """记录错误请求"""
        self.request_count += 1
        self.consecutive_errors += 1
        self.consecutive_successes = 0
        
        # 记录错误历史
        error_info = {
            'timestamp': datetime.now(),
            'type': error_type,
            'status_code': status_code
        }
        self.error_history.append(error_info)
        
        # 保持错误历史窗口大小
        if len(self.error_history) > self.config['error_window_size']:
            self.error_history.pop(0)
        
        # 根据错误类型调整策略
        if status_code == 503 or error_type == "service_unavailable":
            self._handle_503_error()
        elif status_code in [429, 403] or error_type in ["rate_limit", "forbidden"]:
            self._handle_rate_limit_error()
        else:
            self._handle_general_error()
        
        self.logger.warning(f"记录错误请求: {error_type} (状态码: {status_code})，当前延迟: {self.current_delay:.2f}s")
    
    def _handle_503_error(self):
        """处理503服务不可用错误"""
        old_delay = self.current_delay
        self.current_delay = min(
            self.config['max_delay'],
            self.current_delay * self.config['backoff_factor']
        )
        
        self.logger.warning(f"遇到503错误，增加延迟: {old_delay:.2f}s -> {self.current_delay:.2f}s")
    
    def _handle_rate_limit_error(self):
        """处理限流错误"""
        old_delay = self.current_delay
        self.current_delay = min(
            self.config['max_delay'],
            self.current_delay * (self.config['backoff_factor'] * 1.5)  # 更激进的退避
        )
        
        self.logger.warning(f"遇到限流错误，大幅增加延迟: {old_delay:.2f}s -> {self.current_delay:.2f}s")
    
    def _handle_general_error(self):
        """处理一般错误"""
        # 检查错误率
        error_rate = self.get_recent_error_rate()
        
        if error_rate > self.config['error_rate_threshold']:
            old_delay = self.current_delay
            self.current_delay = min(
                self.config['max_delay'],
                self.current_delay * 1.2  # 轻微增加延迟
            )
            
            self.logger.warning(f"错误率过高 ({error_rate:.2%})，增加延迟: {old_delay:.2f}s -> {self.current_delay:.2f}s")
    
    def get_recent_error_rate(self) -> float:
        """获取最近的错误率"""
        if not self.error_history:
            return 0.0
        
        # 计算最近一段时间的错误率
        recent_window = datetime.now() - timedelta(minutes=10)
        recent_errors = [e for e in self.error_history if e['timestamp'] > recent_window]
        
        if len(recent_errors) == 0:
            return 0.0
        
        # 简化计算：假设最近的请求数等于错误数的2倍（这是一个估算）
        estimated_recent_requests = max(len(recent_errors) * 2, 10)
        return len(recent_errors) / estimated_recent_requests
    
    def should_continue(self) -> bool:
        """判断是否应该继续爬取"""
        # 如果连续错误过多，建议暂停
        if self.consecutive_errors >= self.config['max_retries']:
            self.logger.error(f"连续错误 {self.consecutive_errors} 次，建议暂停爬取")
            return False
        
        # 如果延迟已达到最大值且错误率仍然很高，建议暂停
        if (self.current_delay >= self.config['max_delay'] and 
            self.get_recent_error_rate() > 0.5):
            self.logger.error("延迟已达最大值但错误率仍然很高，建议暂停爬取")
            return False
        
        return True
    
    def get_wait_time_for_recovery(self) -> float:
        """获取恢复等待时间"""
        if self.consecutive_errors <= 1:
            return self.current_delay
        
        # 指数退避
        wait_time = self.config['base_delay'] * (self.config['backoff_factor'] ** self.consecutive_errors)
        return min(wait_time, self.config['max_delay'])
    
    def reset_after_long_pause(self):
        """长时间暂停后重置状态"""
        self.current_delay = self.config['base_delay']
        self.consecutive_errors = 0
        self.consecutive_successes = 0
        self.logger.info("长时间暂停后重置反爬虫状态")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        success_rate = self.success_count / self.request_count if self.request_count > 0 else 0
        recent_error_rate = self.get_recent_error_rate()
        
        return {
            'total_requests': self.request_count,
            'success_count': self.success_count,
            'success_rate': success_rate,
            'recent_error_rate': recent_error_rate,
            'current_delay': self.current_delay,
            'consecutive_successes': self.consecutive_successes,
            'consecutive_errors': self.consecutive_errors,
            'error_history_size': len(self.error_history)
        }
