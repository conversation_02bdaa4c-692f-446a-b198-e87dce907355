"""
辅助工具函数

提供项目中使用的各种辅助函数。
"""

import re
import uuid
import time
import functools
from datetime import datetime, date
from typing import Any, Optional, Union, Callable
from decimal import Decimal, InvalidOperation
from .logger import get_logger

logger = get_logger(__name__)


def clean_price(raw_price: str) -> Optional[float]:
    """
    清洗价格数据
    
    Args:
        raw_price: 原始价格字符串
        
    Returns:
        清洗后的价格，如果无法解析则返回None
    """
    if not raw_price or not isinstance(raw_price, str):
        return None
    
    try:
        # 移除非数字字符（保留小数点）
        price_str = re.sub(r'[^\d.]', '', raw_price.strip())
        
        if not price_str:
            return None
        
        # 处理多个小数点的情况
        if price_str.count('.') > 1:
            # 保留第一个小数点
            parts = price_str.split('.')
            price_str = parts[0] + '.' + ''.join(parts[1:])
        
        # 转换为浮点数
        price = float(price_str)
        
        # 验证价格范围
        if price < 0 or price > 100000:
            logger.warning(f"价格超出合理范围: {price}")
            return None
        
        return round(price, 2)
        
    except (ValueError, InvalidOperation) as e:
        logger.warning(f"价格解析失败: {raw_price}, 错误: {e}")
        return None


def standardize_region(raw_region: str) -> str:
    """
    标准化地区名称
    
    Args:
        raw_region: 原始地区名称
        
    Returns:
        标准化后的地区名称
    """
    if not raw_region or not isinstance(raw_region, str):
        return ""
    
    # 地区名称映射表
    region_mapping = {
        '北京市': '北京',
        '天津市': '天津',
        '上海市': '上海',
        '重庆市': '重庆',
        '内蒙古自治区': '内蒙古',
        '广西壮族自治区': '广西',
        '西藏自治区': '西藏',
        '宁夏回族自治区': '宁夏',
        '新疆维吾尔自治区': '新疆',
        '香港特别行政区': '香港',
        '澳门特别行政区': '澳门'
    }
    
    region = raw_region.strip()
    
    # 应用映射
    if region in region_mapping:
        return region_mapping[region]
    
    # 移除常见后缀
    suffixes = ['省', '市', '县', '区', '自治区', '特别行政区']
    for suffix in suffixes:
        if region.endswith(suffix) and len(region) > len(suffix):
            return region[:-len(suffix)]
    
    return region


def parse_date(date_str: str, formats: Optional[list] = None) -> Optional[date]:
    """
    解析日期字符串
    
    Args:
        date_str: 日期字符串
        formats: 日期格式列表
        
    Returns:
        解析后的日期对象，如果解析失败则返回None
    """
    if not date_str or not isinstance(date_str, str):
        return None
    
    if formats is None:
        formats = [
            '%Y-%m-%d',
            '%Y/%m/%d',
            '%Y.%m.%d',
            '%Y年%m月%d日',
            '%m-%d',
            '%m/%d'
        ]
    
    date_str = date_str.strip()
    
    for fmt in formats:
        try:
            parsed_date = datetime.strptime(date_str, fmt).date()
            
            # 如果只有月日，补充当前年份
            if fmt in ['%m-%d', '%m/%d']:
                current_year = datetime.now().year
                parsed_date = parsed_date.replace(year=current_year)
            
            return parsed_date
            
        except ValueError:
            continue
    
    logger.warning(f"日期解析失败: {date_str}")
    return None


def generate_task_id() -> str:
    """
    生成任务ID
    
    Returns:
        唯一的任务ID
    """
    timestamp = int(time.time() * 1000)
    random_part = str(uuid.uuid4()).replace('-', '')[:8]
    return f"task_{timestamp}_{random_part}"


def retry_on_exception(
    max_retries: int = 3,
    delay: float = 1.0,
    backoff: float = 2.0,
    exceptions: tuple = (Exception,)
) -> Callable:
    """
    重试装饰器
    
    Args:
        max_retries: 最大重试次数
        delay: 初始延迟时间（秒）
        backoff: 延迟倍数
        exceptions: 需要重试的异常类型
        
    Returns:
        装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            current_delay = delay
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    if attempt == max_retries:
                        logger.error(f"函数 {func.__name__} 重试 {max_retries} 次后仍然失败: {e}")
                        raise
                    
                    logger.warning(f"函数 {func.__name__} 第 {attempt + 1} 次执行失败: {e}, {current_delay:.1f}秒后重试")
                    time.sleep(current_delay)
                    current_delay *= backoff
            
        return wrapper
    return decorator


def validate_required_fields(data: dict, required_fields: list) -> tuple[bool, list]:
    """
    验证必填字段
    
    Args:
        data: 数据字典
        required_fields: 必填字段列表
        
    Returns:
        (是否验证通过, 缺失字段列表)
    """
    missing_fields = []
    
    for field in required_fields:
        if field not in data or data[field] is None or data[field] == '':
            missing_fields.append(field)
    
    return len(missing_fields) == 0, missing_fields


def safe_get(data: dict, key: str, default: Any = None) -> Any:
    """
    安全获取字典值
    
    Args:
        data: 数据字典
        key: 键名，支持点号分隔的嵌套键
        default: 默认值
        
    Returns:
        字典值或默认值
    """
    if not isinstance(data, dict):
        return default
    
    keys = key.split('.')
    value = data
    
    try:
        for k in keys:
            value = value[k]
        return value
    except (KeyError, TypeError):
        return default


def format_file_size(size_bytes: int) -> str:
    """
    格式化文件大小
    
    Args:
        size_bytes: 字节数
        
    Returns:
        格式化后的文件大小字符串
    """
    if size_bytes == 0:
        return "0B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f}{size_names[i]}"


def truncate_string(text: str, max_length: int, suffix: str = "...") -> str:
    """
    截断字符串
    
    Args:
        text: 原始字符串
        max_length: 最大长度
        suffix: 后缀
        
    Returns:
        截断后的字符串
    """
    if not text or len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix


def normalize_whitespace(text: str) -> str:
    """
    标准化空白字符
    
    Args:
        text: 原始文本
        
    Returns:
        标准化后的文本
    """
    if not text:
        return ""
    
    # 替换所有空白字符为单个空格
    return re.sub(r'\s+', ' ', text.strip())


def extract_numbers(text: str) -> list:
    """
    从文本中提取数字
    
    Args:
        text: 文本
        
    Returns:
        数字列表
    """
    if not text:
        return []
    
    # 匹配整数和小数
    pattern = r'-?\d+\.?\d*'
    matches = re.findall(pattern, text)
    
    numbers = []
    for match in matches:
        try:
            if '.' in match:
                numbers.append(float(match))
            else:
                numbers.append(int(match))
        except ValueError:
            continue
    
    return numbers


def is_valid_url(url: str) -> bool:
    """
    验证URL是否有效
    
    Args:
        url: URL字符串
        
    Returns:
        是否有效
    """
    if not url or not isinstance(url, str):
        return False
    
    url_pattern = re.compile(
        r'^https?://'  # http:// or https://
        r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
        r'localhost|'  # localhost...
        r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
        r'(?::\d+)?'  # optional port
        r'(?:/?|[/?]\S+)$', re.IGNORECASE)
    
    return url_pattern.match(url) is not None


def get_current_timestamp() -> int:
    """
    获取当前时间戳（毫秒）
    
    Returns:
        当前时间戳
    """
    return int(time.time() * 1000)


def format_duration(seconds: float) -> str:
    """
    格式化持续时间
    
    Args:
        seconds: 秒数
        
    Returns:
        格式化后的时间字符串
    """
    if seconds < 60:
        return f"{seconds:.1f}秒"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f}分钟"
    else:
        hours = seconds / 3600
        return f"{hours:.1f}小时"
