"""
数据库管理模块

提供数据库连接、操作和管理功能。
"""

import pymysql
from typing import Dict, List, Any, Optional, Union, Tuple
from contextlib import contextmanager
from threading import Lock
import time
from ..config.database import DatabaseConfig
from .logger import get_logger

logger = get_logger(__name__)


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, config: Optional[DatabaseConfig] = None):
        """
        初始化数据库管理器
        
        Args:
            config: 数据库配置，如果为None则使用默认配置
        """
        self.config = config or DatabaseConfig()
        self._connection_pool = []
        self._pool_lock = Lock()
        self._max_connections = self.config.pool_size
        self._current_connections = 0
        
        # 测试连接
        self._test_connection()
    
    def _test_connection(self):
        """测试数据库连接"""
        try:
            conn = self._create_connection()
            conn.close()
            logger.info("数据库连接测试成功")
        except Exception as e:
            logger.error(f"数据库连接测试失败: {e}")
            raise
    
    def _create_connection(self):
        """创建数据库连接"""
        config = self.config.get_pymysql_config()
        # 设置游标类型为字典游标
        config['cursorclass'] = pymysql.cursors.DictCursor
        return pymysql.connect(**config)
    
    @contextmanager
    def get_connection(self):
        """
        获取数据库连接上下文管理器
        
        Yields:
            数据库连接对象
        """
        conn = None
        try:
            # 从连接池获取连接
            with self._pool_lock:
                if self._connection_pool:
                    conn = self._connection_pool.pop()
                elif self._current_connections < self._max_connections:
                    conn = self._create_connection()
                    self._current_connections += 1
                else:
                    # 等待连接可用
                    time.sleep(0.1)
                    conn = self._create_connection()
            
            # 检查连接是否有效
            if not self._is_connection_alive(conn):
                conn = self._create_connection()
            
            yield conn
            
        except Exception as e:
            logger.error(f"数据库连接错误: {e}")
            if conn:
                conn.rollback()
            raise
        finally:
            # 归还连接到池中
            if conn:
                try:
                    conn.commit()
                    with self._pool_lock:
                        if len(self._connection_pool) < self._max_connections:
                            self._connection_pool.append(conn)
                        else:
                            conn.close()
                            self._current_connections -= 1
                except:
                    conn.close()
                    self._current_connections -= 1
    
    def _is_connection_alive(self, conn) -> bool:
        """检查连接是否有效"""
        try:
            conn.ping(reconnect=False)
            return True
        except:
            return False
    
    def execute_query(self, sql: str, params: Optional[Tuple] = None) -> List[Dict[str, Any]]:
        """
        执行查询语句
        
        Args:
            sql: SQL查询语句
            params: 查询参数
            
        Returns:
            查询结果列表
        """
        with self.get_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(sql, params)
                return cursor.fetchall()
    
    def execute_insert(self, sql: str, params: Optional[Tuple] = None) -> int:
        """
        执行插入语句
        
        Args:
            sql: SQL插入语句
            params: 插入参数
            
        Returns:
            插入的记录ID
        """
        with self.get_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(sql, params)
                return cursor.lastrowid
    
    def execute_update(self, sql: str, params: Optional[Tuple] = None) -> int:
        """
        执行更新语句
        
        Args:
            sql: SQL更新语句
            params: 更新参数
            
        Returns:
            影响的行数
        """
        with self.get_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(sql, params)
                return cursor.rowcount
    
    def execute_delete(self, sql: str, params: Optional[Tuple] = None) -> int:
        """
        执行删除语句
        
        Args:
            sql: SQL删除语句
            params: 删除参数
            
        Returns:
            删除的行数
        """
        with self.get_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(sql, params)
                return cursor.rowcount
    
    def execute_batch(self, sql: str, params_list: List[Tuple]) -> int:
        """
        批量执行语句
        
        Args:
            sql: SQL语句
            params_list: 参数列表
            
        Returns:
            影响的总行数
        """
        with self.get_connection() as conn:
            with conn.cursor() as cursor:
                cursor.executemany(sql, params_list)
                return cursor.rowcount
    
    def insert_or_update(self, table: str, data: Dict[str, Any], 
                        unique_keys: List[str]) -> int:
        """
        插入或更新数据
        
        Args:
            table: 表名
            data: 数据字典
            unique_keys: 唯一键列表
            
        Returns:
            影响的行数
        """
        # 构建INSERT ... ON DUPLICATE KEY UPDATE语句
        columns = list(data.keys())
        placeholders = ', '.join(['%s'] * len(columns))
        
        # 构建更新部分
        update_parts = []
        for col in columns:
            if col not in unique_keys:
                update_parts.append(f"{col} = VALUES({col})")
        
        sql = f"""
        INSERT INTO {table} ({', '.join(columns)})
        VALUES ({placeholders})
        ON DUPLICATE KEY UPDATE {', '.join(update_parts)}
        """
        
        params = tuple(data.values())
        return self.execute_update(sql, params)
    
    def get_table_info(self, table_name: str) -> List[Dict[str, Any]]:
        """
        获取表结构信息
        
        Args:
            table_name: 表名
            
        Returns:
            表结构信息
        """
        sql = "DESCRIBE %s" % table_name
        return self.execute_query(sql)
    
    def table_exists(self, table_name: str) -> bool:
        """
        检查表是否存在
        
        Args:
            table_name: 表名
            
        Returns:
            表是否存在
        """
        sql = """
        SELECT COUNT(*) as count
        FROM information_schema.tables 
        WHERE table_schema = %s AND table_name = %s
        """
        result = self.execute_query(sql, (self.config.database, table_name))
        return result[0]['count'] > 0
    
    def close_all_connections(self):
        """关闭所有连接"""
        with self._pool_lock:
            for conn in self._connection_pool:
                try:
                    conn.close()
                except:
                    pass
            self._connection_pool.clear()
            self._current_connections = 0
        
        logger.info("所有数据库连接已关闭")
    
    def get_connection_stats(self) -> Dict[str, int]:
        """
        获取连接池统计信息
        
        Returns:
            连接池统计信息
        """
        with self._pool_lock:
            return {
                'pool_size': len(self._connection_pool),
                'current_connections': self._current_connections,
                'max_connections': self._max_connections
            }
    
    def __del__(self):
        """析构函数"""
        self.close_all_connections()


# 全局数据库管理器实例
_db_manager = None
_db_lock = Lock()


def get_db_manager() -> DatabaseManager:
    """
    获取全局数据库管理器实例
    
    Returns:
        数据库管理器实例
    """
    global _db_manager
    
    if _db_manager is None:
        with _db_lock:
            if _db_manager is None:
                _db_manager = DatabaseManager()
    
    return _db_manager


def close_db_manager():
    """关闭全局数据库管理器"""
    global _db_manager
    
    if _db_manager:
        _db_manager.close_all_connections()
        _db_manager = None
