"""
日志管理模块

提供统一的日志记录功能。
"""

import os
import sys
from pathlib import Path
from typing import Optional
from loguru import logger


def setup_logging(
    log_level: str = "INFO",
    log_file: Optional[str] = None,
    rotation: str = "1 day",
    retention: str = "30 days",
    compression: str = "gz",
    format_string: Optional[str] = None
):
    """
    设置日志配置
    
    Args:
        log_level: 日志级别
        log_file: 日志文件路径
        rotation: 日志轮转规则
        retention: 日志保留时间
        compression: 压缩格式
        format_string: 日志格式字符串
    """
    # 移除默认处理器
    logger.remove()
    
    # 默认格式
    if format_string is None:
        format_string = (
            "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
            "<level>{level: <8}</level> | "
            "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - "
            "<level>{message}</level>"
        )
    
    # 添加控制台处理器
    logger.add(
        sys.stdout,
        level=log_level,
        format=format_string,
        colorize=True,
        backtrace=True,
        diagnose=True
    )
    
    # 添加文件处理器
    if log_file:
        # 确保日志目录存在
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        logger.add(
            log_file,
            level=log_level,
            format=format_string,
            rotation=rotation,
            retention=retention,
            compression=compression,
            encoding="utf-8",
            backtrace=True,
            diagnose=True
        )


def get_logger(name: str = None) -> logger:
    """
    获取日志器
    
    Args:
        name: 日志器名称
        
    Returns:
        日志器实例
    """
    if name:
        return logger.bind(name=name)
    return logger


# 初始化日志配置
def init_logging():
    """初始化日志配置"""
    # 从环境变量或配置文件获取日志配置
    log_level = os.getenv('LOG_LEVEL', 'INFO')
    log_file = os.getenv('LOG_FILE_PATH', 'logs/spider.log')
    
    setup_logging(
        log_level=log_level,
        log_file=log_file
    )


# 自动初始化
init_logging()


class LoggerMixin:
    """日志混入类"""
    
    @property
    def logger(self):
        """获取当前类的日志器"""
        return get_logger(self.__class__.__name__)


def log_function_call(func):
    """
    函数调用日志装饰器
    
    Args:
        func: 被装饰的函数
        
    Returns:
        装饰后的函数
    """
    def wrapper(*args, **kwargs):
        func_logger = get_logger(func.__module__)
        func_logger.debug(f"调用函数 {func.__name__}, args={args}, kwargs={kwargs}")
        
        try:
            result = func(*args, **kwargs)
            func_logger.debug(f"函数 {func.__name__} 执行成功")
            return result
        except Exception as e:
            func_logger.error(f"函数 {func.__name__} 执行失败: {e}")
            raise
    
    return wrapper


def log_execution_time(func):
    """
    执行时间日志装饰器
    
    Args:
        func: 被装饰的函数
        
    Returns:
        装饰后的函数
    """
    import time
    
    def wrapper(*args, **kwargs):
        func_logger = get_logger(func.__module__)
        start_time = time.time()
        
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            func_logger.info(f"函数 {func.__name__} 执行时间: {execution_time:.2f}秒")
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            func_logger.error(f"函数 {func.__name__} 执行失败 (耗时: {execution_time:.2f}秒): {e}")
            raise
    
    return wrapper


class ContextLogger:
    """上下文日志管理器"""
    
    def __init__(self, logger_name: str, message: str, level: str = "INFO"):
        """
        初始化上下文日志管理器
        
        Args:
            logger_name: 日志器名称
            message: 日志消息
            level: 日志级别
        """
        self.logger = get_logger(logger_name)
        self.message = message
        self.level = level.upper()
        self.start_time = None
    
    def __enter__(self):
        """进入上下文"""
        import time
        self.start_time = time.time()
        getattr(self.logger, self.level.lower())(f"开始 {self.message}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出上下文"""
        import time
        execution_time = time.time() - self.start_time
        
        if exc_type is None:
            getattr(self.logger, self.level.lower())(
                f"完成 {self.message} (耗时: {execution_time:.2f}秒)"
            )
        else:
            self.logger.error(
                f"失败 {self.message} (耗时: {execution_time:.2f}秒): {exc_val}"
            )


# 导出主要接口
__all__ = [
    'setup_logging',
    'get_logger',
    'init_logging',
    'LoggerMixin',
    'log_function_call',
    'log_execution_time',
    'ContextLogger'
]
