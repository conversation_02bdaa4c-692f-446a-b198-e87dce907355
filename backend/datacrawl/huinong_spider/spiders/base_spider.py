"""
基础爬虫类

提供爬虫的基础功能和通用方法。
"""

import time
import random
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Generator
from datetime import datetime, date

import requests
from bs4 import BeautifulSoup

from ..config.settings import settings
from ..utils.logger import get_logger, LoggerMixin
from ..utils.helpers import (
    clean_price, 
    standardize_region, 
    parse_date, 
    generate_task_id,
    retry_on_exception
)
from ..models import (
    Category, CategoryDAO,
    Region, RegionDAO,
    Product, ProductDAO,
    PriceData, PriceDataDAO,
    CrawlLog, CrawlLogDAO, CrawlStatus
)


class BaseSpider(LoggerMixin, ABC):
    """基础爬虫类"""
    
    def __init__(self, name: str = "base_spider"):
        """
        初始化爬虫
        
        Args:
            name: 爬虫名称
        """
        self.name = name
        self.task_id = generate_task_id()
        self.start_time = datetime.now()
        
        # 配置信息
        self.config = settings.spider
        self.download_delay = self.config.get('download_delay', 2)
        self.retry_times = self.config.get('retry_times', 3)
        self.timeout = self.config.get('timeout', 30)
        self.user_agents = self.config.get('user_agents', [])
        
        # 统计信息
        self.stats = {
            'total_requests': 0,
            'success_requests': 0,
            'failed_requests': 0,
            'items_scraped': 0,
            'items_saved': 0
        }
        
        # 数据访问对象
        self.category_dao = CategoryDAO()
        self.region_dao = RegionDAO()
        self.product_dao = ProductDAO()
        self.price_data_dao = PriceDataDAO()
        self.crawl_log_dao = CrawlLogDAO()
        
        # 创建爬取日志
        self._create_crawl_log()
        
        # 初始化会话
        self.session = self._create_session()
        
        self.logger.info(f"爬虫 {self.name} 初始化完成，任务ID: {self.task_id}")
    
    def _create_session(self) -> requests.Session:
        """创建HTTP会话"""
        session = requests.Session()
        
        # 设置默认请求头
        session.headers.update({
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
        
        return session
    
    def _create_crawl_log(self):
        """创建爬取日志"""
        crawl_log = CrawlLog(
            task_id=self.task_id,
            spider_name=self.name,
            start_time=self.start_time,
            status=CrawlStatus.RUNNING.value
        )
        
        self.crawl_log_dao.insert(crawl_log)
    
    def _get_random_user_agent(self) -> str:
        """获取随机User-Agent"""
        if self.user_agents:
            return random.choice(self.user_agents)
        
        return "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    
    @retry_on_exception(max_retries=3, delay=1.0, backoff=2.0)
    def fetch_page(self, url: str, **kwargs) -> Optional[requests.Response]:
        """
        获取网页内容
        
        Args:
            url: 目标URL
            **kwargs: 请求参数
            
        Returns:
            响应对象或None
        """
        try:
            # 更新统计
            self.stats['total_requests'] += 1
            
            # 设置随机User-Agent
            headers = kwargs.get('headers', {})
            if 'User-Agent' not in headers:
                headers['User-Agent'] = self._get_random_user_agent()
                kwargs['headers'] = headers
            
            # 设置超时
            kwargs.setdefault('timeout', self.timeout)
            
            self.logger.debug(f"请求URL: {url}")
            
            # 发送请求
            response = self.session.get(url, **kwargs)
            response.raise_for_status()
            
            # 更新统计
            self.stats['success_requests'] += 1
            
            self.logger.debug(f"请求成功: {url}, 状态码: {response.status_code}")
            
            # 延迟
            if self.download_delay > 0:
                delay = self.download_delay + random.uniform(-0.5, 0.5)
                time.sleep(max(0, delay))
            
            return response
            
        except Exception as e:
            self.stats['failed_requests'] += 1
            self.logger.error(f"请求失败: {url}, 错误: {e}")
            raise
    
    def parse_html(self, html: str, parser: str = 'html.parser') -> BeautifulSoup:
        """
        解析HTML内容
        
        Args:
            html: HTML字符串
            parser: 解析器
            
        Returns:
            BeautifulSoup对象
        """
        return BeautifulSoup(html, parser)
    
    def extract_text(self, element, default: str = "") -> str:
        """
        提取元素文本
        
        Args:
            element: BeautifulSoup元素
            default: 默认值
            
        Returns:
            文本内容
        """
        if element:
            return element.get_text(strip=True)
        return default
    
    def extract_attr(self, element, attr: str, default: str = "") -> str:
        """
        提取元素属性
        
        Args:
            element: BeautifulSoup元素
            attr: 属性名
            default: 默认值
            
        Returns:
            属性值
        """
        if element:
            return element.get(attr, default)
        return default
    
    def save_price_data(self, price_data_list: List[PriceData]) -> int:
        """
        保存价格数据
        
        Args:
            price_data_list: 价格数据列表
            
        Returns:
            保存的数据量
        """
        if not price_data_list:
            return 0
        
        try:
            saved_count = self.price_data_dao.batch_insert_prices(price_data_list)
            self.stats['items_saved'] += saved_count
            self.logger.info(f"保存价格数据 {saved_count} 条")
            return saved_count
            
        except Exception as e:
            self.logger.error(f"保存价格数据失败: {e}")
            return 0
    
    def update_progress(self, **kwargs):
        """更新爬取进度"""
        # 更新统计信息
        self.stats.update(kwargs)
        
        # 更新数据库日志
        self.crawl_log_dao.update_progress(self.task_id, **self.stats)
    
    def finish_crawl(self, status: CrawlStatus = CrawlStatus.SUCCESS, 
                    error_message: str = ""):
        """
        完成爬取
        
        Args:
            status: 最终状态
            error_message: 错误信息
        """
        end_time = datetime.now()
        duration = (end_time - self.start_time).total_seconds()
        
        # 更新日志
        self.crawl_log_dao.finish_task(
            self.task_id,
            status,
            self.stats['items_saved'],
            error_message
        )
        
        # 关闭会话
        self.session.close()
        
        self.logger.info(f"爬虫 {self.name} 完成，状态: {status.value}, 耗时: {duration:.2f}秒")
        self.logger.info(f"统计信息: {self.stats}")
    
    @abstractmethod
    def run(self, **kwargs) -> bool:
        """
        运行爬虫
        
        Args:
            **kwargs: 运行参数
            
        Returns:
            是否成功
        """
        pass
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        if exc_type is None:
            self.finish_crawl(CrawlStatus.SUCCESS)
        else:
            error_message = str(exc_val) if exc_val else "未知错误"
            self.finish_crawl(CrawlStatus.FAILED, error_message)
    
    def __del__(self):
        """析构函数"""
        if hasattr(self, 'session'):
            self.session.close()
