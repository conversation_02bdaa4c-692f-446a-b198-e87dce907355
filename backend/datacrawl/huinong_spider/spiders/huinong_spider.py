"""
惠农网爬虫

专门用于爬取惠农网价格数据的爬虫实现。
"""

import re
import time
from typing import List, Dict, Any, Optional, Generator
from urllib.parse import urljoin
from datetime import date, datetime

from .base_spider import BaseSpider
from ..models import Category, Region, Product, PriceData
from ..utils.helpers import clean_price, standardize_region, parse_date
from ..utils.anti_spider import AntiSpiderManager


class HuinongSpider(BaseSpider):
    """惠农网爬虫"""
    
    def __init__(self):
        super().__init__("huinong_spider")

        # 惠农网相关配置
        self.base_url = "https://www.cnhnb.com/hangqing/"
        self.domain = "www.cnhnb.com"

        # URL模板
        self.category_url_template = self.base_url + "{pinyin}/"
        self.list_url_template = self.base_url + "cdlist-{category_id}-0-{region_id}-0-0-{page}/"

        # 初始化智能反爬虫管理器
        anti_spider_config = {
            'base_delay': 15.0,  # 使用保守的15秒延迟
            'max_delay': 600.0,  # 10分钟
            'backoff_factor': 2.0,
            'max_retries': 5,
        }
        self.anti_spider = AntiSpiderManager(anti_spider_config)

    def fetch_page(self, url: str, **kwargs):
        """
        重写fetch_page方法以使用智能反爬虫管理器

        Args:
            url: 目标URL
            **kwargs: 请求参数

        Returns:
            响应对象或None
        """
        # 检查是否应该继续爬取
        if not self.anti_spider.should_continue():
            wait_time = self.anti_spider.get_wait_time_for_recovery()
            self.logger.warning(f"反爬虫管理器建议暂停，等待 {wait_time:.2f}秒后重试")
            time.sleep(wait_time)
            self.anti_spider.reset_after_long_pause()

        # 请求前等待
        self.anti_spider.wait_before_request()

        try:
            # 更新统计
            self.stats['total_requests'] += 1

            # 使用反爬虫管理器的随机请求头
            headers = kwargs.get('headers', {})
            anti_spider_headers = self.anti_spider.get_random_headers()
            headers.update(anti_spider_headers)
            kwargs['headers'] = headers

            # 设置超时
            kwargs.setdefault('timeout', self.timeout)

            self.logger.debug(f"请求URL: {url}")

            # 发送请求
            response = self.session.get(url, **kwargs)
            response.raise_for_status()

            # 记录成功
            self.anti_spider.record_success()
            self.stats['success_requests'] += 1

            self.logger.debug(f"请求成功: {url}, 状态码: {response.status_code}")

            return response

        except Exception as e:
            self.stats['failed_requests'] += 1

            # 记录错误到反爬虫管理器
            error_type = "unknown"
            status_code = None

            if hasattr(e, 'response') and e.response is not None:
                status_code = e.response.status_code
                if status_code == 503:
                    error_type = "service_unavailable"
                elif status_code == 429:
                    error_type = "rate_limit"
                elif status_code == 403:
                    error_type = "forbidden"

            self.anti_spider.record_error(error_type, status_code)

            self.logger.error(f"请求失败: {url}, 错误: {e}")
            raise
        
        # 分类映射
        self.category_mapping = {
            '水果': {'pinyin': 'sgzw', 'category_id': 2003191},
            '蔬菜': {'pinyin': 'sczw', 'category_id': 2003192},
            '禽畜肉蛋': {'pinyin': 'qcrd', 'category_id': 2003193},
            '水产': {'pinyin': 'shuic', 'category_id': 2003194},
            '农副加工': {'pinyin': 'nfjg', 'category_id': 2003195},
            '粮油米面': {'pinyin': 'lymm', 'category_id': 2003196},
            '种子种苗': {'pinyin': 'zzzm', 'category_id': 2003197},
            '苗木花草': {'pinyin': 'mmhc', 'category_id': 2003198},
            '农资农机': {'pinyin': 'nznj', 'category_id': 2003199},
            '中药材': {'pinyin': 'zyc', 'category_id': 2003200}
        }
    
    def run(self, categories: Optional[List[str]] = None, 
           regions: Optional[List[str]] = None,
           max_pages: int = 0) -> bool:
        """
        运行爬虫
        
        Args:
            categories: 要爬取的分类列表，None表示爬取所有启用的分类
            regions: 要爬取的地区列表，None表示爬取所有地区
            max_pages: 最大页数，0表示不限制
            
        Returns:
            是否成功
        """
        try:
            self.logger.info(f"开始爬取惠农网数据，分类: {categories}, 地区: {regions}")
            
            # 获取要爬取的分类
            target_categories = self._get_target_categories(categories)
            if not target_categories:
                self.logger.warning("没有找到要爬取的分类")
                return False
            
            # 获取要爬取的地区
            target_regions = self._get_target_regions(regions)
            if not target_regions:
                self.logger.warning("没有找到要爬取的地区")
                return False
            
            self.logger.info(f"将爬取 {len(target_categories)} 个分类，{len(target_regions)} 个地区")
            
            # 开始爬取
            total_items = 0
            for category in target_categories:
                for region in target_regions:
                    items = self._crawl_category_region(category, region, max_pages)
                    total_items += items
                    
                    # 更新进度
                    self.update_progress(items_scraped=total_items)
            
            self.logger.info(f"爬取完成，共采集 {total_items} 条数据")
            return True
            
        except Exception as e:
            self.logger.error(f"爬取过程中发生错误: {e}")
            return False
    
    def _get_target_categories(self, categories: Optional[List[str]]) -> List[Category]:
        """获取目标分类列表"""
        if categories:
            # 根据名称查找分类
            target_categories = []
            for cat_name in categories:
                category = self.category_dao.find_by_name(cat_name)
                if category:
                    target_categories.append(category)
                else:
                    self.logger.warning(f"未找到分类: {cat_name}")
            return target_categories
        else:
            # 获取所有启用的分类
            return self.category_dao.find_active_categories()
    
    def _get_target_regions(self, regions: Optional[List[str]]) -> List[Region]:
        """获取目标地区列表"""
        if regions:
            # 根据名称查找地区
            target_regions = []
            for region_name in regions:
                region = self.region_dao.find_by_name(region_name)
                if region:
                    target_regions.append(region)
                else:
                    self.logger.warning(f"未找到地区: {region_name}")
            return target_regions
        else:
            # 获取所有省级地区
            return self.region_dao.find_provinces()
    
    def _crawl_category_region(self, category: Category, region: Region, 
                              max_pages: int = 0) -> int:
        """
        爬取特定分类和地区的数据
        
        Args:
            category: 分类对象
            region: 地区对象
            max_pages: 最大页数
            
        Returns:
            采集的数据量
        """
        self.logger.info(f"开始爬取分类: {category.name}, 地区: {region.name}")
        
        total_items = 0
        page = 1
        
        while True:
            # 构建URL
            url = self.list_url_template.format(
                category_id=category.category_id,
                region_id=region.region_code,
                page=page
            )
            
            self.logger.debug(f"爬取第 {page} 页: {url}")
            
            # 获取页面
            response = self.fetch_page(url)
            if not response:
                break
            
            # 解析页面
            soup = self.parse_html(response.text)
            
            # 提取数据
            items = self._extract_price_data(soup, category, region)
            if not items:
                self.logger.info(f"第 {page} 页没有数据，停止爬取")
                break
            
            # 保存数据
            saved_count = self.save_price_data(items)
            total_items += saved_count
            
            self.logger.info(f"第 {page} 页采集 {len(items)} 条数据，保存 {saved_count} 条")
            
            # 检查是否达到最大页数
            if max_pages > 0 and page >= max_pages:
                self.logger.info(f"达到最大页数 {max_pages}，停止爬取")
                break
            
            # 检查是否还有下一页
            if not self._has_next_page(soup):
                self.logger.info("没有下一页，停止爬取")
                break
            
            page += 1
        
        self.logger.info(f"分类 {category.name} 地区 {region.name} 爬取完成，共 {total_items} 条数据")
        return total_items
    
    def _extract_price_data(self, soup, category: Category, region: Region) -> List[PriceData]:
        """
        从页面中提取价格数据

        Args:
            soup: BeautifulSoup对象
            category: 分类对象
            region: 地区对象

        Returns:
            价格数据列表
        """
        price_data_list = []

        # 查找数据容器 - 新的页面结构使用div而不是table
        quotation_content = soup.find('div', class_='quotation-content-list')
        if not quotation_content:
            self.logger.warning("未找到数据容器 (quotation-content-list)")
            return price_data_list

        # 查找数据列表项
        items = quotation_content.find_all('li', class_='market-list-item')
        if not items:
            self.logger.warning("未找到数据列表项")
            return price_data_list

        self.logger.info(f"找到 {len(items)} 条数据记录")

        for item in items:
            try:
                price_data = self._parse_price_item(item, category, region)
                if price_data:
                    price_data_list.append(price_data)
                    self.stats['items_scraped'] += 1

            except Exception as e:
                self.logger.warning(f"解析价格项失败: {e}")
                continue

        return price_data_list
    
    def _parse_price_item(self, item, category: Category, region: Region) -> Optional[PriceData]:
        """
        解析价格数据项

        Args:
            item: 列表项元素 (li.market-list-item)
            category: 分类对象
            region: 地区对象

        Returns:
            价格数据对象或None
        """
        try:
            # 提取数据 - 新的页面结构使用span元素
            date_span = item.find('span', class_='time')
            product_span = item.find('span', class_='product')
            place_span = item.find('span', class_='place')
            price_span = item.find('span', class_='price')
            lifting_span = item.find('span', class_='lifting')

            if not all([date_span, product_span, place_span, price_span]):
                self.logger.warning("数据项缺少必要字段")
                return None

            # 提取文本内容
            date_text = self.extract_text(date_span)
            product_text = self.extract_text(product_span)
            location_text = self.extract_text(place_span)
            price_text = self.extract_text(price_span)
            change_text = self.extract_text(lifting_span) if lifting_span else ""

            self.logger.debug(f"解析数据: {date_text} | {product_text} | {location_text} | {price_text} | {change_text}")

            # 解析日期
            data_date = parse_date(date_text)
            if not data_date:
                data_date = date.today()

            # 解析价格
            price = clean_price(price_text)
            if price is None:
                self.logger.warning(f"价格解析失败: {price_text}")
                return None

            # 查找或创建产品
            product = self._find_or_create_product(product_text, category)
            if not product:
                return None

            # 解析价格变化趋势
            price_trend = self._parse_price_trend(change_text)

            # 创建价格数据对象
            price_data = PriceData(
                product_id=product.id,
                region_id=region.id,
                price=price,
                unit="斤",  # 默认单位
                price_change=change_text,
                price_trend=price_trend,
                source_url="",  # 当前页面URL
                source_id=f"{product.id}_{region.id}_{data_date}",
                data_date=data_date
            )

            return price_data

        except Exception as e:
            self.logger.warning(f"解析价格项数据失败: {e}")
            return None
    
    def _find_or_create_product(self, product_name: str, category: Category) -> Optional[Product]:
        """
        查找或创建产品
        
        Args:
            product_name: 产品名称
            category: 分类对象
            
        Returns:
            产品对象或None
        """
        if not product_name:
            return None
        
        # 清理产品名称
        product_name = product_name.strip()
        
        # 先尝试查找现有产品
        product = self.product_dao.find_by_name(product_name)
        if product:
            return product
        
        # 创建新产品
        try:
            product = Product(
                name=product_name,
                category_id=category.id,
                unit="斤"
            )
            
            product_id = self.product_dao.insert(product)
            product.id = product_id
            
            self.logger.debug(f"创建新产品: {product_name}")
            return product
            
        except Exception as e:
            self.logger.error(f"创建产品失败: {product_name}, 错误: {e}")
            return None
    
    def _parse_price_trend(self, change_text: str) -> Optional[int]:
        """
        解析价格趋势
        
        Args:
            change_text: 价格变化文本
            
        Returns:
            价格趋势 (1上涨, 0持平, -1下跌)
        """
        if not change_text:
            return None
        
        change_text = change_text.strip()
        
        if '↑' in change_text or '上涨' in change_text or '+' in change_text:
            return 1
        elif '↓' in change_text or '下跌' in change_text or '-' in change_text:
            return -1
        elif '持平' in change_text or change_text == '-':
            return 0
        
        return None
    
    def _has_next_page(self, soup) -> bool:
        """
        检查是否有下一页
        
        Args:
            soup: BeautifulSoup对象
            
        Returns:
            是否有下一页
        """
        # 查找分页导航
        pagination = soup.find('div', class_='pagination') or soup.find('ul', class_='pagination')
        if not pagination:
            return False
        
        # 查找"下一页"链接
        next_link = pagination.find('a', string=re.compile(r'下一页|>|next', re.I))
        return next_link is not None
    
    def crawl_category(self, category_name: str, max_pages: int = 0) -> int:
        """
        爬取特定分类的数据
        
        Args:
            category_name: 分类名称
            max_pages: 最大页数
            
        Returns:
            采集的数据量
        """
        return self.run(categories=[category_name], max_pages=max_pages)
    
    def crawl_region(self, region_name: str, max_pages: int = 0) -> int:
        """
        爬取特定地区的数据

        Args:
            region_name: 地区名称
            max_pages: 最大页数

        Returns:
            采集的数据量
        """
        return self.run(regions=[region_name], max_pages=max_pages)

    def crawl_category_region(self, category: Category, region: Region, max_pages: int = 0) -> int:
        """
        爬取特定分类和地区的数据（公共方法）

        Args:
            category: 分类对象
            region: 地区对象
            max_pages: 最大页数，0表示不限制

        Returns:
            采集的数据量
        """
        return self._crawl_category_region(category, region, max_pages)
