# 惠农网价格数据爬虫项目依赖包
# 更新时间: 2025-07-24

# 核心爬虫框架
scrapy>=2.5.0,<3.0.0
requests>=2.28.0,<3.0.0
beautifulsoup4>=4.11.0,<5.0.0
lxml>=4.9.0,<5.0.0

# 数据库相关
PyMySQL>=1.0.0,<2.0.0
SQLAlchemy>=1.4.0,<2.0.0
alembic>=1.8.0,<2.0.0

# 数据处理
pandas>=1.5.0,<2.0.0
numpy>=1.21.0,<2.0.0
openpyxl>=3.0.0,<4.0.0

# 时间处理
arrow>=1.2.0,<2.0.0
python-dateutil>=2.8.0,<3.0.0

# 配置管理
PyYAML>=6.0,<7.0
python-dotenv>=0.20.0,<1.0.0
configparser>=5.3.0,<6.0.0

# 日志处理
loguru>=0.6.0,<1.0.0

# 任务调度
APScheduler>=3.9.0,<4.0.0

# HTTP客户端增强
httpx>=0.24.0,<1.0.0
aiohttp>=3.8.0,<4.0.0

# 数据验证
pydantic>=1.10.0,<2.0.0
cerberus>=1.3.0,<2.0.0

# 缓存
redis>=4.3.0,<5.0.0
diskcache>=5.4.0,<6.0.0

# 监控和指标
prometheus-client>=0.14.0,<1.0.0
psutil>=5.9.0,<6.0.0

# 图像处理（验证码识别）
Pillow>=9.2.0,<10.0.0
opencv-python>=4.6.0,<5.0.0

# 机器学习（可选，用于验证码识别）
scikit-learn>=1.1.0,<2.0.0
tensorflow>=2.9.0,<3.0.0

# 网络工具
fake-useragent>=1.2.0,<2.0.0
selenium>=4.4.0,<5.0.0
undetected-chromedriver>=3.4.0,<4.0.0

# 数据导出
xlsxwriter>=3.0.0,<4.0.0
reportlab>=3.6.0,<4.0.0

# 邮件发送
smtplib2>=0.2.0,<1.0.0
email-validator>=1.3.0,<2.0.0

# 加密解密
cryptography>=37.0.0,<40.0.0
bcrypt>=4.0.0,<5.0.0

# API框架（可选）
fastapi>=0.85.0,<1.0.0
uvicorn>=0.18.0,<1.0.0
pydantic>=1.10.0,<2.0.0

# 测试框架
pytest>=7.1.0,<8.0.0
pytest-cov>=3.0.0,<4.0.0
pytest-mock>=3.8.0,<4.0.0
pytest-asyncio>=0.19.0,<1.0.0

# 代码质量
black>=22.6.0,<23.0.0
flake8>=5.0.0,<6.0.0
isort>=5.10.0,<6.0.0
mypy>=0.971,<1.0.0

# 文档生成
sphinx>=5.1.0,<6.0.0
sphinx-rtd-theme>=1.0.0,<2.0.0

# 性能分析
memory-profiler>=0.60.0,<1.0.0
line-profiler>=4.0.0,<5.0.0

# 开发工具
ipython>=8.4.0,<9.0.0
jupyter>=1.0.0,<2.0.0
notebook>=6.4.0,<7.0.0

# 部署相关
gunicorn>=20.1.0,<21.0.0
supervisor>=4.2.0,<5.0.0

# 系统监控
docker>=6.0.0,<7.0.0
docker-compose>=1.29.0,<2.0.0
