#!/usr/bin/env python3
"""
惠农网价格数据爬虫项目主程序

使用方法:
    python main.py                          # 运行完整爬虫
    python main.py --category 水果          # 爬取特定分类
    python main.py --region 山东            # 爬取特定地区
    python main.py --max-pages 10           # 限制最大页数
    python main.py --incremental            # 增量更新
"""

import argparse
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from huinong_spider import initialize, get_project_info
from huinong_spider.spiders import HuinongSpider
from huinong_spider.utils.logger import get_logger
from huinong_spider.utils.database import get_db_manager
from huinong_spider.config.settings import settings

logger = get_logger(__name__)


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="惠农网价格数据爬虫",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  %(prog)s                                    # 运行完整爬虫
  %(prog)s --category 水果,蔬菜               # 爬取指定分类
  %(prog)s --region 山东,河南                 # 爬取指定地区
  %(prog)s --category 水果 --region 山东      # 爬取指定分类和地区
  %(prog)s --max-pages 5                     # 限制最大页数
  %(prog)s --incremental                     # 增量更新模式
  %(prog)s --test                            # 测试模式
        """
    )
    
    parser.add_argument(
        '--category', '-c',
        type=str,
        help='要爬取的分类，多个分类用逗号分隔，如: 水果,蔬菜'
    )
    
    parser.add_argument(
        '--region', '-r',
        type=str,
        help='要爬取的地区，多个地区用逗号分隔，如: 山东,河南'
    )
    
    parser.add_argument(
        '--max-pages', '-p',
        type=int,
        default=0,
        help='最大爬取页数，0表示不限制 (默认: 0)'
    )
    
    parser.add_argument(
        '--incremental', '-i',
        action='store_true',
        help='增量更新模式，只爬取最新数据'
    )
    
    parser.add_argument(
        '--test', '-t',
        action='store_true',
        help='测试模式，只爬取少量数据'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='详细输出模式'
    )
    
    parser.add_argument(
        '--config',
        type=str,
        help='配置文件路径'
    )
    
    parser.add_argument(
        '--version',
        action='version',
        version=f"%(prog)s {get_project_info()['version']}"
    )
    
    return parser.parse_args()


def setup_logging(verbose: bool = False):
    """设置日志"""
    from huinong_spider.utils.logger import setup_logging
    
    log_level = "DEBUG" if verbose else settings.get('logging.level', 'INFO')
    log_file = settings.get('logging.file.path', 'logs/spider.log')
    
    setup_logging(
        log_level=log_level,
        log_file=log_file
    )


def test_database_connection():
    """测试数据库连接"""
    try:
        db = get_db_manager()
        with db.get_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                if result:
                    logger.info("数据库连接测试成功")
                    return True
    except Exception as e:
        logger.error(f"数据库连接测试失败: {e}")
        return False
    
    return False


def validate_arguments(args):
    """验证命令行参数"""
    errors = []
    
    # 验证分类
    if args.category:
        valid_categories = [
            '水果', '蔬菜', '禽畜肉蛋', '水产', '农副加工', 
            '粮油米面', '种子种苗', '苗木花草', '农资农机', 
            '中药材', '日用百货', '土地流转', '包装', 
            '农业服务', '农工服务', '租赁服务', '农技服务', '经济作物'
        ]
        
        categories = [cat.strip() for cat in args.category.split(',')]
        for category in categories:
            if category not in valid_categories:
                errors.append(f"无效的分类: {category}")
    
    # 验证页数
    if args.max_pages < 0:
        errors.append("最大页数不能为负数")
    
    return errors


def run_spider(args):
    """运行爬虫"""
    try:
        # 解析参数
        categories = None
        if args.category:
            categories = [cat.strip() for cat in args.category.split(',')]
        
        regions = None
        if args.region:
            regions = [region.strip() for region in args.region.split(',')]
        
        max_pages = args.max_pages
        
        # 测试模式
        if args.test:
            max_pages = 2
            if not categories:
                categories = ['水果']
            if not regions:
                regions = ['山东']
            logger.info("运行测试模式")
        
        # 增量模式
        if args.incremental:
            max_pages = 1
            logger.info("运行增量更新模式")
        
        # 创建并运行爬虫
        logger.info("开始运行惠农网价格数据爬虫")
        
        with HuinongSpider() as spider:
            success = spider.run(
                categories=categories,
                regions=regions,
                max_pages=max_pages
            )
            
            if success:
                logger.info("爬虫运行成功完成")
                return 0
            else:
                logger.error("爬虫运行失败")
                return 1
                
    except KeyboardInterrupt:
        logger.info("用户中断爬虫运行")
        return 130
    except Exception as e:
        logger.error(f"爬虫运行过程中发生错误: {e}")
        return 1


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()
    
    # 设置日志
    setup_logging(args.verbose)
    
    # 初始化项目
    initialize()
    
    # 显示项目信息
    project_info = get_project_info()
    logger.info(f"启动 {project_info['name']} v{project_info['version']}")
    
    # 验证参数
    errors = validate_arguments(args)
    if errors:
        logger.error("参数验证失败:")
        for error in errors:
            logger.error(f"  - {error}")
        return 1
    
    # 测试数据库连接
    if not test_database_connection():
        logger.error("数据库连接失败，请检查配置")
        return 1
    
    # 运行爬虫
    return run_spider(args)


if __name__ == "__main__":
    sys.exit(main())
