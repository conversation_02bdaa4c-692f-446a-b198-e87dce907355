#!/bin/bash
# 惠农网价格数据爬虫项目 - Linux/Mac运行脚本
#
# 使用方法:
#   ./run.sh                    # 运行完整爬虫
#   ./run.sh test               # 运行测试
#   ./run.sh setup              # 初始化项目
#   ./run.sh category 水果      # 爬取特定分类
#   ./run.sh region 山东        # 爬取特定地区

set -e  # 遇到错误立即退出

# 设置项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$PROJECT_ROOT"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印彩色消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Python版本
check_python() {
    if ! command -v python3 &> /dev/null; then
        print_error "未找到Python3，请先安装Python 3.8或更高版本"
        exit 1
    fi
    
    python_version=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
    print_info "Python版本: $python_version"
    
    # 检查版本是否满足要求
    if ! python3 -c "import sys; exit(0 if sys.version_info >= (3, 8) else 1)"; then
        print_error "Python版本过低，需要3.8或更高版本"
        exit 1
    fi
}

# 设置虚拟环境
setup_venv() {
    if [ ! -d "venv" ]; then
        print_info "创建Python虚拟环境..."
        python3 -m venv venv
        if [ $? -ne 0 ]; then
            print_error "创建虚拟环境失败"
            exit 1
        fi
    fi
    
    # 激活虚拟环境
    source venv/bin/activate
    
    # 检查依赖是否安装
    if [ ! -f "venv/lib/python*/site-packages/requests/__init__.py" ]; then
        print_info "安装项目依赖..."
        pip install -r requirements.txt
        if [ $? -ne 0 ]; then
            print_error "安装依赖失败"
            exit 1
        fi
    fi
}

# 显示帮助信息
show_help() {
    echo "惠农网价格数据爬虫项目"
    echo ""
    echo "使用方法:"
    echo "  ./run.sh                    # 运行完整爬虫"
    echo "  ./run.sh test               # 运行测试"
    echo "  ./run.sh setup              # 初始化项目"
    echo "  ./run.sh category 水果      # 爬取特定分类"
    echo "  ./run.sh region 山东        # 爬取特定地区"
    echo "  ./run.sh help               # 显示帮助信息"
    echo ""
    echo "支持的分类:"
    echo "  水果, 蔬菜, 禽畜肉蛋, 水产, 农副加工, 粮油米面"
    echo "  种子种苗, 苗木花草, 农资农机, 中药材, 日用百货"
    echo "  土地流转, 包装, 农业服务, 农工服务, 租赁服务"
    echo "  农技服务, 经济作物"
    echo ""
}

# 运行初始化
run_setup() {
    print_info "初始化项目..."
    python3 scripts/setup.py
}

# 运行测试
run_test() {
    print_info "运行测试..."
    python3 scripts/test_spider.py
}

# 运行爬虫
run_spider() {
    local args="$@"
    print_info "运行惠农网价格数据爬虫..."
    python3 main.py $args
}

# 主函数
main() {
    print_info "惠农网价格数据爬虫项目启动"
    
    # 检查Python
    check_python
    
    # 设置虚拟环境
    setup_venv
    
    # 根据参数执行不同操作
    case "$1" in
        "setup")
            run_setup
            ;;
        "test")
            run_test
            ;;
        "category")
            if [ -z "$2" ]; then
                print_error "请指定分类名称"
                echo "使用方法: ./run.sh category 水果"
                exit 1
            fi
            run_spider --category "$2" --test
            ;;
        "region")
            if [ -z "$2" ]; then
                print_error "请指定地区名称"
                echo "使用方法: ./run.sh region 山东"
                exit 1
            fi
            run_spider --region "$2" --test
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        "")
            # 默认运行完整爬虫
            run_spider
            ;;
        *)
            print_error "未知参数: $1"
            echo "使用 ./run.sh help 查看帮助信息"
            exit 1
            ;;
    esac
    
    print_success "操作完成"
}

# 确保脚本可执行
if [ ! -x "$0" ]; then
    chmod +x "$0"
fi

# 运行主函数
main "$@"
