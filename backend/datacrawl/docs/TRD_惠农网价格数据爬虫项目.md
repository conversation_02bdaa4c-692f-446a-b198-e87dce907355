# 惠农网价格数据爬虫项目 - 技术需求文档 (TRD)

## 1. 技术架构设计

### 1.1 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   数据采集层     │    │   数据处理层     │    │   数据存储层     │
│                │    │                │    │                │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ 网页爬虫    │ │───▶│ │ 数据清洗    │ │───▶│ │ MySQL数据库 │ │
│ │ (Scrapy)    │ │    │ │ 数据验证    │ │    │ │             │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│                │    │                │    │                │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ 反爬虫处理  │ │    │ │ 数据去重    │ │    │ │ 日志文件    │ │
│ │ 请求调度    │ │    │ │ 格式转换    │ │    │ │             │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                │
                    ┌─────────────────┐
                    │   监控管理层     │
                    │                │
                    │ ┌─────────────┐ │
                    │ │ 任务调度    │ │
                    │ │ 状态监控    │ │
                    │ │ 异常告警    │ │
                    │ └─────────────┘ │
                    └─────────────────┘
```

### 1.2 技术栈选择

#### 1.2.1 核心技术栈
- **编程语言**: Python 3.8+
- **爬虫框架**: Scrapy 2.5+
- **HTTP库**: Requests 2.28+
- **HTML解析**: BeautifulSoup4 4.11+
- **数据库**: MySQL 8.0+
- **数据库连接**: PyMySQL 1.0+
- **任务调度**: APScheduler 3.9+

#### 1.2.2 辅助技术栈
- **配置管理**: PyYAML 6.0+
- **日志处理**: Loguru 0.6+
- **数据处理**: Pandas 1.5+
- **时间处理**: Arrow 1.2+
- **环境管理**: python-dotenv 0.20+

### 1.3 模块设计

#### 1.3.1 核心模块
```python
huinong_spider/
├── __init__.py
├── config/                 # 配置模块
│   ├── __init__.py
│   ├── settings.py        # 全局配置
│   └── database.py        # 数据库配置
├── spiders/               # 爬虫模块
│   ├── __init__.py
│   ├── base_spider.py     # 基础爬虫类
│   ├── category_spider.py # 分类爬虫
│   └── product_spider.py  # 产品爬虫
├── items/                 # 数据项模块
│   ├── __init__.py
│   └── product_item.py    # 产品数据项
├── pipelines/             # 数据管道模块
│   ├── __init__.py
│   ├── validation.py      # 数据验证
│   ├── cleaning.py        # 数据清洗
│   └── storage.py         # 数据存储
├── middlewares/           # 中间件模块
│   ├── __init__.py
│   ├── retry.py          # 重试中间件
│   └── proxy.py          # 代理中间件
├── utils/                 # 工具模块
│   ├── __init__.py
│   ├── database.py        # 数据库工具
│   ├── logger.py          # 日志工具
│   └── helpers.py         # 辅助函数
└── scheduler/             # 调度模块
    ├── __init__.py
    ├── task_scheduler.py  # 任务调度器
    └── monitor.py         # 监控模块
```

## 2. 数据库设计

### 2.1 数据库表结构

#### 2.1.1 产品分类表 (categories)
```sql
CREATE TABLE categories (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '分类ID',
    name VARCHAR(50) NOT NULL COMMENT '分类名称',
    pinyin VARCHAR(50) NOT NULL COMMENT '分类拼音',
    category_id INT NOT NULL COMMENT '惠农网分类ID',
    parent_id INT DEFAULT NULL COMMENT '父分类ID',
    level TINYINT NOT NULL DEFAULT 1 COMMENT '分类层级',
    sort_order INT DEFAULT 0 COMMENT '排序',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_category_id (category_id),
    UNIQUE KEY uk_pinyin (pinyin),
    INDEX idx_parent_id (parent_id),
    INDEX idx_level (level)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品分类表';
```

#### 2.1.2 地区表 (regions)
```sql
CREATE TABLE regions (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '地区ID',
    name VARCHAR(100) NOT NULL COMMENT '地区名称',
    region_code INT NOT NULL COMMENT '惠农网地区编码',
    parent_id INT DEFAULT NULL COMMENT '父地区ID',
    level TINYINT NOT NULL COMMENT '地区层级(1省2市3县)',
    full_name VARCHAR(200) COMMENT '完整地区名称',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_region_code (region_code),
    INDEX idx_parent_id (parent_id),
    INDEX idx_level (level)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='地区表';
```

#### 2.1.3 产品表 (products)
```sql
CREATE TABLE products (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '产品ID',
    name VARCHAR(100) NOT NULL COMMENT '产品名称',
    category_id INT NOT NULL COMMENT '分类ID',
    product_code VARCHAR(50) COMMENT '产品编码',
    unit VARCHAR(20) DEFAULT '斤' COMMENT '计量单位',
    description TEXT COMMENT '产品描述',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_category_id (category_id),
    INDEX idx_name (name),
    FOREIGN KEY (category_id) REFERENCES categories(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品表';
```

#### 2.1.4 价格数据表 (price_data)
```sql
CREATE TABLE price_data (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '价格数据ID',
    product_id INT NOT NULL COMMENT '产品ID',
    region_id INT NOT NULL COMMENT '地区ID',
    price DECIMAL(10,2) NOT NULL COMMENT '价格',
    unit VARCHAR(20) NOT NULL DEFAULT '斤' COMMENT '计量单位',
    price_change VARCHAR(20) COMMENT '价格变化',
    price_trend TINYINT COMMENT '价格趋势(1上涨0持平-1下跌)',
    source_url VARCHAR(500) COMMENT '数据源URL',
    source_id VARCHAR(100) COMMENT '源数据ID',
    data_date DATE NOT NULL COMMENT '数据日期',
    crawl_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '采集时间',
    
    UNIQUE KEY uk_product_region_date (product_id, region_id, data_date),
    INDEX idx_data_date (data_date),
    INDEX idx_crawl_time (crawl_time),
    INDEX idx_price (price),
    INDEX idx_source_id (source_id),
    FOREIGN KEY (product_id) REFERENCES products(id),
    FOREIGN KEY (region_id) REFERENCES regions(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='价格数据表';
```

#### 2.1.5 爬取日志表 (crawl_logs)
```sql
CREATE TABLE crawl_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
    task_id VARCHAR(100) NOT NULL COMMENT '任务ID',
    spider_name VARCHAR(50) NOT NULL COMMENT '爬虫名称',
    start_time TIMESTAMP NOT NULL COMMENT '开始时间',
    end_time TIMESTAMP COMMENT '结束时间',
    status ENUM('running', 'success', 'failed', 'stopped') NOT NULL COMMENT '状态',
    total_requests INT DEFAULT 0 COMMENT '总请求数',
    success_requests INT DEFAULT 0 COMMENT '成功请求数',
    failed_requests INT DEFAULT 0 COMMENT '失败请求数',
    items_scraped INT DEFAULT 0 COMMENT '采集条目数',
    items_saved INT DEFAULT 0 COMMENT '保存条目数',
    error_message TEXT COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_task_id (task_id),
    INDEX idx_spider_name (spider_name),
    INDEX idx_start_time (start_time),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='爬取日志表';
```

### 2.2 索引优化策略

#### 2.2.1 主要查询索引
```sql
-- 价格数据查询优化
CREATE INDEX idx_price_query ON price_data(product_id, data_date DESC);
CREATE INDEX idx_region_price ON price_data(region_id, data_date DESC);
CREATE INDEX idx_date_range ON price_data(data_date, crawl_time);

-- 分类查询优化
CREATE INDEX idx_category_tree ON categories(parent_id, level, sort_order);

-- 地区查询优化
CREATE INDEX idx_region_tree ON regions(parent_id, level);
```

#### 2.2.2 分区策略
```sql
-- 价格数据表按月分区
ALTER TABLE price_data PARTITION BY RANGE (YEAR(data_date) * 100 + MONTH(data_date)) (
    PARTITION p202501 VALUES LESS THAN (202502),
    PARTITION p202502 VALUES LESS THAN (202503),
    PARTITION p202503 VALUES LESS THAN (202504),
    -- ... 继续添加分区
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

## 3. 爬虫策略设计

### 3.1 反爬虫应对策略

#### 3.1.1 请求频率控制
```python
# Scrapy设置
DOWNLOAD_DELAY = 2  # 请求间隔2秒
RANDOMIZE_DOWNLOAD_DELAY = 0.5  # 随机延迟0.5倍
CONCURRENT_REQUESTS = 1  # 并发请求数
CONCURRENT_REQUESTS_PER_DOMAIN = 1  # 每域名并发数
```

#### 3.1.2 User-Agent轮换
```python
USER_AGENT_LIST = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36'
]
```

#### 3.1.3 代理IP池
```python
PROXY_POOL = [
    'http://proxy1:port',
    'http://proxy2:port',
    'http://proxy3:port'
]
```

### 3.2 数据采集策略

#### 3.2.1 分层采集策略
```python
# 采集优先级
CRAWL_PRIORITY = {
    'categories': 100,  # 分类数据优先级最高
    'products': 80,     # 产品数据次之
    'prices': 60        # 价格数据最后
}
```

#### 3.2.2 增量更新策略
```python
# 基于时间的增量更新
def get_last_crawl_time(spider_name):
    """获取上次爬取时间"""
    return db.query(
        "SELECT MAX(crawl_time) FROM price_data WHERE spider_name = %s",
        (spider_name,)
    )

def is_data_updated(data_date, last_crawl_time):
    """判断数据是否需要更新"""
    return data_date > last_crawl_time.date()
```

### 3.3 错误处理策略

#### 3.3.1 重试机制
```python
RETRY_TIMES = 3
RETRY_HTTP_CODES = [500, 502, 503, 504, 408, 429]
RETRY_PRIORITY_ADJUST = -1
```

#### 3.3.2 异常处理
```python
class ErrorHandler:
    def handle_network_error(self, error):
        """处理网络错误"""
        pass
    
    def handle_parse_error(self, error):
        """处理解析错误"""
        pass
    
    def handle_database_error(self, error):
        """处理数据库错误"""
        pass
```

## 4. 数据处理流程

### 4.1 数据清洗规则

#### 4.1.1 价格数据清洗
```python
def clean_price_data(raw_price):
    """清洗价格数据"""
    # 移除非数字字符
    price_str = re.sub(r'[^\d.]', '', raw_price)
    
    # 转换为浮点数
    try:
        price = float(price_str)
        return round(price, 2)
    except ValueError:
        return None
```

#### 4.1.2 地区数据标准化
```python
def standardize_region(raw_region):
    """标准化地区数据"""
    # 地区名称映射表
    region_mapping = {
        '北京市': '北京',
        '天津市': '天津',
        '上海市': '上海',
        '重庆市': '重庆'
    }
    
    return region_mapping.get(raw_region, raw_region)
```

### 4.2 数据验证规则

#### 4.2.1 必填字段验证
```python
REQUIRED_FIELDS = ['product_name', 'region', 'price', 'data_date']

def validate_required_fields(item):
    """验证必填字段"""
    for field in REQUIRED_FIELDS:
        if not item.get(field):
            raise ValueError(f"Missing required field: {field}")
```

#### 4.2.2 数据范围验证
```python
def validate_price_range(price):
    """验证价格范围"""
    if price < 0 or price > 10000:
        raise ValueError(f"Price out of range: {price}")
```

## 5. 部署和运维方案

### 5.1 部署架构

#### 5.1.1 单机部署
```bash
# 目录结构
/opt/huinong_spider/
├── app/                # 应用代码
├── config/             # 配置文件
├── logs/               # 日志文件
├── data/               # 数据文件
└── scripts/            # 脚本文件
```

#### 5.1.2 Docker部署
```dockerfile
FROM python:3.8-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
CMD ["python", "main.py"]
```

### 5.2 监控方案

#### 5.2.1 系统监控
- CPU使用率监控
- 内存使用率监控
- 磁盘空间监控
- 网络连接监控

#### 5.2.2 应用监控
- 爬虫运行状态监控
- 数据采集量监控
- 错误率监控
- 响应时间监控

### 5.3 备份策略

#### 5.3.1 数据备份
```bash
# 每日数据备份
mysqldump -u user -p huinong_db > backup_$(date +%Y%m%d).sql

# 增量备份
mysqlbinlog --start-datetime="2025-01-01 00:00:00" > incremental_backup.sql
```

#### 5.3.2 配置备份
```bash
# 配置文件备份
tar -czf config_backup_$(date +%Y%m%d).tar.gz config/
```

## 6. 性能优化方案

### 6.1 数据库优化
- 合理设计索引
- 使用分区表
- 定期清理历史数据
- 优化查询语句

### 6.2 爬虫优化
- 使用连接池
- 实现请求缓存
- 优化解析算法
- 减少内存使用

### 6.3 系统优化
- 使用SSD存储
- 增加内存容量
- 优化网络配置
- 使用CDN加速

## 7. 安全方案

### 7.1 数据安全
- 数据库访问权限控制
- 敏感信息加密存储
- 定期安全审计
- 数据备份加密

### 7.2 网络安全
- 使用HTTPS连接
- 防火墙配置
- VPN访问控制
- 异常流量监控

### 7.3 应用安全
- 输入数据验证
- SQL注入防护
- 异常处理机制
- 日志安全记录

## 8. 开发规范

### 8.1 代码规范
- 遵循PEP 8 Python编码规范
- 使用类型注解 (Type Hints)
- 函数和类必须有文档字符串
- 变量和函数命名使用英文，见名知意

### 8.2 Git规范
```bash
# 分支命名规范
feature/功能名称     # 新功能开发
bugfix/问题描述      # Bug修复
hotfix/紧急修复      # 紧急修复
release/版本号       # 版本发布

# 提交信息规范
feat: 新功能
fix: Bug修复
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

### 8.3 测试规范
- 单元测试覆盖率 ≥ 80%
- 集成测试覆盖主要业务流程
- 性能测试验证系统性能指标
- 使用pytest作为测试框架

## 9. 配置管理

### 9.1 配置文件结构
```yaml
# config/settings.yaml
database:
  host: localhost
  port: 3306
  username: root
  password: ${DB_PASSWORD}
  database: huinong_db
  charset: utf8mb4

spider:
  download_delay: 2
  concurrent_requests: 1
  retry_times: 3
  timeout: 30

logging:
  level: INFO
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} - {message}"
  rotation: "1 day"
  retention: "30 days"

monitoring:
  enable_email_alert: true
  email_recipients:
    - <EMAIL>
  alert_threshold:
    error_rate: 0.05
    response_time: 10
```

### 9.2 环境变量管理
```bash
# .env文件
DB_PASSWORD=your_password
EMAIL_PASSWORD=your_email_password
PROXY_USERNAME=proxy_user
PROXY_PASSWORD=proxy_pass
```

## 10. API接口设计

### 10.1 数据查询接口
```python
# REST API设计
GET /api/v1/categories                    # 获取分类列表
GET /api/v1/categories/{id}/products      # 获取分类下的产品
GET /api/v1/products/{id}/prices          # 获取产品价格数据
GET /api/v1/regions                       # 获取地区列表
GET /api/v1/prices                        # 查询价格数据

# 查询参数
?start_date=2025-01-01    # 开始日期
&end_date=2025-01-31      # 结束日期
&region_id=1              # 地区ID
&category_id=1            # 分类ID
&page=1                   # 页码
&limit=100                # 每页数量
```

### 10.2 数据导出接口
```python
POST /api/v1/export/csv      # 导出CSV格式
POST /api/v1/export/excel    # 导出Excel格式
POST /api/v1/export/json     # 导出JSON格式

# 请求体
{
    "start_date": "2025-01-01",
    "end_date": "2025-01-31",
    "category_ids": [1, 2, 3],
    "region_ids": [1, 2, 3],
    "format": "csv"
}
```

## 11. 数据质量保证

### 11.1 数据质量指标
- **完整性**: 必填字段完整率 ≥ 99%
- **准确性**: 数据格式正确率 ≥ 99.5%
- **一致性**: 同源数据一致性 ≥ 99%
- **时效性**: 数据更新延迟 ≤ 24小时

### 11.2 数据质量监控
```python
class DataQualityMonitor:
    def check_completeness(self, data):
        """检查数据完整性"""
        missing_fields = []
        for field in REQUIRED_FIELDS:
            if not data.get(field):
                missing_fields.append(field)
        return len(missing_fields) == 0, missing_fields

    def check_accuracy(self, data):
        """检查数据准确性"""
        errors = []

        # 价格格式检查
        if not isinstance(data.get('price'), (int, float)):
            errors.append('Invalid price format')

        # 日期格式检查
        try:
            datetime.strptime(data.get('data_date'), '%Y-%m-%d')
        except ValueError:
            errors.append('Invalid date format')

        return len(errors) == 0, errors
```

## 12. 容灾和高可用

### 12.1 容灾方案
- **数据备份**: 每日全量备份 + 实时增量备份
- **异地备份**: 备份数据存储到异地机房
- **故障切换**: 主备数据库自动切换
- **服务恢复**: 故障后快速恢复服务

### 12.2 高可用设计
- **负载均衡**: 多实例负载均衡
- **健康检查**: 实时健康状态检查
- **自动重启**: 服务异常自动重启
- **监控告警**: 24/7监控和告警

## 13. 项目交付物

### 13.1 代码交付物
- [ ] 完整的源代码
- [ ] 单元测试代码
- [ ] 集成测试代码
- [ ] 配置文件模板
- [ ] 部署脚本

### 13.2 文档交付物
- [ ] 技术设计文档
- [ ] API接口文档
- [ ] 部署运维文档
- [ ] 用户使用手册
- [ ] 故障排查手册

### 13.3 数据交付物
- [ ] 数据库表结构脚本
- [ ] 初始化数据脚本
- [ ] 示例数据文件
- [ ] 数据字典文档

## 14. 技术风险评估

### 14.1 高风险项
- **反爬虫升级**: 目标网站可能升级反爬虫机制
- **网站改版**: 页面结构变化导致解析失败
- **IP封禁**: 频繁访问可能导致IP被封

### 14.2 中风险项
- **数据质量**: 源数据质量可能存在问题
- **性能瓶颈**: 大量数据处理可能出现性能问题
- **存储空间**: 长期运行可能导致存储空间不足

### 14.3 风险应对措施
- 建立多套反爬虫应对方案
- 设计灵活的页面解析框架
- 使用代理IP池和请求频率控制
- 建立数据质量监控和清洗机制
- 实施性能监控和优化
- 制定存储空间管理策略
