# 惠农网价格数据爬虫项目环境变量配置
# 复制此文件为 .env 并修改相应配置

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=your_database_password
DB_DATABASE=huinong_spider

# Redis配置（可选）
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
REDIS_DB=0

# 邮件配置
EMAIL_SMTP_SERVER=smtp.gmail.com
EMAIL_SMTP_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your_email_password
EMAIL_FROM=<EMAIL>

# API配置
API_SECRET_KEY=your_secret_key_here_please_change_this
API_HOST=0.0.0.0
API_PORT=8002

# 加密配置
ENCRYPTION_KEY=your_encryption_key_32_characters

# 代理配置（可选）
PROXY_USERNAME=your_proxy_username
PROXY_PASSWORD=your_proxy_password
PROXY_SERVER=proxy.example.com
PROXY_PORT=8080

# 监控配置
MONITORING_ENABLED=true
ALERT_EMAIL=<EMAIL>

# 日志配置
LOG_LEVEL=INFO
LOG_FILE_PATH=logs/spider.log

# 缓存配置
CACHE_ENABLED=true
CACHE_DIRECTORY=cache
CACHE_SIZE_LIMIT=1GB

# 性能配置
MAX_WORKERS=4
MAX_MEMORY_USAGE=2GB
BATCH_SIZE=1000

# 开发配置
DEBUG=false
TEST_MODE=false
PROFILING=false

# 安全配置
ALLOWED_IPS=127.0.0.1,***********/24
AUDIT_ENABLED=true

# 第三方服务配置（可选）
# 验证码识别服务
CAPTCHA_SERVICE_API_KEY=your_captcha_service_api_key
CAPTCHA_SERVICE_URL=https://api.captcha-service.com

# 短信通知服务（可选）
SMS_SERVICE_API_KEY=your_sms_service_api_key
SMS_SERVICE_URL=https://api.sms-service.com

# 云存储配置（可选）
CLOUD_STORAGE_ACCESS_KEY=your_access_key
CLOUD_STORAGE_SECRET_KEY=your_secret_key
CLOUD_STORAGE_BUCKET=your_bucket_name
CLOUD_STORAGE_REGION=your_region

# 时区配置
TIMEZONE=Asia/Shanghai

# 数据保留配置
DATA_RETENTION_DAYS=365
LOG_RETENTION_DAYS=90

# 备份配置
BACKUP_ENABLED=true
BACKUP_DIRECTORY=backups
BACKUP_SCHEDULE=0 3 * * *

# 导出配置
EXPORT_DIRECTORY=exports
EXPORT_MAX_RECORDS=100000

# 爬虫配置
SPIDER_DOWNLOAD_DELAY=2
SPIDER_CONCURRENT_REQUESTS=1
SPIDER_RETRY_TIMES=3
SPIDER_TIMEOUT=30

# 目标网站配置
TARGET_DOMAIN=www.cnhnb.com
BASE_URL=https://www.cnhnb.com/hangqing/

# 用户代理配置
USER_AGENT_ROTATION=true
CUSTOM_USER_AGENT="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"

# 请求头配置
ACCEPT_LANGUAGE=zh-CN,zh;q=0.9,en;q=0.8
ACCEPT_ENCODING=gzip, deflate, br

# Cookie配置
COOKIES_ENABLED=true
COOKIES_DEBUG=false

# 下载中间件配置
DOWNLOADER_MIDDLEWARES_ENABLED=true
RETRY_MIDDLEWARE_ENABLED=true
REDIRECT_MIDDLEWARE_ENABLED=true

# 数据管道配置
ITEM_PIPELINES_ENABLED=true
VALIDATION_PIPELINE_ENABLED=true
CLEANING_PIPELINE_ENABLED=true
STORAGE_PIPELINE_ENABLED=true

# 扩展配置
EXTENSIONS_ENABLED=true
TELNET_ENABLED=false
MEMORY_MONITOR_ENABLED=true

# 统计配置
STATS_CLASS=scrapy.statscollectors.MemoryStatsCollector
STATS_DUMP=true

# 调试配置
SCRAPY_DEBUG=false
SCRAPY_LOG_LEVEL=INFO
SCRAPY_LOG_FILE=logs/scrapy.log

# 自动限速配置
AUTOTHROTTLE_ENABLED=true
AUTOTHROTTLE_START_DELAY=1
AUTOTHROTTLE_MAX_DELAY=60
AUTOTHROTTLE_TARGET_CONCURRENCY=1.0
AUTOTHROTTLE_DEBUG=false

# 机器人协议配置
ROBOTSTXT_OBEY=true
ROBOTSTXT_USER_AGENT=*

# DNS配置
DNSCACHE_ENABLED=true
DNSCACHE_SIZE=10000
DNS_TIMEOUT=60

# HTTP缓存配置
HTTPCACHE_ENABLED=false
HTTPCACHE_EXPIRATION_SECS=0
HTTPCACHE_DIR=httpcache
HTTPCACHE_IGNORE_HTTP_CODES=[503, 504, 505, 500, 403, 404, 408, 429]

# 媒体管道配置
MEDIA_ALLOW_REDIRECTS=true
IMAGES_STORE=images
FILES_STORE=files

# 并发配置
REACTOR_THREADPOOL_MAXSIZE=20
CONCURRENT_ITEMS=100

# 内存使用配置
MEMUSAGE_ENABLED=true
MEMUSAGE_LIMIT_MB=2048
MEMUSAGE_WARNING_MB=1024

# 关闭配置
CLOSESPIDER_TIMEOUT=0
CLOSESPIDER_ITEMCOUNT=0
CLOSESPIDER_PAGECOUNT=0
CLOSESPIDER_ERRORCOUNT=0

# 重复过滤器配置
DUPEFILTER_CLASS=scrapy.dupefilters.RFPDupeFilter
DUPEFILTER_DEBUG=false

# 调度器配置
SCHEDULER=scrapy.core.scheduler.Scheduler
SCHEDULER_DEBUG=false
SCHEDULER_MEMORY_QUEUE=scrapy.squeues.LifoMemoryQueue
SCHEDULER_DISK_QUEUE=scrapy.squeues.PickleLifoDiskQueue
SCHEDULER_PRIORITY_QUEUE=scrapy.pqueues.ScrapyPriorityQueue

# 下载器配置
DOWNLOAD_HANDLERS_BASE={
    'file': 'scrapy.core.downloader.handlers.file.FileDownloadHandler',
    'http': 'scrapy.core.downloader.handlers.http.HTTPDownloadHandler',
    'https': 'scrapy.core.downloader.handlers.http.HTTPDownloadHandler',
    's3': 'scrapy.core.downloader.handlers.s3.S3DownloadHandler',
    'ftp': 'scrapy.core.downloader.handlers.ftp.FTPDownloadHandler',
}

# 请求指纹配置
REQUEST_FINGERPRINTER_IMPLEMENTATION=2.7

# 压缩配置
COMPRESSION_ENABLED=true

# 重定向配置
REDIRECT_MAX_TIMES=20
REDIRECT_PRIORITY_ADJUST=+2

# 元刷新配置
METAREFRESH_ENABLED=true
METAREFRESH_MAXDELAY=100

# HTTP认证配置
HTTPAUTH_ENABLED=false

# 代理中间件配置
HTTPPROXY_ENABLED=true
HTTPPROXY_AUTH_ENCODING=latin-1

# 用户代理中间件配置
USER_AGENT_MIDDLEWARE_ENABLED=true

# 默认请求头配置
DEFAULT_REQUEST_HEADERS={
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br',
    'Cache-Control': 'no-cache',
    'Pragma': 'no-cache',
}

# 爬虫模块配置
SPIDER_MODULES=['huinong_spider.spiders']
NEWSPIDER_MODULE='huinong_spider.spiders'

# 项目名称
BOT_NAME='huinong_spider'

# 版本信息
PROJECT_VERSION=1.0.0
BUILD_DATE=2025-07-24
