@echo off
REM 惠农网价格数据爬虫项目 - Windows运行脚本
REM 
REM 使用方法:
REM   run.bat                    # 运行完整爬虫
REM   run.bat test               # 运行测试
REM   run.bat setup              # 初始化项目
REM   run.bat category 水果      # 爬取特定分类
REM   run.bat region 山东        # 爬取特定地区

setlocal enabledelayedexpansion

REM 设置项目根目录
set PROJECT_ROOT=%~dp0
cd /d "%PROJECT_ROOT%"

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.8或更高版本
    pause
    exit /b 1
)

REM 检查虚拟环境
if not exist "venv" (
    echo 创建Python虚拟环境...
    python -m venv venv
    if errorlevel 1 (
        echo 错误: 创建虚拟环境失败
        pause
        exit /b 1
    )
)

REM 激活虚拟环境
call venv\Scripts\activate.bat

REM 检查依赖是否安装
if not exist "venv\Lib\site-packages\requests" (
    echo 安装项目依赖...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误: 安装依赖失败
        pause
        exit /b 1
    )
)

REM 根据参数执行不同操作
if "%1"=="setup" (
    echo 初始化项目...
    python scripts\setup.py
    goto :end
)

if "%1"=="test" (
    echo 运行测试...
    python scripts\test_spider.py
    goto :end
)

if "%1"=="category" (
    if "%2"=="" (
        echo 错误: 请指定分类名称
        echo 使用方法: run.bat category 水果
        goto :end
    )
    echo 爬取分类: %2
    python main.py --category %2 --test
    goto :end
)

if "%1"=="region" (
    if "%2"=="" (
        echo 错误: 请指定地区名称
        echo 使用方法: run.bat region 山东
        goto :end
    )
    echo 爬取地区: %2
    python main.py --region %2 --test
    goto :end
)

if "%1"=="help" (
    echo 惠农网价格数据爬虫项目
    echo.
    echo 使用方法:
    echo   run.bat                    # 运行完整爬虫
    echo   run.bat test               # 运行测试
    echo   run.bat setup              # 初始化项目
    echo   run.bat category 水果      # 爬取特定分类
    echo   run.bat region 山东        # 爬取特定地区
    echo   run.bat help               # 显示帮助信息
    echo.
    goto :end
)

REM 默认运行完整爬虫
if "%1"=="" (
    echo 运行惠农网价格数据爬虫...
    python main.py
) else (
    echo 未知参数: %1
    echo 使用 run.bat help 查看帮助信息
)

:end
echo.
echo 按任意键退出...
pause >nul
