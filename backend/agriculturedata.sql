CREATE TABLE `address` (
                           `id` bigint NOT NULL AUTO_INCREMENT COMMENT '地址ID',
                           `user_id` bigint NOT NULL COMMENT '用户ID',
                           `name` varchar(50) NOT NULL COMMENT '收货人姓名',
                           `phone` varchar(20) NOT NULL COMMENT '联系电话',
                           `province` varchar(50) NOT NULL COMMENT '省份',
                           `city` varchar(50) NOT NULL COMMENT '城市',
                           `district` varchar(50) NOT NULL COMMENT '区县',
                           `detail` varchar(255) NOT NULL COMMENT '详细地址',
                           `is_default` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否默认地址：0-否，1-是',
                           `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                           `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                           `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
                           PRIMARY KEY (`id`),
                           KEY `idx_user_id` (`user_id`) COMMENT '用户ID索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='收货地址表'

CREATE TABLE `agriculture_news` (
                                    `id` int NOT NULL AUTO_INCREMENT,
                                    `title` varchar(255) NOT NULL COMMENT '新闻标题',
                                    `content` text COMMENT '新闻正文内容',
                                    `summary` varchar(500) DEFAULT NULL COMMENT '新闻摘要',
                                    `source` varchar(100) DEFAULT NULL COMMENT '新闻来源',
                                    `publish_time` datetime DEFAULT NULL COMMENT '发布时间',
                                    `url` varchar(255) NOT NULL COMMENT '原文链接',
                                    `category` varchar(50) DEFAULT NULL COMMENT '新闻分类',
                                    `views` int DEFAULT '0' COMMENT '阅读量',
                                    `crawl_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '爬取时间',
                                    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                    PRIMARY KEY (`id`),
                                    UNIQUE KEY `idx_url` (`url`),
                                    KEY `idx_publish_time` (`publish_time`),
                                    KEY `idx_category` (`category`)
) ENGINE=InnoDB AUTO_INCREMENT=78 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='农业新闻表'

CREATE TABLE `ai_conversations` (
                                    `id` bigint NOT NULL AUTO_INCREMENT,
                                    `ai_response` text COLLATE utf8mb4_unicode_ci,
                                    `created_at` datetime(6) DEFAULT NULL,
                                    `session_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL,
                                    `user_input` text COLLATE utf8mb4_unicode_ci,
                                    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=26 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

CREATE TABLE `api_access_log` (
                                  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
                                  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
                                  `api_path` varchar(255) NOT NULL COMMENT 'API路径',
                                  `http_method` varchar(10) NOT NULL COMMENT 'HTTP方法',
                                  `request_params` text COMMENT '请求参数',
                                  `response_code` int NOT NULL COMMENT '响应状态码',
                                  `response_time` int NOT NULL COMMENT '响应时间(毫秒)',
                                  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
                                  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
                                  `error_message` text COMMENT '错误信息',
                                  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                  PRIMARY KEY (`id`),
                                  KEY `idx_user_id` (`user_id`),
                                  KEY `idx_api_path` (`api_path`),
                                  KEY `idx_response_code` (`response_code`),
                                  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='API访问日志表'

CREATE TABLE `cart_item` (
                             `id` bigint NOT NULL AUTO_INCREMENT COMMENT '购物车项ID',
                             `user_id` bigint NOT NULL COMMENT '用户ID',
                             `product_id` bigint NOT NULL COMMENT '商品ID',
                             `quantity` int NOT NULL DEFAULT '1' COMMENT '数量',
                             `price` decimal(10,2) DEFAULT NULL COMMENT '商品价格(下单时的价格)',
                             `selected` tinyint DEFAULT '1' COMMENT '是否选中(0:否 1:是)',
                             `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                             `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                             `is_deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
                             PRIMARY KEY (`id`),
                             UNIQUE KEY `uk_user_product` (`user_id`,`product_id`),
                             KEY `idx_user_id` (`user_id`),
                             KEY `idx_product_id` (`product_id`),
                             KEY `idx_is_deleted` (`is_deleted`),
                             KEY `idx_user_selected` (`user_id`,`selected`),
                             KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=36 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='购物车表'

CREATE TABLE `category` (
                            `id` bigint NOT NULL AUTO_INCREMENT COMMENT '分类ID',
                            `name` varchar(100) NOT NULL COMMENT '分类名称',
                            `parent_id` bigint DEFAULT '0' COMMENT '父分类ID(0表示顶级分类)',
                            `level` tinyint NOT NULL DEFAULT '1' COMMENT '分类层级',
                            `icon` varchar(255) DEFAULT NULL COMMENT '分类图标',
                            `image` varchar(255) DEFAULT NULL COMMENT '分类图片',
                            `description` text COMMENT '分类描述',
                            `seo_title` varchar(255) DEFAULT NULL COMMENT 'SEO标题',
                            `seo_keywords` varchar(500) DEFAULT NULL COMMENT 'SEO关键词',
                            `seo_description` text COMMENT 'SEO描述',
                            `sort_order` int DEFAULT '0' COMMENT '排序',
                            `is_hot` tinyint DEFAULT '0' COMMENT '是否热门(0:否 1:是)',
                            `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态(0:禁用 1:启用)',
                            `product_count` int DEFAULT '0' COMMENT '该分类下的商品数量',
                            `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                            `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                            `is_deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
                            `level3_id` bigint DEFAULT NULL COMMENT '三级分类ID',
                            `huinong_category_name` varchar(100) DEFAULT NULL COMMENT '惠农网分类名称',
                            `huinong_url` varchar(500) DEFAULT NULL COMMENT '惠农网分类URL',
                            `estimated_products` int DEFAULT '0' COMMENT '预估产品数量',
                            `crawl_priority` enum('high','medium','low') DEFAULT 'medium' COMMENT '爬取优先级',
                            `last_crawl_time` datetime DEFAULT NULL COMMENT '最后爬取时间',
                            PRIMARY KEY (`id`),
                            KEY `idx_parent_id` (`parent_id`),
                            KEY `idx_level` (`level`),
                            KEY `idx_sort_order` (`sort_order`),
                            KEY `idx_is_hot` (`is_hot`),
                            KEY `idx_status_deleted` (`status`,`is_deleted`),
                            KEY `idx_huinong_category_name` (`huinong_category_name`),
                            KEY `idx_crawl_priority` (`crawl_priority`)
) ENGINE=InnoDB AUTO_INCREMENT=67 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='商品分类表'

CREATE TABLE `consultation` (
                                `id` bigint NOT NULL AUTO_INCREMENT COMMENT '咨询ID',
                                `title` varchar(100) NOT NULL COMMENT '标题',
                                `summary` varchar(255) DEFAULT NULL COMMENT '摘要',
                                `content` text NOT NULL COMMENT '内容',
                                `cover_image` varchar(255) DEFAULT NULL COMMENT '封面图片URL',
                                `category_id` bigint NOT NULL COMMENT '分类ID',
                                `author_id` bigint NOT NULL COMMENT '作者ID',
                                `author_name` varchar(50) DEFAULT NULL COMMENT '作者名称',
                                `views_count` int DEFAULT '0' COMMENT '浏览量',
                                `likes_count` int DEFAULT '0' COMMENT '点赞数',
                                `comments_count` int DEFAULT '0' COMMENT '评论数',
                                `is_featured` tinyint(1) DEFAULT '0' COMMENT '是否精选：0-否，1-是',
                                `is_hot` tinyint(1) DEFAULT '0' COMMENT '是否热门：0-否，1-是',
                                `is_new` tinyint(1) DEFAULT '0' COMMENT '是否新发布：0-否，1-是',
                                `keywords` varchar(255) DEFAULT NULL COMMENT '关键词，多个关键词用逗号分隔',
                                `publish_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '发布时间',
                                `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                `status` tinyint DEFAULT '1' COMMENT '状态：0-草稿，1-已发布，2-已下架',
                                `is_deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
                                PRIMARY KEY (`id`),
                                KEY `idx_category_id` (`category_id`) COMMENT '分类索引',
                                KEY `idx_author_id` (`author_id`) COMMENT '作者索引',
                                KEY `idx_publish_date` (`publish_date`) COMMENT '发布时间索引',
                                KEY `idx_status` (`status`) COMMENT '状态索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='农业咨询表'

CREATE TABLE `consultation_attachment` (
                                           `id` bigint NOT NULL AUTO_INCREMENT COMMENT '附件ID',
                                           `consultation_id` bigint NOT NULL COMMENT '咨询ID',
                                           `file_name` varchar(100) NOT NULL COMMENT '文件名称',
                                           `file_path` varchar(255) NOT NULL COMMENT '文件路径',
                                           `file_type` varchar(50) DEFAULT NULL COMMENT '文件类型',
                                           `file_size` bigint DEFAULT NULL COMMENT '文件大小(字节)',
                                           `upload_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
                                           `uploader_id` bigint DEFAULT NULL COMMENT '上传者ID',
                                           `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                           `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                           `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
                                           PRIMARY KEY (`id`),
                                           KEY `idx_consultation_id` (`consultation_id`) COMMENT '咨询ID索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='农业咨询附件表'

CREATE TABLE `consultation_category` (
                                         `id` bigint NOT NULL AUTO_INCREMENT COMMENT '分类ID',
                                         `name` varchar(50) NOT NULL COMMENT '分类名称',
                                         `description` text COMMENT '分类描述',
                                         `parent_id` bigint DEFAULT '0' COMMENT '父分类ID，0表示一级分类',
                                         `level` int DEFAULT '1' COMMENT '分类层级，1为一级分类',
                                         `count` int DEFAULT '0' COMMENT '该分类下的咨询数量',
                                         `sort` int DEFAULT '0' COMMENT '排序',
                                         `status` int DEFAULT '1' COMMENT '状态：0-禁用，1-正常',
                                         `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                         `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                         `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
                                         PRIMARY KEY (`id`),
                                         KEY `idx_parent_id` (`parent_id`) COMMENT '父分类索引'
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='农业咨询分类表'

CREATE TABLE `crawl_task_queue` (
                                    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                    `task_type` enum('category','subcategory','product') NOT NULL COMMENT '任务类型',
                                    `category_name` varchar(100) NOT NULL COMMENT '分类名称',
                                    `subcategory_name` varchar(100) DEFAULT NULL COMMENT '子分类名称',
                                    `target_url` varchar(500) NOT NULL COMMENT '目标URL',
                                    `priority` enum('high','medium','low') DEFAULT 'medium' COMMENT '优先级',
                                    `max_pages` int DEFAULT '5' COMMENT '最大爬取页数',
                                    `status` enum('pending','running','completed','failed','paused') DEFAULT 'pending' COMMENT '任务状态',
                                    `progress` int DEFAULT '0' COMMENT '进度百分比',
                                    `items_crawled` int DEFAULT '0' COMMENT '已爬取项目数',
                                    `items_saved` int DEFAULT '0' COMMENT '已保存项目数',
                                    `error_message` text COMMENT '错误信息',
                                    `started_at` datetime DEFAULT NULL COMMENT '开始时间',
                                    `completed_at` datetime DEFAULT NULL COMMENT '完成时间',
                                    `retry_count` int DEFAULT '0' COMMENT '重试次数',
                                    `max_retries` int DEFAULT '3' COMMENT '最大重试次数',
                                    `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                    `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='爬取任务队列管理表'

CREATE TABLE `encyclopedia` (
                                `id` bigint NOT NULL AUTO_INCREMENT COMMENT '百科ID',
                                `title` varchar(100) NOT NULL COMMENT '标题',
                                `summary` varchar(255) DEFAULT NULL COMMENT '摘要',
                                `content` text NOT NULL COMMENT '内容',
                                `cover_image` varchar(255) DEFAULT NULL COMMENT '封面图片URL',
                                `category_id` bigint NOT NULL COMMENT '分类ID',
                                `category_name` varchar(50) DEFAULT NULL COMMENT '分类名称',
                                `author_id` bigint NOT NULL COMMENT '作者ID',
                                `author_name` varchar(50) DEFAULT NULL COMMENT '作者名称',
                                `views_count` int DEFAULT '0' COMMENT '浏览量',
                                `likes_count` int DEFAULT '0' COMMENT '点赞数',
                                `favorites_count` int DEFAULT '0' COMMENT '收藏数',
                                `is_featured` tinyint(1) DEFAULT '0' COMMENT '是否精选：0-否，1-是',
                                `is_hot` tinyint(1) DEFAULT '0' COMMENT '是否热门：0-否，1-是',
                                `keywords` varchar(255) DEFAULT NULL COMMENT '关键词，多个关键词用逗号分隔',
                                `publish_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '发布时间',
                                `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                `status` tinyint DEFAULT '1' COMMENT '状态：0-草稿，1-已发布，2-已下架',
                                `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
                                PRIMARY KEY (`id`),
                                KEY `idx_category_id` (`category_id`) COMMENT '分类索引',
                                KEY `idx_author_id` (`author_id`) COMMENT '作者索引',
                                KEY `idx_publish_date` (`publish_date`) COMMENT '发布时间索引',
                                KEY `idx_status` (`status`) COMMENT '状态索引'
) ENGINE=InnoDB AUTO_INCREMENT=48 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='农业百科表'

CREATE TABLE `encyclopedia_category` (
                                         `id` bigint NOT NULL AUTO_INCREMENT COMMENT '分类ID',
                                         `name` varchar(50) NOT NULL COMMENT '分类名称',
                                         `description` text COMMENT '分类描述',
                                         `parent_id` bigint DEFAULT '0' COMMENT '父分类ID，0表示一级分类',
                                         `level` int DEFAULT '1' COMMENT '分类层级，1为一级分类',
                                         `count` int DEFAULT '0' COMMENT '该分类下的百科数量',
                                         `sort` int DEFAULT '0' COMMENT '排序',
                                         `icon` varchar(255) DEFAULT NULL COMMENT '图标或封面',
                                         `status` int DEFAULT '1' COMMENT '状态：0-禁用，1-正常',
                                         `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                         `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                         `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
                                         `cover_image` varchar(255) DEFAULT NULL,
                                         `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
                                         `sort_order` int DEFAULT '0',
                                         `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                         `is_deleted` bit(1) NOT NULL,
                                         PRIMARY KEY (`id`),
                                         KEY `idx_parent_id` (`parent_id`) COMMENT '父分类索引'
) ENGINE=InnoDB AUTO_INCREMENT=65 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='农业百科分类表'

CREATE TABLE `encyclopedia_comment` (
                                        `id` bigint NOT NULL AUTO_INCREMENT COMMENT '评论ID',
                                        `encyclopedia_id` bigint NOT NULL COMMENT '百科ID',
                                        `user_id` bigint NOT NULL COMMENT '用户ID',
                                        `user_name` varchar(50) DEFAULT NULL COMMENT '用户名称',
                                        `user_avatar` varchar(255) DEFAULT NULL COMMENT '用户头像URL',
                                        `content` text NOT NULL COMMENT '评论内容',
                                        `parent_id` bigint DEFAULT '0' COMMENT '父评论ID，0表示一级评论',
                                        `level` int DEFAULT '1' COMMENT '评论层级，1为一级评论',
                                        `likes_count` int DEFAULT '0' COMMENT '点赞数',
                                        `is_approved` tinyint(1) DEFAULT '1' COMMENT '是否通过审核：0-未审核，1-已审核',
                                        `reply_count` int DEFAULT '0' COMMENT '回复数量',
                                        `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                        `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                        `status` tinyint DEFAULT '1' COMMENT '状态：0-已屏蔽，1-正常',
                                        `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
                                        PRIMARY KEY (`id`),
                                        KEY `idx_encyclopedia_id` (`encyclopedia_id`) COMMENT '百科索引',
                                        KEY `idx_user_id` (`user_id`) COMMENT '用户索引',
                                        KEY `idx_parent_id` (`parent_id`) COMMENT '父评论索引',
                                        KEY `idx_created_at` (`created_at`) COMMENT '创建时间索引'
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='农业百科评论表'

CREATE TABLE `encyclopedia_favorite` (
                                         `id` bigint NOT NULL AUTO_INCREMENT COMMENT '收藏ID',
                                         `user_id` bigint NOT NULL COMMENT '用户ID',
                                         `encyclopedia_id` bigint NOT NULL COMMENT '百科ID',
                                         `title` varchar(100) DEFAULT NULL COMMENT '百科标题',
                                         `favorite_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '收藏时间',
                                         `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                         `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                         `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
                                         PRIMARY KEY (`id`),
                                         UNIQUE KEY `uk_user_encyclopedia` (`user_id`,`encyclopedia_id`) COMMENT '用户百科唯一索引',
                                         KEY `idx_user_id` (`user_id`) COMMENT '用户索引',
                                         KEY `idx_encyclopedia_id` (`encyclopedia_id`) COMMENT '百科索引'
) ENGINE=InnoDB AUTO_INCREMENT=312 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='农业百科收藏表'

CREATE TABLE `error_log` (
                             `id` bigint NOT NULL AUTO_INCREMENT COMMENT '错误ID',
                             `error_type` varchar(50) NOT NULL COMMENT '错误类型',
                             `error_message` text NOT NULL COMMENT '错误信息',
                             `stack_trace` text COMMENT '堆栈跟踪',
                             `user_id` bigint DEFAULT NULL COMMENT '用户ID',
                             `request_url` varchar(500) DEFAULT NULL COMMENT '请求URL',
                             `request_method` varchar(10) DEFAULT NULL COMMENT '请求方法',
                             `request_params` text COMMENT '请求参数',
                             `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
                             `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
                             `severity` varchar(20) DEFAULT 'ERROR' COMMENT '严重程度(DEBUG,INFO,WARN,ERROR,FATAL)',
                             `resolved` tinyint DEFAULT '0' COMMENT '是否已解决(0:未解决 1:已解决)',
                             `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                             `resolved_at` datetime DEFAULT NULL COMMENT '解决时间',
                             PRIMARY KEY (`id`),
                             KEY `idx_error_type` (`error_type`),
                             KEY `idx_severity` (`severity`),
                             KEY `idx_resolved` (`resolved`),
                             KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='错误日志表'

CREATE TABLE `favorite_backup` (
                                   `id` bigint NOT NULL DEFAULT '0',
                                   `user_id` bigint NOT NULL,
                                   `product_id` bigint NOT NULL,
                                   `created_at` datetime NOT NULL,
                                   `updated_at` datetime NOT NULL,
                                   `deleted` int NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

-- huinong_category_mapping
-- Table 'agriculture_mall.huinong_category_mapping' doesn't exist-- huinong_subcategory_mapping
-- Table 'agriculture_mall.huinong_subcategory_mapping' doesn't exist-- main_product
-- Table 'agriculture_mall.main_product' doesn't existCREATE TABLE `news_images` (
  `id` int NOT NULL AUTO_INCREMENT,
  `news_id` int NOT NULL COMMENT '关联的新闻ID',
  `image_url` varchar(255) NOT NULL COMMENT '原始图片URL',
  `image_path` varchar(255) DEFAULT NULL COMMENT '本地存储路径',
  `is_cover` tinyint(1) DEFAULT '0' COMMENT '是否为封面图',
  `width` int DEFAULT NULL COMMENT '图片宽度',
  `height` int DEFAULT NULL COMMENT '图片高度',
  `file_size` int DEFAULT NULL COMMENT '文件大小(KB)',
  `crawl_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '爬取时间',
  PRIMARY KEY (`id`),
  KEY `idx_news_id` (`news_id`),
  CONSTRAINT `fk_news_images_news` FOREIGN KEY (`news_id`) REFERENCES `agriculture_news` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='新闻图片表'

CREATE TABLE `notification` (
                                `id` bigint NOT NULL AUTO_INCREMENT COMMENT '通知ID',
                                `user_id` bigint NOT NULL COMMENT '用户ID',
                                `title` varchar(100) NOT NULL COMMENT '通知标题',
                                `content` text NOT NULL COMMENT '通知内容',
                                `type` tinyint NOT NULL COMMENT '通知类型：1-系统通知，2-订单通知，3-商品通知',
                                `is_read` tinyint NOT NULL DEFAULT '0' COMMENT '是否已读：0-未读，1-已读',
                                `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
                                PRIMARY KEY (`id`),
                                KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='消息通知表'

CREATE TABLE `order` (
                         `id` bigint NOT NULL AUTO_INCREMENT COMMENT '订单ID',
                         `order_no` varchar(32) NOT NULL COMMENT '订单号(系统生成唯一标识)',
                         `user_id` bigint NOT NULL COMMENT '买家用户ID',
                         `seller_id` bigint NOT NULL COMMENT '卖家用户ID',
                         `total_amount` decimal(10,2) NOT NULL COMMENT '订单总金额(商品总价+运费-优惠)',
                         `discount_amount` decimal(10,2) DEFAULT '0.00' COMMENT '优惠金额(优惠券/满减等)',
                         `shipping_fee` decimal(10,2) DEFAULT '0.00' COMMENT '运费',
                         `actual_amount` decimal(10,2) NOT NULL COMMENT '实付金额(总金额-优惠)',
                         `payment_method` varchar(20) DEFAULT NULL COMMENT '支付方式(ALIPAY/WECHAT/BANK_CARD/BALANCE)',
                         `payment_status` tinyint NOT NULL DEFAULT '0' COMMENT '支付状态(0:未支付 1:已支付 2:支付中 3:支付失败 4:已退款)',
                         `order_status` tinyint NOT NULL DEFAULT '0' COMMENT '订单状态(0:待支付 1:待发货 2:待收货 3:已完成 4:已取消 5:已关闭 6:无效订单)',
                         `shipping_address` text COMMENT '收货地址(JSON格式)',
                         `buyer_note` text COMMENT '买家备注',
                         `seller_note` text COMMENT '卖家备注',
                         `remark` varchar(500) DEFAULT NULL COMMENT '订单备注(系统/客服备注)',
                         `delivery_company` varchar(50) DEFAULT NULL COMMENT '物流公司',
                         `tracking_number` varchar(100) DEFAULT NULL COMMENT '物流单号',
                         `payment_time` datetime DEFAULT NULL COMMENT '支付时间',
                         `shipping_time` datetime DEFAULT NULL COMMENT '发货时间',
                         `delivery_time` datetime DEFAULT NULL COMMENT '物流送达时间',
                         `completion_time` datetime DEFAULT NULL COMMENT '订单完成时间(确认收货时间)',
                         `receive_time` datetime DEFAULT NULL COMMENT '收货时间',
                         `cancel_time` datetime DEFAULT NULL COMMENT '取消时间',
                         `cancel_reason` varchar(255) DEFAULT NULL COMMENT '取消原因',
                         `ip_address` varchar(64) DEFAULT NULL COMMENT '下单IP地址',
                         `device_info` varchar(100) DEFAULT NULL COMMENT '设备信息(浏览器/APP版本等)',
                         `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '是否删除(0:否 1:是)',
                         `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                         `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                         PRIMARY KEY (`id`),
                         UNIQUE KEY `uk_order_no` (`order_no`),
                         KEY `idx_user_id` (`user_id`),
                         KEY `idx_seller_id` (`seller_id`),
                         KEY `idx_order_status` (`order_status`),
                         KEY `idx_payment_status` (`payment_status`),
                         KEY `idx_created_at` (`created_at`),
                         KEY `idx_user_seller` (`user_id`,`seller_id`),
                         KEY `idx_order_seller_status` (`seller_id`,`order_status`,`deleted`),
                         KEY `idx_order_date_status` (`created_at`,`order_status`,`deleted`)
) ENGINE=InnoDB AUTO_INCREMENT=40015 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='订单主表'

CREATE TABLE `order_item` (
                              `id` bigint NOT NULL AUTO_INCREMENT COMMENT '订单商品ID',
                              `order_id` bigint NOT NULL COMMENT '订单ID',
                              `product_id` bigint NOT NULL COMMENT '商品ID',
                              `product_name` varchar(255) NOT NULL COMMENT '商品名称',
                              `product_image` varchar(500) DEFAULT NULL COMMENT '商品图片',
                              `product_price` decimal(10,2) NOT NULL COMMENT '商品单价',
                              `quantity` int NOT NULL COMMENT '购买数量',
                              `total_price` decimal(10,2) NOT NULL COMMENT '小计金额',
                              `specifications` varchar(500) DEFAULT NULL COMMENT '商品规格',
                              `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                              PRIMARY KEY (`id`),
                              KEY `idx_order_id` (`order_id`),
                              KEY `idx_product_id` (`product_id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='订单商品表'

-- price_anomaly_alerts
-- Table 'agriculture_mall.price_anomaly_alerts' doesn't exist-- price_forecast_cache
-- Table 'agriculture_mall.price_forecast_cache' doesn't exist-- price_market_data
-- Table 'agriculture_mall.price_market_data' doesn't exist-- price_user_reports
-- Table 'agriculture_mall.price_user_reports' doesn't existCREATE TABLE `product` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '商品ID',
  `name` varchar(255) NOT NULL COMMENT '商品名称',
  `description` text COMMENT '商品描述',
  `image` varchar(500) DEFAULT NULL COMMENT '商品主图',
  `price` decimal(10,2) NOT NULL COMMENT '商品价格',
  `original_price` decimal(10,2) DEFAULT NULL COMMENT '原价',
  `stock` int NOT NULL DEFAULT '0' COMMENT '库存数量',
  `sales_count` int DEFAULT '0' COMMENT '销售数量',
  `rating` decimal(3,2) DEFAULT '0.00' COMMENT '平均评分',
  `review_count` int DEFAULT '0' COMMENT '评价数量',
  `category_id` bigint NOT NULL COMMENT '分类ID',
  `seller_id` bigint NOT NULL DEFAULT '1',
  `brand` varchar(100) DEFAULT NULL COMMENT '品牌',
  `origin` varchar(100) DEFAULT NULL COMMENT '产地',
  `unit` varchar(20) DEFAULT NULL COMMENT '单位(斤/公斤/箱等)',
  `shelf_life` varchar(50) DEFAULT NULL COMMENT '保质期',
  `storage_method` varchar(100) DEFAULT NULL COMMENT '储存方式',
  `tags` varchar(500) DEFAULT NULL COMMENT '标签(JSON格式)',
  `specifications` text COMMENT '规格参数(JSON格式)',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态(0:下架 1:上架 2:审核中)',
  `is_featured` tinyint DEFAULT '0' COMMENT '是否精选(0:否 1:是)',
  `is_hot` tinyint DEFAULT '0' COMMENT '是否热门(0:否 1:是)',
  `is_new` tinyint DEFAULT '0' COMMENT '是否新品(0:否 1:是)',
  `sort_order` int DEFAULT '0' COMMENT '排序权重',
  `view_count` int DEFAULT '0' COMMENT '浏览次数',
  `favorite_count` int DEFAULT '0' COMMENT '收藏次数',
  `like_count` int NOT NULL DEFAULT '0' COMMENT '点赞数量',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                                                    `deleted` tinyint DEFAULT '0' COMMENT '逻辑删除(0:未删除 1:已删除)',
                                                                    `has_traceability` tinyint(1) DEFAULT '0' COMMENT '是否有溯源信息(0:无 1:有)',
                                                                    `trace_code` varchar(128) DEFAULT NULL COMMENT '关联的溯源码',
                                                                    `qr_code_url` varchar(255) DEFAULT NULL COMMENT '二维码图片URL',
                                                                    `source_type` varchar(20) DEFAULT 'seller_upload' COMMENT '产品来源类型(admin_direct:产品直购 seller_upload:销售者上传)',
                                                                    PRIMARY KEY (`id`),
                                                                    KEY `idx_category_id` (`category_id`),
                                                                    KEY `idx_seller_id` (`seller_id`),
                                                                    KEY `idx_status` (`status`),
                                                                    KEY `idx_created_at` (`created_at`),
                                                                    KEY `idx_sales_count` (`sales_count`),
                                                                    KEY `idx_rating` (`rating`),
                                                                    KEY `idx_product_seller_status` (`seller_id`,`status`,`deleted`),
                                                                    KEY `idx_product_trace_code` (`trace_code`),
                                                                    KEY `idx_product_has_traceability` (`has_traceability`),
                                                                    KEY `idx_like_count` (`like_count`),
                                                                    KEY `idx_rating_sales` (`rating`,`sales_count`),
                                                                    KEY `idx_status_created` (`status`,`created_at`),
                                                                    KEY `idx_product_stats` (`like_count`,`favorite_count`,`review_count`),
                                                                    KEY `idx_product_rating_sales` (`rating`,`sales_count`),
                                                                    KEY `idx_source_type` (`source_type`)
                                                                    ) ENGINE=InnoDB AUTO_INCREMENT=6035 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='商品主表'

CREATE TABLE `product_attribute` (
                                     `id` bigint NOT NULL AUTO_INCREMENT COMMENT '属性ID',
                                     `category_id` bigint NOT NULL COMMENT '分类ID',
                                     `name` varchar(100) NOT NULL COMMENT '属性名称',
                                     `type` tinyint NOT NULL DEFAULT '1' COMMENT '属性类型(1:文本 2:数字 3:选择 4:多选 5:日期)',
                                     `options` text COMMENT '选项值(JSON格式)',
                                     `is_required` tinyint DEFAULT '0' COMMENT '是否必填(0:否 1:是)',
                                     `is_searchable` tinyint DEFAULT '0' COMMENT '是否可搜索(0:否 1:是)',
                                     `sort_order` int DEFAULT '0' COMMENT '排序',
                                     `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态(0:禁用 1:启用)',
                                     `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                     `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                     PRIMARY KEY (`id`),
                                     KEY `idx_category_id` (`category_id`),
                                     KEY `idx_sort_order` (`sort_order`),
                                     CONSTRAINT `fk_product_attribute_category` FOREIGN KEY (`category_id`) REFERENCES `category` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='产品属性定义表'

CREATE TABLE `product_attribute_value` (
                                           `id` bigint NOT NULL AUTO_INCREMENT COMMENT '属性值ID',
                                           `product_id` bigint NOT NULL COMMENT '产品ID',
                                           `attribute_id` bigint NOT NULL COMMENT '属性ID',
                                           `value` text NOT NULL COMMENT '属性值',
                                           `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                           `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                           PRIMARY KEY (`id`),
                                           UNIQUE KEY `uk_product_attribute` (`product_id`,`attribute_id`),
                                           KEY `idx_product_id` (`product_id`),
                                           KEY `idx_attribute_id` (`attribute_id`),
                                           CONSTRAINT `fk_product_attr_value_attribute` FOREIGN KEY (`attribute_id`) REFERENCES `product_attribute` (`id`),
                                           CONSTRAINT `fk_product_attr_value_product` FOREIGN KEY (`product_id`) REFERENCES `product` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='产品属性值表'

CREATE TABLE `product_favorite` (
                                    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                    `user_id` bigint NOT NULL COMMENT '用户ID',
                                    `product_id` bigint NOT NULL COMMENT '商品ID',
                                    `created_at` datetime NOT NULL COMMENT '创建时间',
                                    `updated_at` datetime NOT NULL COMMENT '更新时间',
                                    `deleted` int NOT NULL DEFAULT '0' COMMENT '删除标记:0-未删除,1-已删除',
                                    `folder_name` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT '默认收藏夹' COMMENT '收藏夹名称',
                                    PRIMARY KEY (`id`),
                                    UNIQUE KEY `uk_user_product` (`user_id`,`product_id`),
                                    KEY `idx_user_created` (`user_id`,`created_at`),
                                    KEY `idx_product_created` (`product_id`,`created_at`),
                                    KEY `idx_user_id` (`user_id`),
                                    KEY `idx_product_id` (`product_id`),
                                    KEY `idx_user_folder` (`user_id`,`folder_name`),
                                    KEY `idx_folder_created` (`folder_name`,`created_at`),
                                    CONSTRAINT `fk_product_favorite_product` FOREIGN KEY (`product_id`) REFERENCES `product` (`id`) ON DELETE CASCADE,
                                    CONSTRAINT `fk_product_favorite_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品收藏表'

CREATE TABLE `product_image` (
                                 `id` bigint NOT NULL AUTO_INCREMENT COMMENT '图片ID',
                                 `product_id` bigint NOT NULL COMMENT '商品ID',
                                 `image_url` varchar(500) NOT NULL COMMENT '图片URL',
                                 `image_type` tinyint NOT NULL DEFAULT '1' COMMENT '图片类型(1:主图 2:详情图 3:规格图)',
                                 `sort_order` int DEFAULT '0' COMMENT '排序',
                                 `alt_text` varchar(255) DEFAULT NULL COMMENT '图片描述',
                                 `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                 PRIMARY KEY (`id`),
                                 KEY `idx_product_id` (`product_id`),
                                 KEY `idx_image_type` (`image_type`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='商品图片表'

CREATE TABLE `product_likes` (
                                 `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                 `user_id` bigint NOT NULL COMMENT '用户ID',
                                 `product_id` bigint NOT NULL COMMENT '商品ID',
                                 `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                 `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                 PRIMARY KEY (`id`),
                                 UNIQUE KEY `uk_user_product` (`user_id`,`product_id`),
                                 KEY `idx_product_id` (`product_id`),
                                 KEY `idx_user_id` (`user_id`),
                                 KEY `idx_created_at` (`created_at`),
                                 CONSTRAINT `fk_product_likes_product` FOREIGN KEY (`product_id`) REFERENCES `product` (`id`) ON DELETE CASCADE,
                                 CONSTRAINT `fk_product_likes_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品点赞表'

CREATE TABLE `product_price_history` (
                                         `id` bigint NOT NULL AUTO_INCREMENT COMMENT '价格历史ID',
                                         `product_id` bigint NOT NULL COMMENT '商品ID',
                                         `price` decimal(10,2) NOT NULL COMMENT '价格',
                                         `original_price` decimal(10,2) DEFAULT NULL COMMENT '原价',
                                         `change_reason` varchar(100) DEFAULT NULL COMMENT '变价原因',
                                         `effective_date` datetime NOT NULL COMMENT '生效时间',
                                         `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                         `source` varchar(50) DEFAULT 'manual' COMMENT '数据来源：manual-手动，huinong-惠农网，user-用户上报',
                                         `market_name` varchar(100) DEFAULT NULL COMMENT '市场名称',
                                         `region` varchar(100) DEFAULT NULL COMMENT '地区信息',
                                         `unit` varchar(20) DEFAULT '斤' COMMENT '价格单位',
                                         `quality_score` decimal(3,2) DEFAULT '1.00' COMMENT '数据质量评分(0-1)',
                                         `crawl_time` datetime DEFAULT NULL COMMENT '数据爬取时间',
                                         PRIMARY KEY (`id`),
                                         KEY `idx_product_id` (`product_id`),
                                         KEY `idx_effective_date` (`effective_date`),
                                         KEY `idx_product_time` (`product_id`,`effective_date` DESC),
                                         KEY `idx_source_time` (`source`,`created_at` DESC),
                                         KEY `idx_region_time` (`region`,`effective_date` DESC)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='商品价格历史表'

CREATE TABLE `product_recommendation` (
                                          `id` bigint NOT NULL AUTO_INCREMENT COMMENT '推荐ID',
                                          `user_id` bigint NOT NULL COMMENT '用户ID',
                                          `product_id` bigint NOT NULL COMMENT '商品ID',
                                          `recommendation_type` tinyint NOT NULL COMMENT '推荐类型(1:协同过滤 2:内容推荐 3:热门推荐 4:新品推荐)',
                                          `score` decimal(5,4) NOT NULL COMMENT '推荐分数',
                                          `reason` varchar(255) DEFAULT NULL COMMENT '推荐理由',
                                          `algorithm_version` varchar(20) DEFAULT NULL COMMENT '算法版本',
                                          `is_clicked` tinyint DEFAULT '0' COMMENT '是否点击(0:否 1:是)',
                                          `click_time` datetime DEFAULT NULL COMMENT '点击时间',
                                          `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                          `expired_at` datetime NOT NULL COMMENT '过期时间',
                                          PRIMARY KEY (`id`),
                                          KEY `idx_user_id` (`user_id`),
                                          KEY `idx_product_id` (`product_id`),
                                          KEY `idx_recommendation_type` (`recommendation_type`),
                                          KEY `idx_score` (`score`),
                                          KEY `idx_expired_at` (`expired_at`),
                                          KEY `idx_user_product` (`user_id`,`product_id`),
                                          KEY `idx_type_score` (`recommendation_type`,`score` DESC)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='商品推荐表'

CREATE TABLE `product_review` (
                                  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '评价ID',
                                  `product_id` bigint NOT NULL COMMENT '商品ID',
                                  `user_id` bigint NOT NULL COMMENT '用户ID',
                                  `order_id` bigint NOT NULL COMMENT '订单ID',
                                  `rating` tinyint NOT NULL COMMENT '评分(1-5)',
                                  `content` text COMMENT '评价内容',
                                  `images` varchar(1000) DEFAULT NULL COMMENT '评价图片(JSON格式)',
                                  `reply_content` text COMMENT '商家回复',
                                  `reply_time` datetime DEFAULT NULL COMMENT '回复时间',
                                  `is_anonymous` tinyint DEFAULT '0' COMMENT '是否匿名(0:否 1:是)',
                                  `like_count` int DEFAULT '0' COMMENT '点赞数',
                                  `reply_count` int NOT NULL DEFAULT '0' COMMENT '回复数量',
                                  `is_helpful` tinyint NOT NULL DEFAULT '0' COMMENT '是否有用标记',
                                  `helpful_count` int NOT NULL DEFAULT '0' COMMENT '有用标记数量',
                                  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态(0:隐藏 1:显示)',
                                  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                  `review_type` tinyint DEFAULT '1' COMMENT '评价类型：1-商品评价，2-服务评价，3-物流评价',
                                  `is_additional` tinyint DEFAULT '0' COMMENT '是否追加评价：0-否，1-是',
                                  `parent_review_id` bigint DEFAULT NULL COMMENT '父评价ID（追加评价时使用）',
                                  `additional_days` int DEFAULT '0' COMMENT '追加评价距离首次评价天数',
                                  PRIMARY KEY (`id`),
                                  KEY `idx_product_id` (`product_id`),
                                  KEY `idx_user_id` (`user_id`),
                                  KEY `idx_order_id` (`order_id`),
                                  KEY `idx_rating` (`rating`),
                                  KEY `idx_created_at` (`created_at`),
                                  KEY `idx_product_rating` (`product_id`,`rating`),
                                  KEY `idx_user_created` (`user_id`,`created_at`),
                                  KEY `idx_like_count` (`like_count`),
                                  KEY `idx_helpful_count` (`helpful_count`),
                                  KEY `idx_review_type` (`review_type`,`created_at`),
                                  KEY `idx_parent_review` (`parent_review_id`),
                                  KEY `idx_additional` (`is_additional`,`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='商品评价表'

CREATE TABLE `product_view_history` (
                                        `id` bigint NOT NULL AUTO_INCREMENT COMMENT '浏览记录ID',
                                        `user_id` bigint DEFAULT NULL COMMENT '用户ID(可为空，支持匿名浏览)',
                                        `product_id` bigint NOT NULL COMMENT '商品ID',
                                        `session_id` varchar(64) DEFAULT NULL COMMENT '会话ID',
                                        `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
                                        `user_agent` text COMMENT '用户代理',
                                        `view_duration` int DEFAULT '0' COMMENT '浏览时长(秒)',
                                        `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '浏览时间',
                                        PRIMARY KEY (`id`),
                                        KEY `idx_user_id` (`user_id`),
                                        KEY `idx_product_id` (`product_id`),
                                        KEY `idx_session_id` (`session_id`),
                                        KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='商品浏览记录表'

CREATE TABLE `qr_code_generation_task` (
                                           `id` bigint NOT NULL AUTO_INCREMENT COMMENT '任务ID',
                                           `trace_code` varchar(128) NOT NULL COMMENT '溯源码',
                                           `product_id` bigint NOT NULL COMMENT '产品ID',
                                           `status` tinyint DEFAULT '0' COMMENT '生成状态(0:待生成 1:生成中 2:已完成 3:失败)',
                                           `qr_code_url` varchar(255) DEFAULT NULL COMMENT '生成的二维码URL',
                                           `error_message` text COMMENT '错误信息',
                                           `retry_count` int DEFAULT '0' COMMENT '重试次数',
                                           `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                           `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                           PRIMARY KEY (`id`),
                                           UNIQUE KEY `uk_trace_code` (`trace_code`),
                                           KEY `idx_product_id` (`product_id`),
                                           KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='二维码生成任务表'

CREATE TABLE `quality_report` (
                                  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                  `trace_record_id` bigint NOT NULL COMMENT '溯源记录ID',
                                  `report_number` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '报告编号',
                                  `report_name` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '报告名称',
                                  `test_organization` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '检测机构',
                                  `test_date` datetime NOT NULL COMMENT '检测日期',
                                  `test_items` text COLLATE utf8mb4_unicode_ci COMMENT '检测项目',
                                  `test_result` text COLLATE utf8mb4_unicode_ci COMMENT '检测结果',
                                  `test_standard` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '检测标准',
                                  `is_qualified` tinyint(1) DEFAULT '1' COMMENT '是否合格 (0-不合格, 1-合格)',
                                  `report_file_url` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '报告文件URL',
                                  `remark` text COLLATE utf8mb4_unicode_ci COMMENT '备注',
                                  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除 (0-未删除, 1-已删除)',
                                  PRIMARY KEY (`id`),
                                  KEY `idx_trace_record_id` (`trace_record_id`),
                                  KEY `idx_report_number` (`report_number`),
                                  KEY `idx_test_date` (`test_date`),
                                  KEY `idx_is_qualified` (`is_qualified`),
                                  KEY `idx_created_at` (`created_at`),
                                  CONSTRAINT `quality_report_ibfk_1` FOREIGN KEY (`trace_record_id`) REFERENCES `traceability_record` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='质检报告表'

CREATE TABLE `recommendation_config` (
                                         `id` bigint NOT NULL AUTO_INCREMENT COMMENT '配置ID',
                                         `algorithm_type` varchar(50) NOT NULL COMMENT '算法类型(collaborative_filtering, content_based, hot_products, new_products)',
                                         `config_name` varchar(100) NOT NULL COMMENT '配置名称',
                                         `config_value` text NOT NULL COMMENT '配置值(JSON格式)',
                                         `weight` decimal(5,4) DEFAULT '1.0000' COMMENT '算法权重',
                                         `is_active` tinyint DEFAULT '1' COMMENT '是否启用(0:否 1:是)',
                                         `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                         `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                         PRIMARY KEY (`id`),
                                         UNIQUE KEY `uk_algorithm_config` (`algorithm_type`,`config_name`),
                                         KEY `idx_algorithm_type` (`algorithm_type`),
                                         KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='推荐算法配置表'

CREATE TABLE `review` (
                          `id` bigint NOT NULL AUTO_INCREMENT COMMENT '评价ID',
                          `user_id` bigint NOT NULL COMMENT '用户ID',
                          `product_id` bigint NOT NULL COMMENT '商品ID',
                          `order_id` bigint NOT NULL COMMENT '订单ID',
                          `rating` tinyint NOT NULL COMMENT '评分：1-5星',
                          `content` text COMMENT '评价内容',
                          `images` varchar(1000) DEFAULT NULL COMMENT '评价图片，多个图片用逗号分隔',
                          `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：0-隐藏，1-显示',
                          `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                          `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                          `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
                          PRIMARY KEY (`id`),
                          KEY `idx_user_id` (`user_id`),
                          KEY `idx_product_id` (`product_id`),
                          KEY `idx_order_id` (`order_id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='评价表'

CREATE TABLE `review_images` (
                                 `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                 `review_id` bigint NOT NULL COMMENT '评价ID',
                                 `image_url` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '图片URL',
                                 `image_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '图片名称',
                                 `image_size` int DEFAULT NULL COMMENT '图片大小(字节)',
                                 `sort_order` tinyint NOT NULL DEFAULT '0' COMMENT '排序顺序',
                                 `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                 PRIMARY KEY (`id`),
                                 KEY `idx_review_id` (`review_id`),
                                 KEY `idx_sort_order` (`review_id`,`sort_order`),
                                 CONSTRAINT `fk_review_images_review` FOREIGN KEY (`review_id`) REFERENCES `product_review` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评价图片表'

CREATE TABLE `review_likes` (
                                `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                `user_id` bigint NOT NULL COMMENT '用户ID',
                                `review_id` bigint NOT NULL COMMENT '评价ID',
                                `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                PRIMARY KEY (`id`),
                                UNIQUE KEY `uk_user_review` (`user_id`,`review_id`),
                                KEY `idx_review_id` (`review_id`),
                                KEY `idx_user_id` (`user_id`),
                                CONSTRAINT `fk_review_likes_review` FOREIGN KEY (`review_id`) REFERENCES `product_review` (`id`) ON DELETE CASCADE,
                                CONSTRAINT `fk_review_likes_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评价点赞表'

CREATE TABLE `review_replies` (
                                  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                  `review_id` bigint NOT NULL COMMENT '评价ID',
                                  `user_id` bigint NOT NULL COMMENT '回复用户ID',
                                  `parent_id` bigint DEFAULT NULL COMMENT '父回复ID(支持多级回复)',
                                  `content` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '回复内容',
                                  `reply_type` tinyint NOT NULL DEFAULT '1' COMMENT '回复类型:1-用户回复,2-商家回复',
                                  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态:0-隐藏,1-正常',
                                  `like_count` int NOT NULL DEFAULT '0' COMMENT '点赞数量',
                                  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                  PRIMARY KEY (`id`),
                                  KEY `idx_review_id` (`review_id`),
                                  KEY `idx_user_id` (`user_id`),
                                  KEY `idx_parent_id` (`parent_id`),
                                  KEY `idx_created_at` (`created_at`),
                                  CONSTRAINT `fk_review_replies_parent` FOREIGN KEY (`parent_id`) REFERENCES `review_replies` (`id`) ON DELETE CASCADE,
                                  CONSTRAINT `fk_review_replies_review` FOREIGN KEY (`review_id`) REFERENCES `product_review` (`id`) ON DELETE CASCADE,
                                  CONSTRAINT `fk_review_replies_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评价回复表'

CREATE TABLE `search_keyword` (
                                  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '关键词ID',
                                  `keyword` varchar(100) NOT NULL COMMENT '搜索关键词',
                                  `search_count` int DEFAULT '0' COMMENT '搜索次数',
                                  `result_count` int DEFAULT '0' COMMENT '平均结果数量',
                                  `click_rate` decimal(5,4) DEFAULT '0.0000' COMMENT '点击率',
                                  `category_id` bigint DEFAULT NULL COMMENT '关联分类ID',
                                  `is_hot` tinyint DEFAULT '0' COMMENT '是否热门关键词(0:否 1:是)',
                                  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态(0:禁用 1:启用)',
                                  `last_search_at` datetime DEFAULT NULL COMMENT '最后搜索时间',
                                  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                  PRIMARY KEY (`id`),
                                  UNIQUE KEY `uk_keyword` (`keyword`),
                                  KEY `idx_search_count` (`search_count`),
                                  KEY `idx_is_hot` (`is_hot`),
                                  KEY `idx_category_id` (`category_id`),
                                  KEY `idx_last_search_at` (`last_search_at`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='搜索关键词统计表'

CREATE TABLE `seller_application` (
                                      `id` bigint NOT NULL AUTO_INCREMENT COMMENT '申请ID',
                                      `user_id` bigint NOT NULL COMMENT '用户ID',
                                      `applicant_type` varchar(20) NOT NULL COMMENT '申请者类型: personal(个人), business(企业)',
                                      `name` varchar(100) NOT NULL COMMENT '姓名或企业名称',
                                      `id_number` varchar(50) NOT NULL COMMENT '身份证号或营业执照号',
                                      `phone` varchar(20) NOT NULL COMMENT '联系电话',
                                      `email` varchar(100) NOT NULL COMMENT '联系邮箱',
                                      `address` varchar(255) NOT NULL COMMENT '地址',
                                      `farm_name` varchar(255) DEFAULT NULL COMMENT '农场名称',
                                      `contact_info` varchar(500) DEFAULT NULL COMMENT '联系信息',
                                      `qualification_doc_urls` text COMMENT '资质文档URL列表，以逗号分隔',
                                      `description` text NOT NULL COMMENT '销售描述',
                                      `certificate_urls` text COMMENT '证书URL列表，以逗号分隔',
                                      `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '状态: pending(待审核), approved(已通过), rejected(已拒绝)',
                                      `reject_reason` text COMMENT '拒绝原因',
                                      `approved_by` bigint DEFAULT NULL COMMENT '审批人ID',
                                      `approved_time` datetime DEFAULT NULL COMMENT '审批时间',
                                      `audit_comment` text COMMENT '审核意见',
                                      `auditor_id` bigint DEFAULT NULL COMMENT '审核员ID',
                                      `audit_date` datetime DEFAULT NULL COMMENT '审核日期',
                                      `apply_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '申请日期',
                                      `expected_completion_time` datetime DEFAULT NULL COMMENT '预期完成时间',
                                      `approved_at` datetime DEFAULT NULL COMMENT '批准时间',
                                      `contact_name` varchar(100) DEFAULT NULL COMMENT '联系人姓名',
                                      `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系人电话',
                                      `contact_email` varchar(100) DEFAULT NULL COMMENT '联系人邮箱',
                                      `business_license` varchar(255) DEFAULT NULL COMMENT '营业执照',
                                      `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                      `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                      `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                      `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                      `contact_address` varchar(255) DEFAULT NULL,
                                      `business_scope` varchar(255) DEFAULT NULL,
                                      `admin_comment` varchar(255) DEFAULT NULL,
                                      `deleted` tinyint(1) DEFAULT '0',
                                      PRIMARY KEY (`id`),
                                      KEY `idx_user_id` (`user_id`),
                                      KEY `idx_status` (`status`),
                                      KEY `idx_create_time` (`create_time`),
                                      KEY `idx_seller_application_status` (`status`,`deleted`,`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='销售者申请表'

CREATE TABLE `seller_notification` (
                                       `id` bigint NOT NULL AUTO_INCREMENT COMMENT '通知ID',
                                       `seller_id` bigint NOT NULL COMMENT '销售者用户ID',
                                       `type` varchar(20) NOT NULL COMMENT '通知类型(order:订单 product:商品 system:系统)',
                                       `title` varchar(100) NOT NULL COMMENT '通知标题',
                                       `content` text NOT NULL COMMENT '通知内容',
                                       `related_id` bigint DEFAULT NULL COMMENT '关联ID(订单ID、商品ID等)',
                                       `is_read` tinyint NOT NULL DEFAULT '0' COMMENT '是否已读(0:未读 1:已读)',
                                       `priority` tinyint NOT NULL DEFAULT '1' COMMENT '优先级(1:低 2:中 3:高)',
                                       `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                       `read_at` datetime DEFAULT NULL COMMENT '阅读时间',
                                       PRIMARY KEY (`id`),
                                       KEY `idx_seller_id` (`seller_id`),
                                       KEY `idx_type` (`type`),
                                       KEY `idx_is_read` (`is_read`),
                                       KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='销售者通知消息表'

CREATE TABLE `seller_product` (
                                  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '关联ID',
                                  `seller_id` bigint NOT NULL COMMENT '销售者ID',
                                  `product_id` bigint NOT NULL COMMENT '产品ID',
                                  `seller_price` decimal(10,2) DEFAULT NULL COMMENT '销售者定价',
                                  `seller_stock` int DEFAULT '0' COMMENT '销售者库存',
                                  `commission_rate` decimal(5,4) DEFAULT '0.0000' COMMENT '佣金比例',
                                  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态(0:停售 1:在售 2:审核中)',
                                  `priority` int DEFAULT '0' COMMENT '优先级(数值越大优先级越高)',
                                  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                  PRIMARY KEY (`id`),
                                  UNIQUE KEY `uk_seller_product` (`seller_id`,`product_id`),
                                  KEY `idx_seller_id` (`seller_id`),
                                  KEY `idx_product_id` (`product_id`),
                                  KEY `idx_status` (`status`),
                                  CONSTRAINT `fk_seller_product_product` FOREIGN KEY (`product_id`) REFERENCES `product` (`id`),
                                  CONSTRAINT `fk_seller_product_seller` FOREIGN KEY (`seller_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='销售者产品关联表'

CREATE TABLE `seller_shop` (
                               `id` bigint NOT NULL AUTO_INCREMENT COMMENT '店铺ID',
                               `seller_id` bigint NOT NULL COMMENT '销售者用户ID',
                               `shop_name` varchar(100) NOT NULL COMMENT '店铺名称',
                               `shop_logo` varchar(500) DEFAULT NULL COMMENT '店铺Logo',
                               `shop_banner` varchar(500) DEFAULT NULL COMMENT '店铺横幅',
                               `shop_description` text COMMENT '店铺描述',
                               `business_hours` varchar(100) DEFAULT NULL COMMENT '营业时间',
                               `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
                               `contact_address` varchar(255) DEFAULT NULL COMMENT '联系地址',
                               `service_rating` decimal(3,2) DEFAULT '5.00' COMMENT '服务评分',
                               `delivery_rating` decimal(3,2) DEFAULT '5.00' COMMENT '物流评分',
                               `product_rating` decimal(3,2) DEFAULT '5.00' COMMENT '商品评分',
                               `total_sales` decimal(12,2) DEFAULT '0.00' COMMENT '总销售额',
                               `total_orders` int DEFAULT '0' COMMENT '总订单数',
                               `status` tinyint NOT NULL DEFAULT '1' COMMENT '店铺状态(0:关闭 1:营业)',
                               `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                               `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                               PRIMARY KEY (`id`),
                               UNIQUE KEY `uk_seller_id` (`seller_id`),
                               KEY `idx_shop_name` (`shop_name`),
                               KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='销售者店铺信息表'

CREATE TABLE `seller_statistics` (
                                     `id` bigint NOT NULL AUTO_INCREMENT COMMENT '统计ID',
                                     `seller_id` bigint NOT NULL COMMENT '销售者用户ID',
                                     `stat_date` date NOT NULL COMMENT '统计日期',
                                     `daily_sales` decimal(10,2) DEFAULT '0.00' COMMENT '日销售额',
                                     `daily_orders` int DEFAULT '0' COMMENT '日订单数',
                                     `daily_visitors` int DEFAULT '0' COMMENT '日访客数',
                                     `daily_views` int DEFAULT '0' COMMENT '日浏览量',
                                     `monthly_sales` decimal(12,2) DEFAULT '0.00' COMMENT '月销售额',
                                     `monthly_orders` int DEFAULT '0' COMMENT '月订单数',
                                     `yearly_sales` decimal(15,2) DEFAULT '0.00' COMMENT '年销售额',
                                     `yearly_orders` int DEFAULT '0' COMMENT '年订单数',
                                     `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                     `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                     PRIMARY KEY (`id`),
                                     UNIQUE KEY `uk_seller_date` (`seller_id`,`stat_date`),
                                     KEY `idx_seller_id` (`seller_id`),
                                     KEY `idx_stat_date` (`stat_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='销售者统计数据表'

CREATE TABLE `shipping_template` (
                                     `id` bigint NOT NULL AUTO_INCREMENT COMMENT '模板ID',
                                     `name` varchar(100) NOT NULL COMMENT '模板名称',
                                     `type` tinyint NOT NULL DEFAULT '1' COMMENT '计费方式(1:按重量 2:按件数 3:按体积)',
                                     `free_shipping_amount` decimal(10,2) DEFAULT NULL COMMENT '包邮金额',
                                     `default_areas` text COMMENT '默认配送区域(JSON格式)',
                                     `default_first_unit` decimal(8,3) NOT NULL COMMENT '首重/首件',
                                     `default_first_fee` decimal(10,2) NOT NULL COMMENT '首费',
                                     `default_additional_unit` decimal(8,3) NOT NULL COMMENT '续重/续件',
                                     `default_additional_fee` decimal(10,2) NOT NULL COMMENT '续费',
                                     `special_areas` text COMMENT '特殊区域配置(JSON格式)',
                                     `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态(0:禁用 1:启用)',
                                     `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                     `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                     PRIMARY KEY (`id`),
                                     KEY `idx_type` (`type`),
                                     KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='运费模板表'

CREATE TABLE `system_stats` (
                                `id` bigint NOT NULL AUTO_INCREMENT COMMENT '统计ID',
                                `stat_date` date NOT NULL COMMENT '统计日期',
                                `total_users` int DEFAULT '0' COMMENT '总用户数',
                                `active_users` int DEFAULT '0' COMMENT '活跃用户数（当日登录）',
                                `new_users` int DEFAULT '0' COMMENT '新增用户数',
                                `total_products` int DEFAULT '0' COMMENT '总商品数',
                                `online_products` int DEFAULT '0' COMMENT '在售商品数',
                                `new_products` int DEFAULT '0' COMMENT '新增商品数',
                                `total_orders` int DEFAULT '0' COMMENT '总订单数',
                                `new_orders` int DEFAULT '0' COMMENT '新增订单数',
                                `total_sales` decimal(15,2) DEFAULT '0.00' COMMENT '总销售额',
                                `daily_sales` decimal(15,2) DEFAULT '0.00' COMMENT '当日销售额',
                                `seller_applications` int DEFAULT '0' COMMENT '销售者申请数',
                                `pending_applications` int DEFAULT '0' COMMENT '待审核申请数',
                                `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                PRIMARY KEY (`id`),
                                UNIQUE KEY `uk_stat_date` (`stat_date`),
                                KEY `idx_stat_date` (`stat_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='系统统计表'

CREATE TABLE `trace_certificates` (
                                      `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                      `trace_record_id` bigint NOT NULL COMMENT '关联的溯源记录ID',
                                      `certificate_type` varchar(100) NOT NULL COMMENT '证书类型：ORGANIC(有机认证)，GREEN(绿色食品)，QUALITY(质量检测)，ORIGIN(原产地认证)',
                                      `certificate_no` varchar(100) DEFAULT NULL COMMENT '证书编号',
                                      `issuing_authority` varchar(255) DEFAULT NULL COMMENT '颁发机构',
                                      `issue_date` date DEFAULT NULL COMMENT '颁发日期',
                                      `valid_until` date DEFAULT NULL COMMENT '有效期至',
                                      `certificate_url` varchar(255) DEFAULT NULL COMMENT '证书图片URL',
                                      `description` text COMMENT '证书描述',
                                      `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                      `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                      PRIMARY KEY (`id`),
                                      KEY `idx_trace_record_id` (`trace_record_id`),
                                      KEY `idx_certificate_no` (`certificate_no`),
                                      KEY `idx_trace_certificates_type` (`certificate_type`),
                                      CONSTRAINT `fk_certificate_trace_record` FOREIGN KEY (`trace_record_id`) REFERENCES `traceability_record` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='认证信息表'

CREATE TABLE `trace_code_sequence` (
                                       `id` bigint NOT NULL AUTO_INCREMENT COMMENT '序列ID',
                                       `date_prefix` varchar(8) NOT NULL COMMENT '日期前缀(YYYYMMDD)',
                                       `sequence_number` int NOT NULL DEFAULT '1' COMMENT '当日序列号',
                                       `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                       `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                       PRIMARY KEY (`id`),
                                       UNIQUE KEY `uk_date_prefix` (`date_prefix`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='溯源码序列生成表'

CREATE TABLE `trace_codes` (
                               `code` varchar(128) NOT NULL COMMENT '溯源码（主键）',
                               `trace_record_id` bigint NOT NULL COMMENT '关联的溯源记录ID',
                               `qr_code_url` varchar(255) DEFAULT NULL COMMENT '二维码图片URL',
                               `generated_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '生成时间',
                               `status` tinyint DEFAULT '1' COMMENT '状态：0-无效，1-有效',
                               `scan_count` int DEFAULT '0' COMMENT '扫描次数统计',
                               `daily_scan_count` int DEFAULT '0' COMMENT '今日扫描次数',
                               `weekly_scan_count` int DEFAULT '0' COMMENT '本周扫描次数',
                               `monthly_scan_count` int DEFAULT '0' COMMENT '本月扫描次数',
                               `last_scan_time` datetime DEFAULT NULL COMMENT '最后扫描时间',
                               `first_scan_time` datetime DEFAULT NULL COMMENT '首次扫描时间',
                               PRIMARY KEY (`code`),
                               UNIQUE KEY `idx_trace_record_id` (`trace_record_id`),
                               CONSTRAINT `fk_trace_code_record` FOREIGN KEY (`trace_record_id`) REFERENCES `traceability_record` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='溯源码表'

CREATE TABLE `trace_logistics` (
                                   `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                   `trace_record_id` bigint NOT NULL COMMENT '关联的溯源记录ID',
                                   `carrier_name` varchar(255) DEFAULT NULL COMMENT '承运商名称',
                                   `transport_type` varchar(50) DEFAULT NULL COMMENT '运输方式：ROAD(公路)，RAILWAY(铁路)，AIR(空运)，SHIP(水运)',
                                   `departure_time` datetime DEFAULT NULL COMMENT '出发时间',
                                   `arrival_time` datetime DEFAULT NULL COMMENT '到达时间',
                                   `origin` varchar(255) DEFAULT NULL COMMENT '始发地',
                                   `destination` varchar(255) DEFAULT NULL COMMENT '目的地',
                                   `temperature` decimal(5,2) DEFAULT NULL COMMENT '运输温度（如冷链）',
                                   `humidity` decimal(5,2) DEFAULT NULL COMMENT '运输湿度',
                                   `status` tinyint DEFAULT '0' COMMENT '物流状态：0-待发货，1-运输中，2-已到达，3-已签收',
                                   `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                   PRIMARY KEY (`id`),
                                   KEY `idx_trace_record_id` (`trace_record_id`),
                                   KEY `idx_status` (`status`),
                                   CONSTRAINT `fk_logistics_trace_record` FOREIGN KEY (`trace_record_id`) REFERENCES `traceability_record` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='物流信息表'

CREATE TABLE `traceability_event` (
                                      `id` bigint NOT NULL AUTO_INCREMENT COMMENT '事件ID',
                                      `trace_record_id` bigint NOT NULL COMMENT '关联溯源记录ID',
                                      `event_sequence` int DEFAULT NULL COMMENT '事件序号',
                                      `event_type` varchar(50) NOT NULL COMMENT '事件类型',
                                      `event_date` datetime NOT NULL COMMENT '事件发生的日期和时间',
                                      `description` text COMMENT '事件的详细描述',
                                      `location` varchar(255) DEFAULT NULL COMMENT '事件发生地点',
                                      `responsible_person` varchar(100) DEFAULT NULL COMMENT '事件负责人',
                                      `attachments` text COMMENT '附件信息（JSON格式，包含图片和视频URL）',
                                      `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
                                      `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后更新时间',
                                      `deleted` tinyint(1) DEFAULT '0' COMMENT '逻辑删除标识',
                                      PRIMARY KEY (`id`),
                                      KEY `idx_trace_record_id` (`trace_record_id`),
                                      KEY `idx_event_date` (`event_date`),
                                      KEY `idx_traceability_event_type_date` (`event_type`,`event_date`),
                                      CONSTRAINT `fk_event_trace_record` FOREIGN KEY (`trace_record_id`) REFERENCES `traceability_record` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=275 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='溯源事件详情表'

CREATE TABLE `traceability_query` (
                                      `id` bigint NOT NULL AUTO_INCREMENT COMMENT '查询记录ID',
                                      `trace_code` varchar(128) NOT NULL COMMENT '查询的溯源码',
                                      `trace_record_id` bigint NOT NULL COMMENT '关联的溯源记录ID',
                                      `ip_address` varchar(50) DEFAULT NULL COMMENT '查询IP地址',
                                      `device_info` varchar(255) DEFAULT NULL COMMENT '查询设备信息',
                                      `location` varchar(255) DEFAULT NULL COMMENT '查询地理位置',
                                      `user_id` bigint DEFAULT NULL COMMENT '查询用户ID（可为空，表示未登录用户）',
                                      `query_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '查询时间',
                                      `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
                                      `deleted` tinyint(1) DEFAULT '0' COMMENT '逻辑删除标识',
                                      `query_type` varchar(50) DEFAULT 'user_query' COMMENT '查询类型(user_query:用户查询 admin_verify:管理员验证)',
                                      `product_id` bigint DEFAULT NULL COMMENT '关联的产品ID（如果查询成功）',
                                      PRIMARY KEY (`id`),
                                      KEY `idx_trace_record_id` (`trace_record_id`),
                                      KEY `idx_trace_code` (`trace_code`),
                                      KEY `idx_query_time` (`query_time`),
                                      KEY `idx_traceability_query_time_code` (`query_time`,`trace_code`)
) ENGINE=InnoDB AUTO_INCREMENT=66 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='溯源查询记录表'

CREATE TABLE `traceability_record` (
                                       `id` bigint NOT NULL AUTO_INCREMENT COMMENT '溯源记录ID',
                                       `product_id` bigint NOT NULL COMMENT '关联产品ID',
                                       `trace_code` varchar(128) NOT NULL COMMENT '唯一溯源码',
                                       `product_name` varchar(255) NOT NULL COMMENT '产品名称（冗余字段，方便查询）',
                                       `farm_name` varchar(255) DEFAULT NULL COMMENT '生产基地/农场名称',
                                       `producer_id` bigint NOT NULL COMMENT '生产者ID（关联用户表）',
                                       `producer_name` varchar(255) DEFAULT NULL COMMENT '生产者名称',
                                       `created_by` bigint DEFAULT NULL COMMENT '记录创建者ID',
                                       `batch_number` varchar(100) DEFAULT NULL COMMENT '批次号',
                                       `specification` varchar(255) DEFAULT NULL COMMENT '产品规格',
                                       `quality_grade` varchar(50) DEFAULT NULL COMMENT '质量等级',
                                       `creation_date` date DEFAULT NULL COMMENT '溯源记录创建日期（通常是开始生产的日期）',
                                       `harvest_date` date DEFAULT NULL COMMENT '采摘/收获日期',
                                       `packaging_date` date DEFAULT NULL COMMENT '包装日期',
                                       `qr_code_url` varchar(255) DEFAULT NULL COMMENT '二维码图片URL',
                                       `status` tinyint DEFAULT '0' COMMENT '溯源记录状态（0:草稿/待完善, 1:待审核, 2:已发布, 3:已下架, 4:异常）',
                                       `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
                                       `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后更新时间',
                                       `deleted` tinyint(1) DEFAULT '0' COMMENT '逻辑删除标识',
                                       `source_type` varchar(20) DEFAULT 'seller_upload' COMMENT '产品来源类型(admin_direct:产品直购 seller_upload:销售者上传)',
                                       `production_date` date DEFAULT NULL COMMENT '生产日期',
                                       `processing_date` date DEFAULT NULL COMMENT '加工日期',
                                       `certifications` text COMMENT '认证信息（有机认证、绿色食品认证等）',
                                       `quality_test_results` text COMMENT '质量检测结果',
                                       `pesticides_used` text COMMENT '农药使用情况',
                                       `fertilizers_used` text COMMENT '肥料使用情况',
                                       `irrigation_method` varchar(100) DEFAULT NULL COMMENT '灌溉方式',
                                       `soil_condition` text COMMENT '土壤条件',
                                       `weather_conditions` text COMMENT '天气条件',
                                       `harvest_method` varchar(100) DEFAULT NULL COMMENT '收获方式',
                                       `processing_method` text COMMENT '加工方式',
                                       `packaging_material` varchar(200) DEFAULT NULL COMMENT '包装材料',
                                       `storage_conditions` text COMMENT '储存条件',
                                       `transportation_method` varchar(100) DEFAULT NULL COMMENT '运输方式',
                                       `additional_notes` text COMMENT '附加说明',
                                       `production_info` text COMMENT '生产环节信息(JSON格式)',
                                       `processing_info` text COMMENT '加工环节信息(JSON格式)',
                                       `circulation_info` text COMMENT '流通环节信息(JSON格式)',
                                       PRIMARY KEY (`id`),
                                       UNIQUE KEY `idx_trace_code` (`trace_code`),
                                       UNIQUE KEY `uk_product_id` (`product_id`),
                                       KEY `idx_producer_id` (`producer_id`),
                                       KEY `idx_product_id` (`product_id`),
                                       KEY `idx_source_type` (`source_type`)
) ENGINE=InnoDB AUTO_INCREMENT=102 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='溯源主记录表'

CREATE TABLE `user` (
                        `id` bigint NOT NULL AUTO_INCREMENT,
                        `username` varchar(50) NOT NULL,
                        `password` varchar(100) NOT NULL,
                        `nickname` varchar(50) DEFAULT NULL,
                        `avatar` varchar(255) DEFAULT NULL,
                        `phone` varchar(20) DEFAULT NULL,
                        `email` varchar(100) DEFAULT NULL,
                        `role` varchar(20) NOT NULL,
                        `status` int NOT NULL DEFAULT '1',
                        `created_at` datetime NOT NULL,
                        `updated_at` datetime NOT NULL,
                        `deleted` int NOT NULL DEFAULT '0',
                        `region` varchar(255) DEFAULT NULL COMMENT '所在地区',
                        `bio` text COMMENT '个人简介',
                        `address` varchar(128) DEFAULT NULL,
                        `city` varchar(32) DEFAULT NULL,
                        `create_time` datetime(6) DEFAULT NULL,
                        `district` varchar(32) DEFAULT NULL,
                        `fans` int DEFAULT NULL,
                        `focus` int DEFAULT NULL,
                        `gender` int DEFAULT NULL,
                        `id_card` varchar(18) DEFAULT NULL,
                        `integral` int DEFAULT NULL,
                        `is_real_name_auth` bit(1) DEFAULT NULL,
                        `is_vip` bit(1) DEFAULT NULL,
                        `last_login_time` datetime(6) DEFAULT NULL,
                        `latitude` double DEFAULT NULL,
                        `level` int DEFAULT NULL,
                        `longitude` double DEFAULT NULL,
                        `openid` varchar(32) DEFAULT NULL,
                        `province` varchar(32) DEFAULT NULL,
                        `real_name` varchar(64) DEFAULT NULL,
                        `update_time` datetime(6) DEFAULT NULL,
                        `user_type` varchar(20) DEFAULT 'normal' COMMENT '用户类型',
                        `vip_expire_time` datetime(6) DEFAULT NULL,
                        `country` varchar(50) DEFAULT NULL,
                        `language` varchar(255) DEFAULT NULL,
                        `session_key` varchar(128) DEFAULT NULL,
                        `total_likes_given` int NOT NULL DEFAULT '0' COMMENT '总点赞数(给出)',
                        `total_likes_received` int NOT NULL DEFAULT '0' COMMENT '总点赞数(收到)',
                        `total_reviews` int NOT NULL DEFAULT '0' COMMENT '总评价数',
                        `total_favorites` int NOT NULL DEFAULT '0' COMMENT '总收藏数',
                        PRIMARY KEY (`id`),
                        UNIQUE KEY `username` (`username`),
                        UNIQUE KEY `UK_bpouh13r5970grppm1u0a5lu` (`openid`),
                        KEY `idx_user_role_status` (`role`,`user_type`,`status`,`deleted`),
                        KEY `idx_total_reviews` (`total_reviews`),
                        KEY `idx_role_status` (`role`,`status`),
                        KEY `idx_user_stats` (`total_reviews`,`total_likes_given`,`total_favorites`)
) ENGINE=InnoDB AUTO_INCREMENT=40 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户表'

CREATE TABLE `user_address` (
                                `id` bigint NOT NULL AUTO_INCREMENT COMMENT '地址ID',
                                `user_id` bigint NOT NULL COMMENT '用户ID',
                                `receiver_name` varchar(50) NOT NULL COMMENT '收货人姓名',
                                `receiver_phone` varchar(20) NOT NULL COMMENT '收货人电话',
                                `province` varchar(50) NOT NULL COMMENT '省份',
                                `city` varchar(50) NOT NULL COMMENT '城市',
                                `district` varchar(50) NOT NULL COMMENT '区县',
                                `detail_address` varchar(255) NOT NULL COMMENT '详细地址',
                                `postal_code` varchar(10) DEFAULT NULL COMMENT '邮政编码',
                                `is_default` tinyint NOT NULL DEFAULT '0' COMMENT '是否默认地址(0:否 1:是)',
                                `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                PRIMARY KEY (`id`),
                                KEY `idx_user_id` (`user_id`),
                                KEY `idx_is_default` (`is_default`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户地址管理表'

CREATE TABLE `user_behavior` (
                                 `id` bigint NOT NULL AUTO_INCREMENT COMMENT '行为ID',
                                 `user_id` bigint NOT NULL COMMENT '用户ID',
                                 `product_id` bigint NOT NULL COMMENT '商品ID',
                                 `behavior_type` tinyint NOT NULL COMMENT '行为类型(1:浏览 2:收藏 3:加购物车 4:购买)',
                                 `behavior_value` decimal(5,2) DEFAULT '1.00' COMMENT '行为权重值',
                                 `behavior_duration` int DEFAULT NULL COMMENT '行为持续时间(秒)',
                                 `session_id` varchar(64) DEFAULT NULL COMMENT '会话ID',
                                 `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
                                 `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
                                 `page_source` varchar(100) DEFAULT NULL COMMENT '页面来源',
                                 `referrer` varchar(500) DEFAULT NULL COMMENT '引用页面',
                                 `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                 PRIMARY KEY (`id`),
                                 KEY `idx_user_id` (`user_id`),
                                 KEY `idx_product_id` (`product_id`),
                                 KEY `idx_behavior_type` (`behavior_type`),
                                 KEY `idx_created_at` (`created_at`),
                                 KEY `idx_session_id` (`session_id`),
                                 KEY `idx_user_behavior_time` (`user_id`,`behavior_type`,`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=91 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户行为表'

CREATE TABLE `user_favorite_folders` (
                                         `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                         `user_id` bigint NOT NULL COMMENT '用户ID',
                                         `folder_name` varchar(50) NOT NULL COMMENT '收藏夹名称',
                                         `description` varchar(200) DEFAULT NULL COMMENT '收藏夹描述',
                                         `sort_order` int DEFAULT '0' COMMENT '排序顺序',
                                         `is_default` tinyint DEFAULT '0' COMMENT '是否默认收藏夹：0-否，1-是',
                                         `product_count` int DEFAULT '0' COMMENT '收藏商品数量',
                                         `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                         `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                         PRIMARY KEY (`id`),
                                         UNIQUE KEY `uk_user_folder` (`user_id`,`folder_name`),
                                         KEY `idx_user_sort` (`user_id`,`sort_order`),
                                         KEY `idx_user_default` (`user_id`,`is_default`),
                                         CONSTRAINT `user_favorite_folders_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户收藏夹管理表'

CREATE TABLE `user_search_history` (
                                       `id` bigint NOT NULL AUTO_INCREMENT COMMENT '搜索历史ID',
                                       `user_id` bigint NOT NULL COMMENT '用户ID',
                                       `keyword` varchar(100) NOT NULL COMMENT '搜索关键词',
                                       `result_count` int DEFAULT '0' COMMENT '结果数量',
                                       `search_filters` text COMMENT '搜索筛选条件(JSON格式)',
                                       `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                       PRIMARsY KEY (`id`),
                                       KEY `idx_user_id` (`user_id`),
                                       KEY `idx_keyword` (`keyword`),
                                       KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户搜索历史表'

