<!DOCTYPE html>
<html>
<head>
    <title>Generate Images</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .section {
            background: white;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .section h2 {
            margin-top: 0;
            color: #333;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .item {
            text-align: center;
        }
        canvas {
            border: 1px solid #ddd;
            margin-bottom: 10px;
        }
        button {
            background: #409eff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #66b1ff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>图片生成工具</h1>
        
        <!-- 支付图标 -->
        <div class="section">
            <h2>支付图标 (24x24px)</h2>
            <div class="grid">
                <div class="item">
                    <canvas id="alipay" width="24" height="24"></canvas>
                    <div>支付宝</div>
                    <button onclick="downloadImage('alipay', 'alipay.png')">下载</button>
                </div>
                <div class="item">
                    <canvas id="wechat" width="24" height="24"></canvas>
                    <div>微信支付</div>
                    <button onclick="downloadImage('wechat', 'wechat.png')">下载</button>
                </div>
            </div>
        </div>

        <!-- 商品图片 -->
        <div class="section">
            <h2>商品图片 (400x400px)</h2>
            <div class="grid">
                <div class="item">
                    <canvas id="rice-seeds" width="400" height="400"></canvas>
                    <div>水稻种子</div>
                    <button onclick="downloadImage('rice-seeds', 'rice-seeds.jpg')">下载</button>
                </div>
                <div class="item">
                    <canvas id="rice-seeds-2" width="400" height="400"></canvas>
                    <div>水稻种子2</div>
                    <button onclick="downloadImage('rice-seeds-2', 'rice-seeds-2.jpg')">下载</button>
                </div>
                <div class="item">
                    <canvas id="rice-seeds-3" width="400" height="400"></canvas>
                    <div>水稻种子3</div>
                    <button onclick="downloadImage('rice-seeds-3', 'rice-seeds-3.jpg')">下载</button>
                </div>
                <div class="item">
                    <canvas id="fertilizer" width="400" height="400"></canvas>
                    <div>有机复合肥</div>
                    <button onclick="downloadImage('fertilizer', 'fertilizer.jpg')">下载</button>
                </div>
                <div class="item">
                    <canvas id="sprayer" width="400" height="400"></canvas>
                    <div>智能喷药机</div>
                    <button onclick="downloadImage('sprayer', 'sprayer.jpg')">下载</button>
                </div>
                <div class="item">
                    <canvas id="pesticide" width="400" height="400"></canvas>
                    <div>农药</div>
                    <button onclick="downloadImage('pesticide', 'pesticide.jpg')">下载</button>
                </div>
                <div class="item">
                    <canvas id="seeds" width="400" height="400"></canvas>
                    <div>种子</div>
                    <button onclick="downloadImage('seeds', 'seeds.jpg')">下载</button>
                </div>
                <div class="item">
                    <canvas id="tools" width="400" height="400"></canvas>
                    <div>农具</div>
                    <button onclick="downloadImage('tools', 'tools.jpg')">下载</button>
                </div>
                <div class="item">
                    <canvas id="machinery" width="400" height="400"></canvas>
                    <div>农业机械</div>
                    <button onclick="downloadImage('machinery', 'machinery.jpg')">下载</button>
                </div>
                <div class="item">
                    <canvas id="irrigation" width="400" height="400"></canvas>
                    <div>灌溉设备</div>
                    <button onclick="downloadImage('irrigation', 'irrigation.jpg')">下载</button>
                </div>
                <div class="item">
                    <canvas id="review1" width="400" height="400"></canvas>
                    <div>评价图片1</div>
                    <button onclick="downloadImage('review1', 'review1.jpg')">下载</button>
                </div>
                <div class="item">
                    <canvas id="review2" width="400" height="400"></canvas>
                    <div>评价图片2</div>
                    <button onclick="downloadImage('review2', 'review2.jpg')">下载</button>
                </div>
            </div>
        </div>

        <!-- 新闻图片 -->
        <div class="section">
            <h2>新闻图片 (800x400px)</h2>
            <div class="grid">
                <div class="item">
                    <canvas id="news1" width="800" height="400"></canvas>
                    <div>农业新闻1</div>
                    <button onclick="downloadImage('news1', 'news1.jpg')">下载</button>
                </div>
                <div class="item">
                    <canvas id="news2" width="800" height="400"></canvas>
                    <div>农业新闻2</div>
                    <button onclick="downloadImage('news2', 'news2.jpg')">下载</button>
                </div>
                <div class="item">
                    <canvas id="news3" width="800" height="400"></canvas>
                    <div>农业新闻3</div>
                    <button onclick="downloadImage('news3', 'news3.jpg')">下载</button>
                </div>
            </div>
        </div>

        <!-- 图标 -->
        <div class="section">
            <h2>图标 (24x24px)</h2>
            <div class="grid">
                <div class="item">
                    <canvas id="home" width="24" height="24"></canvas>
                    <div>首页</div>
                    <button onclick="downloadImage('home', 'home.png')">下载</button>
                </div>
                <div class="item">
                    <canvas id="price" width="24" height="24"></canvas>
                    <div>价格查询</div>
                    <button onclick="downloadImage('price', 'price.png')">下载</button>
                </div>
                <div class="item">
                    <canvas id="analysis" width="24" height="24"></canvas>
                    <div>市场分析</div>
                    <button onclick="downloadImage('analysis', 'analysis.png')">下载</button>
                </div>
                <div class="item">
                    <canvas id="news" width="24" height="24"></canvas>
                    <div>农业新闻</div>
                    <button onclick="downloadImage('news', 'news.png')">下载</button>
                </div>
                <div class="item">
                    <canvas id="profile" width="24" height="24"></canvas>
                    <div>个人中心</div>
                    <button onclick="downloadImage('profile', 'profile.png')">下载</button>
                </div>
                <div class="item">
                    <canvas id="shop" width="24" height="24"></canvas>
                    <div>农品汇</div>
                    <button onclick="downloadImage('shop', 'shop.png')">下载</button>
                </div>
                <div class="item">
                    <canvas id="cart" width="24" height="24"></canvas>
                    <div>购物车</div>
                    <button onclick="downloadImage('cart', 'cart.png')">下载</button>
                </div>
                <div class="item">
                    <canvas id="search" width="24" height="24"></canvas>
                    <div>搜索</div>
                    <button onclick="downloadImage('search', 'search.png')">下载</button>
                </div>
                <div class="item">
                    <canvas id="filter" width="24" height="24"></canvas>
                    <div>筛选</div>
                    <button onclick="downloadImage('filter', 'filter.png')">下载</button>
                </div>
                <div class="item">
                    <canvas id="sort" width="24" height="24"></canvas>
                    <div>排序</div>
                    <button onclick="downloadImage('sort', 'sort.png')">下载</button>
                </div>
                <div class="item">
                    <canvas id="delete" width="24" height="24"></canvas>
                    <div>删除</div>
                    <button onclick="downloadImage('delete', 'delete.png')">下载</button>
                </div>
                <div class="item">
                    <canvas id="edit" width="24" height="24"></canvas>
                    <div>编辑</div>
                    <button onclick="downloadImage('edit', 'edit.png')">下载</button>
                </div>
                <div class="item">
                    <canvas id="add" width="24" height="24"></canvas>
                    <div>添加</div>
                    <button onclick="downloadImage('add', 'add.png')">下载</button>
                </div>
                <div class="item">
                    <canvas id="star" width="24" height="24"></canvas>
                    <div>星星</div>
                    <button onclick="downloadImage('star', 'star.png')">下载</button>
                </div>
                <div class="item">
                    <canvas id="star-filled" width="24" height="24"></canvas>
                    <div>星星填充</div>
                    <button onclick="downloadImage('star-filled', 'star-filled.png')">下载</button>
                </div>
                <div class="item">
                    <canvas id="star-half" width="24" height="24"></canvas>
                    <div>星星半填充</div>
                    <button onclick="downloadImage('star-half', 'star-half.png')">下载</button>
                </div>
            </div>
        </div>

        <!-- Logo -->
        <div class="section">
            <h2>Logo</h2>
            <div class="grid">
                <div class="item">
                    <canvas id="logo" width="200" height="60"></canvas>
                    <div>Logo (大尺寸)</div>
                    <button onclick="downloadImage('logo', 'logo.png')">下载</button>
                </div>
                <div class="item">
                    <canvas id="logo-small" width="100" height="30"></canvas>
                    <div>Logo (小尺寸)</div>
                    <button onclick="downloadImage('logo-small', 'logo-small.png')">下载</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 绘制支付图标
        function drawPaymentIcon(canvasId, color) {
            const canvas = document.getElementById(canvasId);
            const ctx = canvas.getContext('2d');
            
            // 背景
            ctx.fillStyle = color;
            ctx.beginPath();
            ctx.arc(12, 12, 10, 0, Math.PI * 2);
            ctx.fill();
            
            // 边框
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.arc(12, 12, 8, 0, Math.PI * 2);
            ctx.stroke();
            
            // 文字
            ctx.fillStyle = 'white';
            ctx.font = 'bold 12px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(canvasId === 'alipay' ? '支' : '微', 12, 12);
        }

        // 绘制商品图片
        function drawProductImage(canvasId, text) {
            const canvas = document.getElementById(canvasId);
            const ctx = canvas.getContext('2d');
            
            // 背景渐变
            const gradient = ctx.createLinearGradient(0, 0, 400, 400);
            gradient.addColorStop(0, '#f8f9fa');
            gradient.addColorStop(1, '#e9ecef');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 400, 400);
            
            // 商品图片区域
            ctx.fillStyle = 'white';
            ctx.shadowColor = 'rgba(0,0,0,0.1)';
            ctx.shadowBlur = 10;
            ctx.shadowOffsetX = 5;
            ctx.shadowOffsetY = 5;
            ctx.fillRect(20, 20, 360, 360);
            
            // 重置阴影
            ctx.shadowColor = 'transparent';
            
            // 商品名称
            ctx.fillStyle = '#333';
            ctx.font = 'bold 28px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(text, 200, 200);
            
            // 价格标签
            ctx.fillStyle = '#f56c6c';
            ctx.font = 'bold 24px Arial';
            ctx.fillText('¥99.00', 200, 250);
            
            // 销量标签
            ctx.fillStyle = '#909399';
            ctx.font = '16px Arial';
            ctx.fillText('已售 1000+', 200, 280);
        }

        // 绘制新闻图片
        function drawNewsImage(canvasId, text) {
            const canvas = document.getElementById(canvasId);
            const ctx = canvas.getContext('2d');
            
            // 背景渐变
            const gradient = ctx.createLinearGradient(0, 0, 800, 400);
            gradient.addColorStop(0, '#e8f5e9');
            gradient.addColorStop(1, '#c8e6c9');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 800, 400);
            
            // 新闻标题
            ctx.fillStyle = '#2e7d32';
            ctx.font = 'bold 36px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(text, 400, 180);
            
            // 新闻摘要
            ctx.fillStyle = '#1b5e20';
            ctx.font = '18px Arial';
            ctx.fillText('点击查看详情', 400, 220);
            
            // 底部装饰
            ctx.fillStyle = '#81c784';
            ctx.fillRect(0, 350, 800, 50);
            
            // 底部文字
            ctx.fillStyle = 'white';
            ctx.font = '16px Arial';
            ctx.fillText('智慧农业平台', 400, 380);
        }

        // 绘制图标
        function drawIcon(canvasId, text) {
            const canvas = document.getElementById(canvasId);
            const ctx = canvas.getContext('2d');
            
            // 背景
            ctx.fillStyle = '#409eff';
            ctx.beginPath();
            ctx.roundRect(2, 2, 20, 20, 4);
            ctx.fill();
            
            // 文字
            ctx.fillStyle = 'white';
            ctx.font = 'bold 12px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(text, 12, 12);
            
            // 边框
            ctx.strokeStyle = 'rgba(255,255,255,0.3)';
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.roundRect(2, 2, 20, 20, 4);
            ctx.stroke();
        }

        // 绘制Logo
        function drawLogo(canvasId, width, height) {
            const canvas = document.getElementById(canvasId);
            const ctx = canvas.getContext('2d');
            
            // 背景渐变
            const gradient = ctx.createLinearGradient(0, 0, width, height);
            gradient.addColorStop(0, '#409eff');
            gradient.addColorStop(1, '#66b1ff');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, width, height);
            
            // Logo文字
            ctx.fillStyle = 'white';
            ctx.font = `bold ${height * 0.6}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('Smart Farmer', width/2, height/2);
            
            // 装饰线
            ctx.strokeStyle = 'rgba(255,255,255,0.3)';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(width * 0.2, height * 0.7);
            ctx.lineTo(width * 0.8, height * 0.7);
            ctx.stroke();
            
            // 装饰点
            ctx.fillStyle = 'rgba(255,255,255,0.5)';
            ctx.beginPath();
            ctx.arc(width * 0.3, height * 0.7, 2, 0, Math.PI * 2);
            ctx.arc(width * 0.5, height * 0.7, 2, 0, Math.PI * 2);
            ctx.arc(width * 0.7, height * 0.7, 2, 0, Math.PI * 2);
            ctx.fill();
        }

        // 下载图片
        function downloadImage(canvasId, filename) {
            const canvas = document.getElementById(canvasId);
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL(filename.endsWith('.png') ? 'image/png' : 'image/jpeg');
            link.click();
        }

        // 初始化所有图片
        window.onload = function() {
            // 支付图标
            drawPaymentIcon('alipay', '#1677ff');
            drawPaymentIcon('wechat', '#07c160');

            // 商品图片
            drawProductImage('rice-seeds', '水稻种子');
            drawProductImage('rice-seeds-2', '水稻种子2');
            drawProductImage('rice-seeds-3', '水稻种子3');
            drawProductImage('fertilizer', '有机复合肥');
            drawProductImage('sprayer', '智能喷药机');
            drawProductImage('pesticide', '农药');
            drawProductImage('seeds', '种子');
            drawProductImage('tools', '农具');
            drawProductImage('machinery', '农业机械');
            drawProductImage('irrigation', '灌溉设备');
            drawProductImage('review1', '评价图片1');
            drawProductImage('review2', '评价图片2');

            // 新闻图片
            drawNewsImage('news1', '农业科技新闻');
            drawNewsImage('news2', '市场动态分析');
            drawNewsImage('news3', '政策解读');

            // 图标
            drawIcon('home', '首页');
            drawIcon('price', '价格');
            drawIcon('analysis', '分析');
            drawIcon('news', '新闻');
            drawIcon('profile', '我的');
            drawIcon('shop', '农品汇');
            drawIcon('cart', '购物车');
            drawIcon('search', '搜索');
            drawIcon('filter', '筛选');
            drawIcon('sort', '排序');
            drawIcon('delete', '删除');
            drawIcon('edit', '编辑');
            drawIcon('add', '添加');
            drawIcon('star', '星星');
            drawIcon('star-filled', '星星填充');
            drawIcon('star-half', '星星半填充');

            // Logo
            drawLogo('logo', 200, 60);
            drawLogo('logo-small', 100, 30);
        };
    </script>
</body>
</html> 