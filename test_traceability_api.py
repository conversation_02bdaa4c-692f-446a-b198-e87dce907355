#!/usr/bin/env python3
import requests
import json

# 测试溯源查询API
def test_traceability_query():
    url = "http://localhost:8081/api/traceability/query"
    
    # 测试数据
    test_data = {
        "traceCode": "SFAPS25071622096012CD351",
        "source": "web",
        "location": "test",
        "ipAddress": "127.0.0.1",
        "deviceInfo": "Test Browser",
        "userId": None
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        print(f"Testing API: {url}")
        print(f"Request data: {json.dumps(test_data, indent=2)}")
        
        response = requests.post(url, json=test_data, headers=headers, timeout=10)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.text:
            try:
                response_json = response.json()
                print(f"Response JSON: {json.dumps(response_json, indent=2, ensure_ascii=False)}")
            except:
                print(f"Response Text: {response.text}")
        else:
            print("Empty response")
            
    except requests.exceptions.RequestException as e:
        print(f"Request failed: {e}")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_traceability_query()