server {
    listen 8200;
    server_name **************;
    
    # 前端资源路径
    root /www/wwwroot/sfap/dist;
    index index.html;
    
    # 解决HTML5 History模式的路由问题
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # 聚合数据天气API代理
    location /weather/api/ {
        proxy_pass http://apis.juhe.cn/;
        proxy_set_header Host apis.juhe.cn;
        proxy_set_header Referer http://apis.juhe.cn;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 后端API代理
    location /api/ {
        proxy_pass http://**************:8081/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 静态资源缓存设置
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        expires 7d;
    }
} 