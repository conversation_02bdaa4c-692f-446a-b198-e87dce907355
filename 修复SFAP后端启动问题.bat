@echo off
echo ========================================
echo SFAP后端启动问题修复脚本
echo ========================================
echo.

echo 检测到的问题：
echo [!] 找不到或无法加载主类 com.agriculture.AgricultureMallApplication
echo [!] pom.xml文件中存在语法错误
echo.

echo 开始修复...
echo.

echo 1. 检查项目结构...
if exist "backend\main\src\main\java\com\agriculture\AgricultureMallApplication.java" (
    echo [✓] 主类文件存在
) else (
    echo [✗] 主类文件不存在
    echo 请检查文件路径：backend\main\src\main\java\com\agriculture\AgricultureMallApplication.java
    pause
    exit /b 1
)

echo 2. 检查pom.xml文件...
if exist "backend\main\pom.xml" (
    echo [✓] pom.xml文件存在
) else (
    echo [✗] pom.xml文件不存在
    pause
    exit /b 1
)

echo 3. 修复pom.xml语法错误...
cd backend\main

echo 正在备份原始pom.xml文件...
copy pom.xml pom.xml.backup

echo 正在修复pom.xml中的语法错误...
powershell -Command "(Get-Content pom.xml) -replace '<n>agriculture-mall</n>', '<name>agriculture-mall</name>' | Set-Content pom.xml"

echo [✓] pom.xml语法错误已修复

echo 4. 清理并重新编译项目...
echo 正在清理项目...
call mvn clean -q

echo 正在编译项目...
call mvn compile -q
if %errorlevel% equ 0 (
    echo [✓] 项目编译成功
) else (
    echo [✗] 项目编译失败
    echo 请检查编译错误信息
    pause
    exit /b 1
)

echo 5. 验证主类是否可以找到...
if exist "target\classes\com\agriculture\AgricultureMallApplication.class" (
    echo [✓] 主类编译成功，class文件已生成
) else (
    echo [✗] 主类编译失败，class文件未生成
    echo 尝试强制重新编译...
    call mvn clean compile -X
    pause
    exit /b 1
)

echo 6. 检查依赖是否正确...
echo 正在检查Maven依赖...
call mvn dependency:resolve -q
if %errorlevel% equ 0 (
    echo [✓] Maven依赖解析成功
) else (
    echo [!] Maven依赖解析有问题，但继续尝试启动...
)

echo.
echo ========================================
echo 修复完成！
echo ========================================
echo.

echo 修复内容总结：
echo [✓] 修复了pom.xml中的语法错误（<n> → <name>）
echo [✓] 清理并重新编译了项目
echo [✓] 验证了主类文件的存在
echo [✓] 检查了Maven依赖
echo.

echo 现在尝试启动后端服务...
echo 启动命令：mvn spring-boot:run
echo.

echo 如果仍然遇到问题，请尝试以下解决方案：
echo.
echo 方案1：使用Maven启动
echo cd backend\main
echo mvn spring-boot:run
echo.
echo 方案2：使用Java直接启动
echo cd backend\main
echo mvn package -DskipTests
echo java -jar target\agriculture-mall-1.0.0.jar
echo.
echo 方案3：在IDE中启动
echo 1. 在IntelliJ IDEA中打开backend\main项目
echo 2. 右键点击AgricultureMallApplication.java
echo 3. 选择"Run AgricultureMallApplication"
echo.
echo 方案4：检查Java版本
echo java -version
echo 确保使用Java 17
echo.

echo 是否现在尝试启动服务？(Y/N)
set /p choice=请选择: 
if /i "%choice%"=="Y" (
    echo 正在启动后端服务...
    call mvn spring-boot:run
) else (
    echo 请手动启动后端服务
)

echo.
echo 修复脚本执行完成！
pause
