<mxfile host="app.diagrams.net" modified="2023-06-10T08:30:00.000Z" agent="5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36" etag="1234567890" version="14.7.7" type="device">
  <diagram id="agriculture-system-architecture" name="农智汇平台系统架构">
    <mxGraphModel dx="1422" dy="762" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- 标题 -->
        <mxCell id="title" value="农智汇平台系统架构图" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=24;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="359.5" y="20" width="450" height="40" as="geometry" />
        </mxCell>
        
        <!-- 用户层 -->
        <mxCell id="user_layer" value="用户层" style="swimlane;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;rounded=1;fontSize=16;fontStyle=1;strokeWidth=2;resizeParent=0;resizeLast=1;shadow=0;dashed=0;align=center;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="359.5" y="80" width="450" height="70" as="geometry" />
        </mxCell>
        <mxCell id="user_content" value="普通用户 | 农户 | 管理员" style="align=center;strokeColor=none;fillColor=none;spacingLeft=4;fontSize=14;verticalAlign=top;resizable=0;rotatable=0;part=1;" vertex="1" parent="user_layer">
          <mxGeometry y="30" width="450" height="40" as="geometry" />
        </mxCell>
        
        <!-- 前端层 -->
        <mxCell id="frontend_layer" value="前端层 (Vue2 + Element UI)" style="swimlane;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;rounded=1;fontSize=16;fontStyle=1;strokeWidth=2;resizeParent=0;resizeLast=1;shadow=0;dashed=0;align=center;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="80" y="180" width="1010" height="170" as="geometry" />
        </mxCell>
        
        <!-- 前端模块 -->
        <mxCell id="views" value="视图层 (Views)" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="100" y="230" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="components" value="组件层 (Components)" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="250" y="230" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="router" value="路由层 (Router)" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="400" y="230" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="store" value="状态管理 (Vuex)" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="550" y="230" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="api" value="API接口层 (Axios)" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="700" y="230" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="utils" value="工具层 (Utils)" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="850" y="230" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 前端功能模块 -->
        <mxCell id="frontend_modules" value="功能模块：首页 | 商城 | 数据分析 | 百科 | 天气 | AI助手 | 用户中心" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="100" y="310" width="970" height="20" as="geometry" />
        </mxCell>
        
        <!-- 后端层 -->
        <mxCell id="backend_layer" value="后端层 (Spring Boot + MyBatis-Plus)" style="swimlane;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;rounded=1;fontSize=16;fontStyle=1;strokeWidth=2;resizeParent=0;resizeLast=1;shadow=0;dashed=0;align=center;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="80" y="380" width="1010" height="170" as="geometry" />
        </mxCell>
        
        <!-- 后端模块 -->
        <mxCell id="controller" value="控制层 (Controller)" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="100" y="430" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="service" value="服务层 (Service)" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="250" y="430" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="serviceImpl" value="服务实现层 (ServiceImpl)" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="400" y="430" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="mapper" value="数据访问层 (Mapper)" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="550" y="430" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="entity" value="实体层 (Entity)" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="700" y="430" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="config" value="配置层 (Config)" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="850" y="430" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 后端业务模块 -->
        <mxCell id="backend_modules" value="业务模块：商城 | 数据分析 | 百科 | 天气 | AI助手 | 用户管理" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="100" y="510" width="970" height="20" as="geometry" />
        </mxCell>
        
        <!-- 数据层 -->
        <mxCell id="data_layer" value="数据层" style="swimlane;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;rounded=1;fontSize=16;fontStyle=1;strokeWidth=2;resizeParent=0;resizeLast=1;shadow=0;dashed=0;align=center;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="80" y="580" width="1010" height="100" as="geometry" />
        </mxCell>
        
        <!-- 数据存储 -->
        <mxCell id="mysql" value="MySQL数据库" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fontSize=14;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="160" y="620" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="redis" value="Redis缓存" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fontSize=14;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="360" y="620" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="static" value="静态资源" style="shape=document;whiteSpace=wrap;html=1;boundedLbl=1;fontSize=14;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="560" y="620" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="file_storage" value="文件存储" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fontSize=14;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="760" y="620" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 外部服务层 -->
        <mxCell id="external_layer" value="外部服务层" style="swimlane;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;rounded=1;fontSize=16;fontStyle=1;strokeWidth=2;resizeParent=0;resizeLast=1;shadow=0;dashed=0;align=center;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="80" y="710" width="1010" height="100" as="geometry" />
        </mxCell>
        
        <!-- 外部服务 -->
        <mxCell id="ai_service" value="阿里云百炼AI服务" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="160" y="750" width="200" height="40" as="geometry" />
        </mxCell>
        <mxCell id="weather_service" value="天气API服务" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="420" y="750" width="200" height="40" as="geometry" />
        </mxCell>
        <mxCell id="payment_service" value="支付API服务" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="680" y="750" width="200" height="40" as="geometry" />
        </mxCell>
        
        <!-- 连接关系 -->
        <!-- 用户层到前端层 -->
        <mxCell id="user_to_frontend" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="user_content" target="frontend_layer">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="410" as="sourcePoint" />
            <mxPoint x="610" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 前端层到后端层 -->
        <mxCell id="frontend_to_backend" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="frontend_layer" target="backend_layer">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="410" as="sourcePoint" />
            <mxPoint x="610" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 后端层到数据层 -->
        <mxCell id="backend_to_data" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="backend_layer" target="data_layer">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="410" as="sourcePoint" />
            <mxPoint x="610" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 后端层到外部服务层 -->
        <mxCell id="backend_to_external" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="data_layer" target="external_layer">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="410" as="sourcePoint" />
            <mxPoint x="610" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 前端模块之间的连接 -->
        <mxCell id="views_to_components" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="views" target="components">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="410" as="sourcePoint" />
            <mxPoint x="610" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="components_to_router" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="components" target="router">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="410" as="sourcePoint" />
            <mxPoint x="610" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="router_to_store" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="router" target="store">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="410" as="sourcePoint" />
            <mxPoint x="610" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="store_to_api" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="store" target="api">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="410" as="sourcePoint" />
            <mxPoint x="610" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="api_to_utils" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="api" target="utils">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="410" as="sourcePoint" />
            <mxPoint x="610" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 后端模块之间的连接 -->
        <mxCell id="controller_to_service" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="controller" target="service">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="410" as="sourcePoint" />
            <mxPoint x="610" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="service_to_serviceImpl" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="service" target="serviceImpl">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="410" as="sourcePoint" />
            <mxPoint x="610" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="serviceImpl_to_mapper" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="serviceImpl" target="mapper">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="410" as="sourcePoint" />
            <mxPoint x="610" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="mapper_to_entity" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="mapper" target="entity">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="410" as="sourcePoint" />
            <mxPoint x="610" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="entity_to_config" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="entity" target="config">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="410" as="sourcePoint" />
            <mxPoint x="610" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- API到Controller的连接 -->
        <mxCell id="api_to_controller" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;dashed=1;" edge="1" parent="1" source="api" target="controller">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="410" as="sourcePoint" />
            <mxPoint x="610" y="360" as="targetPoint" />
            <Array as="points">
              <mxPoint x="760" y="380" />
              <mxPoint x="160" y="380" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <!-- 业务模块说明 -->
        <mxCell id="business_modules" value="主要业务功能：电商系统 | 数据分析系统 | 农业百科 | 天气预报与农事建议 | AI智能助手" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="80" y="830" width="1010" height="30" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile> 