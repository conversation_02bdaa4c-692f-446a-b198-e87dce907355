<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SFAP - 农品商城</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* iPhone 15 Pro 外观模拟 */
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .iphone-frame {
            width: 393px;
            height: 852px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            position: relative;
        }

        .iphone-screen {
            width: 100%;
            height: 100%;
            background: #fff;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }

        /* 动态岛模拟 */
        .dynamic-island {
            position: absolute;
            top: 12px;
            left: 50%;
            transform: translateX(-50%);
            width: 126px;
            height: 37px;
            background: #000;
            border-radius: 19px;
            z-index: 1000;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            .iphone-frame {
                width: 350px;
                height: 760px;
            }
        }
        :root {
            --primary-green: #22C55E;
            --dark-green: #16A34A;
            --light-green: #DCFCE7;
            --bg-green: #F0FDF4;
        }
        
        .ios-status-bar {
            height: 47px;
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        .shop-container {
            min-height: calc(100vh - 47px - 80px);
            background: #F8F9FA;
            overflow-y: auto;
        }
        
        .header {
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            padding: 20px;
            color: white;
        }
        
        .search-section {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-top: 15px;
        }
        
        .search-container {
            flex: 1;
            position: relative;
        }
        
        .search-input {
            width: 100%;
            padding: 12px 20px 12px 45px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            background: rgba(255,255,255,0.9);
            backdrop-filter: blur(10px);
        }
        
        .search-input:focus {
            outline: none;
            background: white;
        }
        
        .search-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #9CA3AF;
            font-size: 18px;
        }
        
        .cart-btn {
            width: 44px;
            height: 44px;
            background: rgba(255,255,255,0.2);
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 18px;
            cursor: pointer;
            position: relative;
        }
        
        .cart-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            width: 20px;
            height: 20px;
            background: #EF4444;
            border-radius: 50%;
            color: white;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .banner-section {
            padding: 20px;
            background: white;
        }
        
        .banner {
            height: 120px;
            background: linear-gradient(135deg, var(--light-green), var(--bg-green));
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--dark-green);
            font-size: 18px;
            font-weight: 600;
            position: relative;
            overflow: hidden;
        }
        
        .banner::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -20px;
            width: 100px;
            height: 200px;
            background: rgba(255,255,255,0.1);
            transform: rotate(15deg);
        }
        
        .categories-section {
            padding: 20px;
            background: white;
            margin-bottom: 10px;
        }
        
        .categories-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
        }
        
        .category-item {
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .category-item:hover {
            transform: translateY(-2px);
        }
        
        .category-icon {
            width: 60px;
            height: 60px;
            background: var(--light-green);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 8px;
            color: var(--dark-green);
            font-size: 24px;
        }
        
        .category-name {
            font-size: 14px;
            color: #1F2937;
            font-weight: 600;
        }
        
        .products-section {
            padding: 20px;
            background: white;
        }
        
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #1F2937;
        }
        
        .more-btn {
            color: var(--primary-green);
            font-size: 14px;
            cursor: pointer;
        }
        
        .products-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }
        
        .product-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .product-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .product-image {
            width: 100%;
            height: 120px;
            background: linear-gradient(135deg, var(--light-green), var(--bg-green));
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--dark-green);
            font-size: 32px;
            position: relative;
        }
        
        .product-badge {
            position: absolute;
            top: 8px;
            left: 8px;
            background: #EF4444;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
        }
        
        .organic-badge {
            background: var(--primary-green);
        }
        
        .product-info {
            padding: 12px;
        }
        
        .product-name {
            font-size: 14px;
            font-weight: 600;
            color: #1F2937;
            margin-bottom: 4px;
            line-height: 1.3;
        }
        
        .product-desc {
            font-size: 12px;
            color: #6B7280;
            margin-bottom: 8px;
        }
        
        .product-price {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .price {
            font-size: 16px;
            font-weight: bold;
            color: #EF4444;
        }
        
        .original-price {
            font-size: 12px;
            color: #9CA3AF;
            text-decoration: line-through;
            margin-left: 5px;
        }
        
        .add-cart-btn {
            width: 32px;
            height: 32px;
            background: var(--primary-green);
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .add-cart-btn:hover {
            transform: scale(1.1);
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: white;
            border-top: 1px solid #E5E7EB;
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding: 10px 0;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 5px 10px;
        }
        
        .nav-item.active {
            color: var(--primary-green);
        }
        
        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .nav-text {
            font-size: 12px;
        }
        
        .rating {
            display: flex;
            align-items: center;
            gap: 2px;
            margin-bottom: 4px;
        }
        
        .star {
            color: #FCD34D;
            font-size: 12px;
        }
        
        .rating-text {
            font-size: 12px;
            color: #6B7280;
            margin-left: 4px;
        }
    </style>
</head>
<body>
    <!-- iPhone 框架 -->
    <div class="iphone-frame">
        <!-- 动态岛 -->
        <div class="dynamic-island"></div>

        <!-- 屏幕内容 -->
        <div class="iphone-screen">
            <!-- iOS状态栏 -->
    <div class="ios-status-bar">
        <div class="flex items-center">
            <span id="current-time">9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-sm"></i>
            <i class="fas fa-wifi text-sm"></i>
            <div class="flex items-center">
                <div class="w-6 h-3 border border-white rounded-sm">
                    <div class="w-4 h-1.5 bg-white rounded-sm m-0.5"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 农品商城页面内容 -->
    <div class="shop-container">
        <!-- 头部 -->
        <div class="header">
            <h1 class="text-xl font-bold mb-2">
                <i class="fas fa-shopping-cart mr-2"></i>
                农品商城
            </h1>
            <p class="text-sm opacity-90">优质农产品，产地直供</p>
            
            <!-- 搜索区域 -->
            <div class="search-section">
                <div class="search-container">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" class="search-input" placeholder="搜索农产品...">
                </div>
                <button class="cart-btn" onclick="showCart()">
                    <i class="fas fa-shopping-cart"></i>
                    <div class="cart-badge">3</div>
                </button>
            </div>
        </div>

        <!-- 横幅广告 -->
        <div class="banner-section">
            <div class="banner">
                <div>
                    <i class="fas fa-leaf mr-2"></i>
                    春季新品上市 · 有机蔬菜8折起
                </div>
            </div>
        </div>

        <!-- 商品分类 -->
        <div class="categories-section">
            <h3 class="section-title mb-4">商品分类</h3>
            <div class="categories-grid">
                <div class="category-item" onclick="filterCategory('vegetables')">
                    <div class="category-icon">
                        <i class="fas fa-carrot"></i>
                    </div>
                    <div class="category-name">蔬菜</div>
                </div>
                <div class="category-item" onclick="filterCategory('fruits')">
                    <div class="category-icon">
                        <i class="fas fa-apple-alt"></i>
                    </div>
                    <div class="category-name">水果</div>
                </div>
                <div class="category-item" onclick="filterCategory('grains')">
                    <div class="category-icon">
                        <i class="fas fa-seedling"></i>
                    </div>
                    <div class="category-name">粮食</div>
                </div>
                <div class="category-item" onclick="filterCategory('meat')">
                    <div class="category-icon">
                        <i class="fas fa-drumstick-bite"></i>
                    </div>
                    <div class="category-name">肉禽蛋</div>
                </div>
            </div>
        </div>

        <!-- 热销商品 -->
        <div class="products-section">
            <div class="section-header">
                <h3 class="section-title">热销商品</h3>
                <div class="more-btn">查看更多 ></div>
            </div>
            
            <div class="products-grid">
                <div class="product-card" onclick="showProduct('organic-carrot')">
                    <div class="product-image">
                        <i class="fas fa-carrot"></i>
                        <div class="product-badge organic-badge">有机</div>
                    </div>
                    <div class="product-info">
                        <div class="product-name">有机胡萝卜</div>
                        <div class="product-desc">山东寿光 · 新鲜采摘</div>
                        <div class="rating">
                            <i class="fas fa-star star"></i>
                            <i class="fas fa-star star"></i>
                            <i class="fas fa-star star"></i>
                            <i class="fas fa-star star"></i>
                            <i class="fas fa-star star"></i>
                            <span class="rating-text">4.9 (128)</span>
                        </div>
                        <div class="product-price">
                            <div>
                                <span class="price">¥8.8</span>
                                <span class="original-price">¥12.0</span>
                            </div>
                            <button class="add-cart-btn" onclick="addToCart(event, 'organic-carrot')">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="product-card" onclick="showProduct('fresh-tomato')">
                    <div class="product-image">
                        <i class="fas fa-circle" style="color: #EF4444;"></i>
                        <div class="product-badge">热销</div>
                    </div>
                    <div class="product-info">
                        <div class="product-name">新鲜番茄</div>
                        <div class="product-desc">河北张家口 · 温室种植</div>
                        <div class="rating">
                            <i class="fas fa-star star"></i>
                            <i class="fas fa-star star"></i>
                            <i class="fas fa-star star"></i>
                            <i class="fas fa-star star"></i>
                            <i class="far fa-star star"></i>
                            <span class="rating-text">4.6 (89)</span>
                        </div>
                        <div class="product-price">
                            <div>
                                <span class="price">¥6.5</span>
                            </div>
                            <button class="add-cart-btn" onclick="addToCart(event, 'fresh-tomato')">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="product-card" onclick="showProduct('organic-rice')">
                    <div class="product-image">
                        <i class="fas fa-seedling"></i>
                        <div class="product-badge organic-badge">有机</div>
                    </div>
                    <div class="product-info">
                        <div class="product-name">有机大米</div>
                        <div class="product-desc">东北五常 · 优质稻米</div>
                        <div class="rating">
                            <i class="fas fa-star star"></i>
                            <i class="fas fa-star star"></i>
                            <i class="fas fa-star star"></i>
                            <i class="fas fa-star star"></i>
                            <i class="fas fa-star star"></i>
                            <span class="rating-text">5.0 (256)</span>
                        </div>
                        <div class="product-price">
                            <div>
                                <span class="price">¥28.8</span>
                                <span class="original-price">¥35.0</span>
                            </div>
                            <button class="add-cart-btn" onclick="addToCart(event, 'organic-rice')">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="product-card" onclick="showProduct('free-range-eggs')">
                    <div class="product-image">
                        <i class="fas fa-egg"></i>
                    </div>
                    <div class="product-info">
                        <div class="product-name">散养土鸡蛋</div>
                        <div class="product-desc">农家散养 · 营养丰富</div>
                        <div class="rating">
                            <i class="fas fa-star star"></i>
                            <i class="fas fa-star star"></i>
                            <i class="fas fa-star star"></i>
                            <i class="fas fa-star star"></i>
                            <i class="far fa-star star"></i>
                            <span class="rating-text">4.7 (167)</span>
                        </div>
                        <div class="product-price">
                            <div>
                                <span class="price">¥18.8</span>
                            </div>
                            <button class="add-cart-btn" onclick="addToCart(event, 'free-range-eggs')">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 推荐商品 -->
        <div class="products-section">
            <div class="section-header">
                <h3 class="section-title">为您推荐</h3>
                <div class="more-btn">查看更多 ></div>
            </div>
            
            <div class="products-grid">
                <div class="product-card">
                    <div class="product-image">
                        <i class="fas fa-apple-alt" style="color: #EF4444;"></i>
                    </div>
                    <div class="product-info">
                        <div class="product-name">红富士苹果</div>
                        <div class="product-desc">山东烟台 · 脆甜多汁</div>
                        <div class="rating">
                            <i class="fas fa-star star"></i>
                            <i class="fas fa-star star"></i>
                            <i class="fas fa-star star"></i>
                            <i class="fas fa-star star"></i>
                            <i class="fas fa-star star"></i>
                            <span class="rating-text">4.8 (203)</span>
                        </div>
                        <div class="product-price">
                            <div>
                                <span class="price">¥12.8</span>
                            </div>
                            <button class="add-cart-btn">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="product-card">
                    <div class="product-image">
                        <i class="fas fa-leaf" style="color: var(--primary-green);"></i>
                        <div class="product-badge organic-badge">有机</div>
                    </div>
                    <div class="product-info">
                        <div class="product-name">有机菠菜</div>
                        <div class="product-desc">本地农场 · 当日采摘</div>
                        <div class="rating">
                            <i class="fas fa-star star"></i>
                            <i class="fas fa-star star"></i>
                            <i class="fas fa-star star"></i>
                            <i class="fas fa-star star"></i>
                            <i class="far fa-star star"></i>
                            <span class="rating-text">4.5 (76)</span>
                        </div>
                        <div class="product-price">
                            <div>
                                <span class="price">¥5.8</span>
                                <span class="original-price">¥8.0</span>
                            </div>
                            <button class="add-cart-btn">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航 -->
    <div class="bottom-nav">
        <div class="nav-item" onclick="navigateTo('home')">
            <i class="fas fa-home nav-icon"></i>
            <span class="nav-text">首页</span>
        </div>
        <div class="nav-item" onclick="navigateTo('pest-detection')">
            <i class="fas fa-bug nav-icon"></i>
            <span class="nav-text">识别</span>
        </div>
        <div class="nav-item" onclick="navigateTo('qr-scan')">
            <i class="fas fa-qrcode nav-icon"></i>
            <span class="nav-text">溯源</span>
        </div>
        <div class="nav-item active">
            <i class="fas fa-shopping-cart nav-icon"></i>
            <span class="nav-text">农品汇</span>
        </div>
        <div class="nav-item" onclick="navigateTo('profile')">
            <i class="fas fa-user nav-icon"></i>
            <span class="nav-text">我的</span>
        </div>
    </div>

    <script>
        // 更新时间显示
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
            });
            document.getElementById('current-time').textContent = timeString;
        }
        
        updateTime();
        setInterval(updateTime, 1000);

        // 分类筛选
        function filterCategory(category) {
            alert(`筛选${category}类商品`);
        }

        // 显示商品详情
        function showProduct(productId) {
            alert(`查看商品详情：${productId}`);
        }

        // 添加到购物车
        function addToCart(event, productId) {
            event.stopPropagation();
            
            // 更新购物车数量
            const badge = document.querySelector('.cart-badge');
            let count = parseInt(badge.textContent);
            badge.textContent = count + 1;
            
            // 按钮动画效果
            const btn = event.target.closest('.add-cart-btn');
            btn.style.transform = 'scale(1.2)';
            setTimeout(() => {
                btn.style.transform = 'scale(1)';
            }, 200);
            
            alert(`已添加到购物车：${productId}`);
        }

        // 显示购物车
        function showCart() {
            alert('打开购物车页面');
        }

        // 页面导航
        function navigateTo(page) {
            window.open(`${page}.html`, '_blank');
        }

        // 底部导航点击效果
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelectorAll('.nav-item').forEach(nav => nav.classList.remove('active'));
                this.classList.add('active');
            });
        });
    </script>
        </div>
    </div>
</body>
</html>
