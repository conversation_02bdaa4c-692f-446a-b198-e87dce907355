<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SFAP - AI智能问答</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* iPhone 15 Pro 外观模拟 */
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .iphone-frame {
            width: 393px;
            height: 852px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            position: relative;
        }

        .iphone-screen {
            width: 100%;
            height: 100%;
            background: #fff;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }

        /* 动态岛模拟 */
        .dynamic-island {
            position: absolute;
            top: 12px;
            left: 50%;
            transform: translateX(-50%);
            width: 126px;
            height: 37px;
            background: #000;
            border-radius: 19px;
            z-index: 1000;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            .iphone-frame {
                width: 350px;
                height: 760px;
            }
        }
        :root {
            --primary-green: #22C55E;
            --dark-green: #16A34A;
            --light-green: #DCFCE7;
            --bg-green: #F0FDF4;
        }
        
        .ios-status-bar {
            height: 47px;
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        .chat-container {
            min-height: calc(100vh - 47px - 80px - 70px);
            background: #F8F9FA;
            overflow-y: auto;
            padding: 20px 15px;
        }
        
        .header {
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            padding: 15px 20px;
            color: white;
            text-align: center;
        }
        
        .chat-messages {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .message {
            display: flex;
            align-items: flex-start;
            gap: 10px;
        }
        
        .message.user {
            flex-direction: row-reverse;
        }
        
        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            flex-shrink: 0;
        }
        
        .ai-avatar {
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            color: white;
        }
        
        .user-avatar {
            background: #6B7280;
            color: white;
        }
        
        .message-content {
            max-width: 75%;
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 15px;
            line-height: 1.4;
            position: relative;
        }
        
        .ai-message {
            background: white;
            color: #1F2937;
            border-bottom-left-radius: 6px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .user-message {
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            color: white;
            border-bottom-right-radius: 6px;
        }
        
        .message-time {
            font-size: 11px;
            color: #9CA3AF;
            margin-top: 5px;
            text-align: center;
        }
        
        .typing-indicator {
            display: none;
            align-items: center;
            gap: 10px;
            margin: 10px 0;
        }
        
        .typing-dots {
            display: flex;
            gap: 4px;
            padding: 12px 16px;
            background: white;
            border-radius: 18px;
            border-bottom-left-radius: 6px;
        }
        
        .typing-dot {
            width: 8px;
            height: 8px;
            background: #9CA3AF;
            border-radius: 50%;
            animation: typingAnimation 1.4s infinite;
        }
        
        .typing-dot:nth-child(2) {
            animation-delay: 0.2s;
        }
        
        .typing-dot:nth-child(3) {
            animation-delay: 0.4s;
        }
        
        .quick-questions {
            background: white;
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .quick-question-btn {
            display: block;
            width: 100%;
            padding: 12px 15px;
            margin: 8px 0;
            background: var(--bg-green);
            border: 1px solid var(--light-green);
            border-radius: 10px;
            color: var(--dark-green);
            text-align: left;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }
        
        .quick-question-btn:hover {
            background: var(--light-green);
            transform: translateY(-1px);
        }
        
        .input-area {
            position: fixed;
            bottom: 80px;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #E5E7EB;
            padding: 15px;
        }
        
        .input-container {
            display: flex;
            align-items: flex-end;
            gap: 10px;
            max-width: 100%;
        }
        
        .message-input {
            flex: 1;
            min-height: 44px;
            max-height: 120px;
            padding: 12px 16px;
            border: 2px solid #E5E7EB;
            border-radius: 22px;
            font-size: 16px;
            resize: none;
            outline: none;
            transition: all 0.3s ease;
            background: #F9FAFB;
        }
        
        .message-input:focus {
            border-color: var(--primary-green);
            background: white;
            box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
        }
        
        .send-btn {
            width: 44px;
            height: 44px;
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 18px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .send-btn:hover {
            transform: scale(1.05);
        }
        
        .send-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .voice-btn {
            width: 44px;
            height: 44px;
            background: var(--light-green);
            border: 2px solid var(--primary-green);
            border-radius: 50%;
            color: var(--dark-green);
            font-size: 18px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .voice-btn:hover {
            background: var(--primary-green);
            color: white;
        }
        
        .voice-btn.recording {
            background: #EF4444;
            border-color: #DC2626;
            color: white;
            animation: pulse 1s infinite;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: white;
            border-top: 1px solid #E5E7EB;
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding: 10px 0;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 5px 10px;
        }
        
        .nav-item.active {
            color: var(--primary-green);
        }
        
        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .nav-text {
            font-size: 12px;
        }
        
        @keyframes typingAnimation {
            0%, 60%, 100% {
                transform: translateY(0);
                opacity: 0.4;
            }
            30% {
                transform: translateY(-10px);
                opacity: 1;
            }
        }
        
        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
        }
        
        .welcome-message {
            text-align: center;
            padding: 20px;
            color: #6B7280;
        }
        
        .welcome-icon {
            font-size: 48px;
            color: var(--primary-green);
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <!-- iPhone 框架 -->
    <div class="iphone-frame">
        <!-- 动态岛 -->
        <div class="dynamic-island"></div>

        <!-- 屏幕内容 -->
        <div class="iphone-screen">
            <!-- iOS状态栏 -->
    <div class="ios-status-bar">
        <div class="flex items-center">
            <span id="current-time">9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-sm"></i>
            <i class="fas fa-wifi text-sm"></i>
            <div class="flex items-center">
                <div class="w-6 h-3 border border-white rounded-sm">
                    <div class="w-4 h-1.5 bg-white rounded-sm m-0.5"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 头部 -->
    <div class="header">
        <h1 class="text-lg font-bold">
            <i class="fas fa-robot mr-2"></i>
            AI智能问答
        </h1>
        <p class="text-sm opacity-90 mt-1">农业专家24小时在线为您服务</p>
    </div>

    <!-- 聊天内容区域 -->
    <div class="chat-container" id="chatContainer">
        <!-- 欢迎消息 -->
        <div class="welcome-message" id="welcomeMessage">
            <div class="welcome-icon">
                <i class="fas fa-robot"></i>
            </div>
            <h3 class="text-lg font-semibold text-gray-800 mb-2">您好！我是SFAP智能助手</h3>
            <p class="text-sm">我可以为您解答农业种植、病虫害防治、市场行情等问题</p>
        </div>

        <!-- 快捷问题 -->
        <div class="quick-questions" id="quickQuestions">
            <h4 class="text-sm font-semibold text-gray-800 mb-2">
                <i class="fas fa-lightbulb mr-2"></i>
                常见问题
            </h4>
            <button class="quick-question-btn" onclick="askQuestion('玉米叶子发黄是什么原因？')">
                <i class="fas fa-leaf mr-2"></i>
                玉米叶子发黄是什么原因？
            </button>
            <button class="quick-question-btn" onclick="askQuestion('现在适合种什么蔬菜？')">
                <i class="fas fa-seedling mr-2"></i>
                现在适合种什么蔬菜？
            </button>
            <button class="quick-question-btn" onclick="askQuestion('如何防治小麦条纹花叶病？')">
                <i class="fas fa-bug mr-2"></i>
                如何防治小麦条纹花叶病？
            </button>
            <button class="quick-question-btn" onclick="askQuestion('今年玉米价格走势如何？')">
                <i class="fas fa-chart-line mr-2"></i>
                今年玉米价格走势如何？
            </button>
        </div>

        <!-- 聊天消息 -->
        <div class="chat-messages" id="chatMessages">
            <!-- 消息将动态添加到这里 -->
        </div>

        <!-- 打字指示器 -->
        <div class="typing-indicator" id="typingIndicator">
            <div class="message-avatar ai-avatar">
                <i class="fas fa-robot"></i>
            </div>
            <div class="typing-dots">
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
            </div>
        </div>
    </div>

    <!-- 输入区域 -->
    <div class="input-area">
        <div class="input-container">
            <button class="voice-btn" id="voiceBtn" onclick="toggleVoice()">
                <i class="fas fa-microphone"></i>
            </button>
            <textarea 
                class="message-input" 
                id="messageInput" 
                placeholder="请输入您的问题..."
                rows="1"
                onkeypress="handleKeyPress(event)"
                oninput="adjustTextareaHeight(this)"
            ></textarea>
            <button class="send-btn" id="sendBtn" onclick="sendMessage()">
                <i class="fas fa-paper-plane"></i>
            </button>
        </div>
    </div>

    <!-- 底部导航 -->
    <div class="bottom-nav">
        <div class="nav-item" onclick="navigateTo('home')">
            <i class="fas fa-home nav-icon"></i>
            <span class="nav-text">首页</span>
        </div>
        <div class="nav-item" onclick="navigateTo('pest-detection')">
            <i class="fas fa-bug nav-icon"></i>
            <span class="nav-text">识别</span>
        </div>
        <div class="nav-item" onclick="navigateTo('qr-scan')">
            <i class="fas fa-qrcode nav-icon"></i>
            <span class="nav-text">溯源</span>
        </div>
        <div class="nav-item" onclick="navigateTo('shop')">
            <i class="fas fa-shopping-cart nav-icon"></i>
            <span class="nav-text">农品汇</span>
        </div>
        <div class="nav-item" onclick="navigateTo('profile')">
            <i class="fas fa-user nav-icon"></i>
            <span class="nav-text">我的</span>
        </div>
    </div>

    <script>
        let isRecording = false;
        
        // 更新时间显示
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
            });
            document.getElementById('current-time').textContent = timeString;
        }
        
        updateTime();
        setInterval(updateTime, 1000);

        // 发送消息
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) return;
            
            // 添加用户消息
            addMessage(message, 'user');
            input.value = '';
            adjustTextareaHeight(input);
            
            // 隐藏欢迎消息和快捷问题
            hideWelcomeContent();
            
            // 显示AI正在输入
            showTypingIndicator();
            
            // 模拟AI回复
            setTimeout(() => {
                hideTypingIndicator();
                const aiResponse = generateAIResponse(message);
                addMessage(aiResponse, 'ai');
            }, 2000);
        }

        // 添加消息到聊天
        function addMessage(content, sender) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            
            const now = new Date();
            const timeString = now.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });
            
            messageDiv.innerHTML = `
                <div class="message-avatar ${sender}-avatar">
                    <i class="fas fa-${sender === 'ai' ? 'robot' : 'user'}"></i>
                </div>
                <div>
                    <div class="message-content ${sender}-message">
                        ${content}
                    </div>
                    <div class="message-time">${timeString}</div>
                </div>
            `;
            
            chatMessages.appendChild(messageDiv);
            scrollToBottom();
        }

        // 生成AI回复
        function generateAIResponse(question) {
            const responses = {
                '玉米叶子发黄': '玉米叶子发黄可能有以下几个原因：\n\n1. **缺氮**：老叶先黄，建议追施氮肥\n2. **病害**：如玉米大斑病，需要喷施杀菌剂\n3. **缺水**：干旱导致，及时灌溉\n4. **虫害**：如玉米螟，需要防虫处理\n\n建议您拍照上传，我可以帮您更准确地诊断。',
                '适合种什么蔬菜': '根据当前季节（春季），建议种植：\n\n🥬 **叶菜类**：小白菜、菠菜、生菜\n🥕 **根茎类**：萝卜、胡萝卜\n🥒 **瓜果类**：黄瓜、丝瓜（温室）\n🌶️ **茄果类**：番茄、辣椒（育苗）\n\n注意关注当地气温变化，做好防寒措施。',
                '小麦条纹花叶病': '小麦条纹花叶病防治方案：\n\n**预防措施**：\n• 选用抗病品种\n• 适期播种，避免过早\n• 清除田间杂草\n\n**治疗方法**：\n• 喷施病毒A或植病灵\n• 加强田间管理\n• 及时追肥增强抗性\n\n发病初期治疗效果最佳。',
                '玉米价格走势': '根据最新市场分析：\n\n📈 **当前价格**：2.85-3.10元/公斤\n📊 **走势预测**：稳中有升\n\n**影响因素**：\n• 春耕需求增加\n• 饲料行业回暖\n• 出口订单增长\n\n建议适时出售，关注政策动向。'
            };
            
            // 简单的关键词匹配
            for (let key in responses) {
                if (question.includes(key)) {
                    return responses[key];
                }
            }
            
            // 默认回复
            return '感谢您的提问！这是一个很好的农业问题。建议您：\n\n1. 详细描述具体情况\n2. 提供相关图片\n3. 说明地区和作物品种\n\n这样我可以为您提供更准确的建议。您也可以联系当地农技专家获得现场指导。';
        }

        // 快捷问题
        function askQuestion(question) {
            document.getElementById('messageInput').value = question;
            sendMessage();
        }

        // 显示/隐藏打字指示器
        function showTypingIndicator() {
            document.getElementById('typingIndicator').style.display = 'flex';
            scrollToBottom();
        }

        function hideTypingIndicator() {
            document.getElementById('typingIndicator').style.display = 'none';
        }

        // 隐藏欢迎内容
        function hideWelcomeContent() {
            document.getElementById('welcomeMessage').style.display = 'none';
            document.getElementById('quickQuestions').style.display = 'none';
        }

        // 滚动到底部
        function scrollToBottom() {
            const container = document.getElementById('chatContainer');
            setTimeout(() => {
                container.scrollTop = container.scrollHeight;
            }, 100);
        }

        // 处理键盘事件
        function handleKeyPress(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        }

        // 调整文本框高度
        function adjustTextareaHeight(textarea) {
            textarea.style.height = 'auto';
            textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
        }

        // 语音功能
        function toggleVoice() {
            const voiceBtn = document.getElementById('voiceBtn');
            const icon = voiceBtn.querySelector('i');
            
            if (!isRecording) {
                // 开始录音
                isRecording = true;
                voiceBtn.classList.add('recording');
                icon.className = 'fas fa-stop';
                alert('开始录音...');
            } else {
                // 停止录音
                isRecording = false;
                voiceBtn.classList.remove('recording');
                icon.className = 'fas fa-microphone';
                alert('录音结束，正在识别...');
                
                // 模拟语音识别结果
                setTimeout(() => {
                    document.getElementById('messageInput').value = '玉米叶子发黄怎么办？';
                }, 1000);
            }
        }

        // 页面导航
        function navigateTo(page) {
            window.open(`${page}.html`, '_blank');
        }

        // 底部导航点击效果
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelectorAll('.nav-item').forEach(nav => nav.classList.remove('active'));
                this.classList.add('active');
            });
        });
    </script>
        </div>
    </div>
</body>
</html>
