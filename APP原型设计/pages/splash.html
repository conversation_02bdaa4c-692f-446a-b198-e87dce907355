<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SFAP - 启动页面</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* iPhone 15 Pro 外观模拟 */
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .iphone-frame {
            width: 393px;
            height: 852px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            position: relative;
        }

        .iphone-screen {
            width: 100%;
            height: 100%;
            background: #fff;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }

        /* 动态岛模拟 */
        .dynamic-island {
            position: absolute;
            top: 12px;
            left: 50%;
            transform: translateX(-50%);
            width: 126px;
            height: 37px;
            background: #000;
            border-radius: 19px;
            z-index: 1000;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            .iphone-frame {
                width: 350px;
                height: 760px;
            }
        }
        :root {
            --primary-green: #22C55E;
            --dark-green: #16A34A;
            --light-green: #DCFCE7;
            --bg-green: #F0FDF4;
        }
        
        /* iOS状态栏 */
        .ios-status-bar {
            height: 47px;
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        /* 启动动画 */
        .splash-container {
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            min-height: calc(100vh - 47px);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            overflow: hidden;
        }
        
        .logo-container {
            text-align: center;
            animation: fadeInUp 1.5s ease-out;
        }
        
        .logo-icon {
            font-size: 80px;
            color: white;
            margin-bottom: 20px;
            animation: bounce 2s infinite;
        }
        
        .app-title {
            font-size: 32px;
            font-weight: bold;
            color: white;
            margin-bottom: 8px;
            letter-spacing: 2px;
        }
        
        .app-subtitle {
            font-size: 16px;
            color: rgba(255,255,255,0.9);
            margin-bottom: 40px;
        }
        
        .loading-container {
            margin-top: 40px;
        }
        
        .loading-dots {
            display: flex;
            gap: 8px;
            justify-content: center;
        }
        
        .loading-dot {
            width: 12px;
            height: 12px;
            background: white;
            border-radius: 50%;
            animation: loadingPulse 1.5s infinite;
        }
        
        .loading-dot:nth-child(2) {
            animation-delay: 0.3s;
        }
        
        .loading-dot:nth-child(3) {
            animation-delay: 0.6s;
        }
        
        .version-info {
            position: absolute;
            bottom: 40px;
            color: rgba(255,255,255,0.8);
            font-size: 14px;
        }
        
        /* 背景装饰 */
        .bg-decoration {
            position: absolute;
            opacity: 0.1;
        }
        
        .bg-leaf-1 {
            top: 10%;
            left: 10%;
            font-size: 60px;
            animation: float 6s ease-in-out infinite;
        }
        
        .bg-leaf-2 {
            top: 20%;
            right: 15%;
            font-size: 40px;
            animation: float 8s ease-in-out infinite reverse;
        }
        
        .bg-leaf-3 {
            bottom: 30%;
            left: 20%;
            font-size: 50px;
            animation: float 7s ease-in-out infinite;
        }
        
        .bg-leaf-4 {
            bottom: 15%;
            right: 10%;
            font-size: 35px;
            animation: float 9s ease-in-out infinite reverse;
        }
        
        /* 动画定义 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }
        
        @keyframes loadingPulse {
            0%, 100% {
                opacity: 0.3;
                transform: scale(0.8);
            }
            50% {
                opacity: 1;
                transform: scale(1);
            }
        }
        
        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
            }
            50% {
                transform: translateY(-20px) rotate(10deg);
            }
        }
    </style>
</head>
<body>
    <!-- iPhone 框架 -->
    <div class="iphone-frame">
        <!-- 动态岛 -->
        <div class="dynamic-island"></div>

        <!-- 屏幕内容 -->
        <div class="iphone-screen">
            <!-- iOS状态栏 -->
    <div class="ios-status-bar">
        <div class="flex items-center">
            <span id="current-time">9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-sm"></i>
            <i class="fas fa-wifi text-sm"></i>
            <div class="flex items-center">
                <div class="w-6 h-3 border border-white rounded-sm">
                    <div class="w-4 h-1.5 bg-white rounded-sm m-0.5"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 启动页面内容 -->
    <div class="splash-container">
        <!-- 背景装饰 -->
        <i class="fas fa-leaf bg-decoration bg-leaf-1"></i>
        <i class="fas fa-seedling bg-decoration bg-leaf-2"></i>
        <i class="fas fa-tree bg-decoration bg-leaf-3"></i>
        <i class="fas fa-spa bg-decoration bg-leaf-4"></i>
        
        <!-- 主要内容 -->
        <div class="logo-container">
            <div class="logo-icon">
                <i class="fas fa-seedling"></i>
            </div>
            <h1 class="app-title">SFAP</h1>
            <p class="app-subtitle">智慧农业助手</p>
            <p class="text-white text-sm opacity-90">让农业更智慧，让生活更美好</p>
        </div>
        
        <!-- 加载动画 -->
        <div class="loading-container">
            <div class="loading-dots">
                <div class="loading-dot"></div>
                <div class="loading-dot"></div>
                <div class="loading-dot"></div>
            </div>
            <p class="text-white text-sm mt-4 text-center opacity-80">正在加载...</p>
        </div>
        
        <!-- 版本信息 -->
        <div class="version-info text-center">
            <p>版本 1.0.0</p>
            <p class="text-xs mt-1">© 2025 SFAP智慧农业平台</p>
        </div>
    </div>

    <script>
        // 更新时间显示
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
            });
            document.getElementById('current-time').textContent = timeString;
        }
        
        // 初始化时间并每秒更新
        updateTime();
        setInterval(updateTime, 1000);
        
        // 3秒后自动跳转到登录页面（在实际应用中）
        setTimeout(() => {
            // 这里可以添加跳转逻辑
            console.log('启动完成，准备跳转到主页面');
        }, 3000);
    </script>
        </div>
    </div>
</body>
</html>
