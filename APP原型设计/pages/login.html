<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SFAP - 登录</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* iPhone 15 Pro 外观模拟 */
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .iphone-frame {
            width: 393px;
            height: 852px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            position: relative;
        }

        .iphone-screen {
            width: 100%;
            height: 100%;
            background: #fff;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }

        /* 动态岛模拟 */
        .dynamic-island {
            position: absolute;
            top: 12px;
            left: 50%;
            transform: translateX(-50%);
            width: 126px;
            height: 37px;
            background: #000;
            border-radius: 19px;
            z-index: 1000;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            .iphone-frame {
                width: 350px;
                height: 760px;
            }
        }
        :root {
            --primary-green: #22C55E;
            --dark-green: #16A34A;
            --light-green: #DCFCE7;
            --bg-green: #F0FDF4;
        }
        
        .ios-status-bar {
            height: 47px;
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        .login-container {
            min-height: calc(100vh - 47px);
            background: linear-gradient(135deg, var(--bg-green), white);
        }
        
        .login-form {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin: 20px;
            padding: 30px 25px;
        }
        
        .input-group {
            position: relative;
            margin-bottom: 20px;
        }
        
        .input-field {
            width: 100%;
            padding: 16px 20px 16px 50px;
            border: 2px solid #E5E7EB;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #F9FAFB;
        }
        
        .input-field:focus {
            outline: none;
            border-color: var(--primary-green);
            background: white;
            box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
        }
        
        .input-icon {
            position: absolute;
            left: 18px;
            top: 50%;
            transform: translateY(-50%);
            color: #9CA3AF;
            font-size: 18px;
        }
        
        .login-btn {
            width: 100%;
            padding: 16px;
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 10px;
        }
        
        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(34, 197, 94, 0.3);
        }
        
        .social-login {
            display: flex;
            gap: 15px;
            margin-top: 25px;
        }
        
        .social-btn {
            flex: 1;
            padding: 12px;
            border: 2px solid #E5E7EB;
            border-radius: 12px;
            background: white;
            color: #6B7280;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .social-btn:hover {
            border-color: var(--primary-green);
            color: var(--primary-green);
        }
        
        .forgot-password {
            text-align: center;
            margin-top: 20px;
        }
        
        .forgot-password a {
            color: var(--primary-green);
            text-decoration: none;
            font-size: 14px;
        }
        
        .register-link {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #E5E7EB;
        }
        
        .register-link a {
            color: var(--primary-green);
            text-decoration: none;
            font-weight: 600;
        }
        
        .user-type-selector {
            display: flex;
            gap: 10px;
            margin-bottom: 25px;
        }
        
        .user-type-btn {
            flex: 1;
            padding: 12px 8px;
            border: 2px solid #E5E7EB;
            border-radius: 10px;
            background: white;
            color: #6B7280;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }
        
        .user-type-btn.active {
            border-color: var(--primary-green);
            background: var(--light-green);
            color: var(--dark-green);
        }
    </style>
</head>
<body>
    <!-- iPhone 框架 -->
    <div class="iphone-frame">
        <!-- 动态岛 -->
        <div class="dynamic-island"></div>

        <!-- 屏幕内容 -->
        <div class="iphone-screen">
            <!-- iOS状态栏 -->
    <div class="ios-status-bar">
        <div class="flex items-center">
            <span id="current-time">9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-sm"></i>
            <i class="fas fa-wifi text-sm"></i>
            <div class="flex items-center">
                <div class="w-6 h-3 border border-white rounded-sm">
                    <div class="w-4 h-1.5 bg-white rounded-sm m-0.5"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 登录页面内容 -->
    <div class="login-container flex items-center justify-center p-4">
        <div class="w-full max-w-sm">
            <!-- Logo区域 -->
            <div class="text-center mb-8">
                <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-green-500 to-green-600 rounded-full mb-4">
                    <i class="fas fa-seedling text-white text-3xl"></i>
                </div>
                <h1 class="text-2xl font-bold text-gray-800">欢迎回来</h1>
                <p class="text-gray-600 mt-2">登录您的SFAP账户</p>
            </div>

            <!-- 登录表单 -->
            <div class="login-form">
                <!-- 用户类型选择 -->
                <div class="user-type-selector">
                    <div class="user-type-btn active" data-type="farmer">
                        <i class="fas fa-tractor text-sm mb-1"></i>
                        <div>农户</div>
                    </div>
                    <div class="user-type-btn" data-type="consumer">
                        <i class="fas fa-shopping-basket text-sm mb-1"></i>
                        <div>消费者</div>
                    </div>
                    <div class="user-type-btn" data-type="business">
                        <i class="fas fa-building text-sm mb-1"></i>
                        <div>企业</div>
                    </div>
                </div>

                <!-- 手机号输入 -->
                <div class="input-group">
                    <i class="fas fa-mobile-alt input-icon"></i>
                    <input type="tel" class="input-field" placeholder="请输入手机号" maxlength="11">
                </div>

                <!-- 密码输入 -->
                <div class="input-group">
                    <i class="fas fa-lock input-icon"></i>
                    <input type="password" class="input-field" placeholder="请输入密码">
                    <i class="fas fa-eye absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 cursor-pointer" onclick="togglePassword(this)"></i>
                </div>

                <!-- 记住密码 -->
                <div class="flex items-center justify-between mb-6">
                    <label class="flex items-center">
                        <input type="checkbox" class="w-4 h-4 text-green-500 border-gray-300 rounded focus:ring-green-500">
                        <span class="ml-2 text-sm text-gray-600">记住密码</span>
                    </label>
                </div>

                <!-- 登录按钮 -->
                <button class="login-btn" onclick="handleLogin()">
                    <i class="fas fa-sign-in-alt mr-2"></i>
                    立即登录
                </button>

                <!-- 第三方登录 -->
                <div class="social-login">
                    <div class="social-btn">
                        <i class="fab fa-weixin text-green-500 text-xl"></i>
                        <div class="text-xs mt-1">微信</div>
                    </div>
                    <div class="social-btn">
                        <i class="fab fa-alipay text-blue-500 text-xl"></i>
                        <div class="text-xs mt-1">支付宝</div>
                    </div>
                </div>

                <!-- 忘记密码 -->
                <div class="forgot-password">
                    <a href="#">忘记密码？</a>
                </div>

                <!-- 注册链接 -->
                <div class="register-link">
                    <span class="text-gray-600">还没有账户？</span>
                    <a href="#" onclick="switchToRegister()">立即注册</a>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 更新时间显示
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
            });
            document.getElementById('current-time').textContent = timeString;
        }
        
        updateTime();
        setInterval(updateTime, 1000);

        // 用户类型选择
        document.querySelectorAll('.user-type-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.user-type-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // 密码显示/隐藏
        function togglePassword(icon) {
            const input = icon.previousElementSibling;
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }

        // 登录处理
        function handleLogin() {
            const phone = document.querySelector('input[type="tel"]').value;
            const password = document.querySelector('input[type="password"]').value;
            
            if (!phone || !password) {
                alert('请填写完整的登录信息');
                return;
            }
            
            // 模拟登录成功
            alert('登录成功！即将跳转到首页');
            // 在实际应用中这里会跳转到首页
        }

        // 切换到注册页面
        function switchToRegister() {
            window.open('register.html', '_blank');
        }
    </script>
        </div>
    </div>
</body>
</html>
