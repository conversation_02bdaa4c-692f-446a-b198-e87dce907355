<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SFAP - 市场信息</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* iPhone 15 Pro 外观模拟 */
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .iphone-frame {
            width: 393px;
            height: 852px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            position: relative;
        }

        .iphone-screen {
            width: 100%;
            height: 100%;
            background: #fff;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }

        /* 动态岛模拟 */
        .dynamic-island {
            position: absolute;
            top: 12px;
            left: 50%;
            transform: translateX(-50%);
            width: 126px;
            height: 37px;
            background: #000;
            border-radius: 19px;
            z-index: 1000;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            .iphone-frame {
                width: 350px;
                height: 760px;
            }
        }
        :root {
            --primary-green: #22C55E;
            --dark-green: #16A34A;
            --light-green: #DCFCE7;
            --bg-green: #F0FDF4;
        }
        
        .ios-status-bar {
            height: 47px;
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        .market-container {
            min-height: calc(100vh - 47px - 80px);
            background: linear-gradient(135deg, var(--bg-green), white);
            overflow-y: auto;
        }
        
        .header {
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            padding: 20px;
            color: white;
            text-align: center;
        }
        
        .search-section {
            padding: 20px;
            background: white;
            border-bottom: 1px solid #E5E7EB;
        }
        
        .search-container {
            position: relative;
        }
        
        .search-input {
            width: 100%;
            padding: 12px 20px 12px 45px;
            border: 2px solid #E5E7EB;
            border-radius: 25px;
            font-size: 16px;
            background: #F9FAFB;
            transition: all 0.3s ease;
        }
        
        .search-input:focus {
            outline: none;
            border-color: var(--primary-green);
            background: white;
            box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
        }
        
        .search-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #9CA3AF;
            font-size: 18px;
        }
        
        .tabs-container {
            background: white;
            padding: 0 20px;
            border-bottom: 1px solid #E5E7EB;
        }
        
        .tabs {
            display: flex;
            gap: 30px;
        }
        
        .tab {
            padding: 15px 0;
            color: #6B7280;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
            font-weight: 600;
        }
        
        .tab.active {
            color: var(--primary-green);
            border-bottom-color: var(--primary-green);
        }
        
        .content-section {
            padding: 20px;
        }
        
        .price-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .price-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .price-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .product-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .product-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
        }
        
        .product-details {
            flex: 1;
        }
        
        .product-name {
            font-size: 16px;
            font-weight: 600;
            color: #1F2937;
            margin-bottom: 2px;
        }
        
        .product-unit {
            font-size: 12px;
            color: #6B7280;
        }
        
        .price-info {
            text-align: right;
        }
        
        .current-price {
            font-size: 20px;
            font-weight: bold;
            color: #1F2937;
            margin-bottom: 2px;
        }
        
        .price-change {
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .price-up {
            color: #EF4444;
        }
        
        .price-down {
            color: var(--primary-green);
        }
        
        .price-stable {
            color: #6B7280;
        }
        
        .price-chart {
            height: 60px;
            background: var(--bg-green);
            border-radius: 8px;
            margin-top: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--dark-green);
            font-size: 14px;
        }
        
        .news-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .news-header {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            margin-bottom: 12px;
        }
        
        .news-icon {
            width: 40px;
            height: 40px;
            background: var(--light-green);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--dark-green);
            font-size: 16px;
            flex-shrink: 0;
        }
        
        .news-content {
            flex: 1;
        }
        
        .news-title {
            font-size: 16px;
            font-weight: 600;
            color: #1F2937;
            margin-bottom: 8px;
            line-height: 1.4;
        }
        
        .news-summary {
            font-size: 14px;
            color: #6B7280;
            line-height: 1.5;
            margin-bottom: 8px;
        }
        
        .news-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #9CA3AF;
        }
        
        .news-source {
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .filter-section {
            background: white;
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .filter-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .filter-btn {
            padding: 8px 16px;
            border: 2px solid var(--light-green);
            border-radius: 20px;
            background: white;
            color: var(--dark-green);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .filter-btn.active {
            background: var(--primary-green);
            border-color: var(--primary-green);
            color: white;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: white;
            border-top: 1px solid #E5E7EB;
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding: 10px 0;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 5px 10px;
        }
        
        .nav-item.active {
            color: var(--primary-green);
        }
        
        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .nav-text {
            font-size: 12px;
        }
        
        .market-summary {
            background: rgba(255,255,255,0.15);
            border-radius: 15px;
            padding: 15px;
            margin-top: 15px;
            backdrop-filter: blur(10px);
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        .summary-item {
            text-align: center;
        }
        
        .summary-value {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 4px;
        }
        
        .summary-label {
            font-size: 12px;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <!-- iPhone 框架 -->
    <div class="iphone-frame">
        <!-- 动态岛 -->
        <div class="dynamic-island"></div>

        <!-- 屏幕内容 -->
        <div class="iphone-screen">
            <!-- iOS状态栏 -->
    <div class="ios-status-bar">
        <div class="flex items-center">
            <span id="current-time">9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-sm"></i>
            <i class="fas fa-wifi text-sm"></i>
            <div class="flex items-center">
                <div class="w-6 h-3 border border-white rounded-sm">
                    <div class="w-4 h-1.5 bg-white rounded-sm m-0.5"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 市场信息页面内容 -->
    <div class="market-container">
        <!-- 头部 -->
        <div class="header">
            <h1 class="text-xl font-bold mb-2">
                <i class="fas fa-chart-line mr-2"></i>
                市场信息
            </h1>
            <p class="text-sm opacity-90">实时农产品价格行情与市场资讯</p>
            
            <!-- 市场概况 -->
            <div class="market-summary">
                <div class="summary-grid">
                    <div class="summary-item">
                        <div class="summary-value text-green-300">↑ 15</div>
                        <div class="summary-label">上涨品种</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-value text-red-300">↓ 8</div>
                        <div class="summary-label">下跌品种</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-value">→ 12</div>
                        <div class="summary-label">持平品种</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-value">2.8%</div>
                        <div class="summary-label">平均涨幅</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索区域 -->
        <div class="search-section">
            <div class="search-container">
                <i class="fas fa-search search-icon"></i>
                <input type="text" class="search-input" placeholder="搜索农产品价格...">
            </div>
        </div>

        <!-- 标签页 -->
        <div class="tabs-container">
            <div class="tabs">
                <div class="tab active" onclick="switchTab('prices')">价格行情</div>
                <div class="tab" onclick="switchTab('news')">市场资讯</div>
                <div class="tab" onclick="switchTab('analysis')">分析报告</div>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-section">
            <!-- 价格行情 -->
            <div id="prices-content">
                <!-- 筛选器 -->
                <div class="filter-section">
                    <h4 class="text-sm font-semibold text-gray-800 mb-3">
                        <i class="fas fa-filter mr-2"></i>
                        分类筛选
                    </h4>
                    <div class="filter-buttons">
                        <button class="filter-btn active">全部</button>
                        <button class="filter-btn">粮食</button>
                        <button class="filter-btn">蔬菜</button>
                        <button class="filter-btn">水果</button>
                        <button class="filter-btn">畜禽</button>
                    </div>
                </div>

                <!-- 价格列表 -->
                <div class="price-card">
                    <div class="price-header">
                        <div class="product-info">
                            <div class="product-icon">
                                <i class="fas fa-seedling"></i>
                            </div>
                            <div class="product-details">
                                <div class="product-name">玉米</div>
                                <div class="product-unit">元/公斤</div>
                            </div>
                        </div>
                        <div class="price-info">
                            <div class="current-price">2.95</div>
                            <div class="price-change price-up">
                                <i class="fas fa-arrow-up"></i>
                                +0.12 (+4.2%)
                            </div>
                        </div>
                    </div>
                    <div class="price-chart">
                        <i class="fas fa-chart-line mr-2"></i>
                        7日涨幅趋势图
                    </div>
                </div>

                <div class="price-card">
                    <div class="price-header">
                        <div class="product-info">
                            <div class="product-icon">
                                <i class="fas fa-leaf"></i>
                            </div>
                            <div class="product-details">
                                <div class="product-name">小麦</div>
                                <div class="product-unit">元/公斤</div>
                            </div>
                        </div>
                        <div class="price-info">
                            <div class="current-price">2.68</div>
                            <div class="price-change price-down">
                                <i class="fas fa-arrow-down"></i>
                                -0.05 (-1.8%)
                            </div>
                        </div>
                    </div>
                    <div class="price-chart">
                        <i class="fas fa-chart-line mr-2"></i>
                        7日跌幅趋势图
                    </div>
                </div>

                <div class="price-card">
                    <div class="price-header">
                        <div class="product-info">
                            <div class="product-icon">
                                <i class="fas fa-carrot"></i>
                            </div>
                            <div class="product-details">
                                <div class="product-name">胡萝卜</div>
                                <div class="product-unit">元/公斤</div>
                            </div>
                        </div>
                        <div class="price-info">
                            <div class="current-price">4.20</div>
                            <div class="price-change price-stable">
                                <i class="fas fa-minus"></i>
                                持平
                            </div>
                        </div>
                    </div>
                    <div class="price-chart">
                        <i class="fas fa-chart-line mr-2"></i>
                        7日价格稳定
                    </div>
                </div>
            </div>

            <!-- 市场资讯 -->
            <div id="news-content" style="display: none;">
                <div class="news-card">
                    <div class="news-header">
                        <div class="news-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <div class="news-content">
                            <div class="news-title">春节后玉米价格持续上涨</div>
                            <div class="news-summary">受饲料需求增加和运输成本上升影响，全国玉米平均价格较节前上涨4.2%，预计短期内仍将保持上涨趋势...</div>
                            <div class="news-meta">
                                <div class="news-source">
                                    <i class="fas fa-newspaper"></i>
                                    农业农村部
                                </div>
                                <div>2小时前</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="news-card">
                    <div class="news-header">
                        <div class="news-icon">
                            <i class="fas fa-leaf"></i>
                        </div>
                        <div class="news-content">
                            <div class="news-title">有机蔬菜市场需求旺盛</div>
                            <div class="news-summary">随着消费者健康意识提升，有机蔬菜销量同比增长35%，价格较普通蔬菜高出50-80%，但仍供不应求...</div>
                            <div class="news-meta">
                                <div class="news-source">
                                    <i class="fas fa-newspaper"></i>
                                    中国农业网
                                </div>
                                <div>5小时前</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="news-card">
                    <div class="news-header">
                        <div class="news-icon">
                            <i class="fas fa-globe"></i>
                        </div>
                        <div class="news-content">
                            <div class="news-title">国际大豆价格波动影响国内市场</div>
                            <div class="news-summary">受国际贸易形势影响，大豆进口价格出现波动，预计将对国内豆制品和饲料价格产生一定影响...</div>
                            <div class="news-meta">
                                <div class="news-source">
                                    <i class="fas fa-newspaper"></i>
                                    期货日报
                                </div>
                                <div>1天前</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分析报告 -->
            <div id="analysis-content" style="display: none;">
                <div class="news-card">
                    <div class="news-header">
                        <div class="news-icon">
                            <i class="fas fa-chart-pie"></i>
                        </div>
                        <div class="news-content">
                            <div class="news-title">2025年春季农产品市场分析报告</div>
                            <div class="news-summary">综合分析春季农产品供需情况、价格走势和市场预期，为农户和经销商提供决策参考...</div>
                            <div class="news-meta">
                                <div class="news-source">
                                    <i class="fas fa-file-alt"></i>
                                    SFAP研究院
                                </div>
                                <div>今天</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="news-card">
                    <div class="news-header">
                        <div class="news-icon">
                            <i class="fas fa-trending-up"></i>
                        </div>
                        <div class="news-content">
                            <div class="news-title">玉米价格预测模型分析</div>
                            <div class="news-summary">基于AI算法的玉米价格预测显示，未来30天价格将在2.8-3.1元/公斤区间波动，建议关注天气变化...</div>
                            <div class="news-meta">
                                <div class="news-source">
                                    <i class="fas fa-file-alt"></i>
                                    AI预测中心
                                </div>
                                <div>昨天</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航 -->
    <div class="bottom-nav">
        <div class="nav-item" onclick="navigateTo('home')">
            <i class="fas fa-home nav-icon"></i>
            <span class="nav-text">首页</span>
        </div>
        <div class="nav-item" onclick="navigateTo('pest-detection')">
            <i class="fas fa-bug nav-icon"></i>
            <span class="nav-text">识别</span>
        </div>
        <div class="nav-item" onclick="navigateTo('qr-scan')">
            <i class="fas fa-qrcode nav-icon"></i>
            <span class="nav-text">溯源</span>
        </div>
        <div class="nav-item" onclick="navigateTo('shop')">
            <i class="fas fa-shopping-cart nav-icon"></i>
            <span class="nav-text">农品汇</span>
        </div>
        <div class="nav-item" onclick="navigateTo('profile')">
            <i class="fas fa-user nav-icon"></i>
            <span class="nav-text">我的</span>
        </div>
    </div>

    <script>
        // 更新时间显示
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
            });
            document.getElementById('current-time').textContent = timeString;
        }
        
        updateTime();
        setInterval(updateTime, 1000);

        // 切换标签页
        function switchTab(tabName) {
            // 移除所有标签的active状态
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 隐藏所有内容
            document.querySelectorAll('[id$="-content"]').forEach(content => {
                content.style.display = 'none';
            });
            
            // 激活当前标签
            event.target.classList.add('active');
            
            // 显示对应内容
            document.getElementById(tabName + '-content').style.display = 'block';
        }

        // 筛选按钮点击
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // 页面导航
        function navigateTo(page) {
            window.open(`${page}.html`, '_blank');
        }

        // 底部导航点击效果
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelectorAll('.nav-item').forEach(nav => nav.classList.remove('active'));
                this.classList.add('active');
            });
        });
    </script>
        </div>
    </div>
</body>
</html>
