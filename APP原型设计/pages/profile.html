<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SFAP - 个人中心</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* iPhone 15 Pro 外观模拟 */
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .iphone-frame {
            width: 393px;
            height: 852px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            position: relative;
        }

        .iphone-screen {
            width: 100%;
            height: 100%;
            background: #fff;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }

        /* 动态岛模拟 */
        .dynamic-island {
            position: absolute;
            top: 12px;
            left: 50%;
            transform: translateX(-50%);
            width: 126px;
            height: 37px;
            background: #000;
            border-radius: 19px;
            z-index: 1000;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            .iphone-frame {
                width: 350px;
                height: 760px;
            }
        }
        :root {
            --primary-green: #22C55E;
            --dark-green: #16A34A;
            --light-green: #DCFCE7;
            --bg-green: #F0FDF4;
        }
        
        .ios-status-bar {
            height: 47px;
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        .profile-container {
            min-height: calc(100vh - 47px - 80px);
            background: linear-gradient(135deg, var(--bg-green), white);
            overflow-y: auto;
        }
        
        .profile-header {
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            padding: 30px 20px;
            color: white;
            text-align: center;
            position: relative;
        }
        
        .profile-avatar {
            width: 80px;
            height: 80px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 32px;
            border: 3px solid rgba(255,255,255,0.3);
        }
        
        .profile-name {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .profile-type {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 15px;
        }
        
        .profile-stats {
            display: flex;
            justify-content: space-around;
            background: rgba(255,255,255,0.15);
            border-radius: 15px;
            padding: 15px;
            backdrop-filter: blur(10px);
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-value {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: 12px;
            opacity: 0.9;
        }
        
        .menu-section {
            background: white;
            margin: 20px;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .menu-item {
            display: flex;
            align-items: center;
            padding: 18px 20px;
            border-bottom: 1px solid #F3F4F6;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .menu-item:last-child {
            border-bottom: none;
        }
        
        .menu-item:hover {
            background: var(--bg-green);
        }
        
        .menu-icon {
            width: 40px;
            height: 40px;
            background: var(--light-green);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--dark-green);
            font-size: 18px;
            margin-right: 15px;
        }
        
        .menu-content {
            flex: 1;
        }
        
        .menu-title {
            font-size: 16px;
            font-weight: 600;
            color: #1F2937;
            margin-bottom: 2px;
        }
        
        .menu-desc {
            font-size: 12px;
            color: #6B7280;
        }
        
        .menu-arrow {
            color: #9CA3AF;
            font-size: 14px;
        }
        
        .menu-badge {
            background: #EF4444;
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: 600;
            margin-right: 10px;
        }
        
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin: 20px;
        }
        
        .action-item {
            background: white;
            border-radius: 12px;
            padding: 20px 10px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .action-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .action-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            margin: 0 auto 10px;
        }
        
        .action-title {
            font-size: 14px;
            font-weight: 600;
            color: #1F2937;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: white;
            border-top: 1px solid #E5E7EB;
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding: 10px 0;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 5px 10px;
        }
        
        .nav-item.active {
            color: var(--primary-green);
        }
        
        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .nav-text {
            font-size: 12px;
        }
        
        .settings-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.2);
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 18px;
            cursor: pointer;
        }
        
        .vip-card {
            background: linear-gradient(135deg, #F59E0B, #D97706);
            border-radius: 15px;
            padding: 20px;
            margin: 20px;
            color: white;
            position: relative;
            overflow: hidden;
        }
        
        .vip-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -20px;
            width: 100px;
            height: 200px;
            background: rgba(255,255,255,0.1);
            transform: rotate(15deg);
        }
        
        .vip-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .vip-title {
            font-size: 18px;
            font-weight: bold;
        }
        
        .vip-level {
            background: rgba(255,255,255,0.2);
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
        }
        
        .vip-benefits {
            font-size: 14px;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <!-- iPhone 框架 -->
    <div class="iphone-frame">
        <!-- 动态岛 -->
        <div class="dynamic-island"></div>

        <!-- 屏幕内容 -->
        <div class="iphone-screen">
            <!-- iOS状态栏 -->
    <div class="ios-status-bar">
        <div class="flex items-center">
            <span id="current-time">9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-sm"></i>
            <i class="fas fa-wifi text-sm"></i>
            <div class="flex items-center">
                <div class="w-6 h-3 border border-white rounded-sm">
                    <div class="w-4 h-1.5 bg-white rounded-sm m-0.5"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 个人中心页面内容 -->
    <div class="profile-container">
        <!-- 个人信息头部 -->
        <div class="profile-header">
            <button class="settings-btn" onclick="openSettings()">
                <i class="fas fa-cog"></i>
            </button>
            
            <div class="profile-avatar">
                <i class="fas fa-user"></i>
            </div>
            <div class="profile-name">张农户</div>
            <div class="profile-type">
                <i class="fas fa-tractor mr-1"></i>
                新型农业经营主体
            </div>
            
            <div class="profile-stats">
                <div class="stat-item">
                    <div class="stat-value">156</div>
                    <div class="stat-label">识别次数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">23</div>
                    <div class="stat-label">溯源查询</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">89</div>
                    <div class="stat-label">AI问答</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">12</div>
                    <div class="stat-label">订单数</div>
                </div>
            </div>
        </div>

        <!-- VIP会员卡 -->
        <div class="vip-card">
            <div class="vip-header">
                <div class="vip-title">
                    <i class="fas fa-crown mr-2"></i>
                    SFAP会员
                </div>
                <div class="vip-level">黄金会员</div>
            </div>
            <div class="vip-benefits">
                享受专家咨询、优先客服、会员折扣等特权
            </div>
        </div>

        <!-- 快捷操作 -->
        <div class="quick-actions">
            <div class="action-item" onclick="openOrders()">
                <div class="action-icon">
                    <i class="fas fa-shopping-bag"></i>
                </div>
                <div class="action-title">我的订单</div>
            </div>
            <div class="action-item" onclick="openFavorites()">
                <div class="action-icon">
                    <i class="fas fa-heart"></i>
                </div>
                <div class="action-title">我的收藏</div>
            </div>
            <div class="action-item" onclick="openWallet()">
                <div class="action-icon">
                    <i class="fas fa-wallet"></i>
                </div>
                <div class="action-title">我的钱包</div>
            </div>
            <div class="action-item" onclick="openCoupons()">
                <div class="action-icon">
                    <i class="fas fa-ticket-alt"></i>
                </div>
                <div class="action-title">优惠券</div>
            </div>
        </div>

        <!-- 功能菜单 -->
        <div class="menu-section">
            <div class="menu-item" onclick="openFarmManagement()">
                <div class="menu-icon">
                    <i class="fas fa-seedling"></i>
                </div>
                <div class="menu-content">
                    <div class="menu-title">农场管理</div>
                    <div class="menu-desc">管理我的农场信息</div>
                </div>
                <i class="fas fa-chevron-right menu-arrow"></i>
            </div>
            
            <div class="menu-item" onclick="openDataAnalysis()">
                <div class="menu-icon">
                    <i class="fas fa-chart-bar"></i>
                </div>
                <div class="menu-content">
                    <div class="menu-title">数据分析</div>
                    <div class="menu-desc">查看农事数据报告</div>
                </div>
                <i class="fas fa-chevron-right menu-arrow"></i>
            </div>
            
            <div class="menu-item" onclick="openExpertConsult()">
                <div class="menu-icon">
                    <i class="fas fa-user-md"></i>
                </div>
                <div class="menu-content">
                    <div class="menu-title">专家咨询</div>
                    <div class="menu-desc">预约农业专家服务</div>
                </div>
                <div class="menu-badge">新</div>
                <i class="fas fa-chevron-right menu-arrow"></i>
            </div>
        </div>

        <div class="menu-section">
            <div class="menu-item" onclick="openMessages()">
                <div class="menu-icon">
                    <i class="fas fa-bell"></i>
                </div>
                <div class="menu-content">
                    <div class="menu-title">消息通知</div>
                    <div class="menu-desc">查看系统消息和提醒</div>
                </div>
                <div class="menu-badge">3</div>
                <i class="fas fa-chevron-right menu-arrow"></i>
            </div>
            
            <div class="menu-item" onclick="openHelp()">
                <div class="menu-icon">
                    <i class="fas fa-question-circle"></i>
                </div>
                <div class="menu-content">
                    <div class="menu-title">帮助中心</div>
                    <div class="menu-desc">常见问题和使用指南</div>
                </div>
                <i class="fas fa-chevron-right menu-arrow"></i>
            </div>
            
            <div class="menu-item" onclick="openFeedback()">
                <div class="menu-icon">
                    <i class="fas fa-comment-alt"></i>
                </div>
                <div class="menu-content">
                    <div class="menu-title">意见反馈</div>
                    <div class="menu-desc">提出建议和问题反馈</div>
                </div>
                <i class="fas fa-chevron-right menu-arrow"></i>
            </div>
        </div>

        <div class="menu-section">
            <div class="menu-item" onclick="openAbout()">
                <div class="menu-icon">
                    <i class="fas fa-info-circle"></i>
                </div>
                <div class="menu-content">
                    <div class="menu-title">关于SFAP</div>
                    <div class="menu-desc">版本信息和服务条款</div>
                </div>
                <i class="fas fa-chevron-right menu-arrow"></i>
            </div>
            
            <div class="menu-item" onclick="logout()">
                <div class="menu-icon" style="background: #FEE2E2; color: #DC2626;">
                    <i class="fas fa-sign-out-alt"></i>
                </div>
                <div class="menu-content">
                    <div class="menu-title" style="color: #DC2626;">退出登录</div>
                    <div class="menu-desc">安全退出当前账户</div>
                </div>
                <i class="fas fa-chevron-right menu-arrow"></i>
            </div>
        </div>
    </div>

    <!-- 底部导航 -->
    <div class="bottom-nav">
        <div class="nav-item" onclick="navigateTo('home')">
            <i class="fas fa-home nav-icon"></i>
            <span class="nav-text">首页</span>
        </div>
        <div class="nav-item" onclick="navigateTo('pest-detection')">
            <i class="fas fa-bug nav-icon"></i>
            <span class="nav-text">识别</span>
        </div>
        <div class="nav-item" onclick="navigateTo('qr-scan')">
            <i class="fas fa-qrcode nav-icon"></i>
            <span class="nav-text">溯源</span>
        </div>
        <div class="nav-item" onclick="navigateTo('shop')">
            <i class="fas fa-shopping-cart nav-icon"></i>
            <span class="nav-text">农品汇</span>
        </div>
        <div class="nav-item active">
            <i class="fas fa-user nav-icon"></i>
            <span class="nav-text">我的</span>
        </div>
    </div>

    <script>
        // 更新时间显示
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
            });
            document.getElementById('current-time').textContent = timeString;
        }
        
        updateTime();
        setInterval(updateTime, 1000);

        // 功能菜单点击事件
        function openSettings() {
            alert('打开设置页面');
        }

        function openOrders() {
            alert('打开我的订单');
        }

        function openFavorites() {
            alert('打开我的收藏');
        }

        function openWallet() {
            alert('打开我的钱包');
        }

        function openCoupons() {
            alert('打开优惠券');
        }

        function openFarmManagement() {
            alert('打开农场管理');
        }

        function openDataAnalysis() {
            alert('打开数据分析');
        }

        function openExpertConsult() {
            alert('打开专家咨询');
        }

        function openMessages() {
            alert('打开消息通知');
        }

        function openHelp() {
            alert('打开帮助中心');
        }

        function openFeedback() {
            alert('打开意见反馈');
        }

        function openAbout() {
            alert('关于SFAP\n\n版本：1.0.0\n开发者：SFAP团队\n\n智慧农业助手，让农业更智慧！');
        }

        function logout() {
            if (confirm('确定要退出登录吗？')) {
                alert('已退出登录');
                // 在实际应用中这里会跳转到登录页面
                window.open('login.html', '_blank');
            }
        }

        // 页面导航
        function navigateTo(page) {
            window.open(`${page}.html`, '_blank');
        }

        // 底部导航点击效果
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelectorAll('.nav-item').forEach(nav => nav.classList.remove('active'));
                this.classList.add('active');
            });
        });
    </script>
        </div>
    </div>
</body>
</html>
