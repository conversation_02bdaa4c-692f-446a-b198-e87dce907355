<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SFAP - 农事提醒</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* iPhone 15 Pro 外观模拟 */
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .iphone-frame {
            width: 393px;
            height: 852px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            position: relative;
        }

        .iphone-screen {
            width: 100%;
            height: 100%;
            background: #fff;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }

        /* 动态岛模拟 */
        .dynamic-island {
            position: absolute;
            top: 12px;
            left: 50%;
            transform: translateX(-50%);
            width: 126px;
            height: 37px;
            background: #000;
            border-radius: 19px;
            z-index: 1000;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            .iphone-frame {
                width: 350px;
                height: 760px;
            }
        }
        :root {
            --primary-green: #22C55E;
            --dark-green: #16A34A;
            --light-green: #DCFCE7;
            --bg-green: #F0FDF4;
        }
        
        .ios-status-bar {
            height: 47px;
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        .reminder-container {
            min-height: calc(100vh - 47px - 80px);
            background: linear-gradient(135deg, var(--bg-green), white);
            overflow-y: auto;
        }
        
        .header {
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            padding: 20px;
            color: white;
            text-align: center;
        }
        
        .weather-banner {
            background: rgba(255,255,255,0.15);
            border-radius: 15px;
            padding: 15px;
            margin-top: 15px;
            backdrop-filter: blur(10px);
        }
        
        .weather-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .weather-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .weather-icon {
            font-size: 32px;
        }
        
        .weather-details {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .weather-advice {
            background: rgba(255,255,255,0.2);
            border-radius: 8px;
            padding: 8px 12px;
            font-size: 12px;
            margin-top: 8px;
        }
        
        .tabs-container {
            background: white;
            padding: 0 20px;
            border-bottom: 1px solid #E5E7EB;
        }
        
        .tabs {
            display: flex;
            gap: 30px;
        }
        
        .tab {
            padding: 15px 0;
            color: #6B7280;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
            font-weight: 600;
        }
        
        .tab.active {
            color: var(--primary-green);
            border-bottom-color: var(--primary-green);
        }
        
        .content-section {
            padding: 20px;
        }
        
        .reminder-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 5px solid var(--primary-green);
            transition: all 0.3s ease;
        }
        
        .reminder-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .reminder-card.urgent {
            border-left-color: #EF4444;
            background: linear-gradient(135deg, #FEF2F2, white);
        }
        
        .reminder-card.warning {
            border-left-color: #F59E0B;
            background: linear-gradient(135deg, #FFFBEB, white);
        }
        
        .reminder-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
        }
        
        .reminder-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
            margin-right: 15px;
        }
        
        .reminder-icon.normal {
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
        }
        
        .reminder-icon.urgent {
            background: linear-gradient(135deg, #EF4444, #DC2626);
        }
        
        .reminder-icon.warning {
            background: linear-gradient(135deg, #F59E0B, #D97706);
        }
        
        .reminder-content {
            flex: 1;
        }
        
        .reminder-title {
            font-size: 16px;
            font-weight: 600;
            color: #1F2937;
            margin-bottom: 5px;
        }
        
        .reminder-desc {
            font-size: 14px;
            color: #6B7280;
            margin-bottom: 8px;
            line-height: 1.4;
        }
        
        .reminder-time {
            font-size: 12px;
            color: #9CA3AF;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .reminder-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        
        .action-btn {
            padding: 8px 16px;
            border-radius: 8px;
            border: none;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .primary-btn {
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            color: white;
        }
        
        .secondary-btn {
            background: var(--bg-green);
            color: var(--dark-green);
            border: 1px solid var(--light-green);
        }
        
        .add-reminder-btn {
            position: fixed;
            bottom: 100px;
            right: 20px;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 8px 20px rgba(34, 197, 94, 0.3);
            transition: all 0.3s ease;
        }
        
        .add-reminder-btn:hover {
            transform: scale(1.1);
        }
        
        .calendar-section {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .calendar-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .calendar-nav {
            background: var(--light-green);
            border: none;
            border-radius: 8px;
            padding: 8px 12px;
            color: var(--dark-green);
            cursor: pointer;
        }
        
        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 5px;
            text-align: center;
        }
        
        .calendar-day {
            padding: 10px 5px;
            font-size: 14px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .calendar-day.header {
            font-weight: 600;
            color: #6B7280;
            cursor: default;
        }
        
        .calendar-day.today {
            background: var(--primary-green);
            color: white;
        }
        
        .calendar-day.has-reminder {
            background: var(--light-green);
            color: var(--dark-green);
        }
        
        .calendar-day:hover:not(.header) {
            background: var(--bg-green);
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: white;
            border-top: 1px solid #E5E7EB;
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding: 10px 0;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 5px 10px;
        }
        
        .nav-item.active {
            color: var(--primary-green);
        }
        
        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .nav-text {
            font-size: 12px;
        }
        
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #6B7280;
        }
        
        .empty-icon {
            font-size: 48px;
            color: var(--light-green);
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <!-- iPhone 框架 -->
    <div class="iphone-frame">
        <!-- 动态岛 -->
        <div class="dynamic-island"></div>

        <!-- 屏幕内容 -->
        <div class="iphone-screen">
            <!-- iOS状态栏 -->
    <div class="ios-status-bar">
        <div class="flex items-center">
            <span id="current-time">9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-sm"></i>
            <i class="fas fa-wifi text-sm"></i>
            <div class="flex items-center">
                <div class="w-6 h-3 border border-white rounded-sm">
                    <div class="w-4 h-1.5 bg-white rounded-sm m-0.5"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 农事提醒页面内容 -->
    <div class="reminder-container">
        <!-- 头部 -->
        <div class="header">
            <h1 class="text-xl font-bold mb-2">
                <i class="fas fa-bell mr-2"></i>
                农事提醒
            </h1>
            <p class="text-sm opacity-90">智能农事管理，不错过每个重要时机</p>
            
            <!-- 天气横幅 -->
            <div class="weather-banner">
                <div class="weather-info">
                    <div class="weather-left">
                        <i class="fas fa-sun weather-icon"></i>
                        <div>
                            <div class="text-lg font-bold">25°C</div>
                            <div class="weather-details">晴转多云</div>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-sm">湿度 65%</div>
                        <div class="text-sm">风力 2级</div>
                    </div>
                </div>
                <div class="weather-advice">
                    <i class="fas fa-lightbulb mr-1"></i>
                    今日天气适宜农事操作，建议进行田间管理
                </div>
            </div>
        </div>

        <!-- 标签页 -->
        <div class="tabs-container">
            <div class="tabs">
                <div class="tab active" onclick="switchTab('today')">今日</div>
                <div class="tab" onclick="switchTab('upcoming')">即将到来</div>
                <div class="tab" onclick="switchTab('calendar')">日历</div>
                <div class="tab" onclick="switchTab('completed')">已完成</div>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-section">
            <!-- 今日提醒 -->
            <div id="today-content">
                <div class="reminder-card urgent">
                    <div class="reminder-header">
                        <div style="display: flex; align-items: center;">
                            <div class="reminder-icon urgent">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="reminder-content">
                                <div class="reminder-title">紧急：小麦条纹花叶病防治</div>
                                <div class="reminder-desc">根据气象预报，未来3天温湿度适宜病害发生，建议立即喷施防治药剂</div>
                                <div class="reminder-time">
                                    <i class="fas fa-clock"></i>
                                    今天 08:00 - 10:00
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="reminder-actions">
                        <button class="action-btn primary-btn">立即处理</button>
                        <button class="action-btn secondary-btn">延后1小时</button>
                    </div>
                </div>

                <div class="reminder-card warning">
                    <div class="reminder-header">
                        <div style="display: flex; align-items: center;">
                            <div class="reminder-icon warning">
                                <i class="fas fa-tint"></i>
                            </div>
                            <div class="reminder-content">
                                <div class="reminder-title">玉米田灌溉</div>
                                <div class="reminder-desc">土壤湿度监测显示需要补充水分，建议进行滴灌或喷灌</div>
                                <div class="reminder-time">
                                    <i class="fas fa-clock"></i>
                                    今天 14:00 - 16:00
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="reminder-actions">
                        <button class="action-btn primary-btn">开始灌溉</button>
                        <button class="action-btn secondary-btn">查看详情</button>
                    </div>
                </div>

                <div class="reminder-card">
                    <div class="reminder-header">
                        <div style="display: flex; align-items: center;">
                            <div class="reminder-icon normal">
                                <i class="fas fa-seedling"></i>
                            </div>
                            <div class="reminder-content">
                                <div class="reminder-title">蔬菜大棚通风</div>
                                <div class="reminder-desc">温度较高，建议打开通风口降低棚内温度</div>
                                <div class="reminder-time">
                                    <i class="fas fa-clock"></i>
                                    今天 18:00
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="reminder-actions">
                        <button class="action-btn primary-btn">已完成</button>
                        <button class="action-btn secondary-btn">推迟</button>
                    </div>
                </div>
            </div>

            <!-- 即将到来 -->
            <div id="upcoming-content" style="display: none;">
                <div class="reminder-card">
                    <div class="reminder-header">
                        <div style="display: flex; align-items: center;">
                            <div class="reminder-icon normal">
                                <i class="fas fa-spray-can"></i>
                            </div>
                            <div class="reminder-content">
                                <div class="reminder-title">玉米除草剂喷施</div>
                                <div class="reminder-desc">玉米6叶期，适合喷施除草剂</div>
                                <div class="reminder-time">
                                    <i class="fas fa-calendar"></i>
                                    明天 06:00 - 08:00
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="reminder-card">
                    <div class="reminder-header">
                        <div style="display: flex; align-items: center;">
                            <div class="reminder-icon normal">
                                <i class="fas fa-leaf"></i>
                            </div>
                            <div class="reminder-content">
                                <div class="reminder-title">小麦追肥</div>
                                <div class="reminder-desc">小麦拔节期，需要追施氮肥</div>
                                <div class="reminder-time">
                                    <i class="fas fa-calendar"></i>
                                    后天 全天
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 日历视图 -->
            <div id="calendar-content" style="display: none;">
                <div class="calendar-section">
                    <div class="calendar-header">
                        <button class="calendar-nav">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <h3 class="text-lg font-semibold">2025年2月</h3>
                        <button class="calendar-nav">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                    
                    <div class="calendar-grid">
                        <div class="calendar-day header">日</div>
                        <div class="calendar-day header">一</div>
                        <div class="calendar-day header">二</div>
                        <div class="calendar-day header">三</div>
                        <div class="calendar-day header">四</div>
                        <div class="calendar-day header">五</div>
                        <div class="calendar-day header">六</div>
                        
                        <div class="calendar-day">26</div>
                        <div class="calendar-day">27</div>
                        <div class="calendar-day">28</div>
                        <div class="calendar-day">29</div>
                        <div class="calendar-day">30</div>
                        <div class="calendar-day today">31</div>
                        <div class="calendar-day has-reminder">1</div>
                        
                        <div class="calendar-day has-reminder">2</div>
                        <div class="calendar-day">3</div>
                        <div class="calendar-day">4</div>
                        <div class="calendar-day has-reminder">5</div>
                        <div class="calendar-day">6</div>
                        <div class="calendar-day">7</div>
                        <div class="calendar-day">8</div>
                    </div>
                </div>
            </div>

            <!-- 已完成 -->
            <div id="completed-content" style="display: none;">
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">暂无已完成的提醒</h3>
                    <p class="text-sm">完成的农事提醒将显示在这里</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加提醒按钮 -->
    <button class="add-reminder-btn" onclick="addReminder()">
        <i class="fas fa-plus"></i>
    </button>

    <!-- 底部导航 -->
    <div class="bottom-nav">
        <div class="nav-item" onclick="navigateTo('home')">
            <i class="fas fa-home nav-icon"></i>
            <span class="nav-text">首页</span>
        </div>
        <div class="nav-item" onclick="navigateTo('pest-detection')">
            <i class="fas fa-bug nav-icon"></i>
            <span class="nav-text">识别</span>
        </div>
        <div class="nav-item" onclick="navigateTo('qr-scan')">
            <i class="fas fa-qrcode nav-icon"></i>
            <span class="nav-text">溯源</span>
        </div>
        <div class="nav-item" onclick="navigateTo('shop')">
            <i class="fas fa-shopping-cart nav-icon"></i>
            <span class="nav-text">农品汇</span>
        </div>
        <div class="nav-item" onclick="navigateTo('profile')">
            <i class="fas fa-user nav-icon"></i>
            <span class="nav-text">我的</span>
        </div>
    </div>

    <script>
        // 更新时间显示
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
            });
            document.getElementById('current-time').textContent = timeString;
        }
        
        updateTime();
        setInterval(updateTime, 1000);

        // 切换标签页
        function switchTab(tabName) {
            // 移除所有标签的active状态
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 隐藏所有内容
            document.querySelectorAll('[id$="-content"]').forEach(content => {
                content.style.display = 'none';
            });
            
            // 激活当前标签
            event.target.classList.add('active');
            
            // 显示对应内容
            document.getElementById(tabName + '-content').style.display = 'block';
        }

        // 添加提醒
        function addReminder() {
            alert('打开添加提醒页面');
        }

        // 页面导航
        function navigateTo(page) {
            window.open(`${page}.html`, '_blank');
        }

        // 底部导航点击效果
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelectorAll('.nav-item').forEach(nav => nav.classList.remove('active'));
                this.classList.add('active');
            });
        });
    </script>
        </div>
    </div>
</body>
</html>
