<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SFAP - 首页</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* iPhone 15 Pro 外观模拟 */
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .iphone-frame {
            width: 393px;
            height: 852px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            position: relative;
        }

        .iphone-screen {
            width: 100%;
            height: 100%;
            background: #fff;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }

        /* 动态岛模拟 */
        .dynamic-island {
            position: absolute;
            top: 12px;
            left: 50%;
            transform: translateX(-50%);
            width: 126px;
            height: 37px;
            background: #000;
            border-radius: 19px;
            z-index: 1000;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            .iphone-frame {
                width: 350px;
                height: 760px;
            }
        }
        :root {
            --primary-green: #22C55E;
            --dark-green: #16A34A;
            --light-green: #DCFCE7;
            --bg-green: #F0FDF4;
        }
        
        .ios-status-bar {
            height: 47px;
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        .home-container {
            min-height: calc(100vh - 47px - 80px);
            background: linear-gradient(135deg, var(--bg-green), white);
            overflow-y: auto;
        }
        
        .header-section {
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            padding: 20px;
            color: white;
        }
        
        .user-greeting {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .avatar {
            width: 50px;
            height: 50px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }
        
        .weather-card {
            background: rgba(255,255,255,0.15);
            border-radius: 15px;
            padding: 15px;
            backdrop-filter: blur(10px);
        }
        
        .main-functions {
            padding: 20px;
        }
        
        .function-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 25px;
        }
        
        .function-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }
        
        .function-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(34, 197, 94, 0.2);
            border-color: var(--light-green);
        }
        
        .function-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 12px;
            color: white;
            font-size: 24px;
        }
        
        .function-title {
            font-size: 16px;
            font-weight: 600;
            color: #1F2937;
            margin-bottom: 5px;
        }
        
        .function-desc {
            font-size: 12px;
            color: #6B7280;
        }
        
        .quick-actions {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .action-buttons {
            display: flex;
            gap: 10px;
        }
        
        .action-btn {
            flex: 1;
            padding: 12px 8px;
            background: var(--bg-green);
            border: 1px solid var(--light-green);
            border-radius: 10px;
            color: var(--dark-green);
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }
        
        .action-btn:hover {
            background: var(--light-green);
        }
        
        .news-section {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .news-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #F3F4F6;
        }
        
        .news-item:last-child {
            border-bottom: none;
        }
        
        .news-icon {
            width: 40px;
            height: 40px;
            background: var(--light-green);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--dark-green);
            margin-right: 12px;
        }
        
        .news-content {
            flex: 1;
        }
        
        .news-title {
            font-size: 14px;
            font-weight: 600;
            color: #1F2937;
            margin-bottom: 2px;
        }
        
        .news-time {
            font-size: 12px;
            color: #6B7280;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: white;
            border-top: 1px solid #E5E7EB;
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding: 10px 0;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 5px 10px;
        }
        
        .nav-item.active {
            color: var(--primary-green);
        }
        
        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .nav-text {
            font-size: 12px;
        }
    </style>
</head>
<body>
    <!-- iPhone 框架 -->
    <div class="iphone-frame">
        <!-- 动态岛 -->
        <div class="dynamic-island"></div>

        <!-- 屏幕内容 -->
        <div class="iphone-screen">
            <!-- iOS状态栏 -->
    <div class="ios-status-bar">
        <div class="flex items-center">
            <span id="current-time">9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-sm"></i>
            <i class="fas fa-wifi text-sm"></i>
            <div class="flex items-center">
                <div class="w-6 h-3 border border-white rounded-sm">
                    <div class="w-4 h-1.5 bg-white rounded-sm m-0.5"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 首页内容 -->
    <div class="home-container">
        <!-- 头部区域 -->
        <div class="header-section">
            <div class="user-greeting">
                <div>
                    <h1 class="text-xl font-bold">早上好，张农户</h1>
                    <p class="text-sm opacity-90">今天是个适合农事的好天气</p>
                </div>
                <div class="avatar">
                    <i class="fas fa-user"></i>
                </div>
            </div>
            
            <div class="weather-card">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-2xl font-bold">25°C</div>
                        <div class="text-sm opacity-90">晴转多云</div>
                    </div>
                    <div class="text-right">
                        <i class="fas fa-sun text-3xl"></i>
                        <div class="text-xs mt-1">适宜农事</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主要功能区域 -->
        <div class="main-functions">
            <h2 class="text-lg font-bold text-gray-800 mb-4">
                <i class="fas fa-seedling text-green-500 mr-2"></i>
                核心功能
            </h2>
            
            <div class="function-grid">
                <div class="function-card" onclick="navigateTo('pest-detection')">
                    <div class="function-icon">
                        <i class="fas fa-bug"></i>
                    </div>
                    <div class="function-title">病虫害识别</div>
                    <div class="function-desc">拍照识别病虫害</div>
                </div>
                
                <div class="function-card" onclick="navigateTo('qr-scan')">
                    <div class="function-icon">
                        <i class="fas fa-qrcode"></i>
                    </div>
                    <div class="function-title">扫码溯源</div>
                    <div class="function-desc">产品来源追溯</div>
                </div>
                
                <div class="function-card" onclick="navigateTo('ai-chat')">
                    <div class="function-icon">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="function-title">AI问答</div>
                    <div class="function-desc">智能农业咨询</div>
                </div>
                
                <div class="function-card" onclick="navigateTo('market-info')">
                    <div class="function-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="function-title">市场行情</div>
                    <div class="function-desc">价格趋势分析</div>
                </div>
            </div>

            <!-- 快捷操作 -->
            <div class="quick-actions">
                <h3 class="text-md font-semibold text-gray-800 mb-3">快捷操作</h3>
                <div class="action-buttons">
                    <div class="action-btn" onclick="navigateTo('farm-reminder')">
                        <i class="fas fa-bell text-sm mb-1"></i>
                        <div>农事提醒</div>
                    </div>
                    <div class="action-btn" onclick="navigateTo('shop')">
                        <i class="fas fa-shopping-cart text-sm mb-1"></i>
                        <div>农品商城</div>
                    </div>
                    <div class="action-btn">
                        <i class="fas fa-cloud-sun text-sm mb-1"></i>
                        <div>天气预报</div>
                    </div>
                </div>
            </div>

            <!-- 农业资讯 -->
            <div class="news-section">
                <h3 class="text-md font-semibold text-gray-800 mb-3">农业资讯</h3>
                
                <div class="news-item">
                    <div class="news-icon">
                        <i class="fas fa-leaf"></i>
                    </div>
                    <div class="news-content">
                        <div class="news-title">春季小麦病虫害防治要点</div>
                        <div class="news-time">2小时前</div>
                    </div>
                </div>
                
                <div class="news-item">
                    <div class="news-icon">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <div class="news-content">
                        <div class="news-title">本周玉米价格上涨3.2%</div>
                        <div class="news-time">5小时前</div>
                    </div>
                </div>
                
                <div class="news-item">
                    <div class="news-icon">
                        <i class="fas fa-award"></i>
                    </div>
                    <div class="news-content">
                        <div class="news-title">农业补贴政策最新解读</div>
                        <div class="news-time">1天前</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航 -->
    <div class="bottom-nav">
        <div class="nav-item active">
            <i class="fas fa-home nav-icon"></i>
            <span class="nav-text">首页</span>
        </div>
        <div class="nav-item" onclick="navigateTo('pest-detection')">
            <i class="fas fa-bug nav-icon"></i>
            <span class="nav-text">识别</span>
        </div>
        <div class="nav-item" onclick="navigateTo('qr-scan')">
            <i class="fas fa-qrcode nav-icon"></i>
            <span class="nav-text">溯源</span>
        </div>
        <div class="nav-item" onclick="navigateTo('shop')">
            <i class="fas fa-shopping-cart nav-icon"></i>
            <span class="nav-text">农品汇</span>
        </div>
        <div class="nav-item" onclick="navigateTo('profile')">
            <i class="fas fa-user nav-icon"></i>
            <span class="nav-text">我的</span>
        </div>
    </div>

    <script>
        // 更新时间显示
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
            });
            document.getElementById('current-time').textContent = timeString;
        }
        
        updateTime();
        setInterval(updateTime, 1000);

        // 页面导航
        function navigateTo(page) {
            window.open(`${page}.html`, '_blank');
        }

        // 底部导航点击效果
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelectorAll('.nav-item').forEach(nav => nav.classList.remove('active'));
                this.classList.add('active');
            });
        });
    </script>
        </div>
    </div>
</body>
</html>
