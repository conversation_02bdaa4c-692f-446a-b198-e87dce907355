<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SFAP - 病虫害识别</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* iPhone 15 Pro 外观模拟 */
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .iphone-frame {
            width: 393px;
            height: 852px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            position: relative;
        }

        .iphone-screen {
            width: 100%;
            height: 100%;
            background: #fff;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }

        /* 动态岛模拟 */
        .dynamic-island {
            position: absolute;
            top: 12px;
            left: 50%;
            transform: translateX(-50%);
            width: 126px;
            height: 37px;
            background: #000;
            border-radius: 19px;
            z-index: 1000;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            .iphone-frame {
                width: 350px;
                height: 760px;
            }
        }
        :root {
            --primary-green: #22C55E;
            --dark-green: #16A34A;
            --light-green: #DCFCE7;
            --bg-green: #F0FDF4;
        }
        
        .ios-status-bar {
            height: 47px;
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        .detection-container {
            min-height: calc(100vh - 47px - 80px);
            background: linear-gradient(135deg, var(--bg-green), white);
            overflow-y: auto;
        }
        
        .header {
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            padding: 20px;
            color: white;
            text-align: center;
        }
        
        .camera-section {
            padding: 20px;
            text-align: center;
        }

        .detection-type-selector {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            justify-content: center;
        }

        .type-btn {
            flex: 1;
            max-width: 140px;
            padding: 12px 20px;
            border: 2px solid var(--light-green);
            border-radius: 25px;
            background: white;
            color: var(--dark-green);
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .type-btn.active {
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            border-color: var(--primary-green);
            color: white;
        }

        .type-btn:hover:not(.active) {
            background: var(--light-green);
            transform: translateY(-1px);
        }
        
        .camera-frame {
            width: 100%;
            max-width: 300px;
            height: 300px;
            background: #f8f9fa;
            border: 3px dashed var(--light-green);
            border-radius: 20px;
            margin: 0 auto 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .camera-frame:hover {
            border-color: var(--primary-green);
            background: var(--bg-green);
        }
        
        .camera-frame.active {
            border-style: solid;
            border-color: var(--primary-green);
            background: url('https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=300&h=300&fit=crop') center/cover;
        }
        
        .camera-icon {
            font-size: 60px;
            color: var(--primary-green);
            margin-bottom: 15px;
        }
        
        .camera-text {
            color: #6B7280;
            font-size: 16px;
            margin-bottom: 10px;
        }
        
        .camera-hint {
            color: #9CA3AF;
            font-size: 14px;
        }
        
        .scan-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(34, 197, 94, 0.1);
            display: none;
            align-items: center;
            justify-content: center;
        }
        
        .scan-line {
            width: 80%;
            height: 3px;
            background: var(--primary-green);
            animation: scanAnimation 2s infinite;
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            margin-top: 20px;
            justify-content: center;
        }
        
        .action-btn {
            padding: 15px 25px;
            border-radius: 15px;
            border: none;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .primary-btn {
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            color: white;
        }
        
        .primary-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(34, 197, 94, 0.3);
        }
        
        .secondary-btn {
            background: white;
            color: var(--dark-green);
            border: 2px solid var(--light-green);
        }
        
        .secondary-btn:hover {
            background: var(--light-green);
        }
        
        .result-section {
            margin: 20px;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            display: none;
        }
        
        .result-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .result-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #EF4444, #DC2626);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            margin-right: 15px;
        }
        
        .result-title {
            font-size: 18px;
            font-weight: bold;
            color: #1F2937;
        }
        
        .result-confidence {
            font-size: 14px;
            color: #6B7280;
            margin-top: 2px;
        }
        
        .result-details {
            margin-top: 15px;
        }
        
        .detail-item {
            margin-bottom: 15px;
            padding: 12px;
            background: var(--bg-green);
            border-radius: 10px;
            border-left: 4px solid var(--primary-green);
        }
        
        .detail-title {
            font-weight: 600;
            color: var(--dark-green);
            margin-bottom: 5px;
        }
        
        .detail-content {
            color: #4B5563;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .history-section {
            margin: 20px;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .history-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #F3F4F6;
        }
        
        .history-item:last-child {
            border-bottom: none;
        }
        
        .history-image {
            width: 50px;
            height: 50px;
            background: var(--light-green);
            border-radius: 10px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--dark-green);
        }
        
        .history-content {
            flex: 1;
        }
        
        .history-title {
            font-size: 14px;
            font-weight: 600;
            color: #1F2937;
            margin-bottom: 2px;
        }
        
        .history-time {
            font-size: 12px;
            color: #6B7280;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: white;
            border-top: 1px solid #E5E7EB;
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding: 10px 0;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 5px 10px;
        }
        
        .nav-item.active {
            color: var(--primary-green);
        }
        
        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .nav-text {
            font-size: 12px;
        }
        
        @keyframes scanAnimation {
            0% { transform: translateY(-100%); }
            100% { transform: translateY(300px); }
        }
        
        .loading-spinner {
            display: none;
            width: 40px;
            height: 40px;
            border: 4px solid var(--light-green);
            border-top: 4px solid var(--primary-green);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- iPhone 框架 -->
    <div class="iphone-frame">
        <!-- 动态岛 -->
        <div class="dynamic-island"></div>

        <!-- 屏幕内容 -->
        <div class="iphone-screen">
            <!-- iOS状态栏 -->
    <div class="ios-status-bar">
        <div class="flex items-center">
            <span id="current-time">9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-sm"></i>
            <i class="fas fa-wifi text-sm"></i>
            <div class="flex items-center">
                <div class="w-6 h-3 border border-white rounded-sm">
                    <div class="w-4 h-1.5 bg-white rounded-sm m-0.5"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 病虫害识别页面内容 -->
    <div class="detection-container">
        <!-- 头部 -->
        <div class="header">
            <h1 class="text-xl font-bold mb-2">
                <i class="fas fa-bug mr-2"></i>
                病虫害识别
            </h1>
            <p class="text-sm opacity-90">拍照识别作物病虫害，获取专业防治建议</p>
        </div>

        <!-- 拍照区域 -->
        <div class="camera-section">
            <!-- 识别类型选择 -->
            <div class="detection-type-selector">
                <button class="type-btn active" id="pestBtn" onclick="selectDetectionType('pest')">
                    <i class="fas fa-bug"></i>
                    识别病虫害
                </button>
                <button class="type-btn" id="cropBtn" onclick="selectDetectionType('crop')">
                    <i class="fas fa-seedling"></i>
                    识别作物
                </button>
            </div>

            <div class="camera-frame" id="cameraFrame" onclick="takePhoto()">
                <div id="cameraContent">
                    <i class="fas fa-camera camera-icon"></i>
                    <div class="camera-text">点击拍照识别</div>
                    <div class="camera-hint" id="cameraHint">请对准病虫害部位拍摄</div>
                </div>
                <div class="scan-overlay" id="scanOverlay">
                    <div class="scan-line"></div>
                </div>
            </div>
            
            <div class="loading-spinner" id="loadingSpinner"></div>
            
            <div class="action-buttons">
                <button class="action-btn primary-btn" onclick="takePhoto()">
                    <i class="fas fa-camera"></i>
                    拍照识别
                </button>
                <button class="action-btn secondary-btn" onclick="selectFromGallery()">
                    <i class="fas fa-images"></i>
                    相册选择
                </button>
            </div>
        </div>

        <!-- 识别结果 -->
        <div class="result-section" id="resultSection">
            <div class="result-header">
                <div class="result-icon">
                    <i class="fas fa-bug"></i>
                </div>
                <div>
                    <div class="result-title">玉米螟虫害</div>
                    <div class="result-confidence">识别准确度：95%</div>
                </div>
            </div>
            
            <div class="result-details" id="resultDetails">
                <!-- 病虫害识别结果 -->
                <div class="pest-details">
                    <div class="detail-item">
                        <div class="detail-title">
                            <i class="fas fa-info-circle mr-2"></i>
                            病虫害描述
                        </div>
                        <div class="detail-content">
                            玉米螟是玉米的主要害虫之一，幼虫钻蛀茎秆，造成茎秆折断，严重影响玉米产量。主要危害期为玉米大喇叭口期至抽雄期。
                        </div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-title">
                            <i class="fas fa-shield-alt mr-2"></i>
                            防治建议
                        </div>
                        <div class="detail-content">
                            1. 生物防治：释放赤眼蜂等天敌昆虫<br>
                            2. 化学防治：使用氯虫苯甲酰胺等药剂<br>
                            3. 农业防治：及时清除田间杂草，减少虫源
                        </div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-title">
                            <i class="fas fa-clock mr-2"></i>
                            最佳防治时期
                        </div>
                        <div class="detail-content">
                            玉米大喇叭口期（6-8叶期），此时幼虫尚未钻入茎秆，防治效果最佳。建议在傍晚或清晨施药。
                        </div>
                    </div>
                </div>

                <!-- 作物识别结果 -->
                <div class="crop-details" style="display: none;">
                    <div class="detail-item">
                        <div class="detail-title">
                            <i class="fas fa-seedling mr-2"></i>
                            作物信息
                        </div>
                        <div class="detail-content">
                            玉米（Zea mays），禾本科玉蜀黍属一年生草本植物。当前生长阶段为6叶期，植株健康状况良好，叶片颜色正常。
                        </div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-title">
                            <i class="fas fa-chart-line mr-2"></i>
                            生长建议
                        </div>
                        <div class="detail-content">
                            1. 水分管理：保持土壤湿润，避免积水<br>
                            2. 施肥建议：适量追施氮肥促进茎叶生长<br>
                            3. 田间管理：及时中耕除草，注意病虫害防治
                        </div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-title">
                            <i class="fas fa-calendar mr-2"></i>
                            管理时期
                        </div>
                        <div class="detail-content">
                            当前为玉米苗期关键管理阶段，建议加强田间管理，为后期生长打好基础。预计15-20天后进入拔节期。
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 识别历史 -->
        <div class="history-section">
            <h3 class="text-md font-semibold text-gray-800 mb-3">
                <i class="fas fa-history mr-2"></i>
                识别历史
            </h3>
            
            <div class="history-item">
                <div class="history-image">
                    <i class="fas fa-bug"></i>
                </div>
                <div class="history-content">
                    <div class="history-title">玉米螟虫害</div>
                    <div class="history-time">今天 14:30</div>
                </div>
            </div>
            
            <div class="history-item">
                <div class="history-image">
                    <i class="fas fa-leaf"></i>
                </div>
                <div class="history-content">
                    <div class="history-title">小麦条纹花叶病</div>
                    <div class="history-time">昨天 09:15</div>
                </div>
            </div>
            
            <div class="history-item">
                <div class="history-image">
                    <i class="fas fa-seedling"></i>
                </div>
                <div class="history-content">
                    <div class="history-title">水稻稻瘟病</div>
                    <div class="history-time">3天前</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航 -->
    <div class="bottom-nav">
        <div class="nav-item" onclick="navigateTo('home')">
            <i class="fas fa-home nav-icon"></i>
            <span class="nav-text">首页</span>
        </div>
        <div class="nav-item active">
            <i class="fas fa-bug nav-icon"></i>
            <span class="nav-text">识别</span>
        </div>
        <div class="nav-item" onclick="navigateTo('qr-scan')">
            <i class="fas fa-qrcode nav-icon"></i>
            <span class="nav-text">溯源</span>
        </div>
        <div class="nav-item" onclick="navigateTo('shop')">
            <i class="fas fa-shopping-cart nav-icon"></i>
            <span class="nav-text">农品汇</span>
        </div>
        <div class="nav-item" onclick="navigateTo('profile')">
            <i class="fas fa-user nav-icon"></i>
            <span class="nav-text">我的</span>
        </div>
    </div>

    <script>
        // 当前识别类型
        let currentDetectionType = 'pest';

        // 识别类型配置
        const detectionConfig = {
            pest: {
                hint: '请对准病虫害部位拍摄',
                resultTitle: '玉米螟虫害',
                resultIcon: 'fas fa-bug',
                resultColor: '#EF4444'
            },
            crop: {
                hint: '请对准作物整体拍摄',
                resultTitle: '玉米（6叶期）',
                resultIcon: 'fas fa-seedling',
                resultColor: 'var(--primary-green)'
            }
        };

        // 更新时间显示
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
            });
            document.getElementById('current-time').textContent = timeString;
        }

        updateTime();
        setInterval(updateTime, 1000);

        // 选择识别类型
        function selectDetectionType(type) {
            currentDetectionType = type;

            // 更新按钮状态
            document.querySelectorAll('.type-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.getElementById(type === 'pest' ? 'pestBtn' : 'cropBtn').classList.add('active');

            // 更新提示文字
            const config = detectionConfig[type];
            document.getElementById('cameraHint').textContent = config.hint;

            // 如果已经有识别结果，更新结果显示
            const resultSection = document.getElementById('resultSection');
            if (resultSection.style.display === 'block') {
                updateResultDisplay(type);
            }
        }

        // 更新识别结果显示
        function updateResultDisplay(type) {
            const config = detectionConfig[type];
            const resultIcon = document.querySelector('.result-icon i');
            const resultTitle = document.querySelector('.result-title');

            if (resultIcon && resultTitle) {
                resultIcon.className = config.resultIcon;
                resultTitle.textContent = config.resultTitle;

                // 更新图标背景色
                const iconContainer = document.querySelector('.result-icon');
                if (type === 'crop') {
                    iconContainer.style.background = 'linear-gradient(135deg, var(--primary-green), var(--dark-green))';
                } else {
                    iconContainer.style.background = 'linear-gradient(135deg, #EF4444, #DC2626)';
                }
            }

            // 切换详细信息显示
            const pestDetails = document.querySelector('.pest-details');
            const cropDetails = document.querySelector('.crop-details');

            if (pestDetails && cropDetails) {
                if (type === 'pest') {
                    pestDetails.style.display = 'block';
                    cropDetails.style.display = 'none';
                } else {
                    pestDetails.style.display = 'none';
                    cropDetails.style.display = 'block';
                }
            }
        }

        // 拍照功能
        function takePhoto() {
            const cameraFrame = document.getElementById('cameraFrame');
            const cameraContent = document.getElementById('cameraContent');
            const scanOverlay = document.getElementById('scanOverlay');
            const loadingSpinner = document.getElementById('loadingSpinner');
            const resultSection = document.getElementById('resultSection');
            
            // 显示扫描效果
            cameraFrame.classList.add('active');
            cameraContent.style.display = 'none';
            scanOverlay.style.display = 'flex';
            
            // 2秒后显示加载动画
            setTimeout(() => {
                scanOverlay.style.display = 'none';
                loadingSpinner.style.display = 'block';
                
                // 3秒后显示识别结果
                setTimeout(() => {
                    loadingSpinner.style.display = 'none';
                    updateResultDisplay(currentDetectionType);
                    resultSection.style.display = 'block';
                    resultSection.scrollIntoView({ behavior: 'smooth' });
                }, 3000);
            }, 2000);
        }

        // 从相册选择
        function selectFromGallery() {
            alert('正在打开相册...');
            // 模拟选择图片后的识别流程
            setTimeout(() => {
                takePhoto();
            }, 1000);
        }

        // 页面导航
        function navigateTo(page) {
            window.open(`${page}.html`, '_blank');
        }

        // 底部导航点击效果
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelectorAll('.nav-item').forEach(nav => nav.classList.remove('active'));
                this.classList.add('active');
            });
        });
    </script>
        </div>
    </div>
</body>
</html>
