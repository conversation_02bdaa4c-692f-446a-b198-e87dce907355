<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SFAP - 注册</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* iPhone 15 Pro 外观模拟 */
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .iphone-frame {
            width: 393px;
            height: 852px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            position: relative;
        }

        .iphone-screen {
            width: 100%;
            height: 100%;
            background: #fff;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }

        /* 动态岛模拟 */
        .dynamic-island {
            position: absolute;
            top: 12px;
            left: 50%;
            transform: translateX(-50%);
            width: 126px;
            height: 37px;
            background: #000;
            border-radius: 19px;
            z-index: 1000;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            .iphone-frame {
                width: 350px;
                height: 760px;
            }
        }
        :root {
            --primary-green: #22C55E;
            --dark-green: #16A34A;
            --light-green: #DCFCE7;
            --bg-green: #F0FDF4;
        }
        
        .ios-status-bar {
            height: 47px;
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        .register-container {
            min-height: calc(100vh - 47px);
            background: linear-gradient(135deg, var(--bg-green), white);
            overflow-y: auto;
        }
        
        .register-form {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin: 20px;
            padding: 25px 20px;
        }
        
        .input-group {
            position: relative;
            margin-bottom: 16px;
        }
        
        .input-field {
            width: 100%;
            padding: 14px 18px 14px 45px;
            border: 2px solid #E5E7EB;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #F9FAFB;
        }
        
        .input-field:focus {
            outline: none;
            border-color: var(--primary-green);
            background: white;
            box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
        }
        
        .input-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #9CA3AF;
            font-size: 16px;
        }
        
        .verification-group {
            display: flex;
            gap: 10px;
        }
        
        .verification-input {
            flex: 1;
        }
        
        .verification-btn {
            padding: 14px 16px;
            background: var(--light-green);
            color: var(--dark-green);
            border: 2px solid var(--light-green);
            border-radius: 10px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
        }
        
        .verification-btn:hover {
            background: var(--primary-green);
            color: white;
            border-color: var(--primary-green);
        }
        
        .register-btn {
            width: 100%;
            padding: 16px;
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
        }
        
        .register-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(34, 197, 94, 0.3);
        }
        
        .user-type-selector {
            display: flex;
            gap: 8px;
            margin-bottom: 20px;
        }
        
        .user-type-btn {
            flex: 1;
            padding: 10px 6px;
            border: 2px solid #E5E7EB;
            border-radius: 8px;
            background: white;
            color: #6B7280;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 13px;
        }
        
        .user-type-btn.active {
            border-color: var(--primary-green);
            background: var(--light-green);
            color: var(--dark-green);
        }
        
        .agreement-section {
            margin: 20px 0;
            padding: 15px;
            background: var(--bg-green);
            border-radius: 10px;
            border: 1px solid var(--light-green);
        }
        
        .agreement-checkbox {
            display: flex;
            align-items: flex-start;
            gap: 10px;
        }
        
        .agreement-text {
            font-size: 13px;
            color: #6B7280;
            line-height: 1.5;
        }
        
        .agreement-text a {
            color: var(--primary-green);
            text-decoration: none;
        }
        
        .login-link {
            text-align: center;
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #E5E7EB;
        }
        
        .login-link a {
            color: var(--primary-green);
            text-decoration: none;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <!-- iPhone 框架 -->
    <div class="iphone-frame">
        <!-- 动态岛 -->
        <div class="dynamic-island"></div>

        <!-- 屏幕内容 -->
        <div class="iphone-screen">
            <!-- iOS状态栏 -->
    <div class="ios-status-bar">
        <div class="flex items-center">
            <span id="current-time">9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-sm"></i>
            <i class="fas fa-wifi text-sm"></i>
            <div class="flex items-center">
                <div class="w-6 h-3 border border-white rounded-sm">
                    <div class="w-4 h-1.5 bg-white rounded-sm m-0.5"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 注册页面内容 -->
    <div class="register-container">
        <div class="w-full max-w-sm mx-auto pt-4">
            <!-- Logo区域 -->
            <div class="text-center mb-6">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-full mb-3">
                    <i class="fas fa-seedling text-white text-2xl"></i>
                </div>
                <h1 class="text-xl font-bold text-gray-800">创建账户</h1>
                <p class="text-gray-600 text-sm mt-1">加入SFAP智慧农业大家庭</p>
            </div>

            <!-- 注册表单 -->
            <div class="register-form">
                <!-- 用户类型选择 -->
                <div class="user-type-selector">
                    <div class="user-type-btn active" data-type="farmer">
                        <i class="fas fa-tractor text-xs mb-1"></i>
                        <div>农户</div>
                    </div>
                    <div class="user-type-btn" data-type="consumer">
                        <i class="fas fa-shopping-basket text-xs mb-1"></i>
                        <div>消费者</div>
                    </div>
                    <div class="user-type-btn" data-type="business">
                        <i class="fas fa-building text-xs mb-1"></i>
                        <div>企业</div>
                    </div>
                </div>

                <!-- 姓名输入 -->
                <div class="input-group">
                    <i class="fas fa-user input-icon"></i>
                    <input type="text" class="input-field" placeholder="请输入真实姓名">
                </div>

                <!-- 手机号输入 -->
                <div class="input-group">
                    <i class="fas fa-mobile-alt input-icon"></i>
                    <input type="tel" class="input-field" placeholder="请输入手机号" maxlength="11">
                </div>

                <!-- 验证码输入 -->
                <div class="input-group">
                    <div class="verification-group">
                        <div class="verification-input">
                            <i class="fas fa-shield-alt input-icon"></i>
                            <input type="text" class="input-field" placeholder="请输入验证码" maxlength="6">
                        </div>
                        <button class="verification-btn" onclick="sendVerificationCode()">
                            <span id="verification-text">获取验证码</span>
                        </button>
                    </div>
                </div>

                <!-- 密码输入 -->
                <div class="input-group">
                    <i class="fas fa-lock input-icon"></i>
                    <input type="password" class="input-field" placeholder="请设置密码（6-20位）">
                    <i class="fas fa-eye absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 cursor-pointer" onclick="togglePassword(this)"></i>
                </div>

                <!-- 确认密码 -->
                <div class="input-group">
                    <i class="fas fa-lock input-icon"></i>
                    <input type="password" class="input-field" placeholder="请确认密码">
                    <i class="fas fa-eye absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 cursor-pointer" onclick="togglePassword(this)"></i>
                </div>

                <!-- 地区选择（农户专用） -->
                <div class="input-group farmer-only">
                    <i class="fas fa-map-marker-alt input-icon"></i>
                    <select class="input-field">
                        <option value="">请选择所在地区</option>
                        <option value="beijing">北京市</option>
                        <option value="shanghai">上海市</option>
                        <option value="guangdong">广东省</option>
                        <option value="shandong">山东省</option>
                        <option value="henan">河南省</option>
                        <option value="jiangsu">江苏省</option>
                        <option value="other">其他地区</option>
                    </select>
                </div>

                <!-- 用户协议 -->
                <div class="agreement-section">
                    <div class="agreement-checkbox">
                        <input type="checkbox" id="agreement" class="w-4 h-4 text-green-500 border-gray-300 rounded focus:ring-green-500 mt-0.5">
                        <label for="agreement" class="agreement-text">
                            我已阅读并同意
                            <a href="#">《用户服务协议》</a>
                            和
                            <a href="#">《隐私政策》</a>
                        </label>
                    </div>
                </div>

                <!-- 注册按钮 -->
                <button class="register-btn" onclick="handleRegister()">
                    <i class="fas fa-user-plus mr-2"></i>
                    立即注册
                </button>

                <!-- 登录链接 -->
                <div class="login-link">
                    <span class="text-gray-600 text-sm">已有账户？</span>
                    <a href="#" onclick="switchToLogin()">立即登录</a>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 更新时间显示
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
            });
            document.getElementById('current-time').textContent = timeString;
        }
        
        updateTime();
        setInterval(updateTime, 1000);

        // 用户类型选择
        document.querySelectorAll('.user-type-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.user-type-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                
                // 显示/隐藏农户专用字段
                const farmerFields = document.querySelectorAll('.farmer-only');
                if (this.dataset.type === 'farmer') {
                    farmerFields.forEach(field => field.style.display = 'block');
                } else {
                    farmerFields.forEach(field => field.style.display = 'none');
                }
            });
        });

        // 密码显示/隐藏
        function togglePassword(icon) {
            const input = icon.previousElementSibling;
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }

        // 发送验证码
        function sendVerificationCode() {
            const phone = document.querySelector('input[type="tel"]').value;
            if (!phone || phone.length !== 11) {
                alert('请输入正确的手机号');
                return;
            }
            
            const btn = document.querySelector('.verification-btn');
            const text = document.getElementById('verification-text');
            let countdown = 60;
            
            btn.disabled = true;
            btn.style.opacity = '0.6';
            
            const timer = setInterval(() => {
                text.textContent = `${countdown}秒后重发`;
                countdown--;
                
                if (countdown < 0) {
                    clearInterval(timer);
                    text.textContent = '获取验证码';
                    btn.disabled = false;
                    btn.style.opacity = '1';
                }
            }, 1000);
            
            alert('验证码已发送到您的手机');
        }

        // 注册处理
        function handleRegister() {
            const name = document.querySelector('input[placeholder="请输入真实姓名"]').value;
            const phone = document.querySelector('input[type="tel"]').value;
            const code = document.querySelector('input[placeholder="请输入验证码"]').value;
            const password = document.querySelector('input[placeholder="请设置密码（6-20位）"]').value;
            const confirmPassword = document.querySelector('input[placeholder="请确认密码"]').value;
            const agreement = document.getElementById('agreement').checked;
            
            if (!name || !phone || !code || !password || !confirmPassword) {
                alert('请填写完整的注册信息');
                return;
            }
            
            if (password !== confirmPassword) {
                alert('两次输入的密码不一致');
                return;
            }
            
            if (!agreement) {
                alert('请阅读并同意用户协议');
                return;
            }
            
            // 模拟注册成功
            alert('注册成功！即将跳转到登录页面');
            switchToLogin();
        }

        // 切换到登录页面
        function switchToLogin() {
            window.open('login.html', '_blank');
        }
    </script>
        </div>
    </div>
</body>
</html>
