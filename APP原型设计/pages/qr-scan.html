<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SFAP - 扫码溯源</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* iPhone 15 Pro 外观模拟 */
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .iphone-frame {
            width: 393px;
            height: 852px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            position: relative;
        }

        .iphone-screen {
            width: 100%;
            height: 100%;
            background: #fff;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }

        /* 动态岛模拟 */
        .dynamic-island {
            position: absolute;
            top: 12px;
            left: 50%;
            transform: translateX(-50%);
            width: 126px;
            height: 37px;
            background: #000;
            border-radius: 19px;
            z-index: 1000;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            .iphone-frame {
                width: 350px;
                height: 760px;
            }
        }
        :root {
            --primary-green: #22C55E;
            --dark-green: #16A34A;
            --light-green: #DCFCE7;
            --bg-green: #F0FDF4;
        }
        
        .ios-status-bar {
            height: 47px;
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        .scan-container {
            min-height: calc(100vh - 47px - 80px);
            background: linear-gradient(135deg, var(--bg-green), white);
            overflow-y: auto;
        }
        
        .header {
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            padding: 20px;
            color: white;
            text-align: center;
        }
        
        .scanner-section {
            padding: 20px;
            text-align: center;
        }
        
        .scanner-frame {
            width: 280px;
            height: 280px;
            margin: 0 auto 20px;
            position: relative;
            background: #000;
            border-radius: 20px;
            overflow: hidden;
        }
        
        .scanner-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
        }
        
        .scan-area {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 200px;
            height: 200px;
            border: 2px solid var(--primary-green);
            border-radius: 10px;
        }
        
        .corner {
            position: absolute;
            width: 20px;
            height: 20px;
            border: 3px solid var(--primary-green);
        }
        
        .corner.top-left {
            top: -3px;
            left: -3px;
            border-right: none;
            border-bottom: none;
        }
        
        .corner.top-right {
            top: -3px;
            right: -3px;
            border-left: none;
            border-bottom: none;
        }
        
        .corner.bottom-left {
            bottom: -3px;
            left: -3px;
            border-right: none;
            border-top: none;
        }
        
        .corner.bottom-right {
            bottom: -3px;
            right: -3px;
            border-left: none;
            border-top: none;
        }
        
        .scan-line {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, var(--primary-green), transparent);
            animation: scanLine 2s infinite;
        }
        
        .scanner-hint {
            color: white;
            font-size: 14px;
            margin-top: 20px;
            position: absolute;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
        }
        
        .input-section {
            margin: 20px;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .input-group {
            position: relative;
            margin-bottom: 15px;
        }
        
        .input-field {
            width: 100%;
            padding: 15px 20px 15px 50px;
            border: 2px solid #E5E7EB;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #F9FAFB;
        }
        
        .input-field:focus {
            outline: none;
            border-color: var(--primary-green);
            background: white;
            box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
        }
        
        .input-icon {
            position: absolute;
            left: 18px;
            top: 50%;
            transform: translateY(-50%);
            color: #9CA3AF;
            font-size: 18px;
        }
        
        .search-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .search-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(34, 197, 94, 0.3);
        }
        
        .result-section {
            margin: 20px;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            display: none;
        }
        
        .product-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .product-image {
            width: 80px;
            height: 80px;
            background: url('https://images.unsplash.com/photo-1574323347407-f5e1ad6d020b?w=80&h=80&fit=crop') center/cover;
            border-radius: 12px;
            margin-right: 15px;
        }
        
        .product-info {
            flex: 1;
        }
        
        .product-name {
            font-size: 18px;
            font-weight: bold;
            color: #1F2937;
            margin-bottom: 5px;
        }
        
        .product-code {
            font-size: 14px;
            color: #6B7280;
            margin-bottom: 5px;
        }
        
        .product-status {
            display: inline-block;
            padding: 4px 12px;
            background: var(--light-green);
            color: var(--dark-green);
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        
        .trace-timeline {
            margin-top: 20px;
        }
        
        .timeline-item {
            display: flex;
            margin-bottom: 20px;
            position: relative;
        }
        
        .timeline-item:not(:last-child)::after {
            content: '';
            position: absolute;
            left: 20px;
            top: 40px;
            bottom: -20px;
            width: 2px;
            background: var(--light-green);
        }
        
        .timeline-icon {
            width: 40px;
            height: 40px;
            background: var(--primary-green);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            margin-right: 15px;
            z-index: 1;
            position: relative;
        }
        
        .timeline-content {
            flex: 1;
            padding-top: 5px;
        }
        
        .timeline-title {
            font-size: 16px;
            font-weight: 600;
            color: #1F2937;
            margin-bottom: 5px;
        }
        
        .timeline-desc {
            font-size: 14px;
            color: #6B7280;
            margin-bottom: 5px;
        }
        
        .timeline-time {
            font-size: 12px;
            color: #9CA3AF;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: white;
            border-top: 1px solid #E5E7EB;
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding: 10px 0;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 5px 10px;
        }
        
        .nav-item.active {
            color: var(--primary-green);
        }
        
        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .nav-text {
            font-size: 12px;
        }
        
        @keyframes scanLine {
            0% { transform: translateY(0); }
            100% { transform: translateY(200px); }
        }
        
        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        
        .action-btn {
            flex: 1;
            padding: 12px;
            border-radius: 10px;
            border: none;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .primary-btn {
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            color: white;
        }
        
        .secondary-btn {
            background: var(--bg-green);
            color: var(--dark-green);
            border: 1px solid var(--light-green);
        }
    </style>
</head>
<body>
    <!-- iPhone 框架 -->
    <div class="iphone-frame">
        <!-- 动态岛 -->
        <div class="dynamic-island"></div>

        <!-- 屏幕内容 -->
        <div class="iphone-screen">
            <!-- iOS状态栏 -->
    <div class="ios-status-bar">
        <div class="flex items-center">
            <span id="current-time">9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-sm"></i>
            <i class="fas fa-wifi text-sm"></i>
            <div class="flex items-center">
                <div class="w-6 h-3 border border-white rounded-sm">
                    <div class="w-4 h-1.5 bg-white rounded-sm m-0.5"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 扫码溯源页面内容 -->
    <div class="scan-container">
        <!-- 头部 -->
        <div class="header">
            <h1 class="text-xl font-bold mb-2">
                <i class="fas fa-qrcode mr-2"></i>
                扫码溯源
            </h1>
            <p class="text-sm opacity-90">扫描二维码或输入溯源码查询产品信息</p>
        </div>

        <!-- 扫描区域 -->
        <div class="scanner-section">
            <div class="scanner-frame" onclick="startScan()">
                <div class="scanner-overlay"></div>
                <div class="scan-area">
                    <div class="corner top-left"></div>
                    <div class="corner top-right"></div>
                    <div class="corner bottom-left"></div>
                    <div class="corner bottom-right"></div>
                    <div class="scan-line"></div>
                </div>
                <div class="scanner-hint">
                    将二维码放入框内即可自动扫描
                </div>
            </div>
            
            <div class="action-buttons">
                <button class="action-btn primary-btn" onclick="startScan()">
                    <i class="fas fa-camera mr-2"></i>
                    开始扫描
                </button>
                <button class="action-btn secondary-btn" onclick="toggleFlash()">
                    <i class="fas fa-flashlight mr-2"></i>
                    闪光灯
                </button>
            </div>
        </div>

        <!-- 手动输入区域 -->
        <div class="input-section">
            <h3 class="text-md font-semibold text-gray-800 mb-3">
                <i class="fas fa-keyboard mr-2"></i>
                手动输入溯源码
            </h3>
            
            <div class="input-group">
                <i class="fas fa-barcode input-icon"></i>
                <input type="text" class="input-field" placeholder="请输入溯源码" id="traceCodeInput">
            </div>
            
            <button class="search-btn" onclick="searchTraceCode()">
                <i class="fas fa-search mr-2"></i>
                查询溯源信息
            </button>
        </div>

        <!-- 溯源结果 -->
        <div class="result-section" id="resultSection">
            <div class="product-header">
                <div class="product-image"></div>
                <div class="product-info">
                    <div class="product-name">有机胡萝卜</div>
                    <div class="product-code">溯源码：SF2025010001</div>
                    <div class="product-status">
                        <i class="fas fa-check-circle mr-1"></i>
                        已认证
                    </div>
                </div>
            </div>
            
            <div class="trace-timeline">
                <h4 class="text-md font-semibold text-gray-800 mb-3">
                    <i class="fas fa-route mr-2"></i>
                    溯源轨迹
                </h4>
                
                <div class="timeline-item">
                    <div class="timeline-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="timeline-content">
                        <div class="timeline-title">消费者购买</div>
                        <div class="timeline-desc">北京市朝阳区某超市</div>
                        <div class="timeline-time">2025-01-31 14:30</div>
                    </div>
                </div>
                
                <div class="timeline-item">
                    <div class="timeline-icon">
                        <i class="fas fa-truck"></i>
                    </div>
                    <div class="timeline-content">
                        <div class="timeline-title">物流配送</div>
                        <div class="timeline-desc">冷链运输，温度2-8°C</div>
                        <div class="timeline-time">2025-01-30 08:00</div>
                    </div>
                </div>
                
                <div class="timeline-item">
                    <div class="timeline-icon">
                        <i class="fas fa-warehouse"></i>
                    </div>
                    <div class="timeline-content">
                        <div class="timeline-title">仓储包装</div>
                        <div class="timeline-desc">山东寿光蔬菜批发市场</div>
                        <div class="timeline-time">2025-01-29 16:00</div>
                    </div>
                </div>
                
                <div class="timeline-item">
                    <div class="timeline-icon">
                        <i class="fas fa-cut"></i>
                    </div>
                    <div class="timeline-content">
                        <div class="timeline-title">采收加工</div>
                        <div class="timeline-desc">人工采收，分拣包装</div>
                        <div class="timeline-time">2025-01-29 06:00</div>
                    </div>
                </div>
                
                <div class="timeline-item">
                    <div class="timeline-icon">
                        <i class="fas fa-seedling"></i>
                    </div>
                    <div class="timeline-content">
                        <div class="timeline-title">种植生产</div>
                        <div class="timeline-desc">山东省寿光市张农户有机农场</div>
                        <div class="timeline-time">2024-10-15 播种</div>
                    </div>
                </div>
            </div>
            
            <div class="action-buttons">
                <button class="action-btn primary-btn">
                    <i class="fas fa-share mr-2"></i>
                    分享溯源
                </button>
                <button class="action-btn secondary-btn">
                    <i class="fas fa-download mr-2"></i>
                    下载报告
                </button>
            </div>
        </div>
    </div>

    <!-- 底部导航 -->
    <div class="bottom-nav">
        <div class="nav-item" onclick="navigateTo('home')">
            <i class="fas fa-home nav-icon"></i>
            <span class="nav-text">首页</span>
        </div>
        <div class="nav-item" onclick="navigateTo('pest-detection')">
            <i class="fas fa-bug nav-icon"></i>
            <span class="nav-text">识别</span>
        </div>
        <div class="nav-item active">
            <i class="fas fa-qrcode nav-icon"></i>
            <span class="nav-text">溯源</span>
        </div>
        <div class="nav-item" onclick="navigateTo('shop')">
            <i class="fas fa-shopping-cart nav-icon"></i>
            <span class="nav-text">农品汇</span>
        </div>
        <div class="nav-item" onclick="navigateTo('profile')">
            <i class="fas fa-user nav-icon"></i>
            <span class="nav-text">我的</span>
        </div>
    </div>

    <script>
        // 更新时间显示
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
            });
            document.getElementById('current-time').textContent = timeString;
        }
        
        updateTime();
        setInterval(updateTime, 1000);

        // 开始扫描
        function startScan() {
            // 模拟扫描成功
            setTimeout(() => {
                document.getElementById('resultSection').style.display = 'block';
                document.getElementById('resultSection').scrollIntoView({ behavior: 'smooth' });
                alert('扫描成功！已找到溯源信息');
            }, 2000);
        }

        // 切换闪光灯
        function toggleFlash() {
            alert('闪光灯已切换');
        }

        // 搜索溯源码
        function searchTraceCode() {
            const code = document.getElementById('traceCodeInput').value;
            if (!code) {
                alert('请输入溯源码');
                return;
            }
            
            // 模拟查询成功
            document.getElementById('resultSection').style.display = 'block';
            document.getElementById('resultSection').scrollIntoView({ behavior: 'smooth' });
        }

        // 页面导航
        function navigateTo(page) {
            window.open(`${page}.html`, '_blank');
        }

        // 底部导航点击效果
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelectorAll('.nav-item').forEach(nav => nav.classList.remove('active'));
                this.classList.add('active');
            });
        });
    </script>
        </div>
    </div>
</body>
</html>
