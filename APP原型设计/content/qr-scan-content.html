<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SFAP - 扫码溯源内容</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-green: #22C55E;
            --dark-green: #16A34A;
            --light-green: #DCFCE7;
            --bg-green: #F0FDF4;
        }
        
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: white;
            width: 393px;
            height: 852px;
            overflow: hidden;
        }
        
        .ios-status-bar {
            height: 47px;
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        .scan-container {
            height: calc(852px - 47px - 80px);
            background: linear-gradient(135deg, var(--bg-green), white);
            overflow-y: auto;
        }
        
        .header {
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            padding: 20px;
            color: white;
            text-align: center;
        }
        
        .scanner-section {
            padding: 20px;
            text-align: center;
        }
        
        .scanner-frame {
            width: 280px;
            height: 280px;
            margin: 0 auto 20px;
            position: relative;
            background: #000;
            border-radius: 20px;
            overflow: hidden;
        }
        
        .scanner-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
        }
        
        .scan-area {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 200px;
            height: 200px;
            border: 2px solid var(--primary-green);
            border-radius: 10px;
        }
        
        .corner {
            position: absolute;
            width: 20px;
            height: 20px;
            border: 3px solid var(--primary-green);
        }
        
        .corner.top-left {
            top: -3px;
            left: -3px;
            border-right: none;
            border-bottom: none;
        }
        
        .corner.top-right {
            top: -3px;
            right: -3px;
            border-left: none;
            border-bottom: none;
        }
        
        .corner.bottom-left {
            bottom: -3px;
            left: -3px;
            border-right: none;
            border-top: none;
        }
        
        .corner.bottom-right {
            bottom: -3px;
            right: -3px;
            border-left: none;
            border-top: none;
        }
        
        .scan-line {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, var(--primary-green), transparent);
            animation: scanLine 2s infinite;
        }
        
        .scanner-hint {
            color: white;
            font-size: 14px;
            margin-top: 20px;
            position: absolute;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
        }
        
        .result-section {
            margin: 20px;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .product-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .product-image {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--light-green), var(--bg-green));
            border-radius: 12px;
            margin-right: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--dark-green);
            font-size: 32px;
        }
        
        .product-info {
            flex: 1;
        }
        
        .product-name {
            font-size: 18px;
            font-weight: bold;
            color: #1F2937;
            margin-bottom: 5px;
        }
        
        .product-code {
            font-size: 14px;
            color: #6B7280;
            margin-bottom: 5px;
        }
        
        .product-status {
            display: inline-block;
            padding: 4px 12px;
            background: var(--light-green);
            color: var(--dark-green);
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        
        .trace-timeline {
            margin-top: 20px;
        }
        
        .timeline-item {
            display: flex;
            margin-bottom: 20px;
            position: relative;
        }
        
        .timeline-item:not(:last-child)::after {
            content: '';
            position: absolute;
            left: 20px;
            top: 40px;
            bottom: -20px;
            width: 2px;
            background: var(--light-green);
        }
        
        .timeline-icon {
            width: 40px;
            height: 40px;
            background: var(--primary-green);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            margin-right: 15px;
            z-index: 1;
            position: relative;
        }
        
        .timeline-content {
            flex: 1;
            padding-top: 5px;
        }
        
        .timeline-title {
            font-size: 16px;
            font-weight: 600;
            color: #1F2937;
            margin-bottom: 5px;
        }
        
        .timeline-desc {
            font-size: 14px;
            color: #6B7280;
            margin-bottom: 5px;
        }
        
        .timeline-time {
            font-size: 12px;
            color: #9CA3AF;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: white;
            border-top: 1px solid #E5E7EB;
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding: 10px 0;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 5px 10px;
        }
        
        .nav-item.active {
            color: var(--primary-green);
        }
        
        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .nav-text {
            font-size: 12px;
        }
        
        @keyframes scanLine {
            0% { transform: translateY(0); }
            100% { transform: translateY(200px); }
        }
    </style>
</head>
<body>
    <!-- iOS状态栏 -->
    <div class="ios-status-bar">
        <div class="flex items-center">
            <span>9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-sm"></i>
            <i class="fas fa-wifi text-sm"></i>
            <div class="flex items-center">
                <div class="w-6 h-3 border border-white rounded-sm">
                    <div class="w-4 h-1.5 bg-white rounded-sm m-0.5"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 扫码溯源页面内容 -->
    <div class="scan-container">
        <!-- 头部 -->
        <div class="header">
            <h1 class="text-xl font-bold mb-2">
                <i class="fas fa-qrcode mr-2"></i>
                扫码溯源
            </h1>
            <p class="text-sm opacity-90">扫描二维码查询产品溯源信息</p>
        </div>

        <!-- 扫描区域 -->
        <div class="scanner-section">
            <div class="scanner-frame">
                <div class="scanner-overlay"></div>
                <div class="scan-area">
                    <div class="corner top-left"></div>
                    <div class="corner top-right"></div>
                    <div class="corner bottom-left"></div>
                    <div class="corner bottom-right"></div>
                    <div class="scan-line"></div>
                </div>
                <div class="scanner-hint">
                    将二维码放入框内即可自动扫描
                </div>
            </div>
        </div>

        <!-- 溯源结果 -->
        <div class="result-section">
            <div class="product-header">
                <div class="product-image">
                    <i class="fas fa-carrot"></i>
                </div>
                <div class="product-info">
                    <div class="product-name">有机胡萝卜</div>
                    <div class="product-code">溯源码：SF2025010001</div>
                    <div class="product-status">
                        <i class="fas fa-check-circle mr-1"></i>
                        已认证
                    </div>
                </div>
            </div>
            
            <div class="trace-timeline">
                <h4 class="text-md font-semibold text-gray-800 mb-3">
                    <i class="fas fa-route mr-2"></i>
                    溯源轨迹
                </h4>
                
                <div class="timeline-item">
                    <div class="timeline-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="timeline-content">
                        <div class="timeline-title">消费者购买</div>
                        <div class="timeline-desc">北京市朝阳区某超市</div>
                        <div class="timeline-time">2025-01-31 14:30</div>
                    </div>
                </div>
                
                <div class="timeline-item">
                    <div class="timeline-icon">
                        <i class="fas fa-truck"></i>
                    </div>
                    <div class="timeline-content">
                        <div class="timeline-title">物流配送</div>
                        <div class="timeline-desc">冷链运输，温度2-8°C</div>
                        <div class="timeline-time">2025-01-30 08:00</div>
                    </div>
                </div>
                
                <div class="timeline-item">
                    <div class="timeline-icon">
                        <i class="fas fa-seedling"></i>
                    </div>
                    <div class="timeline-content">
                        <div class="timeline-title">种植生产</div>
                        <div class="timeline-desc">山东省寿光市有机农场</div>
                        <div class="timeline-time">2024-10-15 播种</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航 -->
    <div class="bottom-nav">
        <div class="nav-item">
            <i class="fas fa-home nav-icon"></i>
            <span class="nav-text">首页</span>
        </div>
        <div class="nav-item">
            <i class="fas fa-bug nav-icon"></i>
            <span class="nav-text">识别</span>
        </div>
        <div class="nav-item active">
            <i class="fas fa-qrcode nav-icon"></i>
            <span class="nav-text">溯源</span>
        </div>
        <div class="nav-item">
            <i class="fas fa-shopping-cart nav-icon"></i>
            <span class="nav-text">农品汇</span>
        </div>
        <div class="nav-item">
            <i class="fas fa-user nav-icon"></i>
            <span class="nav-text">我的</span>
        </div>
    </div>
</body>
</html>
