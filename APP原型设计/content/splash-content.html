<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SFAP - 启动页面内容</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-green: #22C55E;
            --dark-green: #16A34A;
            --light-green: #DCFCE7;
            --bg-green: #F0FDF4;
        }
        
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            width: 393px;
            height: 852px;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            position: relative;
        }
        
        .logo-container {
            text-align: center;
            margin-bottom: 40px;
            animation: fadeInUp 1s ease-out;
        }
        
        .logo-icon {
            width: 120px;
            height: 120px;
            background: rgba(255,255,255,0.2);
            border-radius: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 60px;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255,255,255,0.3);
        }
        
        .app-name {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 10px;
            letter-spacing: 2px;
        }
        
        .app-slogan {
            font-size: 16px;
            opacity: 0.9;
            font-weight: 300;
        }
        
        .features-list {
            margin-top: 60px;
            text-align: center;
            animation: fadeInUp 1s ease-out 0.5s both;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 15px 0;
            font-size: 16px;
            opacity: 0.9;
        }
        
        .feature-icon {
            width: 24px;
            height: 24px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .loading-section {
            position: absolute;
            bottom: 100px;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
            animation: fadeInUp 1s ease-out 1s both;
        }
        
        .loading-text {
            font-size: 14px;
            opacity: 0.8;
            margin-bottom: 20px;
        }
        
        .loading-bar {
            width: 200px;
            height: 4px;
            background: rgba(255,255,255,0.3);
            border-radius: 2px;
            overflow: hidden;
            margin: 0 auto;
        }
        
        .loading-progress {
            height: 100%;
            background: white;
            border-radius: 2px;
            animation: loadingProgress 3s ease-out;
        }
        
        .floating-elements {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            overflow: hidden;
        }
        
        .floating-icon {
            position: absolute;
            color: rgba(255,255,255,0.1);
            animation: float 6s ease-in-out infinite;
        }
        
        .floating-icon:nth-child(1) {
            top: 10%;
            left: 10%;
            font-size: 24px;
            animation-delay: 0s;
        }
        
        .floating-icon:nth-child(2) {
            top: 20%;
            right: 15%;
            font-size: 32px;
            animation-delay: 1s;
        }
        
        .floating-icon:nth-child(3) {
            bottom: 30%;
            left: 20%;
            font-size: 28px;
            animation-delay: 2s;
        }
        
        .floating-icon:nth-child(4) {
            bottom: 20%;
            right: 10%;
            font-size: 20px;
            animation-delay: 3s;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes loadingProgress {
            from {
                width: 0%;
            }
            to {
                width: 100%;
            }
        }
        
        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
            }
            50% {
                transform: translateY(-20px) rotate(180deg);
            }
        }
        
        .version-info {
            position: absolute;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 12px;
            opacity: 0.6;
            animation: fadeInUp 1s ease-out 1.5s both;
        }
    </style>
</head>
<body>
    <!-- 浮动装饰元素 -->
    <div class="floating-elements">
        <i class="fas fa-seedling floating-icon"></i>
        <i class="fas fa-leaf floating-icon"></i>
        <i class="fas fa-tractor floating-icon"></i>
        <i class="fas fa-sun floating-icon"></i>
    </div>

    <!-- 主要内容 -->
    <div class="logo-container">
        <div class="logo-icon">
            <i class="fas fa-seedling"></i>
        </div>
        <h1 class="app-name">SFAP</h1>
        <p class="app-slogan">智慧农业助手</p>
    </div>

    <!-- 功能特色 -->
    <div class="features-list">
        <div class="feature-item">
            <div class="feature-icon">
                <i class="fas fa-bug"></i>
            </div>
            <span>AI病虫害识别</span>
        </div>
        <div class="feature-item">
            <div class="feature-icon">
                <i class="fas fa-qrcode"></i>
            </div>
            <span>产品溯源追踪</span>
        </div>
        <div class="feature-item">
            <div class="feature-icon">
                <i class="fas fa-robot"></i>
            </div>
            <span>智能农业问答</span>
        </div>
        <div class="feature-item">
            <div class="feature-icon">
                <i class="fas fa-chart-line"></i>
            </div>
            <span>市场行情分析</span>
        </div>
    </div>

    <!-- 加载区域 -->
    <div class="loading-section">
        <div class="loading-text">正在初始化...</div>
        <div class="loading-bar">
            <div class="loading-progress"></div>
        </div>
    </div>

    <!-- 版本信息 -->
    <div class="version-info">
        版本 1.0.0 | © 2025 SFAP
    </div>
</body>
</html>
