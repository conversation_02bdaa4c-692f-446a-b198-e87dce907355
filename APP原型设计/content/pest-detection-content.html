<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SFAP - 病虫害识别内容</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-green: #22C55E;
            --dark-green: #16A34A;
            --light-green: #DCFCE7;
            --bg-green: #F0FDF4;
        }
        
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: white;
            width: 393px;
            height: 852px;
            overflow: hidden;
        }
        
        .ios-status-bar {
            height: 47px;
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        .detection-container {
            height: calc(852px - 47px - 80px);
            background: linear-gradient(135deg, var(--bg-green), white);
            overflow-y: auto;
        }
        
        .header {
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            padding: 20px;
            color: white;
            text-align: center;
        }
        
        .camera-section {
            padding: 20px;
            text-align: center;
        }
        
        .detection-type-selector {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            justify-content: center;
        }
        
        .type-btn {
            flex: 1;
            max-width: 140px;
            padding: 12px 20px;
            border: 2px solid var(--light-green);
            border-radius: 25px;
            background: white;
            color: var(--dark-green);
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .type-btn.active {
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            border-color: var(--primary-green);
            color: white;
        }
        
        .camera-frame {
            width: 100%;
            max-width: 300px;
            height: 300px;
            background: #f8f9fa;
            border: 3px dashed var(--light-green);
            border-radius: 20px;
            margin: 0 auto 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .camera-frame:hover {
            border-color: var(--primary-green);
            background: var(--bg-green);
        }
        
        .camera-icon {
            font-size: 60px;
            color: var(--primary-green);
            margin-bottom: 15px;
        }
        
        .camera-text {
            color: #6B7280;
            font-size: 16px;
            margin-bottom: 10px;
        }
        
        .camera-hint {
            color: #9CA3AF;
            font-size: 14px;
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            margin-top: 20px;
            justify-content: center;
        }
        
        .action-btn {
            padding: 15px 25px;
            border-radius: 15px;
            border: none;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .primary-btn {
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            color: white;
        }
        
        .secondary-btn {
            background: white;
            color: var(--dark-green);
            border: 2px solid var(--light-green);
        }
        
        .result-section {
            margin: 20px;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .result-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .result-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #EF4444, #DC2626);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            margin-right: 15px;
        }
        
        .result-title {
            font-size: 18px;
            font-weight: bold;
            color: #1F2937;
        }
        
        .result-confidence {
            font-size: 14px;
            color: #6B7280;
            margin-top: 2px;
        }
        
        .result-details {
            margin-top: 15px;
        }
        
        .detail-item {
            margin-bottom: 15px;
            padding: 12px;
            background: var(--bg-green);
            border-radius: 10px;
            border-left: 4px solid var(--primary-green);
        }
        
        .detail-title {
            font-weight: 600;
            color: var(--dark-green);
            margin-bottom: 5px;
        }
        
        .detail-content {
            color: #4B5563;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: white;
            border-top: 1px solid #E5E7EB;
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding: 10px 0;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 5px 10px;
        }
        
        .nav-item.active {
            color: var(--primary-green);
        }
        
        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .nav-text {
            font-size: 12px;
        }
    </style>
</head>
<body>
    <!-- iOS状态栏 -->
    <div class="ios-status-bar">
        <div class="flex items-center">
            <span>9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-sm"></i>
            <i class="fas fa-wifi text-sm"></i>
            <div class="flex items-center">
                <div class="w-6 h-3 border border-white rounded-sm">
                    <div class="w-4 h-1.5 bg-white rounded-sm m-0.5"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 病虫害识别页面内容 -->
    <div class="detection-container">
        <!-- 头部 -->
        <div class="header">
            <h1 class="text-xl font-bold mb-2">
                <i class="fas fa-bug mr-2"></i>
                病虫害识别
            </h1>
            <p class="text-sm opacity-90">拍照识别作物病虫害，获取专业防治建议</p>
        </div>

        <!-- 拍照区域 -->
        <div class="camera-section">
            <!-- 识别类型选择 -->
            <div class="detection-type-selector">
                <button class="type-btn active">
                    <i class="fas fa-bug"></i>
                    识别病虫害
                </button>
                <button class="type-btn">
                    <i class="fas fa-seedling"></i>
                    识别作物
                </button>
            </div>
            
            <div class="camera-frame">
                <i class="fas fa-camera camera-icon"></i>
                <div class="camera-text">点击拍照识别</div>
                <div class="camera-hint">请对准病虫害部位拍摄</div>
            </div>
            
            <div class="action-buttons">
                <button class="action-btn primary-btn">
                    <i class="fas fa-camera"></i>
                    拍照识别
                </button>
                <button class="action-btn secondary-btn">
                    <i class="fas fa-images"></i>
                    相册选择
                </button>
            </div>
        </div>

        <!-- 识别结果 -->
        <div class="result-section">
            <div class="result-header">
                <div class="result-icon">
                    <i class="fas fa-bug"></i>
                </div>
                <div>
                    <div class="result-title">玉米螟虫害</div>
                    <div class="result-confidence">识别准确度：95%</div>
                </div>
            </div>
            
            <div class="result-details">
                <div class="detail-item">
                    <div class="detail-title">
                        <i class="fas fa-info-circle mr-2"></i>
                        病虫害描述
                    </div>
                    <div class="detail-content">
                        玉米螟是玉米的主要害虫之一，幼虫钻蛀茎秆，造成茎秆折断，严重影响玉米产量。
                    </div>
                </div>
                
                <div class="detail-item">
                    <div class="detail-title">
                        <i class="fas fa-shield-alt mr-2"></i>
                        防治建议
                    </div>
                    <div class="detail-content">
                        1. 生物防治：释放赤眼蜂等天敌昆虫<br>
                        2. 化学防治：使用氯虫苯甲酰胺等药剂<br>
                        3. 农业防治：及时清除田间杂草
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航 -->
    <div class="bottom-nav">
        <div class="nav-item">
            <i class="fas fa-home nav-icon"></i>
            <span class="nav-text">首页</span>
        </div>
        <div class="nav-item active">
            <i class="fas fa-bug nav-icon"></i>
            <span class="nav-text">识别</span>
        </div>
        <div class="nav-item">
            <i class="fas fa-qrcode nav-icon"></i>
            <span class="nav-text">溯源</span>
        </div>
        <div class="nav-item">
            <i class="fas fa-shopping-cart nav-icon"></i>
            <span class="nav-text">农品汇</span>
        </div>
        <div class="nav-item">
            <i class="fas fa-user nav-icon"></i>
            <span class="nav-text">我的</span>
        </div>
    </div>
</body>
</html>
