<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SFAP - 市场信息内容</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-green: #22C55E;
            --dark-green: #16A34A;
            --light-green: #DCFCE7;
            --bg-green: #F0FDF4;
        }
        
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: white;
            width: 393px;
            height: 852px;
            overflow: hidden;
        }
        
        .ios-status-bar {
            height: 47px;
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        .market-container {
            height: calc(852px - 47px - 80px);
            background: linear-gradient(135deg, var(--bg-green), white);
            overflow-y: auto;
        }
        
        .header {
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            padding: 20px;
            color: white;
            text-align: center;
        }
        
        .price-overview {
            background: white;
            margin: 20px;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .price-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }
        
        .price-card {
            background: var(--bg-green);
            border-radius: 12px;
            padding: 15px;
            text-align: center;
            border: 2px solid var(--light-green);
        }
        
        .price-icon {
            width: 40px;
            height: 40px;
            background: var(--primary-green);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin: 0 auto 10px;
            font-size: 18px;
        }
        
        .price-name {
            font-size: 14px;
            font-weight: 600;
            color: #1F2937;
            margin-bottom: 5px;
        }
        
        .price-value {
            font-size: 18px;
            font-weight: bold;
            color: #1F2937;
            margin-bottom: 5px;
        }
        
        .price-change {
            font-size: 12px;
            font-weight: 600;
        }
        
        .price-up {
            color: #EF4444;
        }
        
        .price-down {
            color: var(--primary-green);
        }
        
        .chart-section {
            background: white;
            margin: 20px;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .chart-title {
            font-size: 16px;
            font-weight: 600;
            color: #1F2937;
        }
        
        .time-selector {
            display: flex;
            gap: 8px;
        }
        
        .time-btn {
            padding: 6px 12px;
            border: 1px solid var(--light-green);
            border-radius: 20px;
            background: white;
            color: var(--dark-green);
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .time-btn.active {
            background: var(--primary-green);
            color: white;
            border-color: var(--primary-green);
        }
        
        .chart-placeholder {
            height: 150px;
            background: linear-gradient(135deg, var(--light-green), var(--bg-green));
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--dark-green);
            font-size: 14px;
            position: relative;
            overflow: hidden;
        }
        
        .chart-line {
            position: absolute;
            bottom: 20px;
            left: 20px;
            right: 20px;
            height: 2px;
            background: var(--primary-green);
            border-radius: 1px;
        }
        
        .chart-line::before {
            content: '';
            position: absolute;
            top: -10px;
            left: 30%;
            width: 4px;
            height: 24px;
            background: var(--primary-green);
            border-radius: 2px;
        }
        
        .chart-line::after {
            content: '';
            position: absolute;
            top: -20px;
            right: 20%;
            width: 4px;
            height: 44px;
            background: #EF4444;
            border-radius: 2px;
        }
        
        .news-section {
            background: white;
            margin: 20px;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .news-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #F3F4F6;
        }
        
        .news-item:last-child {
            border-bottom: none;
        }
        
        .news-icon {
            width: 40px;
            height: 40px;
            background: var(--light-green);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--dark-green);
            margin-right: 12px;
            font-size: 16px;
        }
        
        .news-content {
            flex: 1;
        }
        
        .news-title {
            font-size: 14px;
            font-weight: 600;
            color: #1F2937;
            margin-bottom: 4px;
        }
        
        .news-desc {
            font-size: 12px;
            color: #6B7280;
            margin-bottom: 4px;
        }
        
        .news-time {
            font-size: 11px;
            color: #9CA3AF;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: white;
            border-top: 1px solid #E5E7EB;
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding: 10px 0;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 5px 10px;
        }
        
        .nav-item.active {
            color: var(--primary-green);
        }
        
        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .nav-text {
            font-size: 12px;
        }
    </style>
</head>
<body>
    <!-- iOS状态栏 -->
    <div class="ios-status-bar">
        <div class="flex items-center">
            <span>9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-sm"></i>
            <i class="fas fa-wifi text-sm"></i>
            <div class="flex items-center">
                <div class="w-6 h-3 border border-white rounded-sm">
                    <div class="w-4 h-1.5 bg-white rounded-sm m-0.5"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 市场信息页面内容 -->
    <div class="market-container">
        <!-- 头部 -->
        <div class="header">
            <h1 class="text-xl font-bold mb-2">
                <i class="fas fa-chart-line mr-2"></i>
                市场信息
            </h1>
            <p class="text-sm opacity-90">实时价格行情，把握市场动态</p>
        </div>

        <!-- 价格概览 -->
        <div class="price-overview">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">
                <i class="fas fa-coins mr-2"></i>
                今日行情
            </h3>
            
            <div class="price-grid">
                <div class="price-card">
                    <div class="price-icon">
                        <i class="fas fa-seedling"></i>
                    </div>
                    <div class="price-name">玉米</div>
                    <div class="price-value">¥2.85</div>
                    <div class="price-change price-up">
                        <i class="fas fa-arrow-up mr-1"></i>
                        +3.2%
                    </div>
                </div>
                
                <div class="price-card">
                    <div class="price-icon">
                        <i class="fas fa-apple-alt"></i>
                    </div>
                    <div class="price-name">苹果</div>
                    <div class="price-value">¥8.50</div>
                    <div class="price-change price-down">
                        <i class="fas fa-arrow-down mr-1"></i>
                        -1.5%
                    </div>
                </div>
                
                <div class="price-card">
                    <div class="price-icon">
                        <i class="fas fa-carrot"></i>
                    </div>
                    <div class="price-name">胡萝卜</div>
                    <div class="price-value">¥4.20</div>
                    <div class="price-change price-up">
                        <i class="fas fa-arrow-up mr-1"></i>
                        +2.1%
                    </div>
                </div>
                
                <div class="price-card">
                    <div class="price-icon">
                        <i class="fas fa-circle" style="color: #EF4444;"></i>
                    </div>
                    <div class="price-name">番茄</div>
                    <div class="price-value">¥6.80</div>
                    <div class="price-change price-up">
                        <i class="fas fa-arrow-up mr-1"></i>
                        +4.8%
                    </div>
                </div>
            </div>
        </div>

        <!-- 价格走势图 -->
        <div class="chart-section">
            <div class="chart-header">
                <div class="chart-title">玉米价格走势</div>
                <div class="time-selector">
                    <button class="time-btn">日</button>
                    <button class="time-btn active">周</button>
                    <button class="time-btn">月</button>
                </div>
            </div>
            
            <div class="chart-placeholder">
                <div>
                    <i class="fas fa-chart-line text-2xl mb-2"></i>
                    <div>价格走势图</div>
                </div>
                <div class="chart-line"></div>
            </div>
        </div>

        <!-- 市场资讯 -->
        <div class="news-section">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">
                <i class="fas fa-newspaper mr-2"></i>
                市场资讯
            </h3>
            
            <div class="news-item">
                <div class="news-icon">
                    <i class="fas fa-trending-up"></i>
                </div>
                <div class="news-content">
                    <div class="news-title">玉米价格持续上涨</div>
                    <div class="news-desc">受天气影响，本周玉米价格上涨3.2%</div>
                    <div class="news-time">2小时前</div>
                </div>
            </div>
            
            <div class="news-item">
                <div class="news-icon">
                    <i class="fas fa-leaf"></i>
                </div>
                <div class="news-content">
                    <div class="news-title">有机蔬菜需求增长</div>
                    <div class="news-desc">消费者对有机蔬菜需求持续增长</div>
                    <div class="news-time">5小时前</div>
                </div>
            </div>
            
            <div class="news-item">
                <div class="news-icon">
                    <i class="fas fa-shipping-fast"></i>
                </div>
                <div class="news-content">
                    <div class="news-title">物流成本下降</div>
                    <div class="news-desc">农产品物流成本较上月下降2.1%</div>
                    <div class="news-time">1天前</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航 -->
    <div class="bottom-nav">
        <div class="nav-item">
            <i class="fas fa-home nav-icon"></i>
            <span class="nav-text">首页</span>
        </div>
        <div class="nav-item">
            <i class="fas fa-bug nav-icon"></i>
            <span class="nav-text">识别</span>
        </div>
        <div class="nav-item">
            <i class="fas fa-qrcode nav-icon"></i>
            <span class="nav-text">溯源</span>
        </div>
        <div class="nav-item">
            <i class="fas fa-shopping-cart nav-icon"></i>
            <span class="nav-text">农品汇</span>
        </div>
        <div class="nav-item">
            <i class="fas fa-user nav-icon"></i>
            <span class="nav-text">我的</span>
        </div>
    </div>
</body>
</html>
