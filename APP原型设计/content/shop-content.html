<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SFAP - 农品汇内容</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-green: #22C55E;
            --dark-green: #16A34A;
            --light-green: #DCFCE7;
            --bg-green: #F0FDF4;
        }
        
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: white;
            width: 393px;
            height: 852px;
            overflow: hidden;
        }
        
        .ios-status-bar {
            height: 47px;
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        .shop-container {
            height: calc(852px - 47px - 80px);
            background: linear-gradient(135deg, var(--bg-green), white);
            overflow-y: auto;
        }
        
        .header {
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            padding: 20px;
            color: white;
        }
        
        .search-bar {
            display: flex;
            align-items: center;
            background: rgba(255,255,255,0.2);
            border-radius: 25px;
            padding: 12px 20px;
            margin-top: 15px;
        }
        
        .search-input {
            flex: 1;
            background: none;
            border: none;
            color: white;
            font-size: 16px;
            outline: none;
        }
        
        .search-input::placeholder {
            color: rgba(255,255,255,0.8);
        }
        
        .search-icon {
            margin-right: 10px;
            font-size: 18px;
        }
        
        .categories-section {
            background: white;
            margin: 20px;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .categories-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
        }
        
        .category-item {
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .category-icon {
            width: 50px;
            height: 50px;
            background: var(--light-green);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 8px;
            color: var(--dark-green);
            font-size: 20px;
        }
        
        .category-name {
            font-size: 12px;
            color: #374151;
            font-weight: 600;
        }
        
        .banner-section {
            margin: 20px;
            border-radius: 15px;
            overflow: hidden;
            background: linear-gradient(135deg, #F59E0B, #D97706);
            color: white;
            padding: 20px;
            position: relative;
        }
        
        .banner-content {
            position: relative;
            z-index: 2;
        }
        
        .banner-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .banner-desc {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .products-section {
            background: white;
            margin: 20px;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .products-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }
        
        .product-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .product-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.15);
        }
        
        .product-image {
            height: 100px;
            background: linear-gradient(135deg, var(--light-green), var(--bg-green));
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--dark-green);
            font-size: 32px;
        }
        
        .product-info {
            padding: 12px;
        }
        
        .product-name {
            font-size: 14px;
            font-weight: 600;
            color: #1F2937;
            margin-bottom: 4px;
        }
        
        .product-desc {
            font-size: 12px;
            color: #6B7280;
            margin-bottom: 8px;
        }
        
        .product-price {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .price {
            font-size: 16px;
            font-weight: bold;
            color: #EF4444;
        }
        
        .original-price {
            font-size: 12px;
            color: #9CA3AF;
            text-decoration: line-through;
        }
        
        .rating {
            display: flex;
            align-items: center;
            gap: 4px;
            margin-top: 4px;
        }
        
        .stars {
            color: #F59E0B;
            font-size: 12px;
        }
        
        .rating-text {
            font-size: 11px;
            color: #6B7280;
        }
        
        .add-cart-btn {
            width: 24px;
            height: 24px;
            background: var(--primary-green);
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: white;
            border-top: 1px solid #E5E7EB;
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding: 10px 0;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 5px 10px;
        }
        
        .nav-item.active {
            color: var(--primary-green);
        }
        
        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .nav-text {
            font-size: 12px;
        }
    </style>
</head>
<body>
    <!-- iOS状态栏 -->
    <div class="ios-status-bar">
        <div class="flex items-center">
            <span>9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-sm"></i>
            <i class="fas fa-wifi text-sm"></i>
            <div class="flex items-center">
                <div class="w-6 h-3 border border-white rounded-sm">
                    <div class="w-4 h-1.5 bg-white rounded-sm m-0.5"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 农品汇页面内容 -->
    <div class="shop-container">
        <!-- 头部 -->
        <div class="header">
            <h1 class="text-xl font-bold mb-2">
                <i class="fas fa-shopping-cart mr-2"></i>
                农品汇
            </h1>
            <p class="text-sm opacity-90">优质农产品，直达您的餐桌</p>
            
            <!-- 搜索栏 -->
            <div class="search-bar">
                <i class="fas fa-search search-icon"></i>
                <input type="text" class="search-input" placeholder="搜索优质农产品">
            </div>
        </div>

        <!-- 分类导航 -->
        <div class="categories-section">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">
                <i class="fas fa-th-large mr-2"></i>
                商品分类
            </h3>
            
            <div class="categories-grid">
                <div class="category-item">
                    <div class="category-icon">
                        <i class="fas fa-apple-alt"></i>
                    </div>
                    <div class="category-name">水果</div>
                </div>
                <div class="category-item">
                    <div class="category-icon">
                        <i class="fas fa-carrot"></i>
                    </div>
                    <div class="category-name">蔬菜</div>
                </div>
                <div class="category-item">
                    <div class="category-icon">
                        <i class="fas fa-seedling"></i>
                    </div>
                    <div class="category-name">粮食</div>
                </div>
                <div class="category-item">
                    <div class="category-icon">
                        <i class="fas fa-leaf"></i>
                    </div>
                    <div class="category-name">有机</div>
                </div>
            </div>
        </div>

        <!-- 促销横幅 -->
        <div class="banner-section">
            <div class="banner-content">
                <div class="banner-title">
                    <i class="fas fa-fire mr-2"></i>
                    春季特惠
                </div>
                <div class="banner-desc">新鲜蔬菜全场8折，限时抢购</div>
            </div>
        </div>

        <!-- 热销商品 -->
        <div class="products-section">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">
                <i class="fas fa-fire mr-2"></i>
                热销商品
            </h3>
            
            <div class="products-grid">
                <div class="product-card">
                    <div class="product-image">
                        <i class="fas fa-apple-alt"></i>
                    </div>
                    <div class="product-info">
                        <div class="product-name">有机苹果</div>
                        <div class="product-desc">新鲜采摘，香甜可口</div>
                        <div class="product-price">
                            <div>
                                <span class="price">¥12.8</span>
                                <span class="original-price">¥16.0</span>
                            </div>
                            <button class="add-cart-btn">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                        <div class="rating">
                            <div class="stars">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                            <span class="rating-text">4.9 (128)</span>
                        </div>
                    </div>
                </div>
                
                <div class="product-card">
                    <div class="product-image">
                        <i class="fas fa-carrot"></i>
                    </div>
                    <div class="product-info">
                        <div class="product-name">有机胡萝卜</div>
                        <div class="product-desc">营养丰富，口感清甜</div>
                        <div class="product-price">
                            <div>
                                <span class="price">¥8.5</span>
                                <span class="original-price">¥10.0</span>
                            </div>
                            <button class="add-cart-btn">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                        <div class="rating">
                            <div class="stars">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="far fa-star"></i>
                            </div>
                            <span class="rating-text">4.7 (89)</span>
                        </div>
                    </div>
                </div>
                
                <div class="product-card">
                    <div class="product-image">
                        <i class="fas fa-circle" style="color: #EF4444;"></i>
                    </div>
                    <div class="product-info">
                        <div class="product-name">新鲜番茄</div>
                        <div class="product-desc">沙瓤多汁，酸甜适中</div>
                        <div class="product-price">
                            <div>
                                <span class="price">¥6.8</span>
                                <span class="original-price">¥8.5</span>
                            </div>
                            <button class="add-cart-btn">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                        <div class="rating">
                            <div class="stars">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                            <span class="rating-text">4.8 (156)</span>
                        </div>
                    </div>
                </div>
                
                <div class="product-card">
                    <div class="product-image">
                        <i class="fas fa-seedling"></i>
                    </div>
                    <div class="product-info">
                        <div class="product-name">优质大米</div>
                        <div class="product-desc">粒粒饱满，香味浓郁</div>
                        <div class="product-price">
                            <div>
                                <span class="price">¥25.0</span>
                                <span class="original-price">¥30.0</span>
                            </div>
                            <button class="add-cart-btn">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                        <div class="rating">
                            <div class="stars">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="far fa-star"></i>
                            </div>
                            <span class="rating-text">4.6 (203)</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航 -->
    <div class="bottom-nav">
        <div class="nav-item">
            <i class="fas fa-home nav-icon"></i>
            <span class="nav-text">首页</span>
        </div>
        <div class="nav-item">
            <i class="fas fa-bug nav-icon"></i>
            <span class="nav-text">识别</span>
        </div>
        <div class="nav-item">
            <i class="fas fa-qrcode nav-icon"></i>
            <span class="nav-text">溯源</span>
        </div>
        <div class="nav-item active">
            <i class="fas fa-shopping-cart nav-icon"></i>
            <span class="nav-text">农品汇</span>
        </div>
        <div class="nav-item">
            <i class="fas fa-user nav-icon"></i>
            <span class="nav-text">我的</span>
        </div>
    </div>
</body>
</html>
