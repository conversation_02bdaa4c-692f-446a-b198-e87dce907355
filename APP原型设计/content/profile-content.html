<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SFAP - 个人中心内容</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-green: #22C55E;
            --dark-green: #16A34A;
            --light-green: #DCFCE7;
            --bg-green: #F0FDF4;
        }
        
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: white;
            width: 393px;
            height: 852px;
            overflow: hidden;
        }
        
        .ios-status-bar {
            height: 47px;
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        .profile-container {
            height: calc(852px - 47px - 80px);
            background: linear-gradient(135deg, var(--bg-green), white);
            overflow-y: auto;
        }
        
        .profile-header {
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            padding: 30px 20px;
            color: white;
            text-align: center;
            position: relative;
        }
        
        .profile-avatar {
            width: 80px;
            height: 80px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 32px;
            border: 3px solid rgba(255,255,255,0.3);
        }
        
        .profile-name {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .profile-type {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 15px;
        }
        
        .profile-stats {
            display: flex;
            justify-content: space-around;
            background: rgba(255,255,255,0.15);
            border-radius: 15px;
            padding: 15px;
            backdrop-filter: blur(10px);
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-value {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: 12px;
            opacity: 0.9;
        }
        
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin: 20px;
        }
        
        .action-item {
            background: white;
            border-radius: 12px;
            padding: 20px 10px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .action-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            margin: 0 auto 10px;
        }
        
        .action-title {
            font-size: 14px;
            font-weight: 600;
            color: #1F2937;
        }
        
        .menu-section {
            background: white;
            margin: 20px;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .menu-item {
            display: flex;
            align-items: center;
            padding: 18px 20px;
            border-bottom: 1px solid #F3F4F6;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .menu-item:last-child {
            border-bottom: none;
        }
        
        .menu-icon {
            width: 40px;
            height: 40px;
            background: var(--light-green);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--dark-green);
            font-size: 18px;
            margin-right: 15px;
        }
        
        .menu-content {
            flex: 1;
        }
        
        .menu-title {
            font-size: 16px;
            font-weight: 600;
            color: #1F2937;
            margin-bottom: 2px;
        }
        
        .menu-desc {
            font-size: 12px;
            color: #6B7280;
        }
        
        .menu-arrow {
            color: #9CA3AF;
            font-size: 14px;
        }
        
        .vip-card {
            background: linear-gradient(135deg, #F59E0B, #D97706);
            border-radius: 15px;
            padding: 20px;
            margin: 20px;
            color: white;
            position: relative;
            overflow: hidden;
        }
        
        .vip-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .vip-title {
            font-size: 18px;
            font-weight: bold;
        }
        
        .vip-level {
            background: rgba(255,255,255,0.2);
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
        }
        
        .vip-benefits {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: white;
            border-top: 1px solid #E5E7EB;
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding: 10px 0;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 5px 10px;
        }
        
        .nav-item.active {
            color: var(--primary-green);
        }
        
        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .nav-text {
            font-size: 12px;
        }
    </style>
</head>
<body>
    <!-- iOS状态栏 -->
    <div class="ios-status-bar">
        <div class="flex items-center">
            <span>9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-sm"></i>
            <i class="fas fa-wifi text-sm"></i>
            <div class="flex items-center">
                <div class="w-6 h-3 border border-white rounded-sm">
                    <div class="w-4 h-1.5 bg-white rounded-sm m-0.5"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 个人中心页面内容 -->
    <div class="profile-container">
        <!-- 个人信息头部 -->
        <div class="profile-header">
            <div class="profile-avatar">
                <i class="fas fa-user"></i>
            </div>
            <div class="profile-name">张农户</div>
            <div class="profile-type">
                <i class="fas fa-tractor mr-1"></i>
                新型农业经营主体
            </div>
            
            <div class="profile-stats">
                <div class="stat-item">
                    <div class="stat-value">156</div>
                    <div class="stat-label">识别次数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">23</div>
                    <div class="stat-label">溯源查询</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">89</div>
                    <div class="stat-label">AI问答</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">12</div>
                    <div class="stat-label">订单数</div>
                </div>
            </div>
        </div>

        <!-- VIP会员卡 -->
        <div class="vip-card">
            <div class="vip-header">
                <div class="vip-title">
                    <i class="fas fa-crown mr-2"></i>
                    SFAP会员
                </div>
                <div class="vip-level">黄金会员</div>
            </div>
            <div class="vip-benefits">
                享受专家咨询、优先客服、会员折扣等特权
            </div>
        </div>

        <!-- 快捷操作 -->
        <div class="quick-actions">
            <div class="action-item">
                <div class="action-icon">
                    <i class="fas fa-shopping-bag"></i>
                </div>
                <div class="action-title">我的订单</div>
            </div>
            <div class="action-item">
                <div class="action-icon">
                    <i class="fas fa-heart"></i>
                </div>
                <div class="action-title">我的收藏</div>
            </div>
            <div class="action-item">
                <div class="action-icon">
                    <i class="fas fa-wallet"></i>
                </div>
                <div class="action-title">我的钱包</div>
            </div>
            <div class="action-item">
                <div class="action-icon">
                    <i class="fas fa-ticket-alt"></i>
                </div>
                <div class="action-title">优惠券</div>
            </div>
        </div>

        <!-- 功能菜单 -->
        <div class="menu-section">
            <div class="menu-item">
                <div class="menu-icon">
                    <i class="fas fa-seedling"></i>
                </div>
                <div class="menu-content">
                    <div class="menu-title">农场管理</div>
                    <div class="menu-desc">管理我的农场信息</div>
                </div>
                <i class="fas fa-chevron-right menu-arrow"></i>
            </div>
            
            <div class="menu-item">
                <div class="menu-icon">
                    <i class="fas fa-chart-bar"></i>
                </div>
                <div class="menu-content">
                    <div class="menu-title">数据分析</div>
                    <div class="menu-desc">查看农事数据报告</div>
                </div>
                <i class="fas fa-chevron-right menu-arrow"></i>
            </div>
            
            <div class="menu-item">
                <div class="menu-icon">
                    <i class="fas fa-cog"></i>
                </div>
                <div class="menu-content">
                    <div class="menu-title">设置</div>
                    <div class="menu-desc">账户设置和偏好</div>
                </div>
                <i class="fas fa-chevron-right menu-arrow"></i>
            </div>
        </div>
    </div>

    <!-- 底部导航 -->
    <div class="bottom-nav">
        <div class="nav-item">
            <i class="fas fa-home nav-icon"></i>
            <span class="nav-text">首页</span>
        </div>
        <div class="nav-item">
            <i class="fas fa-bug nav-icon"></i>
            <span class="nav-text">识别</span>
        </div>
        <div class="nav-item">
            <i class="fas fa-qrcode nav-icon"></i>
            <span class="nav-text">溯源</span>
        </div>
        <div class="nav-item">
            <i class="fas fa-shopping-cart nav-icon"></i>
            <span class="nav-text">农品汇</span>
        </div>
        <div class="nav-item active">
            <i class="fas fa-user nav-icon"></i>
            <span class="nav-text">我的</span>
        </div>
    </div>
</body>
</html>
