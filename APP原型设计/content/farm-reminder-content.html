<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SFAP - 农事提醒内容</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-green: #22C55E;
            --dark-green: #16A34A;
            --light-green: #DCFCE7;
            --bg-green: #F0FDF4;
        }
        
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: white;
            width: 393px;
            height: 852px;
            overflow: hidden;
        }
        
        .ios-status-bar {
            height: 47px;
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        .reminder-container {
            height: calc(852px - 47px - 80px);
            background: linear-gradient(135deg, var(--bg-green), white);
            overflow-y: auto;
        }
        
        .header {
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            padding: 20px;
            color: white;
            text-align: center;
        }
        
        .calendar-section {
            background: white;
            margin: 20px;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .calendar-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .month-year {
            font-size: 18px;
            font-weight: bold;
            color: #1F2937;
        }
        
        .calendar-nav {
            display: flex;
            gap: 10px;
        }
        
        .nav-btn {
            width: 32px;
            height: 32px;
            border: none;
            border-radius: 50%;
            background: var(--light-green);
            color: var(--dark-green);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 8px;
            margin-bottom: 20px;
        }
        
        .calendar-day {
            aspect-ratio: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            border-radius: 8px;
            cursor: pointer;
            position: relative;
        }
        
        .day-header {
            font-weight: 600;
            color: #6B7280;
            background: none;
            cursor: default;
        }
        
        .day-number {
            color: #374151;
            transition: all 0.3s ease;
        }
        
        .day-number:hover {
            background: var(--light-green);
        }
        
        .day-number.today {
            background: var(--primary-green);
            color: white;
            font-weight: bold;
        }
        
        .day-number.has-event::after {
            content: '';
            position: absolute;
            bottom: 2px;
            left: 50%;
            transform: translateX(-50%);
            width: 4px;
            height: 4px;
            background: #EF4444;
            border-radius: 50%;
        }
        
        .reminders-section {
            background: white;
            margin: 20px;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .reminder-item {
            display: flex;
            align-items: center;
            padding: 15px;
            margin-bottom: 12px;
            background: var(--bg-green);
            border-radius: 12px;
            border-left: 4px solid var(--primary-green);
        }
        
        .reminder-icon {
            width: 40px;
            height: 40px;
            background: var(--primary-green);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: 15px;
            font-size: 16px;
        }
        
        .reminder-content {
            flex: 1;
        }
        
        .reminder-title {
            font-size: 16px;
            font-weight: 600;
            color: #1F2937;
            margin-bottom: 4px;
        }
        
        .reminder-time {
            font-size: 14px;
            color: #6B7280;
            margin-bottom: 4px;
        }
        
        .reminder-desc {
            font-size: 12px;
            color: #9CA3AF;
        }
        
        .priority-high {
            border-left-color: #EF4444;
        }
        
        .priority-high .reminder-icon {
            background: #EF4444;
        }
        
        .weather-card {
            background: linear-gradient(135deg, #3B82F6, #1D4ED8);
            margin: 20px;
            border-radius: 15px;
            padding: 20px;
            color: white;
        }
        
        .weather-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .weather-temp {
            font-size: 24px;
            font-weight: bold;
        }
        
        .weather-desc {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .weather-icon {
            font-size: 32px;
        }
        
        .weather-details {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            opacity: 0.8;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: white;
            border-top: 1px solid #E5E7EB;
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding: 10px 0;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 5px 10px;
        }
        
        .nav-item.active {
            color: var(--primary-green);
        }
        
        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .nav-text {
            font-size: 12px;
        }
    </style>
</head>
<body>
    <!-- iOS状态栏 -->
    <div class="ios-status-bar">
        <div class="flex items-center">
            <span>9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-sm"></i>
            <i class="fas fa-wifi text-sm"></i>
            <div class="flex items-center">
                <div class="w-6 h-3 border border-white rounded-sm">
                    <div class="w-4 h-1.5 bg-white rounded-sm m-0.5"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 农事提醒页面内容 -->
    <div class="reminder-container">
        <!-- 头部 -->
        <div class="header">
            <h1 class="text-xl font-bold mb-2">
                <i class="fas fa-bell mr-2"></i>
                农事提醒
            </h1>
            <p class="text-sm opacity-90">智能农事管理，不错过每个重要时机</p>
        </div>

        <!-- 天气卡片 -->
        <div class="weather-card">
            <div class="weather-header">
                <div>
                    <div class="weather-temp">25°C</div>
                    <div class="weather-desc">晴转多云</div>
                </div>
                <div class="weather-icon">
                    <i class="fas fa-sun"></i>
                </div>
            </div>
            <div class="weather-details">
                <span>湿度 65%</span>
                <span>风速 3级</span>
                <span>适宜农事</span>
            </div>
        </div>

        <!-- 日历区域 -->
        <div class="calendar-section">
            <div class="calendar-header">
                <div class="month-year">2025年1月</div>
                <div class="calendar-nav">
                    <button class="nav-btn">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button class="nav-btn">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
            
            <div class="calendar-grid">
                <div class="calendar-day day-header">日</div>
                <div class="calendar-day day-header">一</div>
                <div class="calendar-day day-header">二</div>
                <div class="calendar-day day-header">三</div>
                <div class="calendar-day day-header">四</div>
                <div class="calendar-day day-header">五</div>
                <div class="calendar-day day-header">六</div>
                
                <div class="calendar-day day-number">29</div>
                <div class="calendar-day day-number">30</div>
                <div class="calendar-day day-number today">31</div>
                <div class="calendar-day day-number has-event">1</div>
                <div class="calendar-day day-number has-event">2</div>
                <div class="calendar-day day-number">3</div>
                <div class="calendar-day day-number">4</div>
            </div>
        </div>

        <!-- 今日提醒 -->
        <div class="reminders-section">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">
                <i class="fas fa-calendar-day mr-2"></i>
                今日提醒
            </h3>
            
            <div class="reminder-item priority-high">
                <div class="reminder-icon">
                    <i class="fas fa-tint"></i>
                </div>
                <div class="reminder-content">
                    <div class="reminder-title">玉米田灌溉</div>
                    <div class="reminder-time">今天 08:00</div>
                    <div class="reminder-desc">土壤湿度偏低，建议及时灌溉</div>
                </div>
            </div>
            
            <div class="reminder-item">
                <div class="reminder-icon">
                    <i class="fas fa-spray-can"></i>
                </div>
                <div class="reminder-content">
                    <div class="reminder-title">病虫害防治</div>
                    <div class="reminder-time">今天 16:00</div>
                    <div class="reminder-desc">喷洒杀虫剂，预防玉米螟</div>
                </div>
            </div>
            
            <div class="reminder-item">
                <div class="reminder-icon">
                    <i class="fas fa-seedling"></i>
                </div>
                <div class="reminder-content">
                    <div class="reminder-title">蔬菜播种</div>
                    <div class="reminder-time">明天 09:00</div>
                    <div class="reminder-desc">春季蔬菜播种最佳时期</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航 -->
    <div class="bottom-nav">
        <div class="nav-item">
            <i class="fas fa-home nav-icon"></i>
            <span class="nav-text">首页</span>
        </div>
        <div class="nav-item">
            <i class="fas fa-bug nav-icon"></i>
            <span class="nav-text">识别</span>
        </div>
        <div class="nav-item">
            <i class="fas fa-qrcode nav-icon"></i>
            <span class="nav-text">溯源</span>
        </div>
        <div class="nav-item">
            <i class="fas fa-shopping-cart nav-icon"></i>
            <span class="nav-text">农品汇</span>
        </div>
        <div class="nav-item">
            <i class="fas fa-user nav-icon"></i>
            <span class="nav-text">我的</span>
        </div>
    </div>
</body>
</html>
