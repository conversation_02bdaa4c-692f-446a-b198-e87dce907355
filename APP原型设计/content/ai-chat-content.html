<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SFAP - AI智能问答内容</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-green: #22C55E;
            --dark-green: #16A34A;
            --light-green: #DCFCE7;
            --bg-green: #F0FDF4;
        }
        
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: white;
            width: 393px;
            height: 852px;
            overflow: hidden;
        }
        
        .ios-status-bar {
            height: 47px;
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        .chat-container {
            height: calc(852px - 47px - 80px - 70px);
            background: #F8F9FA;
            overflow-y: auto;
            padding: 20px 15px;
        }
        
        .header {
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            padding: 15px 20px;
            color: white;
            text-align: center;
        }
        
        .chat-messages {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .message {
            display: flex;
            align-items: flex-start;
            gap: 10px;
        }
        
        .message.user {
            flex-direction: row-reverse;
        }
        
        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            flex-shrink: 0;
        }
        
        .ai-avatar {
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            color: white;
        }
        
        .user-avatar {
            background: #6B7280;
            color: white;
        }
        
        .message-content {
            max-width: 75%;
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 15px;
            line-height: 1.4;
            position: relative;
        }
        
        .ai-message {
            background: white;
            color: #1F2937;
            border-bottom-left-radius: 6px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .user-message {
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            color: white;
            border-bottom-right-radius: 6px;
        }
        
        .message-time {
            font-size: 11px;
            color: #9CA3AF;
            margin-top: 5px;
            text-align: center;
        }
        
        .quick-questions {
            background: white;
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .quick-question-btn {
            display: block;
            width: 100%;
            padding: 12px 15px;
            margin: 8px 0;
            background: var(--bg-green);
            border: 1px solid var(--light-green);
            border-radius: 10px;
            color: var(--dark-green);
            text-align: left;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }
        
        .input-area {
            position: fixed;
            bottom: 80px;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #E5E7EB;
            padding: 15px;
        }
        
        .input-container {
            display: flex;
            align-items: flex-end;
            gap: 10px;
            max-width: 100%;
        }
        
        .message-input {
            flex: 1;
            min-height: 44px;
            max-height: 120px;
            padding: 12px 16px;
            border: 2px solid #E5E7EB;
            border-radius: 22px;
            font-size: 16px;
            resize: none;
            outline: none;
            transition: all 0.3s ease;
            background: #F9FAFB;
        }
        
        .send-btn {
            width: 44px;
            height: 44px;
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 18px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: white;
            border-top: 1px solid #E5E7EB;
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding: 10px 0;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 5px 10px;
        }
        
        .nav-item.active {
            color: var(--primary-green);
        }
        
        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .nav-text {
            font-size: 12px;
        }
    </style>
</head>
<body>
    <!-- iOS状态栏 -->
    <div class="ios-status-bar">
        <div class="flex items-center">
            <span>9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-sm"></i>
            <i class="fas fa-wifi text-sm"></i>
            <div class="flex items-center">
                <div class="w-6 h-3 border border-white rounded-sm">
                    <div class="w-4 h-1.5 bg-white rounded-sm m-0.5"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 头部 -->
    <div class="header">
        <h1 class="text-lg font-bold">
            <i class="fas fa-robot mr-2"></i>
            AI智能问答
        </h1>
        <p class="text-sm opacity-90 mt-1">农业专家24小时在线为您服务</p>
    </div>

    <!-- 聊天内容区域 -->
    <div class="chat-container">
        <!-- 快捷问题 -->
        <div class="quick-questions">
            <h4 class="text-sm font-semibold text-gray-800 mb-2">
                <i class="fas fa-lightbulb mr-2"></i>
                常见问题
            </h4>
            <button class="quick-question-btn">
                <i class="fas fa-leaf mr-2"></i>
                玉米叶子发黄是什么原因？
            </button>
            <button class="quick-question-btn">
                <i class="fas fa-seedling mr-2"></i>
                现在适合种什么蔬菜？
            </button>
        </div>

        <!-- 聊天消息 -->
        <div class="chat-messages">
            <div class="message">
                <div class="message-avatar ai-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div>
                    <div class="message-content ai-message">
                        您好！我是SFAP智能助手，可以为您解答农业种植、病虫害防治、市场行情等问题。请问有什么可以帮助您的吗？
                    </div>
                    <div class="message-time">9:30</div>
                </div>
            </div>
            
            <div class="message user">
                <div class="message-avatar user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div>
                    <div class="message-content user-message">
                        玉米叶子发黄怎么办？
                    </div>
                    <div class="message-time">9:32</div>
                </div>
            </div>
            
            <div class="message">
                <div class="message-avatar ai-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div>
                    <div class="message-content ai-message">
                        玉米叶子发黄可能有以下几个原因：<br><br>
                        1. <strong>缺氮</strong>：老叶先黄，建议追施氮肥<br>
                        2. <strong>病害</strong>：如玉米大斑病，需要喷施杀菌剂<br>
                        3. <strong>缺水</strong>：干旱导致，及时灌溉<br><br>
                        建议您拍照上传，我可以帮您更准确地诊断。
                    </div>
                    <div class="message-time">9:33</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 输入区域 -->
    <div class="input-area">
        <div class="input-container">
            <textarea 
                class="message-input" 
                placeholder="请输入您的问题..."
                rows="1"
            ></textarea>
            <button class="send-btn">
                <i class="fas fa-paper-plane"></i>
            </button>
        </div>
    </div>

    <!-- 底部导航 -->
    <div class="bottom-nav">
        <div class="nav-item">
            <i class="fas fa-home nav-icon"></i>
            <span class="nav-text">首页</span>
        </div>
        <div class="nav-item">
            <i class="fas fa-bug nav-icon"></i>
            <span class="nav-text">识别</span>
        </div>
        <div class="nav-item">
            <i class="fas fa-qrcode nav-icon"></i>
            <span class="nav-text">溯源</span>
        </div>
        <div class="nav-item">
            <i class="fas fa-shopping-cart nav-icon"></i>
            <span class="nav-text">农品汇</span>
        </div>
        <div class="nav-item">
            <i class="fas fa-user nav-icon"></i>
            <span class="nav-text">我的</span>
        </div>
    </div>
</body>
</html>
