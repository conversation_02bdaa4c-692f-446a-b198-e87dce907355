<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SFAP智慧农业助手 - 移动端原型展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        :root {
            --primary-green: #22C55E;
            --dark-green: #16A34A;
            --light-green: #DCFCE7;
            --bg-green: #F0FDF4;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            background: white;
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .title {
            font-size: 2.5rem;
            font-weight: bold;
            color: var(--dark-green);
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 1.2rem;
            color: #6B7280;
            margin-bottom: 20px;
        }

        .description {
            color: #4B5563;
            line-height: 1.6;
        }

        .pages-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .page-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }

        .page-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(34, 197, 94, 0.2);
            border-color: var(--light-green);
        }

        .page-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            margin-bottom: 15px;
        }

        .page-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #1F2937;
            margin-bottom: 8px;
        }

        .page-desc {
            color: #6B7280;
            font-size: 0.95rem;
            line-height: 1.5;
            margin-bottom: 15px;
        }

        .page-link {
            display: inline-flex;
            align-items: center;
            color: var(--primary-green);
            font-weight: 600;
            text-decoration: none;
            font-size: 0.9rem;
        }

        .page-link:hover {
            color: var(--dark-green);
        }

        .footer {
            text-align: center;
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            color: #6B7280;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部介绍 -->
        <div class="header">
            <h1 class="title">
                <i class="fas fa-seedling mr-3"></i>
                SFAP智慧农业助手
            </h1>
            <p class="subtitle">移动端APP高保真原型展示</p>
            <p class="description">
                每个功能页面都有独立的手机原型展示，点击下方卡片可在新窗口中查看完整的iPhone 15 Pro模拟效果。
                <br>所有页面都包含完整的交互功能和真实的农业内容。
            </p>

            <!-- 快捷链接 -->
            <div class="mt-6 flex justify-center gap-4">
                <button onclick="window.open('showcase.html', '_blank')"
                        class="px-6 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-lg font-semibold hover:from-green-600 hover:to-green-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                    <i class="fas fa-mobile-alt mr-2"></i>
                    查看完整展示页面
                </button>
                <button onclick="window.open('pages/home.html', '_blank')"
                        class="px-6 py-3 bg-white text-green-600 border-2 border-green-500 rounded-lg font-semibold hover:bg-green-50 transition-all duration-300">
                    <i class="fas fa-home mr-2"></i>
                    直接进入首页
                </button>
            </div>
        </div>

        <!-- 页面展示网格 -->
        <div class="pages-grid">
            <div class="page-card" onclick="openPage('splash')">
                <div class="page-icon">
                    <i class="fas fa-play-circle"></i>
                </div>
                <h3 class="page-title">启动页面</h3>
                <p class="page-desc">品牌展示、加载动画、欢迎界面</p>
                <a href="#" class="page-link">
                    查看原型 <i class="fas fa-external-link-alt ml-1"></i>
                </a>
            </div>

            <div class="page-card" onclick="openPage('login')">
                <div class="page-icon">
                    <i class="fas fa-sign-in-alt"></i>
                </div>
                <h3 class="page-title">登录页面</h3>
                <p class="page-desc">用户身份验证、多用户类型选择、第三方登录</p>
                <a href="#" class="page-link">
                    查看原型 <i class="fas fa-external-link-alt ml-1"></i>
                </a>
            </div>

            <div class="page-card" onclick="openPage('register')">
                <div class="page-icon">
                    <i class="fas fa-user-plus"></i>
                </div>
                <h3 class="page-title">注册页面</h3>
                <p class="page-desc">账户创建、用户信息填写、验证码验证</p>
                <a href="#" class="page-link">
                    查看原型 <i class="fas fa-external-link-alt ml-1"></i>
                </a>
            </div>

            <div class="page-card" onclick="openPage('home')">
                <div class="page-icon">
                    <i class="fas fa-home"></i>
                </div>
                <h3 class="page-title">首页</h3>
                <p class="page-desc">核心功能入口、天气信息、农业资讯</p>
                <a href="#" class="page-link">
                    查看原型 <i class="fas fa-external-link-alt ml-1"></i>
                </a>
            </div>

            <div class="page-card" onclick="openPage('pest-detection')">
                <div class="page-icon">
                    <i class="fas fa-bug"></i>
                </div>
                <h3 class="page-title">病虫害识别</h3>
                <p class="page-desc">拍照识别、作物识别、AI诊断、防治建议</p>
                <a href="#" class="page-link">
                    查看原型 <i class="fas fa-external-link-alt ml-1"></i>
                </a>
            </div>

            <div class="page-card" onclick="openPage('qr-scan')">
                <div class="page-icon">
                    <i class="fas fa-qrcode"></i>
                </div>
                <h3 class="page-title">扫码溯源</h3>
                <p class="page-desc">二维码扫描、产品溯源、时间轴展示</p>
                <a href="#" class="page-link">
                    查看原型 <i class="fas fa-external-link-alt ml-1"></i>
                </a>
            </div>

            <div class="page-card" onclick="openPage('ai-chat')">
                <div class="page-icon">
                    <i class="fas fa-robot"></i>
                </div>
                <h3 class="page-title">AI智能问答</h3>
                <p class="page-desc">农业咨询、智能对话、语音交互</p>
                <a href="#" class="page-link">
                    查看原型 <i class="fas fa-external-link-alt ml-1"></i>
                </a>
            </div>

            <div class="page-card" onclick="openPage('farm-reminder')">
                <div class="page-icon">
                    <i class="fas fa-bell"></i>
                </div>
                <h3 class="page-title">农事提醒</h3>
                <p class="page-desc">智能提醒、日历管理、天气集成</p>
                <a href="#" class="page-link">
                    查看原型 <i class="fas fa-external-link-alt ml-1"></i>
                </a>
            </div>

            <div class="page-card" onclick="openPage('market-info')">
                <div class="page-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <h3 class="page-title">市场信息</h3>
                <p class="page-desc">价格行情、市场资讯、趋势分析</p>
                <a href="#" class="page-link">
                    查看原型 <i class="fas fa-external-link-alt ml-1"></i>
                </a>
            </div>

            <div class="page-card" onclick="openPage('shop')">
                <div class="page-icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <h3 class="page-title">农品汇</h3>
                <p class="page-desc">农产品购买、商品展示、购物车功能</p>
                <a href="#" class="page-link">
                    查看原型 <i class="fas fa-external-link-alt ml-1"></i>
                </a>
            </div>

            <div class="page-card" onclick="openPage('profile')">
                <div class="page-icon">
                    <i class="fas fa-user"></i>
                </div>
                <h3 class="page-title">个人中心</h3>
                <p class="page-desc">用户信息、功能设置、数据统计</p>
                <a href="#" class="page-link">
                    查看原型 <i class="fas fa-external-link-alt ml-1"></i>
                </a>
            </div>
        </div>

        <!-- 底部说明 -->
        <div class="footer">
            <p><strong>使用说明：</strong></p>
            <p>每个页面都是独立的手机原型，包含完整的iPhone 15 Pro外观和交互功能。</p>
            <p>页面间可通过底部导航或功能按钮进行跳转，所有链接都会在新窗口中打开。</p>
            <p class="mt-4 text-sm">
                <i class="fas fa-info-circle mr-1"></i>
                版本 v1.1 | © 2025 SFAP智慧农业平台
            </p>
        </div>
    </div>

    <script>
        function openPage(pageName) {
            window.open(`pages/${pageName}.html`, '_blank');
        }
    </script>
</body>
</html>
