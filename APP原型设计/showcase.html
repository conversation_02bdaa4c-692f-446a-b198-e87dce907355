<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SFAP智慧农业助手 - 移动端原型展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .showcase-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            background: white;
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .title {
            font-size: 2.5rem;
            font-weight: bold;
            color: #16A34A;
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 1.2rem;
            color: #6B7280;
            margin-bottom: 20px;
        }

        .phones-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 30px;
            justify-items: center;
            margin-bottom: 40px;
        }

        /* iPhone 15 Pro 外观模拟 */
        .iphone-frame {
            width: 280px;
            height: 607px;
            background: #000;
            border-radius: 30px;
            padding: 6px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.3);
            position: relative;
            transition: all 0.3s ease;
        }

        .iphone-frame:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.4);
        }

        .iphone-screen {
            width: 100%;
            height: 100%;
            background: #fff;
            border-radius: 24px;
            overflow: hidden;
            position: relative;
        }

        /* 动态岛模拟 */
        .dynamic-island {
            position: absolute;
            top: 8px;
            left: 50%;
            transform: translateX(-50%);
            width: 90px;
            height: 26px;
            background: #000;
            border-radius: 13px;
            z-index: 1000;
        }

        .phone-iframe {
            width: 100%;
            height: 100%;
            border: none;
            background: white;
            transform: scale(0.71);
            transform-origin: top left;
            width: 140.8%;
            height: 140.8%;
        }

        .phone-label {
            text-align: center;
            margin-top: 15px;
            font-size: 14px;
            font-weight: 600;
            color: #374151;
        }

        .phone-desc {
            text-align: center;
            margin-top: 5px;
            font-size: 12px;
            color: #6B7280;
        }

        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #6B7280;
            font-size: 14px;
        }

        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #E5E7EB;
            border-top: 2px solid #22C55E;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .footer {
            text-align: center;
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            color: #6B7280;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .phones-grid {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 20px;
            }
            
            .iphone-frame {
                width: 250px;
                height: 542px;
            }
        }

        @media (max-width: 480px) {
            .phones-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            
            .iphone-frame {
                width: 220px;
                height: 477px;
            }
        }
    </style>
</head>
<body>
    <div class="showcase-container">
        <!-- 头部介绍 -->
        <div class="header">
            <h1 class="title">
                <i class="fas fa-seedling mr-3"></i>
                智慧农业平台 - 移动应用原型
            </h1>
            <p class="subtitle">SFAP智慧农业助手完整功能展示</p>
            <p class="text-gray-600">
                涵盖病虫害识别、农产品溯源、AI智能问答、农事提醒、市场信息等核心功能的高保真移动端原型
            </p>
        </div>

        <!-- 手机展示网格 -->
        <div class="phones-grid">
            <!-- 启动页面 -->
            <div class="phone-container">
                <div class="iphone-frame">
                    <div class="dynamic-island"></div>
                    <div class="iphone-screen">
                        <iframe class="phone-iframe" src="content/splash-content.html" onload="hideLoading(this)"></iframe>
                        <div class="loading">
                            <div class="spinner"></div>
                            加载中...
                        </div>
                    </div>
                </div>
                <div class="phone-label">启动页面</div>
                <div class="phone-desc">品牌展示</div>
            </div>

            <!-- 登录页面 -->
            <div class="phone-container">
                <div class="iphone-frame">
                    <div class="dynamic-island"></div>
                    <div class="iphone-screen">
                        <iframe class="phone-iframe" src="content/login-content.html" onload="hideLoading(this)"></iframe>
                        <div class="loading">
                            <div class="spinner"></div>
                            加载中...
                        </div>
                    </div>
                </div>
                <div class="phone-label">登录页面</div>
                <div class="phone-desc">用户身份验证</div>
            </div>

            <!-- 首页 -->
            <div class="phone-container">
                <div class="iphone-frame">
                    <div class="dynamic-island"></div>
                    <div class="iphone-screen">
                        <iframe class="phone-iframe" src="content/home-content.html" onload="hideLoading(this)"></iframe>
                        <div class="loading">
                            <div class="spinner"></div>
                            加载中...
                        </div>
                    </div>
                </div>
                <div class="phone-label">首页</div>
                <div class="phone-desc">核心功能入口</div>
            </div>

            <!-- 病虫害识别 -->
            <div class="phone-container">
                <div class="iphone-frame">
                    <div class="dynamic-island"></div>
                    <div class="iphone-screen">
                        <iframe class="phone-iframe" src="content/pest-detection-content.html" onload="hideLoading(this)"></iframe>
                        <div class="loading">
                            <div class="spinner"></div>
                            加载中...
                        </div>
                    </div>
                </div>
                <div class="phone-label">病虫害识别</div>
                <div class="phone-desc">AI智能诊断</div>
            </div>

            <!-- 扫码溯源 -->
            <div class="phone-container">
                <div class="iphone-frame">
                    <div class="dynamic-island"></div>
                    <div class="iphone-screen">
                        <iframe class="phone-iframe" src="content/qr-scan-content.html" onload="hideLoading(this)"></iframe>
                        <div class="loading">
                            <div class="spinner"></div>
                            加载中...
                        </div>
                    </div>
                </div>
                <div class="phone-label">扫码溯源</div>
                <div class="phone-desc">产品来源追溯</div>
            </div>

            <!-- AI智能问答 -->
            <div class="phone-container">
                <div class="iphone-frame">
                    <div class="dynamic-island"></div>
                    <div class="iphone-screen">
                        <iframe class="phone-iframe" src="content/ai-chat-content.html" onload="hideLoading(this)"></iframe>
                        <div class="loading">
                            <div class="spinner"></div>
                            加载中...
                        </div>
                    </div>
                </div>
                <div class="phone-label">AI智能问答</div>
                <div class="phone-desc">24小时农业咨询</div>
            </div>

            <!-- 农事提醒 -->
            <div class="phone-container">
                <div class="iphone-frame">
                    <div class="dynamic-island"></div>
                    <div class="iphone-screen">
                        <iframe class="phone-iframe" src="content/farm-reminder-content.html" onload="hideLoading(this)"></iframe>
                        <div class="loading">
                            <div class="spinner"></div>
                            加载中...
                        </div>
                    </div>
                </div>
                <div class="phone-label">农事提醒</div>
                <div class="phone-desc">智能农事管理</div>
            </div>

            <!-- 市场信息 -->
            <div class="phone-container">
                <div class="iphone-frame">
                    <div class="dynamic-island"></div>
                    <div class="iphone-screen">
                        <iframe class="phone-iframe" src="content/market-info-content.html" onload="hideLoading(this)"></iframe>
                        <div class="loading">
                            <div class="spinner"></div>
                            加载中...
                        </div>
                    </div>
                </div>
                <div class="phone-label">市场信息</div>
                <div class="phone-desc">价格行情分析</div>
            </div>

            <!-- 农品汇 -->
            <div class="phone-container">
                <div class="iphone-frame">
                    <div class="dynamic-island"></div>
                    <div class="iphone-screen">
                        <iframe class="phone-iframe" src="content/shop-content.html" onload="hideLoading(this)"></iframe>
                        <div class="loading">
                            <div class="spinner"></div>
                            加载中...
                        </div>
                    </div>
                </div>
                <div class="phone-label">农品汇</div>
                <div class="phone-desc">优质农产品商城</div>
            </div>

            <!-- 个人中心 -->
            <div class="phone-container">
                <div class="iphone-frame">
                    <div class="dynamic-island"></div>
                    <div class="iphone-screen">
                        <iframe class="phone-iframe" src="content/profile-content.html" onload="hideLoading(this)"></iframe>
                        <div class="loading">
                            <div class="spinner"></div>
                            加载中...
                        </div>
                    </div>
                </div>
                <div class="phone-label">个人中心</div>
                <div class="phone-desc">用户信息管理</div>
            </div>
        </div>

        <!-- 底部说明 -->
        <div class="footer">
            <p><strong>SFAP智慧农业助手移动端原型</strong></p>
            <p>基于真实农业场景设计，提供完整的智慧农业解决方案</p>
            <p class="mt-4 text-sm">
                <i class="fas fa-info-circle mr-1"></i>
                版本 v1.2 | © 2025 SFAP智慧农业平台 | 高保真移动端原型展示
            </p>
        </div>
    </div>

    <script>
        function hideLoading(iframe) {
            const container = iframe.closest('.iphone-screen');
            const loading = container.querySelector('.loading');
            if (loading) {
                loading.style.display = 'none';
            }
        }

        // 延迟加载优化
        document.addEventListener('DOMContentLoaded', function() {
            const iframes = document.querySelectorAll('.phone-iframe');
            
            // 使用Intersection Observer实现懒加载
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const iframe = entry.target;
                        if (!iframe.src) {
                            iframe.src = iframe.dataset.src;
                        }
                    }
                });
            });

            iframes.forEach(iframe => {
                observer.observe(iframe);
            });
        });
    </script>
</body>
</html>
