-- =====================================================
-- SFAP农品汇平台数据库部署SQL文件
-- 版本: v4.0
-- 创建日期: 2025-01-25
-- 目标数据库: MySQL 8.0+
-- 字符集: utf8mb4
-- 排序规则: utf8mb4_unicode_ci
-- =====================================================

-- 设置SQL模式和字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;
SET SQL_MODE = 'NO_AUTO_VALUE_ON_ZERO';
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+08:00";

-- =====================================================
-- 1. 数据库创建
-- =====================================================

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS `agriculture_mall` 
DEFAULT CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE `agriculture_mall`;

-- =====================================================
-- 2. 用户权限设置
-- =====================================================

-- 创建应用专用用户（生产环境使用）
CREATE USER IF NOT EXISTS 'sfap_user'@'localhost' IDENTIFIED BY 'sfap_secure_password_2025';
CREATE USER IF NOT EXISTS 'sfap_user'@'%' IDENTIFIED BY 'sfap_secure_password_2025';

-- 授予权限
GRANT SELECT, INSERT, UPDATE, DELETE, CREATE, DROP, INDEX, ALTER, CREATE TEMPORARY TABLES, LOCK TABLES ON agriculture_mall.* TO 'sfap_user'@'localhost';
GRANT SELECT, INSERT, UPDATE, DELETE, CREATE, DROP, INDEX, ALTER, CREATE TEMPORARY TABLES, LOCK TABLES ON agriculture_mall.* TO 'sfap_user'@'%';

-- 刷新权限
FLUSH PRIVILEGES;

-- =====================================================
-- 3. 核心表结构创建
-- =====================================================

-- -----------------------------------------------------
-- 3.1 用户管理表
-- -----------------------------------------------------

-- 用户表
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码',
  `nickname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '头像URL',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '邮箱',
  `role` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'user' COMMENT '角色：admin,seller,user',
  `status` int NOT NULL DEFAULT '1' COMMENT '状态：1-正常，0-禁用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` int NOT NULL DEFAULT '0' COMMENT '删除标记：0-未删除，1-已删除',
  `region` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '地区',
  `bio` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '个人简介',
  `address` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '地址',
  `city` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '城市',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间（兼容字段）',
  `district` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '区县',
  `fans` int DEFAULT '0' COMMENT '粉丝数',
  `focus` int DEFAULT '0' COMMENT '关注数',
  `gender` int DEFAULT '0' COMMENT '性别：0-未知，1-男，2-女',
  `id_card` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '身份证号',
  `integral` int DEFAULT '0' COMMENT '积分',
  `is_real_name_auth` bit(1) DEFAULT b'0' COMMENT '是否实名认证',
  `is_vip` bit(1) DEFAULT b'0' COMMENT '是否VIP',
  `last_login_time` datetime(6) DEFAULT NULL COMMENT '最后登录时间',
  `latitude` double DEFAULT NULL COMMENT '纬度',
  `level` int DEFAULT '1' COMMENT '用户等级',
  `longitude` double DEFAULT NULL COMMENT '经度',
  `openid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '微信OpenID',
  `province` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '省份',
  `real_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '真实姓名',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间（兼容字段）',
  `user_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'normal' COMMENT '用户类型',
  `vip_expire_time` datetime(6) DEFAULT NULL COMMENT 'VIP过期时间',
  `country` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '国家',
  `language` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '语言',
  `session_key` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '会话密钥',
  `total_likes_given` int NOT NULL DEFAULT '0' COMMENT '总点赞数（给出）',
  `total_likes_received` int NOT NULL DEFAULT '0' COMMENT '总点赞数（收到）',
  `total_reviews` int NOT NULL DEFAULT '0' COMMENT '总评论数',
  `total_favorites` int NOT NULL DEFAULT '0' COMMENT '总收藏数',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `openid` (`openid`),
  KEY `idx_role` (`role`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_total_reviews` (`total_reviews`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 用户地址表
DROP TABLE IF EXISTS `user_address`;
CREATE TABLE `user_address` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '地址ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `receiver_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '收货人姓名',
  `receiver_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '收货人电话',
  `province` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '省份',
  `city` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '城市',
  `district` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '区县',
  `detail_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '详细地址',
  `postal_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '邮政编码',
  `is_default` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否默认地址',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_is_default` (`is_default`),
  CONSTRAINT `fk_user_address_user_id` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户地址表';

-- 用户行为表
DROP TABLE IF EXISTS `user_behavior`;
CREATE TABLE `user_behavior` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '行为ID',
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  `action_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '行为类型',
  `target_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '目标类型',
  `target_id` bigint DEFAULT NULL COMMENT '目标ID',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '用户代理',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_action_type` (`action_type`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_user_behavior_user_id` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户行为表';

-- -----------------------------------------------------
-- 3.2 商品管理表
-- -----------------------------------------------------

-- 商品分类表
DROP TABLE IF EXISTS `category`;
CREATE TABLE `category` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类名称',
  `parent_id` bigint DEFAULT '0' COMMENT '父分类ID，0表示顶级分类',
  `level` int NOT NULL DEFAULT '1' COMMENT '分类层级',
  `sort_order` int NOT NULL DEFAULT '0' COMMENT '排序',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '分类图标',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '分类描述',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-启用，0-禁用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_level` (`level`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品分类表';

-- 商品表
DROP TABLE IF EXISTS `product`;
CREATE TABLE `product` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '商品ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '商品名称',
  `category_id` bigint NOT NULL COMMENT '分类ID',
  `seller_id` bigint NOT NULL COMMENT '销售者ID',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '商品描述',
  `price` decimal(10,2) NOT NULL COMMENT '价格',
  `original_price` decimal(10,2) DEFAULT NULL COMMENT '原价',
  `stock` int NOT NULL DEFAULT '0' COMMENT '库存',
  `unit` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '件' COMMENT '单位',
  `weight` decimal(8,2) DEFAULT NULL COMMENT '重量（kg）',
  `main_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '主图',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-上架，0-下架',
  `sales_count` int NOT NULL DEFAULT '0' COMMENT '销量',
  `view_count` int NOT NULL DEFAULT '0' COMMENT '浏览量',
  `favorite_count` int NOT NULL DEFAULT '0' COMMENT '收藏量',
  `rating` decimal(3,2) DEFAULT '5.00' COMMENT '评分',
  `review_count` int NOT NULL DEFAULT '0' COMMENT '评论数',
  `origin` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '产地',
  `shelf_life` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '保质期',
  `storage_method` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '储存方法',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_seller_id` (`seller_id`),
  KEY `idx_status` (`status`),
  KEY `idx_price` (`price`),
  KEY `idx_sales_count` (`sales_count`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_product_category_id` FOREIGN KEY (`category_id`) REFERENCES `category` (`id`),
  CONSTRAINT `fk_product_seller_id` FOREIGN KEY (`seller_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品表';

-- 商品图片表
DROP TABLE IF EXISTS `product_image`;
CREATE TABLE `product_image` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '图片ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `image_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '图片URL',
  `sort_order` int NOT NULL DEFAULT '0' COMMENT '排序',
  `is_main` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否主图',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_sort_order` (`sort_order`),
  CONSTRAINT `fk_product_image_product_id` FOREIGN KEY (`product_id`) REFERENCES `product` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品图片表';

-- 商品收藏表
DROP TABLE IF EXISTS `product_favorite`;
CREATE TABLE `product_favorite` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '收藏ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_product` (`user_id`,`product_id`),
  KEY `idx_product_id` (`product_id`),
  CONSTRAINT `fk_product_favorite_user_id` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_product_favorite_product_id` FOREIGN KEY (`product_id`) REFERENCES `product` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品收藏表';

-- 商品评论表
DROP TABLE IF EXISTS `product_review`;
CREATE TABLE `product_review` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '评论ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `order_id` bigint DEFAULT NULL COMMENT '订单ID',
  `rating` int NOT NULL DEFAULT '5' COMMENT '评分：1-5星',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '评论内容',
  `images` json DEFAULT NULL COMMENT '评论图片',
  `reply` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '商家回复',
  `reply_time` datetime DEFAULT NULL COMMENT '回复时间',
  `like_count` int NOT NULL DEFAULT '0' COMMENT '点赞数',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-正常，0-隐藏',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_rating` (`rating`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_product_review_product_id` FOREIGN KEY (`product_id`) REFERENCES `product` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_product_review_user_id` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品评论表';

-- 购物车表
DROP TABLE IF EXISTS `cart_item`;
CREATE TABLE `cart_item` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '购物车项ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `quantity` int NOT NULL DEFAULT '1' COMMENT '数量',
  `selected` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否选中',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_product` (`user_id`,`product_id`),
  KEY `idx_product_id` (`product_id`),
  CONSTRAINT `fk_cart_item_user_id` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_cart_item_product_id` FOREIGN KEY (`product_id`) REFERENCES `product` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='购物车表';

-- -----------------------------------------------------
-- 3.3 订单管理表
-- -----------------------------------------------------

-- 订单表
DROP TABLE IF EXISTS `order`;
CREATE TABLE `order` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单号',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `seller_id` bigint NOT NULL COMMENT '销售者ID',
  `total_amount` decimal(10,2) NOT NULL COMMENT '订单总金额',
  `actual_amount` decimal(10,2) NOT NULL COMMENT '实际支付金额',
  `discount_amount` decimal(10,2) DEFAULT '0.00' COMMENT '优惠金额',
  `shipping_fee` decimal(10,2) DEFAULT '0.00' COMMENT '运费',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending' COMMENT '订单状态',
  `payment_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'unpaid' COMMENT '支付状态',
  `payment_method` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '支付方式',
  `payment_time` datetime DEFAULT NULL COMMENT '支付时间',
  `shipping_time` datetime DEFAULT NULL COMMENT '发货时间',
  `delivery_time` datetime DEFAULT NULL COMMENT '送达时间',
  `finish_time` datetime DEFAULT NULL COMMENT '完成时间',
  `cancel_time` datetime DEFAULT NULL COMMENT '取消时间',
  `cancel_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '取消原因',
  `receiver_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '收货人姓名',
  `receiver_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '收货人电话',
  `receiver_address` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '收货地址',
  `logistics_company` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '物流公司',
  `logistics_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '物流单号',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '备注',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_seller_id` (`seller_id`),
  KEY `idx_status` (`status`),
  KEY `idx_payment_status` (`payment_status`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_order_user_id` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`),
  CONSTRAINT `fk_order_seller_id` FOREIGN KEY (`seller_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单表';

-- 订单项表
DROP TABLE IF EXISTS `order_item`;
CREATE TABLE `order_item` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '订单项ID',
  `order_id` bigint NOT NULL COMMENT '订单ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `product_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '商品名称',
  `product_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '商品图片',
  `price` decimal(10,2) NOT NULL COMMENT '商品单价',
  `quantity` int NOT NULL COMMENT '购买数量',
  `total_amount` decimal(10,2) NOT NULL COMMENT '小计金额',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_product_id` (`product_id`),
  CONSTRAINT `fk_order_item_order_id` FOREIGN KEY (`order_id`) REFERENCES `order` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_order_item_product_id` FOREIGN KEY (`product_id`) REFERENCES `product` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单项表';

-- -----------------------------------------------------
-- 3.4 溯源系统表
-- -----------------------------------------------------

-- 溯源记录表
DROP TABLE IF EXISTS `traceability_record`;
CREATE TABLE `traceability_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '溯源记录ID',
  `trace_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '溯源码',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `seller_id` bigint NOT NULL COMMENT '销售者ID',
  `product_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '产品名称',
  `batch_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '生产批次',
  `production_date` date DEFAULT NULL COMMENT '生产日期',
  `expiry_date` date DEFAULT NULL COMMENT '过期日期',
  `origin` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '产地',
  `farm_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '农场名称',
  `producer_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '生产者姓名',
  `producer_contact` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '生产者联系方式',
  `production_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '生产地址',
  `production_environment` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '生产环境',
  `cultivation_method` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '种植/养殖方式',
  `pesticides_used` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '使用农药/饲料',
  `quality_grade` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '质量等级',
  `testing_organization` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '检测机构',
  `quality_report` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '质量检测报告',
  `certification_info` json DEFAULT NULL COMMENT '认证信息',
  `images` json DEFAULT NULL COMMENT '相关图片',
  `documents` json DEFAULT NULL COMMENT '相关文档',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '备注说明',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'draft' COMMENT '状态：draft-草稿，pending-待审核，published-已发布',
  `audit_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'pending' COMMENT '审核状态',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `audit_user_id` bigint DEFAULT NULL COMMENT '审核人ID',
  `audit_remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '审核备注',
  `qr_code_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '二维码URL',
  `view_count` int NOT NULL DEFAULT '0' COMMENT '查看次数',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_trace_code` (`trace_code`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_seller_id` (`seller_id`),
  KEY `idx_status` (`status`),
  KEY `idx_audit_status` (`audit_status`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_traceability_record_product_id` FOREIGN KEY (`product_id`) REFERENCES `product` (`id`),
  CONSTRAINT `fk_traceability_record_seller_id` FOREIGN KEY (`seller_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='溯源记录表';

-- 溯源查询记录表
DROP TABLE IF EXISTS `traceability_query`;
CREATE TABLE `traceability_query` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '查询记录ID',
  `trace_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '溯源码',
  `user_id` bigint DEFAULT NULL COMMENT '查询用户ID',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '用户代理',
  `query_result` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '查询结果：success-成功，not_found-未找到',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '查询时间',
  PRIMARY KEY (`id`),
  KEY `idx_trace_code` (`trace_code`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_traceability_query_user_id` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='溯源查询记录表';

-- 溯源事件表
DROP TABLE IF EXISTS `traceability_event`;
CREATE TABLE `traceability_event` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '事件ID',
  `trace_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '溯源码',
  `event_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '事件类型',
  `event_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '事件标题',
  `event_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '事件描述',
  `event_time` datetime NOT NULL COMMENT '事件时间',
  `location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '事件地点',
  `operator` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作人',
  `images` json DEFAULT NULL COMMENT '相关图片',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_trace_code` (`trace_code`),
  KEY `idx_event_type` (`event_type`),
  KEY `idx_event_time` (`event_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='溯源事件表';

-- -----------------------------------------------------
-- 3.5 销售者管理表
-- -----------------------------------------------------

-- 销售者申请表
DROP TABLE IF EXISTS `seller_application`;
CREATE TABLE `seller_application` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '申请ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `shop_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '店铺名称',
  `business_license` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '营业执照',
  `contact_person` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '联系人',
  `contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '联系电话',
  `business_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '经营地址',
  `business_scope` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '经营范围',
  `application_reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '申请理由',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending' COMMENT '申请状态',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `audit_user_id` bigint DEFAULT NULL COMMENT '审核人ID',
  `audit_remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '审核备注',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_seller_application_user_id` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='销售者申请表';

-- 销售者店铺表
DROP TABLE IF EXISTS `seller_shop`;
CREATE TABLE `seller_shop` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '店铺ID',
  `seller_id` bigint NOT NULL COMMENT '销售者ID',
  `shop_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '店铺名称',
  `shop_logo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '店铺Logo',
  `shop_banner` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '店铺横幅',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '店铺描述',
  `contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系电话',
  `business_hours` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '营业时间',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '店铺地址',
  `rating` decimal(3,2) DEFAULT '5.00' COMMENT '店铺评分',
  `total_sales` int NOT NULL DEFAULT '0' COMMENT '总销量',
  `product_count` int NOT NULL DEFAULT '0' COMMENT '商品数量',
  `follower_count` int NOT NULL DEFAULT '0' COMMENT '关注数',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-正常，0-关闭',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_seller_id` (`seller_id`),
  KEY `idx_status` (`status`),
  KEY `idx_rating` (`rating`),
  CONSTRAINT `fk_seller_shop_seller_id` FOREIGN KEY (`seller_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='销售者店铺表';

-- -----------------------------------------------------
-- 3.6 价格数据表
-- -----------------------------------------------------

-- 价格数据表
DROP TABLE IF EXISTS `price_data`;
CREATE TABLE `price_data` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '价格数据ID',
  `product_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '产品名称',
  `category` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '产品分类',
  `market_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '市场名称',
  `region` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '地区',
  `price` decimal(10,2) NOT NULL COMMENT '价格',
  `unit` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '元/公斤' COMMENT '单位',
  `price_date` date NOT NULL COMMENT '价格日期',
  `source` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '数据来源',
  `quality_grade` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '质量等级',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_product_name` (`product_name`),
  KEY `idx_category` (`category`),
  KEY `idx_region` (`region`),
  KEY `idx_price_date` (`price_date`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='价格数据表';

-- 价格历史表
DROP TABLE IF EXISTS `price_history`;
CREATE TABLE `price_history` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '价格历史ID',
  `product_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '产品名称',
  `region` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '地区',
  `avg_price` decimal(10,2) NOT NULL COMMENT '平均价格',
  `min_price` decimal(10,2) DEFAULT NULL COMMENT '最低价格',
  `max_price` decimal(10,2) DEFAULT NULL COMMENT '最高价格',
  `price_date` date NOT NULL COMMENT '价格日期',
  `data_count` int DEFAULT '1' COMMENT '数据条数',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_product_region_date` (`product_name`,`region`,`price_date`),
  KEY `idx_price_date` (`price_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='价格历史表';

-- -----------------------------------------------------
-- 3.7 AI预测相关表
-- -----------------------------------------------------

-- 预测模型表
DROP TABLE IF EXISTS `prediction_models`;
CREATE TABLE `prediction_models` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '模型ID',
  `model_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模型名称',
  `model_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模型类型',
  `product_category` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '适用产品分类',
  `model_version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '1.0' COMMENT '模型版本',
  `model_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '模型文件路径',
  `accuracy` decimal(5,4) DEFAULT NULL COMMENT '模型准确率',
  `training_data_count` int DEFAULT NULL COMMENT '训练数据量',
  `last_training_time` datetime DEFAULT NULL COMMENT '最后训练时间',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '状态',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '模型描述',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_model_type` (`model_type`),
  KEY `idx_product_category` (`product_category`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='预测模型表';

-- 预测结果表
DROP TABLE IF EXISTS `prediction_results`;
CREATE TABLE `prediction_results` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '预测结果ID',
  `model_id` bigint NOT NULL COMMENT '模型ID',
  `product_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '产品名称',
  `region` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '地区',
  `prediction_date` date NOT NULL COMMENT '预测日期',
  `predicted_price` decimal(10,2) NOT NULL COMMENT '预测价格',
  `confidence_score` decimal(5,4) DEFAULT NULL COMMENT '置信度',
  `actual_price` decimal(10,2) DEFAULT NULL COMMENT '实际价格（用于验证）',
  `prediction_accuracy` decimal(5,4) DEFAULT NULL COMMENT '预测准确度',
  `input_features` json DEFAULT NULL COMMENT '输入特征',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_model_id` (`model_id`),
  KEY `idx_product_name` (`product_name`),
  KEY `idx_prediction_date` (`prediction_date`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_prediction_results_model_id` FOREIGN KEY (`model_id`) REFERENCES `prediction_models` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='预测结果表';

-- 模型评估表
DROP TABLE IF EXISTS `model_evaluations`;
CREATE TABLE `model_evaluations` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '评估ID',
  `model_id` bigint NOT NULL COMMENT '模型ID',
  `evaluation_date` date NOT NULL COMMENT '评估日期',
  `mae` decimal(10,4) DEFAULT NULL COMMENT '平均绝对误差',
  `mse` decimal(10,4) DEFAULT NULL COMMENT '均方误差',
  `rmse` decimal(10,4) DEFAULT NULL COMMENT '均方根误差',
  `mape` decimal(8,4) DEFAULT NULL COMMENT '平均绝对百分比误差',
  `r2_score` decimal(8,4) DEFAULT NULL COMMENT 'R²决定系数',
  `test_data_count` int DEFAULT NULL COMMENT '测试数据量',
  `evaluation_period` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '评估周期',
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '评估备注',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_model_id` (`model_id`),
  KEY `idx_evaluation_date` (`evaluation_date`),
  CONSTRAINT `fk_model_evaluations_model_id` FOREIGN KEY (`model_id`) REFERENCES `prediction_models` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='模型评估表';

-- -----------------------------------------------------
-- 3.8 新闻和百科表
-- -----------------------------------------------------

-- 农业新闻表
DROP TABLE IF EXISTS `agriculture_news`;
CREATE TABLE `agriculture_news` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '新闻ID',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '新闻标题',
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '新闻内容',
  `summary` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '新闻摘要',
  `author` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '作者',
  `source` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '新闻来源',
  `source_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '原文链接',
  `category` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '新闻分类',
  `tags` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '标签',
  `cover_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '封面图片',
  `view_count` int NOT NULL DEFAULT '0' COMMENT '浏览量',
  `like_count` int NOT NULL DEFAULT '0' COMMENT '点赞数',
  `comment_count` int NOT NULL DEFAULT '0' COMMENT '评论数',
  `publish_time` datetime DEFAULT NULL COMMENT '发布时间',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'published' COMMENT '状态',
  `is_top` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否置顶',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_category` (`category`),
  KEY `idx_status` (`status`),
  KEY `idx_publish_time` (`publish_time`),
  KEY `idx_view_count` (`view_count`),
  KEY `idx_is_top` (`is_top`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='农业新闻表';

-- 新闻图片表
DROP TABLE IF EXISTS `news_images`;
CREATE TABLE `news_images` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '图片ID',
  `news_id` bigint NOT NULL COMMENT '新闻ID',
  `image_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '图片URL',
  `image_alt` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '图片描述',
  `sort_order` int NOT NULL DEFAULT '0' COMMENT '排序',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_news_id` (`news_id`),
  KEY `idx_sort_order` (`sort_order`),
  CONSTRAINT `fk_news_images_news_id` FOREIGN KEY (`news_id`) REFERENCES `agriculture_news` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='新闻图片表';

-- 农业百科表
DROP TABLE IF EXISTS `encyclopedia`;
CREATE TABLE `encyclopedia` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '百科ID',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '标题',
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '内容',
  `summary` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '摘要',
  `category_id` bigint DEFAULT NULL COMMENT '分类ID',
  `tags` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '标签',
  `cover_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '封面图片',
  `author_id` bigint DEFAULT NULL COMMENT '作者ID',
  `view_count` int NOT NULL DEFAULT '0' COMMENT '浏览量',
  `like_count` int NOT NULL DEFAULT '0' COMMENT '点赞数',
  `favorite_count` int NOT NULL DEFAULT '0' COMMENT '收藏数',
  `comment_count` int NOT NULL DEFAULT '0' COMMENT '评论数',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'published' COMMENT '状态',
  `is_featured` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否精选',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_author_id` (`author_id`),
  KEY `idx_status` (`status`),
  KEY `idx_view_count` (`view_count`),
  KEY `idx_is_featured` (`is_featured`),
  CONSTRAINT `fk_encyclopedia_author_id` FOREIGN KEY (`author_id`) REFERENCES `user` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='农业百科表';

-- 百科分类表
DROP TABLE IF EXISTS `encyclopedia_category`;
CREATE TABLE `encyclopedia_category` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类名称',
  `parent_id` bigint DEFAULT '0' COMMENT '父分类ID',
  `level` int NOT NULL DEFAULT '1' COMMENT '层级',
  `sort_order` int NOT NULL DEFAULT '0' COMMENT '排序',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '图标',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '描述',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_level` (`level`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='百科分类表';

-- -----------------------------------------------------
-- 3.9 系统配置和日志表
-- -----------------------------------------------------

-- 系统配置表
DROP TABLE IF EXISTS `system_configs`;
CREATE TABLE `system_configs` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置键',
  `config_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '配置值',
  `config_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'string' COMMENT '配置类型',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '配置描述',
  `group_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'default' COMMENT '配置分组',
  `is_public` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否公开',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`),
  KEY `idx_group_name` (`group_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- API访问日志表
DROP TABLE IF EXISTS `api_access_log`;
CREATE TABLE `api_access_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  `api_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'API路径',
  `method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '请求方法',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '用户代理',
  `request_params` json DEFAULT NULL COMMENT '请求参数',
  `response_status` int DEFAULT NULL COMMENT '响应状态码',
  `response_time` int DEFAULT NULL COMMENT '响应时间（毫秒）',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '错误信息',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_api_path` (`api_path`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_response_status` (`response_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='API访问日志表';

-- 错误日志表
DROP TABLE IF EXISTS `error_log`;
CREATE TABLE `error_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '错误ID',
  `error_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '错误类型',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '错误信息',
  `stack_trace` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '堆栈跟踪',
  `request_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '请求URL',
  `request_method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '请求方法',
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '用户代理',
  `severity` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'error' COMMENT '严重程度',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'new' COMMENT '处理状态',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_error_type` (`error_type`),
  KEY `idx_severity` (`severity`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='错误日志表';

-- =====================================================
-- 4. 视图定义
-- =====================================================

-- 产品溯源视图
CREATE OR REPLACE VIEW `v_product_traceability` AS
SELECT
    p.id AS product_id,
    p.name AS product_name,
    p.seller_id,
    u.username AS seller_name,
    tr.trace_code,
    tr.batch_number,
    tr.production_date,
    tr.origin,
    tr.farm_name,
    tr.quality_grade,
    tr.status AS trace_status,
    tr.view_count AS trace_view_count,
    tr.created_at AS trace_created_at
FROM product p
LEFT JOIN traceability_record tr ON p.id = tr.product_id
LEFT JOIN user u ON p.seller_id = u.id
WHERE p.status = 1;

-- 用户行为分析视图
CREATE OR REPLACE VIEW `v_user_behavior_analysis` AS
SELECT
    u.id AS user_id,
    u.username,
    u.role,
    u.created_at AS register_time,
    COUNT(DISTINCT ub.id) AS total_actions,
    COUNT(DISTINCT CASE WHEN ub.action_type = 'view_product' THEN ub.id END) AS product_views,
    COUNT(DISTINCT CASE WHEN ub.action_type = 'add_to_cart' THEN ub.id END) AS cart_additions,
    COUNT(DISTINCT o.id) AS total_orders,
    COALESCE(SUM(o.actual_amount), 0) AS total_spent,
    u.last_login_time
FROM user u
LEFT JOIN user_behavior ub ON u.id = ub.user_id
LEFT JOIN `order` o ON u.id = o.user_id
WHERE u.deleted = 0
GROUP BY u.id;

-- 销售者基础统计视图
CREATE OR REPLACE VIEW `v_seller_basic_stats` AS
SELECT
    u.id AS seller_id,
    u.username AS seller_name,
    ss.shop_name,
    ss.rating AS shop_rating,
    COUNT(DISTINCT p.id) AS product_count,
    COUNT(DISTINCT o.id) AS order_count,
    COALESCE(SUM(o.actual_amount), 0) AS total_revenue,
    COALESCE(AVG(pr.rating), 0) AS avg_product_rating,
    COUNT(DISTINCT tr.id) AS traceability_records,
    u.created_at AS join_date
FROM user u
LEFT JOIN seller_shop ss ON u.id = ss.seller_id
LEFT JOIN product p ON u.id = p.seller_id AND p.status = 1
LEFT JOIN `order` o ON u.id = o.seller_id AND o.status = 'completed'
LEFT JOIN product_review pr ON p.id = pr.product_id
LEFT JOIN traceability_record tr ON u.id = tr.seller_id
WHERE u.role = 'seller' AND u.deleted = 0
GROUP BY u.id;

-- 最新价格视图
CREATE OR REPLACE VIEW `v_latest_prices` AS
SELECT
    pd.product_name,
    pd.category,
    pd.region,
    pd.price,
    pd.unit,
    pd.price_date,
    pd.source,
    pd.quality_grade
FROM price_data pd
INNER JOIN (
    SELECT
        product_name,
        region,
        MAX(price_date) AS latest_date
    FROM price_data
    GROUP BY product_name, region
) latest ON pd.product_name = latest.product_name
    AND pd.region = latest.region
    AND pd.price_date = latest.latest_date;

-- 系统实时状态视图
CREATE OR REPLACE VIEW `v_system_realtime_status` AS
SELECT
    (SELECT COUNT(*) FROM user WHERE deleted = 0) AS total_users,
    (SELECT COUNT(*) FROM user WHERE role = 'seller' AND deleted = 0) AS total_sellers,
    (SELECT COUNT(*) FROM product WHERE status = 1) AS active_products,
    (SELECT COUNT(*) FROM `order` WHERE DATE(created_at) = CURDATE()) AS today_orders,
    (SELECT COALESCE(SUM(actual_amount), 0) FROM `order` WHERE DATE(created_at) = CURDATE()) AS today_revenue,
    (SELECT COUNT(*) FROM traceability_record WHERE status = 'published') AS published_traces,
    (SELECT COUNT(*) FROM agriculture_news WHERE status = 'published') AS published_news,
    (SELECT COUNT(*) FROM api_access_log WHERE DATE(created_at) = CURDATE()) AS today_api_calls;

-- =====================================================
-- 5. 索引优化
-- =====================================================

-- 为高频查询字段添加复合索引
CREATE INDEX idx_product_category_status ON product(category_id, status);
CREATE INDEX idx_product_seller_status ON product(seller_id, status);
CREATE INDEX idx_order_user_status ON `order`(user_id, status);
CREATE INDEX idx_order_seller_status ON `order`(seller_id, status);
CREATE INDEX idx_price_data_name_date ON price_data(product_name, price_date);
CREATE INDEX idx_user_behavior_user_action ON user_behavior(user_id, action_type);
CREATE INDEX idx_traceability_product_status ON traceability_record(product_id, status);

-- =====================================================
-- 6. 初始数据插入
-- =====================================================

-- 插入系统管理员用户
INSERT INTO `user` (
    `username`, `password`, `nickname`, `role`, `status`,
    `created_at`, `updated_at`
) VALUES (
    'admin',
    '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EH.tjsxcz7wES6YzM6PtBG', -- 密码: admin123
    '系统管理员',
    'admin',
    1,
    NOW(),
    NOW()
);

-- 插入默认商品分类
INSERT INTO `category` (`name`, `parent_id`, `level`, `sort_order`, `status`, `created_at`, `updated_at`) VALUES
('水果', 0, 1, 1, 1, NOW(), NOW()),
('蔬菜', 0, 1, 2, 1, NOW(), NOW()),
('粮食', 0, 1, 3, 1, NOW(), NOW()),
('肉类', 0, 1, 4, 1, NOW(), NOW()),
('水产', 0, 1, 5, 1, NOW(), NOW()),
('禽蛋', 0, 1, 6, 1, NOW(), NOW()),
('调料', 0, 1, 7, 1, NOW(), NOW()),
('茶叶', 0, 1, 8, 1, NOW(), NOW());

-- 插入水果子分类
INSERT INTO `category` (`name`, `parent_id`, `level`, `sort_order`, `status`, `created_at`, `updated_at`) VALUES
('苹果', 1, 2, 1, 1, NOW(), NOW()),
('香蕉', 1, 2, 2, 1, NOW(), NOW()),
('橙子', 1, 2, 3, 1, NOW(), NOW()),
('葡萄', 1, 2, 4, 1, NOW(), NOW()),
('草莓', 1, 2, 5, 1, NOW(), NOW());

-- 插入蔬菜子分类
INSERT INTO `category` (`name`, `parent_id`, `level`, `sort_order`, `status`, `created_at`, `updated_at`) VALUES
('白菜', 2, 2, 1, 1, NOW(), NOW()),
('萝卜', 2, 2, 2, 1, NOW(), NOW()),
('土豆', 2, 2, 3, 1, NOW(), NOW()),
('番茄', 2, 2, 4, 1, NOW(), NOW()),
('黄瓜', 2, 2, 5, 1, NOW(), NOW());

-- 插入百科分类
INSERT INTO `encyclopedia_category` (`name`, `parent_id`, `level`, `sort_order`, `status`, `created_at`, `updated_at`) VALUES
('种植技术', 0, 1, 1, 1, NOW(), NOW()),
('养殖技术', 0, 1, 2, 1, NOW(), NOW()),
('病虫害防治', 0, 1, 3, 1, NOW(), NOW()),
('农业政策', 0, 1, 4, 1, NOW(), NOW()),
('市场行情', 0, 1, 5, 1, NOW(), NOW()),
('农业科技', 0, 1, 6, 1, NOW(), NOW());

-- 插入系统配置
INSERT INTO `system_configs` (`config_key`, `config_value`, `config_type`, `description`, `group_name`, `is_public`, `created_at`, `updated_at`) VALUES
('site_name', 'SFAP农品汇', 'string', '网站名称', 'basic', 1, NOW(), NOW()),
('site_description', '智慧农业综合服务平台', 'string', '网站描述', 'basic', 1, NOW(), NOW()),
('default_page_size', '20', 'integer', '默认分页大小', 'pagination', 0, NOW(), NOW()),
('max_upload_size', '10485760', 'integer', '最大上传文件大小（字节）', 'upload', 0, NOW(), NOW()),
('allowed_image_types', 'jpg,jpeg,png,gif,webp', 'string', '允许的图片类型', 'upload', 0, NOW(), NOW()),
('price_update_interval', '24', 'integer', '价格数据更新间隔（小时）', 'crawler', 0, NOW(), NOW()),
('ai_prediction_enabled', 'true', 'boolean', '是否启用AI价格预测', 'ai', 0, NOW(), NOW()),
('traceability_qr_domain', 'https://sfap.com/trace/', 'string', '溯源二维码域名', 'traceability', 0, NOW(), NOW());

-- 插入预测模型
INSERT INTO `prediction_models` (`model_name`, `model_type`, `product_category`, `model_version`, `accuracy`, `status`, `description`, `created_at`, `updated_at`) VALUES
('RNN价格预测模型', 'RNN', '水果', '1.0', 0.8500, 'active', '基于循环神经网络的农产品价格预测模型', NOW(), NOW()),
('ARIMA时间序列模型', 'ARIMA', '蔬菜', '1.0', 0.7800, 'active', '基于ARIMA的时间序列价格预测模型', NOW(), NOW()),
('LSTM深度学习模型', 'LSTM', '粮食', '1.0', 0.8200, 'active', '基于LSTM的深度学习价格预测模型', NOW(), NOW());

-- =====================================================
-- 7. 触发器定义
-- =====================================================

-- 用户统计更新触发器
DELIMITER $$
CREATE TRIGGER tr_update_user_stats_after_review_insert
AFTER INSERT ON product_review
FOR EACH ROW
BEGIN
    UPDATE user SET total_reviews = total_reviews + 1 WHERE id = NEW.user_id;
END$$

CREATE TRIGGER tr_update_user_stats_after_review_delete
AFTER DELETE ON product_review
FOR EACH ROW
BEGIN
    UPDATE user SET total_reviews = total_reviews - 1 WHERE id = OLD.user_id;
END$$

-- 商品统计更新触发器
CREATE TRIGGER tr_update_product_stats_after_review_insert
AFTER INSERT ON product_review
FOR EACH ROW
BEGIN
    UPDATE product SET
        review_count = review_count + 1,
        rating = (SELECT AVG(rating) FROM product_review WHERE product_id = NEW.product_id)
    WHERE id = NEW.product_id;
END$$

CREATE TRIGGER tr_update_product_stats_after_review_delete
AFTER DELETE ON product_review
FOR EACH ROW
BEGIN
    UPDATE product SET
        review_count = review_count - 1,
        rating = COALESCE((SELECT AVG(rating) FROM product_review WHERE product_id = OLD.product_id), 5.00)
    WHERE id = OLD.product_id;
END$$

-- 溯源查询计数触发器
CREATE TRIGGER tr_update_trace_view_count
AFTER INSERT ON traceability_query
FOR EACH ROW
BEGIN
    IF NEW.query_result = 'success' THEN
        UPDATE traceability_record SET view_count = view_count + 1 WHERE trace_code = NEW.trace_code;
    END IF;
END$$

DELIMITER ;

-- =====================================================
-- 8. 存储过程定义
-- =====================================================

DELIMITER $$

-- 获取用户统计信息的存储过程
CREATE PROCEDURE GetUserStatistics(IN user_id BIGINT)
BEGIN
    SELECT
        u.id,
        u.username,
        u.nickname,
        u.role,
        u.total_reviews,
        u.total_favorites,
        COUNT(DISTINCT pf.id) AS favorite_products,
        COUNT(DISTINCT o.id) AS total_orders,
        COALESCE(SUM(o.actual_amount), 0) AS total_spent,
        u.created_at
    FROM user u
    LEFT JOIN product_favorite pf ON u.id = pf.user_id
    LEFT JOIN `order` o ON u.id = o.user_id
    WHERE u.id = user_id AND u.deleted = 0
    GROUP BY u.id;
END$$

-- 获取商品推荐的存储过程
CREATE PROCEDURE GetProductRecommendations(IN user_id BIGINT, IN limit_count INT)
BEGIN
    SELECT DISTINCT
        p.id,
        p.name,
        p.price,
        p.main_image,
        p.rating,
        p.sales_count,
        u.username AS seller_name
    FROM product p
    JOIN user u ON p.seller_id = u.id
    LEFT JOIN user_behavior ub ON ub.user_id = user_id AND ub.target_type = 'product'
    LEFT JOIN product p2 ON ub.target_id = p2.id
    WHERE p.status = 1
        AND (p.category_id = p2.category_id OR p2.id IS NULL)
        AND p.id NOT IN (
            SELECT pf.product_id FROM product_favorite pf WHERE pf.user_id = user_id
        )
    ORDER BY p.rating DESC, p.sales_count DESC
    LIMIT limit_count;
END$$

DELIMITER ;

-- =====================================================
-- 9. 完成部署
-- =====================================================

-- 提交事务
COMMIT;

-- 重新启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 刷新权限
FLUSH PRIVILEGES;

-- =====================================================
-- 部署完成信息
-- =====================================================

SELECT 'SFAP农品汇平台数据库部署完成！' AS message;
SELECT 'Database: agriculture_mall' AS database_name;
SELECT 'Character Set: utf8mb4' AS charset;
SELECT 'Collation: utf8mb4_unicode_ci' AS collation;
SELECT 'Tables Created: 30+' AS tables_count;
SELECT 'Views Created: 5' AS views_count;
SELECT 'Triggers Created: 5' AS triggers_count;
SELECT 'Stored Procedures: 2' AS procedures_count;
SELECT 'Default Admin User: admin/admin123' AS admin_info;

-- =====================================================
-- 部署验证查询
-- =====================================================

-- 验证表是否创建成功
SELECT
    TABLE_NAME,
    TABLE_ROWS,
    DATA_LENGTH,
    INDEX_LENGTH,
    TABLE_COMMENT
FROM information_schema.TABLES
WHERE TABLE_SCHEMA = 'agriculture_mall'
    AND TABLE_TYPE = 'BASE TABLE'
ORDER BY TABLE_NAME;

-- 验证视图是否创建成功
SELECT
    TABLE_NAME AS VIEW_NAME,
    TABLE_COMMENT
FROM information_schema.TABLES
WHERE TABLE_SCHEMA = 'agriculture_mall'
    AND TABLE_TYPE = 'VIEW'
ORDER BY TABLE_NAME;

-- =====================================================
-- 文件结束
-- =====================================================
