name: SFAP CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '16'
  JAVA_VERSION: '11'
  PYTHON_VERSION: '3.9'

jobs:
  # 前端构建和测试
  frontend-build:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run ESLint
      run: npm run lint

    - name: Run tests
      run: npm run test:unit

    - name: Build frontend
      run: npm run build

    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: frontend-dist
        path: dist/

  # 后端构建和测试
  backend-build:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Setup Java
      uses: actions/setup-java@v3
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'

    - name: Cache Maven dependencies
      uses: actions/cache@v3
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
        restore-keys: ${{ runner.os }}-m2

    - name: Build backend
      run: |
        cd backend
        mvn clean compile

    - name: Run backend tests
      run: |
        cd backend
        mvn test

    - name: Package application
      run: |
        cd backend
        mvn package -DskipTests

    - name: Upload backend artifacts
      uses: actions/upload-artifact@v3
      with:
        name: backend-jar
        path: backend/target/*.jar

  # AI服务构建和测试
  ai-service-build:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: ${{ runner.os }}-pip-

    - name: Install AI service dependencies
      run: |
        cd ai-service
        pip install -r requirements.txt

    - name: Run AI service tests
      run: |
        cd ai-service
        python -m pytest tests/ || echo "No tests found"

  # Docker镜像构建
  docker-build:
    needs: [frontend-build, backend-build, ai-service-build]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Download frontend artifacts
      uses: actions/download-artifact@v3
      with:
        name: frontend-dist
        path: dist/

    - name: Download backend artifacts
      uses: actions/download-artifact@v3
      with:
        name: backend-jar
        path: backend/target/

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2

    - name: Login to Docker Hub
      uses: docker/login-action@v2
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}

    - name: Build and push frontend image
      uses: docker/build-push-action@v4
      with:
        context: .
        file: ./deploy/frontend-Dockerfile
        push: true
        tags: ${{ secrets.DOCKER_USERNAME }}/sfap-frontend:latest

    - name: Build and push backend image
      uses: docker/build-push-action@v4
      with:
        context: ./backend
        file: ./deploy/backend-Dockerfile
        push: true
        tags: ${{ secrets.DOCKER_USERNAME }}/sfap-backend:latest

    - name: Build and push AI service image
      uses: docker/build-push-action@v4
      with:
        context: ./ai-service
        file: ./deploy/ai-service-Dockerfile
        push: true
        tags: ${{ secrets.DOCKER_USERNAME }}/sfap-ai-service:latest

  # 部署到生产环境
  deploy:
    needs: [docker-build]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Deploy to production server
      uses: appleboy/ssh-action@v0.1.5
      with:
        host: ${{ secrets.PROD_HOST }}
        username: ${{ secrets.PROD_USER }}
        key: ${{ secrets.PROD_SSH_KEY }}
        script: |
          cd /opt/sfap
          docker-compose pull
          docker-compose down
          docker-compose up -d
          docker system prune -f

  # 通知部署结果
  notify:
    needs: [deploy]
    runs-on: ubuntu-latest
    if: always()
    steps:
    - name: Notify deployment status
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
      if: always()
