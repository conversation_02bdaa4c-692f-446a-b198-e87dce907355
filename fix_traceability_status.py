#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SFAP 溯源状态修复脚本
修复产品上架状态与溯源记录状态的同步问题
"""

import mysql.connector
import sys
from datetime import datetime

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'fan13965711955',
    'database': 'agriculture_mall',
    'charset': 'utf8mb4'
}

def connect_database():
    """连接数据库"""
    try:
        conn = mysql.connector.connect(**DB_CONFIG)
        print("✅ 数据库连接成功")
        return conn
    except mysql.connector.Error as e:
        print(f"❌ 数据库连接失败: {e}")
        return None

def check_status_sync_issues(conn):
    """检查状态同步问题"""
    cursor = conn.cursor(dictionary=True)
    
    print("\n=== 检查产品上架状态与溯源记录状态同步问题 ===")
    
    # 查询上架产品但溯源记录为草稿状态的情况
    query = """
    SELECT 
        p.id as product_id,
        p.name as product_name,
        p.status as product_status,
        p.is_on_shelf,
        tr.id as trace_id,
        tr.status as trace_status,
        tr.trace_code,
        tr.created_at
    FROM product p 
    LEFT JOIN traceability_record tr ON p.id = tr.product_id 
    WHERE p.seller_id IS NOT NULL 
      AND p.source_type = 'seller_upload'
      AND p.status = 1  -- 产品已上架
      AND tr.status = 0  -- 但溯源记录为草稿状态
      AND tr.deleted = 0
    ORDER BY tr.created_at DESC
    """
    
    cursor.execute(query)
    sync_issues = cursor.fetchall()
    
    if sync_issues:
        print(f"🔍 发现 {len(sync_issues)} 个状态同步问题:")
        for issue in sync_issues:
            print(f"  - 产品ID: {issue['product_id']}, 产品名: {issue['product_name']}")
            print(f"    溯源码: {issue['trace_code']}, 溯源状态: {issue['trace_status']} (应为2)")
    else:
        print("✅ 未发现状态同步问题")
    
    cursor.close()
    return sync_issues

def fix_status_sync_issues(conn, issues):
    """修复状态同步问题"""
    if not issues:
        print("✅ 无需修复状态同步问题")
        return True
    
    cursor = conn.cursor()
    
    print(f"\n=== 开始修复 {len(issues)} 个状态同步问题 ===")
    
    try:
        # 批量更新溯源记录状态
        trace_ids = [issue['trace_id'] for issue in issues]
        
        update_query = """
        UPDATE traceability_record 
        SET status = 2, updated_at = NOW() 
        WHERE id IN ({})
        """.format(','.join(['%s'] * len(trace_ids)))
        
        cursor.execute(update_query, trace_ids)
        updated_count = cursor.rowcount
        
        conn.commit()
        
        print(f"✅ 成功更新 {updated_count} 条溯源记录状态为已发布")
        
        # 验证修复结果
        for issue in issues:
            print(f"  - 溯源码 {issue['trace_code']} 状态已更新")
        
        cursor.close()
        return True
        
    except mysql.connector.Error as e:
        print(f"❌ 修复状态同步问题失败: {e}")
        conn.rollback()
        cursor.close()
        return False

def check_qr_code_sync_issues(conn):
    """检查二维码URL同步问题"""
    cursor = conn.cursor(dictionary=True)
    
    print("\n=== 检查二维码URL同步问题 ===")
    
    query = """
    SELECT 
        p.id as product_id,
        p.name as product_name,
        p.qr_code_url as product_qr_url,
        tr.qr_code_url as record_qr_url,
        tr.trace_code,
        CASE 
            WHEN p.qr_code_url = tr.qr_code_url THEN '已同步'
            WHEN p.qr_code_url IS NULL OR p.qr_code_url = '' THEN '产品缺失'
            WHEN tr.qr_code_url IS NULL OR tr.qr_code_url = '' THEN '记录缺失'
            ELSE '不一致'
        END as sync_status
    FROM product p 
    LEFT JOIN traceability_record tr ON p.id = tr.product_id 
    WHERE p.seller_id IS NOT NULL 
      AND p.source_type = 'seller_upload'
      AND tr.qr_code_url IS NOT NULL 
      AND tr.qr_code_url != ''
      AND (p.qr_code_url IS NULL OR p.qr_code_url = '' OR p.qr_code_url != tr.qr_code_url)
    ORDER BY p.id DESC
    """
    
    cursor.execute(query)
    qr_issues = cursor.fetchall()
    
    if qr_issues:
        print(f"🔍 发现 {len(qr_issues)} 个二维码URL同步问题:")
        for issue in qr_issues:
            print(f"  - 产品ID: {issue['product_id']}, 产品名: {issue['product_name']}")
            print(f"    溯源码: {issue['trace_code']}, 同步状态: {issue['sync_status']}")
    else:
        print("✅ 未发现二维码URL同步问题")
    
    cursor.close()
    return qr_issues

def fix_qr_code_sync_issues(conn, issues):
    """修复二维码URL同步问题"""
    if not issues:
        print("✅ 无需修复二维码URL同步问题")
        return True
    
    cursor = conn.cursor()
    
    print(f"\n=== 开始修复 {len(issues)} 个二维码URL同步问题 ===")
    
    try:
        # 批量更新产品表的二维码URL
        for issue in issues:
            update_query = """
            UPDATE product 
            SET qr_code_url = %s, updated_at = NOW() 
            WHERE id = %s
            """
            cursor.execute(update_query, (issue['record_qr_url'], issue['product_id']))
        
        updated_count = cursor.rowcount
        conn.commit()
        
        print(f"✅ 成功同步 {len(issues)} 个产品的二维码URL")
        
        # 验证修复结果
        for issue in issues:
            print(f"  - 产品 {issue['product_name']} 的二维码URL已同步")
        
        cursor.close()
        return True
        
    except mysql.connector.Error as e:
        print(f"❌ 修复二维码URL同步问题失败: {e}")
        conn.rollback()
        cursor.close()
        return False

def test_traceability_query(conn, trace_code):
    """测试溯源查询功能"""
    cursor = conn.cursor(dictionary=True)
    
    print(f"\n=== 测试溯源查询: {trace_code} ===")
    
    query = """
    SELECT 
        tr.id,
        tr.trace_code,
        tr.product_name,
        tr.status,
        tr.qr_code_url,
        p.name as product_table_name,
        p.status as product_status
    FROM traceability_record tr
    LEFT JOIN product p ON tr.product_id = p.id
    WHERE tr.trace_code = %s
      AND tr.deleted = 0
    """
    
    cursor.execute(query, (trace_code,))
    result = cursor.fetchone()
    
    if result:
        print("✅ 溯源记录查询成功:")
        print(f"  - 溯源码: {result['trace_code']}")
        print(f"  - 产品名: {result['product_name']}")
        print(f"  - 溯源状态: {result['status']} ({'已发布' if result['status'] == 2 else '草稿' if result['status'] == 0 else '其他'})")
        print(f"  - 产品状态: {result['product_status']} ({'上架' if result['product_status'] == 1 else '下架'})")
        print(f"  - 二维码URL: {result['qr_code_url'] or '未设置'}")
        
        if result['status'] == 2:
            print("✅ 溯源记录状态正确，可以正常查询")
        else:
            print("❌ 溯源记录状态不正确，需要修复")
    else:
        print(f"❌ 未找到溯源码 {trace_code} 的记录")
    
    cursor.close()
    return result

def main():
    """主函数"""
    print("🚀 SFAP 溯源状态修复脚本启动")
    print("=" * 50)
    
    # 连接数据库
    conn = connect_database()
    if not conn:
        sys.exit(1)
    
    try:
        # 1. 检查状态同步问题
        status_issues = check_status_sync_issues(conn)
        
        # 2. 修复状态同步问题
        if status_issues:
            fix_status_sync_issues(conn, status_issues)
        
        # 3. 检查二维码URL同步问题
        qr_issues = check_qr_code_sync_issues(conn)
        
        # 4. 修复二维码URL同步问题
        if qr_issues:
            fix_qr_code_sync_issues(conn, qr_issues)
        
        # 5. 测试最新的溯源码
        cursor = conn.cursor(dictionary=True)
        cursor.execute("""
            SELECT trace_code 
            FROM traceability_record 
            WHERE source_type = 'seller_upload' 
              AND deleted = 0 
            ORDER BY created_at DESC 
            LIMIT 1
        """)
        latest_trace = cursor.fetchone()
        cursor.close()
        
        if latest_trace:
            test_traceability_query(conn, latest_trace['trace_code'])
        
        print("\n" + "=" * 50)
        print("✅ 修复脚本执行完成")
        
    except Exception as e:
        print(f"❌ 脚本执行出错: {e}")
    finally:
        conn.close()
        print("🔒 数据库连接已关闭")

if __name__ == "__main__":
    main()