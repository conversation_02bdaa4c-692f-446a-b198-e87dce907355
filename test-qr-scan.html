<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>二维码扫描测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .header p {
            color: #7f8c8d;
            font-size: 16px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        .test-section h3 {
            color: #34495e;
            margin-top: 0;
            margin-bottom: 15px;
        }
        .qr-url {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            word-break: break-all;
            margin: 10px 0;
        }
        .test-link {
            display: inline-block;
            background: #3498db;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px;
            transition: background 0.3s;
        }
        .test-link:hover {
            background: #2980b9;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .instructions {
            background: #e8f4fd;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .instructions h4 {
            margin-top: 0;
            color: #2c3e50;
        }
        .instructions ol {
            margin: 10px 0;
            padding-left: 20px;
        }
        .instructions li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 SFAP 二维码扫描测试</h1>
            <p>测试二维码生成和扫描功能是否正常工作</p>
        </div>

        <div class="instructions">
            <h4>📱 测试说明</h4>
            <ol>
                <li>使用手机扫描二维码或点击测试链接</li>
                <li>检查是否能正确跳转到溯源查询页面</li>
                <li>验证溯源码是否自动填入并开始查询</li>
                <li>确认查询结果是否正确显示</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>🎯 测试用例 1: 标准溯源码</h3>
            <p><strong>溯源码:</strong> SFAPA2401130900500214BB0</p>
            <div class="qr-url">
                二维码URL: http://120.26.140.157:8200/trace/SFAPA2401130900500214BB0
            </div>
            <a href="http://120.26.140.157:8200/trace/SFAPA2401130900500214BB0" 
               class="test-link" target="_blank">🔗 点击测试</a>
            <div class="status warning">
                <strong>预期结果:</strong> 页面应该自动填入溯源码并开始查询，显示产品信息
            </div>
        </div>

        <div class="test-section">
            <h3>🎯 测试用例 2: 另一个溯源码</h3>
            <p><strong>溯源码:</strong> SFAPA24011009002008E8BC9</p>
            <div class="qr-url">
                二维码URL: http://120.26.140.157:8200/trace/SFAPA24011009002008E8BC9
            </div>
            <a href="http://120.26.140.157:8200/trace/SFAPA24011009002008E8BC9" 
               class="test-link" target="_blank">🔗 点击测试</a>
            <div class="status warning">
                <strong>预期结果:</strong> 页面应该自动填入溯源码并开始查询，显示产品信息
            </div>
        </div>

        <div class="test-section">
            <h3>🎯 测试用例 3: 你提到的溯源码</h3>
            <p><strong>溯源码:</strong> SFAPA24011208005001FD1A8</p>
            <div class="qr-url">
                二维码URL: http://120.26.140.157:8200/trace/SFAPA24011208005001FD1A8
            </div>
            <a href="http://120.26.140.157:8200/trace/SFAPA24011208005001FD1A8" 
               class="test-link" target="_blank">🔗 点击测试</a>
            <div class="status warning">
                <strong>预期结果:</strong> 页面应该自动填入溯源码并开始查询，显示产品信息
            </div>
        </div>

        <div class="test-section">
            <h3>🔧 故障排除</h3>
            <div class="status error">
                <strong>如果出现问题:</strong>
                <ul>
                    <li>检查网络连接是否正常</li>
                    <li>确认服务器 120.26.140.157:8200 是否可访问</li>
                    <li>查看浏览器控制台是否有错误信息</li>
                    <li>验证后端服务 120.26.140.157:8081 是否正常运行</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h3>📊 测试结果记录</h3>
            <div id="test-results">
                <p>请手动记录测试结果:</p>
                <div class="status">
                    <strong>测试用例 1:</strong> ⬜ 成功 ⬜ 失败 - 备注: _______________
                </div>
                <div class="status">
                    <strong>测试用例 2:</strong> ⬜ 成功 ⬜ 失败 - 备注: _______________
                </div>
                <div class="status">
                    <strong>测试用例 3:</strong> ⬜ 成功 ⬜ 失败 - 备注: _______________
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🛠️ 技术细节</h3>
            <p><strong>前端地址:</strong> http://120.26.140.157:8200</p>
            <p><strong>后端地址:</strong> http://120.26.140.157:8081</p>
            <p><strong>路由配置:</strong> /trace/:traceCode</p>
            <p><strong>API接口:</strong> /api/traceability/query</p>
            <div class="status success">
                <strong>修复内容:</strong>
                <ul>
                    <li>✅ 修复了NGINX代理超时配置</li>
                    <li>✅ 统一了前端axios超时设置</li>
                    <li>✅ 增强了错误处理机制</li>
                    <li>✅ 确认了路由配置正确</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // 简单的测试脚本
        console.log('二维码扫描测试页面已加载');
        console.log('当前时间:', new Date().toLocaleString());
        
        // 检测是否在移动设备上
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        if (isMobile) {
            console.log('检测到移动设备，建议使用扫码功能测试');
        } else {
            console.log('检测到桌面设备，可以点击链接测试');
        }
    </script>
</body>
</html>
