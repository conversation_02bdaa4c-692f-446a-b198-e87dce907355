#!/bin/bash

# 智慧农业平台API连接测试脚本
# 测试前后端通信是否正常

SERVER_IP="**************"
BACKEND_PORT="8081"
FRONTEND_PORT="8200"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}智慧农业平台API连接测试${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

# 测试函数
test_api() {
    local name=$1
    local url=$2
    local expected_status=$3
    local check_content=$4
    
    echo -n "测试 $name ... "
    
    # 发送请求并获取状态码
    response=$(curl -s -w "%{http_code}" -o /tmp/api_response.txt "$url" 2>/dev/null)
    status_code="${response: -3}"
    
    if [ "$status_code" = "$expected_status" ]; then
        if [ -n "$check_content" ]; then
            # 检查响应内容
            if grep -q "$check_content" /tmp/api_response.txt 2>/dev/null; then
                echo -e "${GREEN}✅ 成功${NC} (HTTP $status_code, 内容正确)"
            else
                echo -e "${YELLOW}⚠️ 部分成功${NC} (HTTP $status_code, 内容异常)"
                echo "    响应内容: $(cat /tmp/api_response.txt | head -c 100)..."
            fi
        else
            echo -e "${GREEN}✅ 成功${NC} (HTTP $status_code)"
        fi
        return 0
    else
        echo -e "${RED}❌ 失败${NC} (HTTP $status_code)"
        if [ -f /tmp/api_response.txt ]; then
            echo "    错误信息: $(cat /tmp/api_response.txt | head -c 200)..."
        fi
        return 1
    fi
}

# 测试WebSocket连接
test_websocket() {
    local name=$1
    local url=$2
    
    echo -n "测试 $name ... "
    
    # 使用websocat或nc测试WebSocket连接（如果可用）
    if command -v websocat >/dev/null 2>&1; then
        timeout 5 websocat "$url" --exit-on-eof < /dev/null >/dev/null 2>&1
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ 连接成功${NC}"
            return 0
        else
            echo -e "${RED}❌ 连接失败${NC}"
            return 1
        fi
    else
        echo -e "${YELLOW}⚠️ 跳过${NC} (需要websocat工具)"
        return 0
    fi
}

echo "🔍 1. 基础连接测试"
echo "===================="

# 测试端口连通性
echo -n "测试后端端口 $BACKEND_PORT ... "
if nc -z $SERVER_IP $BACKEND_PORT 2>/dev/null; then
    echo -e "${GREEN}✅ 开放${NC}"
else
    echo -e "${RED}❌ 关闭${NC}"
fi

echo -n "测试前端端口 $FRONTEND_PORT ... "
if nc -z $SERVER_IP $FRONTEND_PORT 2>/dev/null; then
    echo -e "${GREEN}✅ 开放${NC}"
else
    echo -e "${RED}❌ 关闭${NC}"
fi

echo ""
echo "🚀 2. 后端API测试"
echo "===================="

# 基础API测试
test_api "根路径" "http://$SERVER_IP:$BACKEND_PORT/" "200" "智慧农业辅助平台"
test_api "健康检查" "http://$SERVER_IP:$BACKEND_PORT/health" "200" "UP"
test_api "API信息" "http://$SERVER_IP:$BACKEND_PORT/api" "200" "智慧农业辅助平台API"

echo ""
echo "📰 3. 新闻API测试"
echo "===================="

# 新闻API测试
test_api "新闻列表" "http://$SERVER_IP:$BACKEND_PORT/api/news?page=1&size=5" "200" "data"
test_api "热门新闻" "http://$SERVER_IP:$BACKEND_PORT/api/news/hot?limit=5" "200" "data"
test_api "新闻分类" "http://$SERVER_IP:$BACKEND_PORT/api/news/categories" "200" "data"

echo ""
echo "🛒 4. 商品API测试"
echo "===================="

# 商品API测试
test_api "商品列表" "http://$SERVER_IP:$BACKEND_PORT/api/products?page=1&size=5" "200" "data"
test_api "商品分类" "http://$SERVER_IP:$BACKEND_PORT/api/categories" "200" "data"
test_api "首页数据" "http://$SERVER_IP:$BACKEND_PORT/api/home" "200" "data"

echo ""
echo "👤 5. 用户API测试"
echo "===================="

# 用户API测试（可能需要认证）
test_api "用户信息" "http://$SERVER_IP:$BACKEND_PORT/api/users/profile" "401" ""
test_api "登录页面" "http://$SERVER_IP:$BACKEND_PORT/api/auth/login" "405" ""

echo ""
echo "🔌 6. WebSocket连接测试"
echo "===================="

# WebSocket测试
test_websocket "管理员通知" "ws://$SERVER_IP:$BACKEND_PORT/ws/admin/notifications/1"
test_websocket "销售者通知" "ws://$SERVER_IP:$BACKEND_PORT/ws/seller/notifications/1"

echo ""
echo "🌐 7. CORS跨域测试"
echo "===================="

# CORS测试
echo -n "测试CORS预检请求 ... "
cors_response=$(curl -s -X OPTIONS \
    -H "Origin: http://$SERVER_IP:$FRONTEND_PORT" \
    -H "Access-Control-Request-Method: GET" \
    -H "Access-Control-Request-Headers: Content-Type" \
    -w "%{http_code}" \
    -o /dev/null \
    "http://$SERVER_IP:$BACKEND_PORT/api/news" 2>/dev/null)

if [ "$cors_response" = "200" ] || [ "$cors_response" = "204" ]; then
    echo -e "${GREEN}✅ CORS配置正常${NC} (HTTP $cors_response)"
else
    echo -e "${RED}❌ CORS配置异常${NC} (HTTP $cors_response)"
fi

echo ""
echo "📊 8. 性能测试"
echo "===================="

# 响应时间测试
echo -n "API响应时间测试 ... "
start_time=$(date +%s%N)
curl -s "http://$SERVER_IP:$BACKEND_PORT/api/home" > /dev/null 2>&1
end_time=$(date +%s%N)
response_time=$(( (end_time - start_time) / 1000000 ))

if [ $response_time -lt 1000 ]; then
    echo -e "${GREEN}✅ 响应时间: ${response_time}ms${NC}"
elif [ $response_time -lt 3000 ]; then
    echo -e "${YELLOW}⚠️ 响应时间: ${response_time}ms (稍慢)${NC}"
else
    echo -e "${RED}❌ 响应时间: ${response_time}ms (过慢)${NC}"
fi

echo ""
echo "🔍 9. 前端页面测试"
echo "===================="

# 前端页面测试
test_api "前端首页" "http://$SERVER_IP:$FRONTEND_PORT/" "200" "SFAP"
test_api "前端商城" "http://$SERVER_IP:$FRONTEND_PORT/shop" "200" ""

echo ""
echo "📋 10. 测试总结"
echo "===================="

echo "✅ 测试完成！"
echo ""
echo "🔗 访问地址："
echo "  - 后端API: http://$SERVER_IP:$BACKEND_PORT"
echo "  - 前端应用: http://$SERVER_IP:$FRONTEND_PORT"
echo "  - API文档: http://$SERVER_IP:$BACKEND_PORT/swagger-ui.html"
echo ""
echo "🛠️ 如果发现问题："
echo "  1. 检查服务是否正常运行"
echo "  2. 检查防火墙端口设置"
echo "  3. 查看应用日志文件"
echo "  4. 验证数据库连接"
echo ""
echo "📝 查看日志命令："
echo "  tail -f /www/wwwroot/agriculture/logs/agriculture-mall.log"

# 清理临时文件
rm -f /tmp/api_response.txt
