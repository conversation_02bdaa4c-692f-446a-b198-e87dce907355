-- 数据库初始化和权限设置脚本
-- 在宝塔面板数据库管理中执行此脚本

-- 1. 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS agriculture_mall 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 2. 使用数据库
USE agriculture_mall;

-- 3. 创建专用用户（推荐方式）
-- 删除可能存在的用户
DROP USER IF EXISTS 'agriculture_user'@'localhost';
DROP USER IF EXISTS 'agriculture_user'@'%';

-- 创建新用户
CREATE USER 'agriculture_user'@'localhost' IDENTIFIED BY 'Agriculture@2024!';
CREATE USER 'agriculture_user'@'%' IDENTIFIED BY 'Agriculture@2024!';

-- 授予权限
GRANT ALL PRIVILEGES ON agriculture_mall.* TO 'agriculture_user'@'localhost';
GRANT ALL PRIVILEGES ON agriculture_mall.* TO 'agriculture_user'@'%';

-- 4. 确保root用户权限（备用方案）
-- 更新root用户权限
UPDATE mysql.user SET host='%' WHERE user='root' AND host='localhost';
GRANT ALL PRIVILEGES ON *.* TO 'root'@'%' IDENTIFIED BY 'fan13965711955' WITH GRANT OPTION;
GRANT ALL PRIVILEGES ON *.* TO 'root'@'localhost' IDENTIFIED BY 'fan13965711955' WITH GRANT OPTION;

-- 5. 刷新权限
FLUSH PRIVILEGES;

-- 6. 验证用户创建
SELECT user, host FROM mysql.user WHERE user IN ('root', 'agriculture_user');

-- 7. 验证数据库权限
SHOW GRANTS FOR 'root'@'%';
SHOW GRANTS FOR 'agriculture_user'@'%';

-- 8. 测试连接（可选）
-- SELECT 'Database connection test successful!' as status;
