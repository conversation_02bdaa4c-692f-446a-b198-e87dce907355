# 🐍 SFAP价格预测微服务实现方案

> **基于现有新闻爬取架构的价格预测微服务设计**  
> **架构模式**：混合微服务（Java主服务 + Python专业服务）  
> **参考实现**：现有新闻爬取服务架构  

## 📊 现有架构分析

### 🏗️ 当前新闻服务架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端Vue应用   │◄──►│   Java主服务    │    │  Python爬虫服务 │
│   (端口8080)    │    │   (端口8081)    │    │   (端口5000)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                       │
                                ▼                       ▼
                       ┌─────────────────────────────────────────┐
                       │          MySQL数据库                    │
                       │    (agriculture_news表)                │
                       └─────────────────────────────────────────┘
```

### 📋 现有服务特点
- **Java服务**：处理API接口、业务逻辑、用户管理
- **Python服务**：专门负责数据采集和处理
- **共享数据库**：两个服务操作同一数据库
- **独立部署**：各服务可独立启动和维护

---

## 🎯 价格预测微服务设计

### 🏗️ 目标架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端Vue应用   │◄──►│   Java主服务    │◄──►│ Python价格服务  │
│   (端口8080)    │    │   (端口8081)    │    │   (端口5001)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                       │
                                ▼                       ▼
                       ┌─────────────────────────────────────────┐
                       │          MySQL数据库                    │
                       │  (product_price_history + 新增表)       │
                       └─────────────────────────────────────────┘
```

### 📁 目录结构设计
```
SFAP/
├── backend/
│   ├── main/                          # Java主服务
│   │   └── src/main/java/com/agriculture/
│   │       ├── controller/
│   │       │   └── PricePredictionController.java
│   │       └── service/
│   │           └── PythonPriceServiceClient.java
│   └── python-price/                  # Python价格预测微服务
│       ├── app.py                     # Flask主应用
│       ├── crawler/                   # 价格数据爬虫
│       │   ├── huinong_spider.py
│       │   └── data_processor.py
│       ├── models/                    # AI预测模型
│       │   ├── arima_model.py
│       │   ├── lstm_model.py
│       │   └── ensemble_model.py
│       ├── api/                       # API接口
│       │   ├── prediction_api.py
│       │   └── crawler_api.py
│       ├── utils/
│       │   ├── db_manager.py
│       │   └── redis_client.py
│       └── requirements.txt
```

---

## 🔧 具体实现方案

### 1. **Python价格预测微服务**

#### 📄 主应用文件 (app.py)
```python
from flask import Flask, request, jsonify
from flask_cors import CORS
import logging
import threading
import time
from datetime import datetime, timedelta
from db_manager import get_db_manager
from models.ensemble_model import EnsemblePredictor
from crawler.huinong_spider import HuiNongPriceCrawler

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# 初始化组件
db_manager = get_db_manager()
price_predictor = EnsemblePredictor()
price_crawler = HuiNongPriceCrawler()

# 服务状态
service_status = {
    'crawler': {'is_running': False, 'last_run': None, 'next_run': None},
    'predictor': {'is_ready': False, 'last_train': None, 'models_loaded': False}
}

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'service_status': service_status
    })

@app.route('/api/price/predict', methods=['POST'])
def predict_price():
    """价格预测API"""
    try:
        data = request.json
        product_id = data.get('product_id')
        days = data.get('days', 7)
        
        # 获取历史数据
        historical_data = db_manager.get_price_history(product_id)
        
        if len(historical_data) < 30:
            return jsonify({
                'success': False,
                'error': '历史数据不足，需要至少30天数据'
            }), 400
        
        # 执行预测
        result = price_predictor.predict(historical_data, days)
        
        return jsonify({
            'success': True,
            'data': result,
            'product_id': product_id
        })
    
    except Exception as e:
        logger.error(f"价格预测失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/price/crawl', methods=['POST'])
def trigger_price_crawl():
    """触发价格数据爬取"""
    if service_status['crawler']['is_running']:
        return jsonify({
            'success': False,
            'error': '爬虫已在运行中'
        }), 400
    
    # 在新线程中运行爬虫
    threading.Thread(target=run_price_crawler).start()
    
    return jsonify({
        'success': True,
        'message': '价格爬虫启动成功'
    })

def run_price_crawler():
    """运行价格爬虫"""
    global service_status
    
    try:
        service_status['crawler']['is_running'] = True
        service_status['crawler']['last_run'] = datetime.now()
        
        # 执行爬虫
        price_data = price_crawler.crawl_price_data()
        
        # 保存数据
        for data in price_data:
            db_manager.save_price_data(data)
        
        logger.info(f"成功爬取 {len(price_data)} 条价格数据")
        
    except Exception as e:
        logger.error(f"价格爬虫运行失败: {str(e)}")
    finally:
        service_status['crawler']['is_running'] = False
        service_status['crawler']['next_run'] = datetime.now() + timedelta(hours=1)

def schedule_tasks():
    """定时任务调度"""
    while True:
        now = datetime.now()
        
        # 检查是否需要运行爬虫
        crawler_next_run = service_status['crawler'].get('next_run')
        if (crawler_next_run and now >= crawler_next_run and 
            not service_status['crawler']['is_running']):
            threading.Thread(target=run_price_crawler).start()
        
        # 检查是否需要重新训练模型
        last_train = service_status['predictor'].get('last_train')
        if (not last_train or 
            (now - last_train).days >= 7):  # 每周重新训练
            threading.Thread(target=retrain_models).start()
        
        time.sleep(300)  # 每5分钟检查一次

def retrain_models():
    """重新训练预测模型"""
    try:
        logger.info("开始重新训练预测模型")
        
        # 获取所有产品的历史数据
        all_products = db_manager.get_all_products()
        
        for product in all_products:
            historical_data = db_manager.get_price_history(product['id'])
            if len(historical_data) >= 30:
                price_predictor.train_for_product(product['id'], historical_data)
        
        service_status['predictor']['last_train'] = datetime.now()
        service_status['predictor']['models_loaded'] = True
        service_status['predictor']['is_ready'] = True
        
        logger.info("模型重新训练完成")
        
    except Exception as e:
        logger.error(f"模型训练失败: {str(e)}")

if __name__ == '__main__':
    # 启动定时任务
    scheduler_thread = threading.Thread(target=schedule_tasks, daemon=True)
    scheduler_thread.start()
    
    # 启动Flask应用
    app.run(host='0.0.0.0', port=5001)
```

#### 🕷️ 价格爬虫实现 (crawler/huinong_spider.py)
```python
import requests
import time
import random
from bs4 import BeautifulSoup
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class HuiNongPriceCrawler:
    def __init__(self):
        self.base_url = "https://www.cnhnb.com/hangqing/"
        self.headers = {
            'User-Agent': 'SFAP-Research-Bot/1.0 (Academic Research)',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
    
    def crawl_price_data(self):
        """爬取价格数据"""
        price_data = []
        
        try:
            logger.info("开始爬取惠农网价格数据")
            
            # 请求主页面
            response = self.session.get(self.base_url, timeout=30)
            response.raise_for_status()
            
            # 解析页面
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 查找价格数据项
            price_items = soup.find_all('div', class_='price-item')
            
            for item in price_items:
                try:
                    # 提取数据
                    product_name = self.extract_text(item, '.product-name')
                    price = self.extract_price(item, '.price')
                    unit = self.extract_text(item, '.unit')
                    market = self.extract_text(item, '.market')
                    region = self.extract_text(item, '.region')
                    
                    if product_name and price:
                        price_data.append({
                            'product_name': product_name,
                            'price': price,
                            'unit': unit or '斤',
                            'market': market or '综合市场',
                            'region': region or '全国',
                            'source': 'huinong',
                            'crawl_time': datetime.now()
                        })
                
                except Exception as e:
                    logger.warning(f"解析价格项失败: {str(e)}")
                    continue
                
                # 遵守爬取间隔
                time.sleep(random.uniform(8, 12))
            
            logger.info(f"成功爬取 {len(price_data)} 条价格数据")
            
        except Exception as e:
            logger.error(f"爬取价格数据失败: {str(e)}")
        
        return price_data
    
    def extract_text(self, element, selector):
        """提取文本内容"""
        try:
            found = element.select_one(selector)
            return found.get_text(strip=True) if found else None
        except:
            return None
    
    def extract_price(self, element, selector):
        """提取价格数值"""
        try:
            price_text = self.extract_text(element, selector)
            if price_text:
                # 提取数字
                import re
                price_match = re.search(r'(\d+\.?\d*)', price_text)
                return float(price_match.group(1)) if price_match else None
        except:
            return None
```

### 2. **Java服务集成**

#### ☕ Python服务客户端 (PythonPriceServiceClient.java)
```java
@Service
public class PythonPriceServiceClient {
    
    @Value("${python.price.service.url:http://localhost:5001}")
    private String pythonServiceUrl;
    
    @Autowired
    private RestTemplate restTemplate;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    private static final Logger logger = LoggerFactory.getLogger(PythonPriceServiceClient.class);
    
    public PredictionResult getPricePrediction(Long productId, int days) {
        String cacheKey = "price_prediction:" + productId + ":" + days;
        
        // 检查缓存
        PredictionResult cached = (PredictionResult) redisTemplate.opsForValue().get(cacheKey);
        if (cached != null) {
            logger.info("从缓存获取预测结果: productId={}, days={}", productId, days);
            return cached;
        }
        
        try {
            // 调用Python服务
            Map<String, Object> request = new HashMap<>();
            request.put("product_id", productId);
            request.put("days", days);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(request, headers);
            
            ResponseEntity<PythonPredictionResponse> response = restTemplate.postForEntity(
                pythonServiceUrl + "/api/price/predict",
                entity,
                PythonPredictionResponse.class
            );
            
            if (response.getBody() != null && response.getBody().isSuccess()) {
                PredictionResult result = response.getBody().getData();
                
                // 缓存结果（30分钟）
                redisTemplate.opsForValue().set(cacheKey, result, 1800, TimeUnit.SECONDS);
                
                logger.info("Python预测服务调用成功: productId={}, days={}", productId, days);
                return result;
            } else {
                throw new RuntimeException("Python预测服务返回错误: " + 
                    (response.getBody() != null ? response.getBody().getError() : "未知错误"));
            }
            
        } catch (Exception e) {
            logger.error("调用Python预测服务失败: productId={}, days={}", productId, days, e);
            
            // 降级到Java简单预测
            return fallbackPrediction(productId, days);
        }
    }
    
    public boolean triggerPriceCrawl() {
        try {
            ResponseEntity<Map> response = restTemplate.postForEntity(
                pythonServiceUrl + "/api/price/crawl",
                null,
                Map.class
            );
            
            return response.getStatusCode().is2xxSuccessful();
            
        } catch (Exception e) {
            logger.error("触发价格爬虫失败", e);
            return false;
        }
    }
    
    private PredictionResult fallbackPrediction(Long productId, int days) {
        // 使用Java简单算法作为降级方案
        logger.warn("使用Java简单预测算法作为降级方案: productId={}, days={}", productId, days);
        
        // 这里调用现有的Java预测服务
        return simplePredictionService.predict(productId, days);
    }
}
```

#### 🎮 价格预测控制器扩展 (PricePredictionController.java)
```java
@RestController
@RequestMapping("/api/price")
public class PricePredictionController {
    
    @Autowired
    private PythonPriceServiceClient pythonPriceClient;
    
    @Autowired
    private PriceForecastService priceForecastService;
    
    @ApiOperation("获取AI价格预测")
    @PostMapping("/predict/ai")
    public Result<PredictionResult> getAIPrediction(
            @RequestParam Long productId,
            @RequestParam(defaultValue = "7") int days) {
        
        try {
            PredictionResult result = pythonPriceClient.getPricePrediction(productId, days);
            return Result.ok(result);
        } catch (Exception e) {
            return Result.fail("AI预测失败: " + e.getMessage());
        }
    }
    
    @ApiOperation("对比预测结果")
    @PostMapping("/predict/compare")
    public Result<Map<String, Object>> comparePredictions(
            @RequestParam Long productId,
            @RequestParam(defaultValue = "7") int days) {
        
        try {
            // 获取AI预测结果
            PredictionResult aiResult = pythonPriceClient.getPricePrediction(productId, days);
            
            // 获取Java简单预测结果
            List<PriceForecast> javaResult = priceForecastService.getPriceForecast(productId, days);
            
            Map<String, Object> comparison = new HashMap<>();
            comparison.put("ai_prediction", aiResult);
            comparison.put("simple_prediction", javaResult);
            comparison.put("comparison_time", LocalDateTime.now());
            
            return Result.ok(comparison);
            
        } catch (Exception e) {
            return Result.fail("预测对比失败: " + e.getMessage());
        }
    }
    
    @ApiOperation("触发价格数据爬取")
    @PostMapping("/crawl/trigger")
    public Result<String> triggerPriceCrawl() {
        try {
            boolean success = pythonPriceClient.triggerPriceCrawl();
            
            if (success) {
                return Result.ok("价格爬虫启动成功");
            } else {
                return Result.fail("价格爬虫启动失败");
            }
            
        } catch (Exception e) {
            return Result.fail("触发爬虫失败: " + e.getMessage());
        }
    }
}
```

---

## 🚀 部署和运行

### 📋 启动步骤
1. **启动MySQL数据库**
2. **启动Java主服务** (端口8081)
3. **启动Python价格服务** (端口5001)
4. **启动前端应用** (端口8080)

### 🔧 配置文件
```yaml
# application.yml (Java服务配置)
python:
  price:
    service:
      url: http://localhost:5001
```

```python
# requirements.txt (Python服务依赖)
flask>=2.3.3
flask-cors>=4.0.0
scrapy>=2.11.0
tensorflow>=2.13.0
statsmodels>=0.14.0
scikit-learn>=1.3.2
pandas>=2.1.3
numpy>=1.24.3
mysql-connector-python>=8.0.0
redis>=5.0.1
requests>=2.31.0
beautifulsoup4>=4.12.2
```

这个方案完全基于现有的新闻爬取架构，确保了技术栈的一致性和可维护性。
