# 🐍 SFAP Python AI微服务技术方案

> **项目名称**：SFAP农品汇平台Python AI预测和数据采集微服务  
> **创建时间**：2025-01-19  
> **技术栈**：Python + Flask + TensorFlow + Scrapy + Docker  
> **部署方式**：微服务架构 + 容器化部署

## 🏗️ 整体架构设计

### 📊 系统架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   SFAP前端      │    │   Java后端      │    │  Python微服务   │
│   (Vue 2)       │◄──►│  (Spring Boot)  │◄──►│   (Flask API)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                       │
                                ▼                       ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │   MySQL数据库   │    │   Redis缓存     │
                       └─────────────────┘    └─────────────────┘
```

### 🔧 微服务组件
1. **价格数据采集服务** (price-crawler)
2. **AI预测模型服务** (price-prediction)
3. **API网关服务** (api-gateway)
4. **监控服务** (monitoring)

---

## 🕷️ 价格数据采集微服务

### 📁 目录结构
```
python-services/
├── price-crawler/
│   ├── app.py                 # Flask应用主文件
│   ├── crawler/
│   │   ├── __init__.py
│   │   ├── huinong_spider.py  # 惠农网爬虫
│   │   ├── data_processor.py  # 数据处理
│   │   └── scheduler.py       # 定时任务
│   ├── models/
│   │   ├── __init__.py
│   │   └── price_data.py      # 数据模型
│   ├── utils/
│   │   ├── __init__.py
│   │   ├── redis_client.py    # Redis客户端
│   │   └── logger.py          # 日志工具
│   ├── config/
│   │   ├── __init__.py
│   │   └── settings.py        # 配置文件
│   ├── requirements.txt       # 依赖包
│   ├── Dockerfile            # Docker配置
│   └── docker-compose.yml    # 容器编排
```

### 🛠️ 核心技术栈
```python
# requirements.txt
Flask==2.3.3
Scrapy==2.11.0
requests==2.31.0
beautifulsoup4==4.12.2
redis==5.0.1
celery==5.3.4
APScheduler==3.10.4
pandas==2.1.3
numpy==1.24.3
python-dotenv==1.0.0
gunicorn==21.2.0
```

### 🕸️ 爬虫实现方案
```python
# huinong_spider.py
import scrapy
import time
import random
from scrapy.http import Request

class HuiNongPriceSpider(scrapy.Spider):
    name = 'huinong_price'
    allowed_domains = ['cnhnb.com']
    start_urls = ['https://www.cnhnb.com/hangqing/']
    
    custom_settings = {
        'DOWNLOAD_DELAY': 8,  # 8秒间隔
        'RANDOMIZE_DOWNLOAD_DELAY': 0.5,
        'USER_AGENT': 'SFAP-Research-Bot/1.0 (Academic Research)',
        'ROBOTSTXT_OBEY': True,
    }
    
    def parse(self, response):
        # 解析价格数据
        price_items = response.css('.price-item')
        for item in price_items:
            yield {
                'product_name': item.css('.name::text').get(),
                'price': item.css('.price::text').get(),
                'unit': item.css('.unit::text').get(),
                'market': item.css('.market::text').get(),
                'date': item.css('.date::text').get(),
                'source': 'huinong',
                'crawl_time': time.time()
            }
```

### 🔄 数据处理流程
```python
# data_processor.py
class PriceDataProcessor:
    def __init__(self):
        self.redis_client = RedisClient()
    
    def process_raw_data(self, raw_data):
        """处理原始爬取数据"""
        # 1. 数据清洗
        cleaned_data = self.clean_data(raw_data)
        
        # 2. 数据标准化
        normalized_data = self.normalize_data(cleaned_data)
        
        # 3. 数据验证
        validated_data = self.validate_data(normalized_data)
        
        # 4. 存储到Redis
        self.store_to_redis(validated_data)
        
        return validated_data
    
    def clean_data(self, data):
        """数据清洗"""
        # 去除异常值、格式化价格等
        pass
    
    def normalize_data(self, data):
        """数据标准化"""
        # 统一单位、格式等
        pass
```

---

## 🤖 AI预测模型微服务

### 📁 目录结构
```
python-services/
├── price-prediction/
│   ├── app.py                    # Flask应用主文件
│   ├── models/
│   │   ├── __init__.py
│   │   ├── arima_model.py        # ARIMA模型
│   │   ├── lstm_model.py         # LSTM模型
│   │   ├── ensemble_model.py     # 集成模型
│   │   └── model_trainer.py      # 模型训练
│   ├── data/
│   │   ├── __init__.py
│   │   ├── preprocessor.py       # 数据预处理
│   │   └── feature_engineer.py   # 特征工程
│   ├── api/
│   │   ├── __init__.py
│   │   ├── prediction_api.py     # 预测API
│   │   └── model_api.py          # 模型管理API
│   ├── utils/
│   │   ├── __init__.py
│   │   ├── metrics.py            # 评估指标
│   │   └── visualization.py      # 可视化工具
│   ├── saved_models/             # 保存的模型
│   ├── requirements.txt
│   ├── Dockerfile
│   └── docker-compose.yml
```

### 🧠 AI技术栈
```python
# requirements.txt
Flask==2.3.3
tensorflow==2.13.0
keras==2.13.1
statsmodels==0.14.0
scikit-learn==1.3.2
pandas==2.1.3
numpy==1.24.3
matplotlib==3.8.2
seaborn==0.13.0
joblib==1.3.2
redis==5.0.1
gunicorn==21.2.0
```

### 📈 ARIMA模型实现
```python
# arima_model.py
import pandas as pd
import numpy as np
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.stattools import adfuller
from statsmodels.graphics.tsaplots import plot_acf, plot_pacf

class ARIMAPredictor:
    def __init__(self):
        self.model = None
        self.fitted_model = None
    
    def prepare_data(self, price_data):
        """数据预处理"""
        # 转换为时间序列
        ts = pd.Series(price_data['price'].values, 
                      index=pd.to_datetime(price_data['date']))
        
        # 平稳性检验
        if not self.is_stationary(ts):
            ts = self.make_stationary(ts)
        
        return ts
    
    def is_stationary(self, ts):
        """平稳性检验"""
        result = adfuller(ts)
        return result[1] <= 0.05
    
    def auto_arima(self, ts):
        """自动选择ARIMA参数"""
        best_aic = np.inf
        best_params = None
        
        for p in range(0, 4):
            for d in range(0, 2):
                for q in range(0, 4):
                    try:
                        model = ARIMA(ts, order=(p, d, q))
                        fitted = model.fit()
                        if fitted.aic < best_aic:
                            best_aic = fitted.aic
                            best_params = (p, d, q)
                    except:
                        continue
        
        return best_params
    
    def train(self, price_data):
        """训练ARIMA模型"""
        ts = self.prepare_data(price_data)
        
        # 自动选择参数
        params = self.auto_arima(ts)
        
        # 训练模型
        self.model = ARIMA(ts, order=params)
        self.fitted_model = self.model.fit()
        
        return self.fitted_model
    
    def predict(self, steps=7):
        """预测未来价格"""
        if self.fitted_model is None:
            raise ValueError("模型未训练")
        
        forecast = self.fitted_model.forecast(steps=steps)
        confidence_intervals = self.fitted_model.get_forecast(steps=steps).conf_int()
        
        return {
            'predictions': forecast.tolist(),
            'confidence_intervals': confidence_intervals.values.tolist(),
            'model_type': 'ARIMA'
        }
```

### 🧠 LSTM模型实现
```python
# lstm_model.py
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout
from sklearn.preprocessing import MinMaxScaler
import numpy as np

class LSTMPredictor:
    def __init__(self, sequence_length=30):
        self.sequence_length = sequence_length
        self.model = None
        self.scaler = MinMaxScaler()
    
    def prepare_data(self, price_data):
        """准备LSTM训练数据"""
        # 数据标准化
        scaled_data = self.scaler.fit_transform(
            price_data['price'].values.reshape(-1, 1)
        )
        
        # 创建序列数据
        X, y = [], []
        for i in range(self.sequence_length, len(scaled_data)):
            X.append(scaled_data[i-self.sequence_length:i, 0])
            y.append(scaled_data[i, 0])
        
        return np.array(X), np.array(y)
    
    def build_model(self):
        """构建LSTM模型"""
        model = Sequential([
            LSTM(50, return_sequences=True, 
                 input_shape=(self.sequence_length, 1)),
            Dropout(0.2),
            LSTM(50, return_sequences=True),
            Dropout(0.2),
            LSTM(50),
            Dropout(0.2),
            Dense(1)
        ])
        
        model.compile(optimizer='adam', loss='mse', metrics=['mae'])
        return model
    
    def train(self, price_data, epochs=100, batch_size=32):
        """训练LSTM模型"""
        X, y = self.prepare_data(price_data)
        
        # 重塑数据
        X = X.reshape((X.shape[0], X.shape[1], 1))
        
        # 构建模型
        self.model = self.build_model()
        
        # 训练模型
        history = self.model.fit(
            X, y, 
            epochs=epochs, 
            batch_size=batch_size,
            validation_split=0.2,
            verbose=1
        )
        
        return history
    
    def predict(self, recent_data, steps=7):
        """预测未来价格"""
        if self.model is None:
            raise ValueError("模型未训练")
        
        # 准备输入数据
        last_sequence = recent_data[-self.sequence_length:]
        scaled_sequence = self.scaler.transform(
            last_sequence.reshape(-1, 1)
        )
        
        predictions = []
        current_sequence = scaled_sequence.copy()
        
        for _ in range(steps):
            # 预测下一个值
            next_pred = self.model.predict(
                current_sequence.reshape(1, self.sequence_length, 1)
            )
            predictions.append(next_pred[0, 0])
            
            # 更新序列
            current_sequence = np.roll(current_sequence, -1)
            current_sequence[-1] = next_pred
        
        # 反标准化
        predictions = self.scaler.inverse_transform(
            np.array(predictions).reshape(-1, 1)
        ).flatten()
        
        return {
            'predictions': predictions.tolist(),
            'model_type': 'LSTM'
        }
```

### 🎯 集成模型实现
```python
# ensemble_model.py
class EnsemblePredictor:
    def __init__(self):
        self.arima_model = ARIMAPredictor()
        self.lstm_model = LSTMPredictor()
        self.weights = {'arima': 0.4, 'lstm': 0.6}
    
    def train(self, price_data):
        """训练所有模型"""
        # 训练ARIMA模型
        self.arima_model.train(price_data)
        
        # 训练LSTM模型
        self.lstm_model.train(price_data)
    
    def predict(self, recent_data, steps=7):
        """集成预测"""
        # 获取各模型预测结果
        arima_pred = self.arima_model.predict(steps)
        lstm_pred = self.lstm_model.predict(recent_data, steps)
        
        # 加权融合
        ensemble_pred = []
        for i in range(steps):
            weighted_pred = (
                self.weights['arima'] * arima_pred['predictions'][i] +
                self.weights['lstm'] * lstm_pred['predictions'][i]
            )
            ensemble_pred.append(weighted_pred)
        
        return {
            'predictions': ensemble_pred,
            'arima_predictions': arima_pred['predictions'],
            'lstm_predictions': lstm_pred['predictions'],
            'model_type': 'Ensemble',
            'weights': self.weights
        }
```

---

## 🔌 Java-Python集成方案

### 📡 Python API接口设计
```python
# prediction_api.py
from flask import Flask, request, jsonify
from models.ensemble_model import EnsemblePredictor

app = Flask(__name__)
predictor = EnsemblePredictor()

@app.route('/api/predict', methods=['POST'])
def predict_price():
    """价格预测API"""
    data = request.json
    product_id = data.get('product_id')
    days = data.get('days', 7)
    
    try:
        # 获取历史数据
        historical_data = get_historical_data(product_id)
        
        # 执行预测
        result = predictor.predict(historical_data, days)
        
        return jsonify({
            'success': True,
            'data': result,
            'product_id': product_id
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/retrain', methods=['POST'])
def retrain_models():
    """重新训练模型"""
    data = request.json
    product_id = data.get('product_id')
    
    try:
        # 获取训练数据
        training_data = get_training_data(product_id)
        
        # 重新训练
        predictor.train(training_data)
        
        return jsonify({
            'success': True,
            'message': '模型训练完成'
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
```

### ☕ Java客户端实现
```java
// PythonServiceClient.java
@Service
public class PythonServiceClient {
    
    @Value("${python.service.url}")
    private String pythonServiceUrl;
    
    @Autowired
    private RestTemplate restTemplate;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    public PredictionResult getPricePrediction(Long productId, int days) {
        String cacheKey = "prediction:" + productId + ":" + days;
        
        // 先检查缓存
        PredictionResult cached = (PredictionResult) redisTemplate.opsForValue().get(cacheKey);
        if (cached != null) {
            return cached;
        }
        
        try {
            // 调用Python服务
            Map<String, Object> request = new HashMap<>();
            request.put("product_id", productId);
            request.put("days", days);
            
            ResponseEntity<PredictionResponse> response = restTemplate.postForEntity(
                pythonServiceUrl + "/api/predict",
                request,
                PredictionResponse.class
            );
            
            if (response.getBody().isSuccess()) {
                PredictionResult result = response.getBody().getData();
                
                // 缓存结果（1小时）
                redisTemplate.opsForValue().set(cacheKey, result, 3600, TimeUnit.SECONDS);
                
                return result;
            } else {
                throw new RuntimeException("Python服务预测失败: " + response.getBody().getError());
            }
            
        } catch (Exception e) {
            log.error("调用Python预测服务失败", e);
            
            // 降级到Java简单预测
            return fallbackPrediction(productId, days);
        }
    }
    
    private PredictionResult fallbackPrediction(Long productId, int days) {
        // 使用Java简单算法作为降级方案
        return simplePredictionService.predict(productId, days);
    }
}
```

这个技术方案提供了完整的Python AI微服务架构，包括专业的ARIMA和LSTM模型实现，以及与Java后端的无缝集成。通过这种架构，SFAP平台将获得显著提升的价格预测能力。

请确认这个Python微服务方案是否符合您的要求，我可以继续完善具体的实现细节。