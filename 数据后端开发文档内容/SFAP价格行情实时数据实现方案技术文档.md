# SFAP农品汇平台价格行情和市场动态数据中心实时数据实现方案技术文档

## 1. 项目概述

### 1.1 平台架构
SFAP农品汇平台采用前后端分离架构：
- **前端**：Vue 2 + Element UI + ECharts + SCSS
- **后端**：Spring Boot + MyBatis Plus + MySQL
- **缓存**：Redis
- **消息队列**：RabbitMQ（待集成）

### 1.2 当前价格行情功能模块
基于代码分析，平台已具备以下价格相关功能：
- 价格预测控制器（`PriceForecastController`）
- 价格指数控制器（`PriceIndexController`）
- 价格预测服务实现（`PriceForecastServiceImpl`）
- 前端市场分析页面（`Market.vue`）
- 价格趋势图表组件（`PriceTrendChart.vue`）

## 2. 现状分析

### 2.1 数据库现状

#### 2.1.1 现有表结构
```sql
CREATE TABLE `product_price_history` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '价格历史ID',
    `product_id` bigint NOT NULL COMMENT '商品ID',
    `price` decimal(10,2) NOT NULL COMMENT '价格',
    `original_price` decimal(10,2) DEFAULT NULL COMMENT '原价',
    `change_reason` varchar(100) DEFAULT NULL COMMENT '变价原因',
    `effective_date` datetime NOT NULL COMMENT '生效时间',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_effective_date` (`effective_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品价格历史表'
```

#### 2.1.2 实体类定义
```java
@Data
public class ProductPrice implements Serializable {
    private Long id;
    private Long productId;
    private BigDecimal price;
    private Date date;
    private Long marketId;
    private String marketName;
    private Integer volume;
    private String remark;
    private Date createdAt;
    private Date updatedAt;
}
```

### 2.2 后端API现状

#### 2.2.1 价格预测控制器
```java
@RestController
@RequestMapping("/api/price-forecast")
public class PriceForecastController {
    
    @GetMapping("/{productId}")
    public Result<List<PriceForecastModel.PriceForecast>> getPriceForecast(
            @PathVariable Long productId, 
            @RequestParam(defaultValue = "7") Integer days) {
        // 获取价格预测数据
        List<PriceForecastModel.PriceForecast> forecasts = 
            priceForecastService.getPriceForecast(productId, days);
        return Result.ok(forecasts);
    }
}
```

#### 2.2.2 价格预测服务实现
```java
@Service
public class PriceForecastServiceImpl implements PriceForecastService {
    
    // 外部因素数据（静态配置）
    private final Map<String, Double> externalFactors = new HashMap<String, Double>() {{
        put("天气", 0.3);     // 默认天气影响
        put("供应", 0.5);     // 默认供应影响
        put("需求", 0.4);     // 默认需求影响
        put("政策", 0.2);     // 默认政策影响
        put("季节性", 0.6);   // 默认季节性影响
    }};
}
```

### 2.3 前端现状

#### 2.3.1 静态数据问题
```javascript
// 模拟农产品价格数据
const mockPriceData = [
  {
    id: 1,
    name: '优质大米',
    currentPrice: 2.5,
    previousPrice: 2.2,
    unit: '元/kg',
    change: 0.3,
    changeRate: 13.64,
    trend: 'up',
    volume: 1350,
    description: '本地优质大米，口感好，品质高',
    category: '粮食',
    updateTime: '2024-03-28'
  }
  // ... 更多静态数据
]
```

#### 2.3.2 前端API服务
```javascript
export const priceForecastService = {
  getPriceForecast(productId, days = 7) {
    return request({
      url: `${BASE_URL}/forecast`,
      method: 'get',
      params: { productId, days }
    })
  },
  
  getPredictionConfidence(productId, days = 7) {
    return request({
      url: `${BASE_URL}/confidence`,
      method: 'get',
      params: { productId, days }
    })
  }
}
```

## 3. 问题识别

### 3.1 核心问题

#### 3.1.1 数据源问题
- **静态数据依赖**：前端使用硬编码的模拟数据，无法反映真实市场情况
- **数据更新滞后**：缺乏自动化数据采集机制，数据时效性差
- **数据覆盖不全**：仅有少量农产品的价格数据，品类覆盖不足

#### 3.1.2 技术架构问题
- **缺乏实时更新机制**：没有定时任务或消息队列支持数据实时更新
- **预测算法简单**：当前预测逻辑过于简化，准确性低
- **缓存策略缺失**：没有有效的缓存机制，影响系统性能

#### 3.1.3 业务功能问题
- **地域性支持不足**：缺乏不同地区的价格差异化展示
- **季节性分析缺失**：没有考虑农产品的季节性价格波动
- **预警机制缺失**：缺乏价格异常波动的预警功能

## 4. 实时数据解决方案

### 4.1 数据源接入方案

#### 4.1.1 多渠道数据采集架构

```mermaid
graph TD
    A[数据采集层] --> B[农业部API]
    A --> C[批发市场爬虫]
    A --> D[用户上报数据]
    A --> E[第三方数据源]
    
    B --> F[数据清洗服务]
    C --> F
    D --> F
    E --> F
    
    F --> G[数据验证服务]
    G --> H[数据存储层]
    H --> I[Redis缓存]
    H --> J[MySQL数据库]
    
    I --> K[实时API服务]
    J --> K
    K --> L[前端展示]
```

#### 4.1.2 数据采集服务实现

**新增数据采集服务类**：

```java
@Service
@Slf4j
public class PriceDataCollectionService {
    
    @Autowired
    private RestTemplate restTemplate;
    
    @Autowired
    private PriceDataRepository priceDataRepository;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 从农业部API获取价格数据
     */
    @Scheduled(fixedRate = 3600000) // 每小时执行一次
    public void collectFromAgricultureAPI() {
        try {
            log.info("开始从农业部API采集价格数据");
            
            // 调用农业部价格API
            String apiUrl = "http://zdscxx.moa.gov.cn:8080/nyb/pc/search.jsp";
            ResponseEntity<String> response = restTemplate.getForEntity(apiUrl, String.class);
            
            if (response.getStatusCode() == HttpStatus.OK) {
                List<PriceData> priceDataList = parseAgricultureAPIResponse(response.getBody());
                savePriceData(priceDataList, "农业部API");
                
                // 更新Redis缓存
                updatePriceCache(priceDataList);
                
                log.info("成功采集到 {} 条价格数据", priceDataList.size());
            }
            
        } catch (Exception e) {
            log.error("从农业部API采集数据失败", e);
        }
    }
    
    /**
     * 爬取批发市场价格数据
     */
    @Scheduled(fixedRate = 1800000) // 每30分钟执行一次
    public void collectFromWholesaleMarkets() {
        try {
            log.info("开始爬取批发市场价格数据");
            
            // 主要批发市场列表
            List<String> marketUrls = Arrays.asList(
                "http://www.xinfadi.com.cn/marketanalysis/0/list/1.shtml", // 新发地
                "http://www.cawa.org.cn/", // 中国农产品批发市场网
                "http://www.shucaiwang.org/" // 蔬菜网
            );
            
            for (String marketUrl : marketUrls) {
                List<PriceData> marketData = crawlMarketData(marketUrl);
                savePriceData(marketData, "批发市场爬虫");
                updatePriceCache(marketData);
            }
            
        } catch (Exception e) {
            log.error("爬取批发市场数据失败", e);
        }
    }
    
    /**
     * 处理用户上报的价格数据
     */
    public void processUserReportedData(UserPriceReport report) {
        try {
            // 数据验证
            if (validateUserReport(report)) {
                PriceData priceData = convertUserReportToPriceData(report);
                savePriceData(Arrays.asList(priceData), "用户上报");
                
                // 实时更新缓存
                updatePriceCache(Arrays.asList(priceData));
                
                // 发送WebSocket通知
                sendRealTimeUpdate(priceData);
            }
            
        } catch (Exception e) {
            log.error("处理用户上报数据失败", e);
        }
    }
    
    private void updatePriceCache(List<PriceData> priceDataList) {
        for (PriceData data : priceDataList) {
            String cacheKey = "price:product:" + data.getProductId();
            redisTemplate.opsForValue().set(cacheKey, data, Duration.ofHours(1));
        }
    }
}
```

### 4.2 数据更新机制

#### 4.2.1 Spring Batch定时任务配置

```java
@Configuration
@EnableBatchProcessing
@EnableScheduling
public class PriceDataBatchConfig {
    
    @Bean
    public Job priceDataUpdateJob(JobBuilderFactory jobBuilderFactory,
                                  Step priceDataUpdateStep) {
        return jobBuilderFactory.get("priceDataUpdateJob")
                .incrementer(new RunIdIncrementer())
                .start(priceDataUpdateStep)
                .build();
    }
    
    @Bean
    public Step priceDataUpdateStep(StepBuilderFactory stepBuilderFactory,
                                    PriceDataReader reader,
                                    PriceDataProcessor processor,
                                    PriceDataWriter writer) {
        return stepBuilderFactory.get("priceDataUpdateStep")
                .<RawPriceData, ProcessedPriceData>chunk(100)
                .reader(reader)
                .processor(processor)
                .writer(writer)
                .build();
    }
    
    @Component
    public class PriceDataProcessor implements ItemProcessor<RawPriceData, ProcessedPriceData> {
        
        @Override
        public ProcessedPriceData process(RawPriceData rawData) throws Exception {
            // 数据清洗和验证
            ProcessedPriceData processed = new ProcessedPriceData();
            
            // 价格异常检测
            if (isPriceAnomalous(rawData)) {
                log.warn("检测到异常价格数据: {}", rawData);
                return null; // 跳过异常数据
            }
            
            // 数据标准化
            processed.setProductId(rawData.getProductId());
            processed.setPrice(normalizePrice(rawData.getPrice()));
            processed.setRegion(standardizeRegion(rawData.getRegion()));
            processed.setMarketName(rawData.getMarketName());
            processed.setVolume(rawData.getVolume());
            processed.setCollectTime(LocalDateTime.now());
            
            return processed;
        }
    }
}
```

## 5. 数据库设计优化

### 5.1 扩展数据库表结构

```sql
-- 实时价格数据表
CREATE TABLE `real_time_price_data` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `product_id` bigint NOT NULL COMMENT '产品ID',
    `product_name` varchar(255) NOT NULL COMMENT '产品名称',
    `category_id` bigint NOT NULL COMMENT '分类ID',
    `price` decimal(10,2) NOT NULL COMMENT '当前价格',
    `previous_price` decimal(10,2) DEFAULT NULL COMMENT '前一日价格',
    `price_change` decimal(10,2) DEFAULT NULL COMMENT '价格变化',
    `change_rate` decimal(5,2) DEFAULT NULL COMMENT '变化率(%)',
    `volume` int DEFAULT NULL COMMENT '成交量',
    `market_id` bigint DEFAULT NULL COMMENT '市场ID',
    `market_name` varchar(255) DEFAULT NULL COMMENT '市场名称',
    `region` varchar(100) DEFAULT NULL COMMENT '地区',
    `province` varchar(50) DEFAULT NULL COMMENT '省份',
    `city` varchar(50) DEFAULT NULL COMMENT '城市',
    `data_source` varchar(50) NOT NULL COMMENT '数据来源',
    `quality_score` decimal(3,2) DEFAULT '1.00' COMMENT '数据质量评分',
    `collect_time` datetime NOT NULL COMMENT '采集时间',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_product_market_date` (`product_id`, `market_id`, `collect_time`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_category_id` (`category_id`),
    KEY `idx_region` (`region`),
    KEY `idx_collect_time` (`collect_time`),
    KEY `idx_data_source` (`data_source`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='实时价格数据表';

-- 价格预测模型参数表
CREATE TABLE `price_forecast_models` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `product_id` bigint NOT NULL COMMENT '产品ID',
    `model_type` varchar(50) NOT NULL COMMENT '模型类型(ARIMA,LSTM,LINEAR)',
    `model_parameters` json NOT NULL COMMENT '模型参数(JSON格式)',
    `accuracy_score` decimal(5,4) DEFAULT NULL COMMENT '准确率评分',
    `last_trained` datetime DEFAULT NULL COMMENT '最后训练时间',
    `is_active` tinyint DEFAULT '1' COMMENT '是否激活',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_product_model` (`product_id`, `model_type`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_model_type` (`model_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='价格预测模型参数表';

-- 价格异常监控表
CREATE TABLE `price_anomaly_alerts` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `product_id` bigint NOT NULL COMMENT '产品ID',
    `alert_type` varchar(50) NOT NULL COMMENT '异常类型',
    `current_price` decimal(10,2) NOT NULL COMMENT '当前价格',
    `expected_price` decimal(10,2) DEFAULT NULL COMMENT '预期价格',
    `deviation_rate` decimal(5,2) NOT NULL COMMENT '偏差率(%)',
    `severity_level` tinyint NOT NULL COMMENT '严重程度(1-5)',
    `alert_message` text COMMENT '异常描述',
    `is_resolved` tinyint DEFAULT '0' COMMENT '是否已解决',
    `resolved_at` datetime DEFAULT NULL COMMENT '解决时间',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_alert_type` (`alert_type`),
    KEY `idx_severity_level` (`severity_level`),
    KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='价格异常监控表';

-- 市场数据源配置表
CREATE TABLE `market_data_sources` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `source_name` varchar(100) NOT NULL COMMENT '数据源名称',
    `source_type` varchar(50) NOT NULL COMMENT '数据源类型(API,CRAWLER,MANUAL)',
    `api_url` varchar(500) DEFAULT NULL COMMENT 'API地址',
    `api_key` varchar(255) DEFAULT NULL COMMENT 'API密钥',
    `crawl_config` json DEFAULT NULL COMMENT '爬虫配置',
    `update_frequency` int NOT NULL COMMENT '更新频率(分钟)',
    `is_active` tinyint DEFAULT '1' COMMENT '是否激活',
    `last_update` datetime DEFAULT NULL COMMENT '最后更新时间',
    `success_rate` decimal(5,2) DEFAULT '100.00' COMMENT '成功率(%)',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_source_name` (`source_name`),
    KEY `idx_source_type` (`source_type`),
    KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='市场数据源配置表';
```

### 5.2 Redis缓存策略

```java
@Service
public class PriceCacheService {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private static final String PRICE_CACHE_PREFIX = "price:";
    private static final String TREND_CACHE_PREFIX = "trend:";
    private static final String FORECAST_CACHE_PREFIX = "forecast:";

    /**
     * 缓存实时价格数据
     */
    public void cacheRealTimePrice(Long productId, RealTimePriceData priceData) {
        String key = PRICE_CACHE_PREFIX + "realtime:" + productId;
        redisTemplate.opsForValue().set(key, priceData, Duration.ofMinutes(30));

        // 同时更新价格历史缓存（滑动窗口）
        String historyKey = PRICE_CACHE_PREFIX + "history:" + productId;
        redisTemplate.opsForList().leftPush(historyKey, priceData);
        redisTemplate.opsForList().trim(historyKey, 0, 99); // 保留最近100条
        redisTemplate.expire(historyKey, Duration.ofHours(24));
    }

    /**
     * 缓存价格趋势数据
     */
    public void cachePriceTrend(Long productId, String period, List<PriceTrendData> trendData) {
        String key = TREND_CACHE_PREFIX + productId + ":" + period;
        redisTemplate.opsForValue().set(key, trendData, Duration.ofHours(2));
    }

    /**
     * 缓存价格预测数据
     */
    public void cachePriceForecast(Long productId, int days, List<PriceForecast> forecasts) {
        String key = FORECAST_CACHE_PREFIX + productId + ":" + days + "d";
        redisTemplate.opsForValue().set(key, forecasts, Duration.ofHours(6));
    }

    /**
     * 获取缓存的实时价格
     */
    public RealTimePriceData getCachedRealTimePrice(Long productId) {
        String key = PRICE_CACHE_PREFIX + "realtime:" + productId;
        return (RealTimePriceData) redisTemplate.opsForValue().get(key);
    }

    /**
     * 批量预热缓存
     */
    @Scheduled(fixedRate = 3600000) // 每小时执行一次
    public void warmUpCache() {
        log.info("开始预热价格数据缓存");

        // 获取热门产品列表
        List<Long> hotProductIds = getHotProductIds();

        for (Long productId : hotProductIds) {
            try {
                // 预热实时价格
                RealTimePriceData priceData = fetchRealTimePriceFromDB(productId);
                if (priceData != null) {
                    cacheRealTimePrice(productId, priceData);
                }

                // 预热趋势数据
                List<PriceTrendData> trendData = fetchPriceTrendFromDB(productId, "7d");
                if (!trendData.isEmpty()) {
                    cachePriceTrend(productId, "7d", trendData);
                }

            } catch (Exception e) {
                log.error("预热产品 {} 的缓存失败", productId, e);
            }
        }

        log.info("缓存预热完成，处理了 {} 个产品", hotProductIds.size());
    }
}
```

## 6. WebSocket实时推送

### 6.1 WebSocket服务端实现

```java
@Component
@ServerEndpoint("/websocket/price-updates")
@Slf4j
public class PriceUpdateWebSocket {

    private static final Map<String, Session> sessions = new ConcurrentHashMap<>();

    @OnOpen
    public void onOpen(Session session, @PathParam("userId") String userId) {
        sessions.put(userId, session);
        log.info("用户 {} 连接价格更新WebSocket", userId);
    }

    @OnClose
    public void onClose(@PathParam("userId") String userId) {
        sessions.remove(userId);
        log.info("用户 {} 断开价格更新WebSocket连接", userId);
    }

    /**
     * 广播价格更新消息
     */
    public static void broadcastPriceUpdate(PriceUpdateMessage message) {
        String jsonMessage = JSON.toJSONString(message);

        sessions.values().parallelStream().forEach(session -> {
            try {
                if (session.isOpen()) {
                    session.getBasicRemote().sendText(jsonMessage);
                }
            } catch (IOException e) {
                log.error("发送价格更新消息失败", e);
            }
        });
    }

    /**
     * 发送个性化价格提醒
     */
    public static void sendPersonalizedAlert(String userId, PriceAlertMessage alert) {
        Session session = sessions.get(userId);
        if (session != null && session.isOpen()) {
            try {
                session.getBasicRemote().sendText(JSON.toJSONString(alert));
            } catch (IOException e) {
                log.error("发送个性化价格提醒失败", e);
            }
        }
    }
}
```

### 6.2 前端WebSocket客户端

```javascript
// 前端WebSocket连接
class PriceWebSocketClient {
    constructor() {
        this.ws = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectInterval = 5000;
    }

    connect() {
        const wsUrl = `ws://localhost:8081/websocket/price-updates`;
        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = () => {
            console.log('价格数据WebSocket连接成功');
            this.reconnectAttempts = 0;

            // 订阅感兴趣的产品价格更新
            this.subscribe(['1', '2', '3']); // 产品ID列表
        };

        this.ws.onmessage = (event) => {
            const data = JSON.parse(event.data);
            this.handlePriceUpdate(data);
        };

        this.ws.onclose = () => {
            console.log('价格数据WebSocket连接关闭');
            this.reconnect();
        };

        this.ws.onerror = (error) => {
            console.error('价格数据WebSocket错误:', error);
        };
    }

    subscribe(productIds) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify({
                type: 'subscribe',
                productIds: productIds
            }));
        }
    }

    handlePriceUpdate(data) {
        switch (data.type) {
            case 'price_update':
                this.updatePriceDisplay(data.payload);
                break;
            case 'price_alert':
                this.showPriceAlert(data.payload);
                break;
            case 'forecast_update':
                this.updateForecastDisplay(data.payload);
                break;
        }
    }

    reconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            setTimeout(() => {
                console.log(`尝试重连WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
                this.connect();
            }, this.reconnectInterval);
        }
    }
}
```

## 7. API接口设计

### 7.1 RESTful API规范

#### 7.1.1 价格查询接口

```java
@RestController
@RequestMapping("/api/v2/prices")
@Api(tags = "价格数据API v2.0")
public class PriceDataController {

    /**
     * 获取实时价格数据
     */
    @GetMapping("/realtime")
    @ApiOperation("获取实时价格数据")
    public Result<PageResult<RealTimePriceVO>> getRealTimePrices(
            @RequestParam(required = false) String category,
            @RequestParam(required = false) String region,
            @RequestParam(required = false) String keyword,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {

        PriceQueryParams params = PriceQueryParams.builder()
                .category(category)
                .region(region)
                .keyword(keyword)
                .page(page)
                .size(size)
                .build();

        PageResult<RealTimePriceVO> result = priceDataService.getRealTimePrices(params);
        return Result.ok(result);
    }

    /**
     * 获取价格历史趋势
     */
    @GetMapping("/{productId}/trend")
    @ApiOperation("获取价格历史趋势")
    public Result<PriceTrendVO> getPriceTrend(
            @PathVariable Long productId,
            @RequestParam(defaultValue = "30") Integer days,
            @RequestParam(required = false) String region) {

        PriceTrendVO trend = priceDataService.getPriceTrend(productId, days, region);
        return Result.ok(trend);
    }

    /**
     * 获取价格统计分析
     */
    @GetMapping("/statistics")
    @ApiOperation("获取价格统计分析")
    public Result<PriceStatisticsVO> getPriceStatistics(
            @RequestParam(required = false) String category,
            @RequestParam(required = false) String region,
            @RequestParam(defaultValue = "7") Integer days) {

        PriceStatisticsVO statistics = priceDataService.getPriceStatistics(category, region, days);
        return Result.ok(statistics);
    }
}
```

#### 7.1.2 价格预测接口增强

```java
@RestController
@RequestMapping("/api/v2/price-forecast")
@Api(tags = "价格预测API v2.0")
public class PriceForecastControllerV2 {

    /**
     * 获取智能价格预测
     */
    @GetMapping("/{productId}/intelligent")
    @ApiOperation("获取智能价格预测")
    public Result<IntelligentForecastVO> getIntelligentForecast(
            @PathVariable Long productId,
            @RequestParam(defaultValue = "7") Integer days,
            @RequestParam(required = false) String region) {

        IntelligentForecastVO forecast = forecastService.getIntelligentForecast(productId, days, region);
        return Result.ok(forecast);
    }

    /**
     * 获取多模型预测对比
     */
    @GetMapping("/{productId}/multi-model")
    @ApiOperation("获取多模型预测对比")
    public Result<MultiModelForecastVO> getMultiModelForecast(
            @PathVariable Long productId,
            @RequestParam(defaultValue = "7") Integer days) {

        MultiModelForecastVO forecast = forecastService.getMultiModelForecast(productId, days);
        return Result.ok(forecast);
    }

    /**
     * 获取价格影响因素分析
     */
    @GetMapping("/{productId}/factors")
    @ApiOperation("获取价格影响因素分析")
    public Result<PriceFactorsVO> getPriceFactors(@PathVariable Long productId) {
        PriceFactorsVO factors = forecastService.analyzePriceFactors(productId);
        return Result.ok(factors);
    }
}
```

## 8. 前端实时展示方案

### 8.1 实时价格展示组件

```vue
<template>
  <div class="real-time-price-dashboard">
    <!-- 价格概览卡片 -->
    <div class="price-overview-cards">
      <el-row :gutter="20">
        <el-col :span="6" v-for="price in realTimePrices" :key="price.productId">
          <el-card class="price-card" :class="getPriceCardClass(price)">
            <div class="price-header">
              <h3>{{ price.productName }}</h3>
              <el-tag :type="price.trend === 'up' ? 'success' : 'danger'" size="small">
                {{ price.trend === 'up' ? '上涨' : '下跌' }}
              </el-tag>
            </div>

            <div class="price-content">
              <div class="current-price">
                ¥{{ price.currentPrice }}
                <span class="unit">{{ price.unit }}</span>
              </div>

              <div class="price-change" :class="price.trend">
                <i :class="getPriceChangeIcon(price.trend)"></i>
                {{ price.changeRate }}%
                <span class="change-amount">({{ price.priceChange > 0 ? '+' : '' }}{{ price.priceChange }})</span>
              </div>
            </div>

            <div class="price-footer">
              <span class="update-time">{{ formatUpdateTime(price.updateTime) }}</span>
              <span class="market-info">{{ price.marketName }}</span>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 实时价格趋势图 -->
    <div class="real-time-chart-container">
      <el-card>
        <div slot="header" class="chart-header">
          <span>实时价格趋势</span>
          <div class="chart-controls">
            <el-select v-model="selectedProducts" multiple placeholder="选择产品" size="small">
              <el-option
                v-for="product in availableProducts"
                :key="product.id"
                :label="product.name"
                :value="product.id">
              </el-option>
            </el-select>

            <el-button-group size="small">
              <el-button :type="timeRange === '1h' ? 'primary' : ''" @click="setTimeRange('1h')">1小时</el-button>
              <el-button :type="timeRange === '4h' ? 'primary' : ''" @click="setTimeRange('4h')">4小时</el-button>
              <el-button :type="timeRange === '1d' ? 'primary' : ''" @click="setTimeRange('1d')">1天</el-button>
            </el-button-group>
          </div>
        </div>

        <div ref="realTimeChart" class="real-time-chart"></div>
      </el-card>
    </div>

    <!-- 价格预警通知 -->
    <div class="price-alerts" v-if="priceAlerts.length > 0">
      <el-alert
        v-for="alert in priceAlerts"
        :key="alert.id"
        :title="alert.title"
        :description="alert.description"
        :type="alert.type"
        :closable="true"
        @close="dismissAlert(alert.id)"
        show-icon>
      </el-alert>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { priceForecastService } from '@/api/priceforecast'

export default {
  name: 'RealTimePriceDashboard',
  data() {
    return {
      realTimePrices: [],
      selectedProducts: [],
      availableProducts: [],
      timeRange: '1h',
      priceAlerts: [],
      chart: null,
      wsClient: null,
      updateTimer: null
    }
  },

  mounted() {
    this.initializeComponent()
    this.connectWebSocket()
    this.startAutoUpdate()
  },

  beforeDestroy() {
    this.cleanup()
  },

  methods: {
    async initializeComponent() {
      try {
        // 加载初始数据
        await this.loadRealTimePrices()
        await this.loadAvailableProducts()

        // 初始化图表
        this.initChart()

        // 加载图表数据
        await this.loadChartData()

      } catch (error) {
        console.error('初始化实时价格面板失败:', error)
        this.$message.error('加载价格数据失败，请刷新页面重试')
      }
    },

    async loadRealTimePrices() {
      const response = await this.$api.prices.getRealTimePrices({
        page: 1,
        size: 8 // 显示8个主要产品
      })

      if (response.code === 200) {
        this.realTimePrices = response.data.records
      }
    },

    connectWebSocket() {
      // 连接WebSocket获取实时更新
      this.wsClient = new PriceWebSocketClient()
      this.wsClient.connect()

      // 监听价格更新事件
      this.wsClient.onPriceUpdate = (priceData) => {
        this.updateRealTimePrice(priceData)
        this.updateChart(priceData)
      }

      // 监听价格预警事件
      this.wsClient.onPriceAlert = (alertData) => {
        this.addPriceAlert(alertData)
      }
    },

    updateRealTimePrice(priceData) {
      const index = this.realTimePrices.findIndex(p => p.productId === priceData.productId)
      if (index !== -1) {
        // 添加动画效果
        this.$set(this.realTimePrices, index, {
          ...this.realTimePrices[index],
          ...priceData,
          isUpdating: true
        })

        // 移除动画效果
        setTimeout(() => {
          this.$set(this.realTimePrices[index], 'isUpdating', false)
        }, 1000)
      }
    }
  }
}
</script>
```

## 9. 实施计划

### 9.1 分阶段开发计划

#### 第一阶段：基础数据接入（2周）

**目标**：建立基本的数据采集和存储机制

**任务清单**：
1. **数据库表结构扩展**（3天）
   - 创建实时价格数据表
   - 创建价格预测模型参数表
   - 创建价格异常监控表
   - 创建市场数据源配置表

2. **数据采集服务开发**（5天）
   - 实现农业部API数据采集
   - 实现批发市场数据爬虫
   - 实现数据清洗和验证逻辑
   - 实现数据质量评分机制

3. **基础API接口开发**（4天）
   - 扩展价格查询API
   - 实现实时价格数据API
   - 实现价格历史趋势API
   - 添加API文档和测试

4. **定时任务配置**（2天）
   - 配置Spring Batch任务
   - 设置数据采集定时器
   - 实现任务监控和日志

#### 第二阶段：实时更新机制（2周）

**目标**：实现数据的实时更新和推送

**任务清单**：
1. **Redis缓存集成**（3天）
   - 实现价格数据缓存策略
   - 实现缓存预热机制
   - 实现缓存更新和失效策略

2. **WebSocket实时推送**（4天）
   - 实现WebSocket服务端
   - 实现前端WebSocket客户端
   - 实现消息订阅和推送机制
   - 实现断线重连逻辑

3. **消息队列集成**（3天）
   - 集成RabbitMQ消息队列
   - 实现价格更新消息生产者
   - 实现价格更新消息消费者
   - 实现消息持久化和重试机制

4. **异常监控和预警**（4天）
   - 实现价格异常检测算法
   - 实现预警消息推送
   - 实现预警规则配置
   - 实现预警历史记录

#### 第三阶段：前端优化（2周）

**目标**：优化前端展示和用户体验

**任务清单**：
1. **实时价格展示组件**（4天）
   - 重构价格展示组件
   - 实现实时数据更新动画
   - 实现价格变化提醒
   - 优化移动端适配

2. **智能预测展示**（4天）
   - 实现多模型预测对比
   - 实现影响因素分析图表
   - 实现预测置信度展示
   - 实现预测准确率统计

3. **用户交互优化**（3天）
   - 实现个性化价格订阅
   - 实现价格提醒设置
   - 实现历史查询功能
   - 实现数据导出功能

4. **性能优化**（3天）
   - 实现图表懒加载
   - 优化数据请求频率
   - 实现前端缓存策略
   - 优化页面加载速度

#### 第四阶段：高级功能（2周）

**目标**：实现高级分析和预测功能

**任务清单**：
1. **智能预测算法**（5天）
   - 集成ARIMA时间序列模型
   - 集成LSTM神经网络模型
   - 实现多模型融合预测
   - 实现模型自动训练和更新

2. **市场分析功能**（4天）
   - 实现价格相关性分析
   - 实现季节性趋势分析
   - 实现地域价格差异分析
   - 实现供需关系分析

3. **数据可视化增强**（3天）
   - 实现3D价格趋势图
   - 实现地图价格分布
   - 实现实时数据大屏
   - 实现移动端图表优化

4. **系统集成测试**（2天）
   - 端到端功能测试
   - 性能压力测试
   - 数据准确性验证
   - 用户体验测试

### 9.2 技术风险评估

#### 9.2.1 高风险项
1. **数据源稳定性**
   - 风险：外部API可能不稳定或变更
   - 缓解措施：多数据源备份，实现降级机制

2. **数据质量控制**
   - 风险：爬虫数据可能存在质量问题
   - 缓解措施：多重数据验证，异常数据过滤

3. **系统性能压力**
   - 风险：实时数据处理可能影响系统性能
   - 缓解措施：异步处理，缓存优化，负载均衡

#### 9.2.2 中风险项
1. **预测算法准确性**
   - 风险：预测模型可能准确率不高
   - 缓解措施：多模型对比，持续优化算法

2. **WebSocket连接稳定性**
   - 风险：网络问题可能导致连接中断
   - 缓解措施：自动重连，消息补偿机制

### 9.3 资源需求

#### 9.3.1 人力资源
- **后端开发工程师**：2人，负责API开发和数据处理
- **前端开发工程师**：1人，负责界面优化和交互实现
- **数据工程师**：1人，负责数据采集和算法优化
- **测试工程师**：1人，负责功能测试和性能测试

#### 9.3.2 技术资源
- **服务器资源**：增加1台数据采集服务器
- **数据库资源**：扩容MySQL存储空间
- **缓存资源**：部署Redis集群
- **消息队列**：部署RabbitMQ服务

## 10. 运维和监控方案

### 10.1 系统监控

#### 10.1.1 数据采集监控
```java
@Component
public class DataCollectionMonitor {

    @Autowired
    private MeterRegistry meterRegistry;

    private final Counter successCounter;
    private final Counter failureCounter;
    private final Timer collectionTimer;

    public DataCollectionMonitor(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.successCounter = Counter.builder("data.collection.success")
                .description("成功采集数据次数")
                .register(meterRegistry);
        this.failureCounter = Counter.builder("data.collection.failure")
                .description("采集数据失败次数")
                .register(meterRegistry);
        this.collectionTimer = Timer.builder("data.collection.duration")
                .description("数据采集耗时")
                .register(meterRegistry);
    }

    public void recordSuccess() {
        successCounter.increment();
    }

    public void recordFailure() {
        failureCounter.increment();
    }

    public Timer.Sample startTimer() {
        return Timer.start(meterRegistry);
    }
}
```

#### 10.1.2 价格异常监控
```java
@Service
public class PriceAnomalyMonitorService {

    @Autowired
    private PriceAnomalyAlertRepository alertRepository;

    @Autowired
    private NotificationService notificationService;

    /**
     * 检测价格异常
     */
    public void detectPriceAnomaly(RealTimePriceData priceData) {
        try {
            // 获取历史价格数据
            List<RealTimePriceData> historicalData = getHistoricalPriceData(
                priceData.getProductId(), 30);

            if (historicalData.size() < 10) {
                return; // 数据不足，跳过检测
            }

            // 计算价格统计指标
            double mean = calculateMean(historicalData);
            double stdDev = calculateStandardDeviation(historicalData, mean);

            // 检测异常（使用3σ原则）
            double currentPrice = priceData.getPrice().doubleValue();
            double zScore = Math.abs((currentPrice - mean) / stdDev);

            if (zScore > 3.0) {
                // 发现异常价格
                PriceAnomalyAlert alert = createAnomalyAlert(priceData, mean, zScore);
                alertRepository.save(alert);

                // 发送通知
                notificationService.sendPriceAnomalyAlert(alert);

                log.warn("检测到价格异常: 产品ID={}, 当前价格={}, 历史均价={}, Z-Score={}",
                    priceData.getProductId(), currentPrice, mean, zScore);
            }

        } catch (Exception e) {
            log.error("价格异常检测失败", e);
        }
    }

    private PriceAnomalyAlert createAnomalyAlert(RealTimePriceData priceData,
                                                double expectedPrice, double zScore) {
        PriceAnomalyAlert alert = new PriceAnomalyAlert();
        alert.setProductId(priceData.getProductId());
        alert.setAlertType("PRICE_SPIKE");
        alert.setCurrentPrice(priceData.getPrice());
        alert.setExpectedPrice(BigDecimal.valueOf(expectedPrice));
        alert.setDeviationRate(BigDecimal.valueOf((zScore - 1) * 100));
        alert.setSeverityLevel(getSeverityLevel(zScore));
        alert.setAlertMessage(String.format("产品价格异常波动，当前价格 %.2f 偏离历史均价 %.2f",
            priceData.getPrice().doubleValue(), expectedPrice));
        alert.setCreatedAt(LocalDateTime.now());
        return alert;
    }
}
```

### 10.2 性能优化建议

#### 10.2.1 数据库优化
1. **索引优化**
   - 为查询频繁的字段添加复合索引
   - 定期分析慢查询并优化

2. **分区策略**
   - 按时间分区存储历史价格数据
   - 定期归档老旧数据

3. **读写分离**
   - 实时查询使用读库
   - 数据写入使用主库

#### 10.2.2 缓存优化
1. **多级缓存**
   - L1缓存：应用内存缓存
   - L2缓存：Redis分布式缓存
   - L3缓存：CDN边缘缓存

2. **缓存预热**
   - 系统启动时预热热门数据
   - 定期更新缓存内容

### 10.3 部署方案

#### 10.3.1 Docker容器化部署
```dockerfile
# 价格数据采集服务
FROM openjdk:11-jre-slim

COPY target/price-collection-service.jar app.jar

EXPOSE 8082

ENTRYPOINT ["java", "-jar", "/app.jar"]
```

#### 10.3.2 Kubernetes部署配置
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: price-collection-service
spec:
  replicas: 2
  selector:
    matchLabels:
      app: price-collection-service
  template:
    metadata:
      labels:
        app: price-collection-service
    spec:
      containers:
      - name: price-collection-service
        image: sfap/price-collection-service:latest
        ports:
        - containerPort: 8082
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "production"
        - name: MYSQL_HOST
          value: "mysql-service"
        - name: REDIS_HOST
          value: "redis-service"
```

## 11. 总结

本技术文档详细分析了SFAP农品汇平台价格行情和市场动态数据中心的现状，识别了核心问题，并提供了完整的实时数据实现方案。

### 11.1 核心改进点
1. **数据源多样化**：从静态数据转向多渠道实时数据采集
2. **技术架构升级**：引入缓存、消息队列、WebSocket等技术
3. **用户体验优化**：实现实时数据展示和智能预警
4. **系统可扩展性**：采用微服务架构，支持水平扩展

### 11.2 预期效果
1. **数据时效性**：从静态数据更新为实时数据，时效性提升90%
2. **数据准确性**：多数据源验证，数据准确性提升80%
3. **用户体验**：实时更新和智能预警，用户满意度提升70%
4. **系统性能**：缓存优化和异步处理，响应速度提升60%

### 11.3 后续优化方向
1. **AI算法增强**：引入更先进的机器学习算法
2. **数据源扩展**：接入更多权威数据源
3. **移动端优化**：开发专门的移动端应用
4. **国际化支持**：支持多语言和多货币

通过本方案的实施，SFAP农品汇平台将具备真正的实时价格行情和市场动态分析能力，为用户提供更准确、及时的市场信息服务。
```
