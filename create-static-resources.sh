#!/bin/bash

# 创建生产环境静态资源脚本
# 用于解决404错误问题

echo "=== 创建生产环境静态资源 ==="

# 后端静态资源目录
BACKEND_STATIC_DIR="backend/main/src/main/resources/static"

# 创建目录结构
echo "1. 创建目录结构..."
mkdir -p "$BACKEND_STATIC_DIR/images/brands"
mkdir -p "$BACKEND_STATIC_DIR/images/avatars"
mkdir -p "$BACKEND_STATIC_DIR/images/products"
mkdir -p "$BACKEND_STATIC_DIR/images/icons"

# 创建品牌占位图片
echo "2. 创建品牌占位图片..."
cat > "$BACKEND_STATIC_DIR/images/brands/brand1.jpg.html" << 'EOF'
<!-- 品牌1占位图片 -->
<svg xmlns="http://www.w3.org/2000/svg" width="200" height="100" viewBox="0 0 200 100">
  <rect width="200" height="100" fill="#4CAF50"/>
  <text x="100" y="50" text-anchor="middle" dy=".3em" fill="white" font-family="Arial" font-size="16">绿野仙踪</text>
</svg>
EOF

cat > "$BACKEND_STATIC_DIR/images/brands/brand2.jpg.html" << 'EOF'
<!-- 品牌2占位图片 -->
<svg xmlns="http://www.w3.org/2000/svg" width="200" height="100" viewBox="0 0 200 100">
  <rect width="200" height="100" fill="#2196F3"/>
  <text x="100" y="50" text-anchor="middle" dy=".3em" fill="white" font-family="Arial" font-size="16">田园牧歌</text>
</svg>
EOF

cat > "$BACKEND_STATIC_DIR/images/brands/brand3.jpg.html" << 'EOF'
<!-- 品牌3占位图片 -->
<svg xmlns="http://www.w3.org/2000/svg" width="200" height="100" viewBox="0 0 200 100">
  <rect width="200" height="100" fill="#FF9800"/>
  <text x="100" y="50" text-anchor="middle" dy=".3em" fill="white" font-family="Arial" font-size="16">农家小院</text>
</svg>
EOF

# 创建用户头像占位图片
echo "3. 创建用户头像占位图片..."
cat > "$BACKEND_STATIC_DIR/images/avatars/user1.jpg.html" << 'EOF'
<!-- 用户1头像占位图片 -->
<svg xmlns="http://www.w3.org/2000/svg" width="80" height="80" viewBox="0 0 80 80">
  <circle cx="40" cy="40" r="40" fill="#E3F2FD"/>
  <circle cx="40" cy="30" r="12" fill="#1976D2"/>
  <path d="M40 45c-8 0-15 4-15 9v6h30v-6c0-5-7-9-15-9z" fill="#1976D2"/>
</svg>
EOF

cat > "$BACKEND_STATIC_DIR/images/avatars/user2.jpg.html" << 'EOF'
<!-- 用户2头像占位图片 -->
<svg xmlns="http://www.w3.org/2000/svg" width="80" height="80" viewBox="0 0 80 80">
  <circle cx="40" cy="40" r="40" fill="#FCE4EC"/>
  <circle cx="40" cy="30" r="12" fill="#C2185B"/>
  <path d="M40 45c-8 0-15 4-15 9v6h30v-6c0-5-7-9-15-9z" fill="#C2185B"/>
</svg>
EOF

cat > "$BACKEND_STATIC_DIR/images/avatars/user3.jpg.html" << 'EOF'
<!-- 用户3头像占位图片 -->
<svg xmlns="http://www.w3.org/2000/svg" width="80" height="80" viewBox="0 0 80 80">
  <circle cx="40" cy="40" r="40" fill="#E8F5E8"/>
  <circle cx="40" cy="30" r="12" fill="#388E3C"/>
  <path d="M40 45c-8 0-15 4-15 9v6h30v-6c0-5-7-9-15-9z" fill="#388E3C"/>
</svg>
EOF

# 创建实际的图片文件（如果有ImageMagick）
if command -v convert >/dev/null 2>&1; then
    echo "4. 使用ImageMagick创建实际图片文件..."
    
    # 品牌图片
    convert -size 200x100 xc:"#4CAF50" -gravity center -pointsize 16 -fill white -annotate +0+0 "绿野仙踪" "$BACKEND_STATIC_DIR/images/brands/brand1.jpg"
    convert -size 200x100 xc:"#2196F3" -gravity center -pointsize 16 -fill white -annotate +0+0 "田园牧歌" "$BACKEND_STATIC_DIR/images/brands/brand2.jpg"
    convert -size 200x100 xc:"#FF9800" -gravity center -pointsize 16 -fill white -annotate +0+0 "农家小院" "$BACKEND_STATIC_DIR/images/brands/brand3.jpg"
    
    # 用户头像
    convert -size 80x80 xc:"#E3F2FD" -gravity center -pointsize 12 -fill "#1976D2" -annotate +0+0 "张先生" "$BACKEND_STATIC_DIR/images/avatars/user1.jpg"
    convert -size 80x80 xc:"#FCE4EC" -gravity center -pointsize 12 -fill "#C2185B" -annotate +0+0 "李女士" "$BACKEND_STATIC_DIR/images/avatars/user2.jpg"
    convert -size 80x80 xc:"#E8F5E8" -gravity center -pointsize 12 -fill "#388E3C" -annotate +0+0 "王老师" "$BACKEND_STATIC_DIR/images/avatars/user3.jpg"
    
    echo "✅ ImageMagick图片创建完成"
else
    echo "⚠️ ImageMagick未安装，只创建了HTML占位文件"
    echo "请手动创建以下图片文件："
    echo "- $BACKEND_STATIC_DIR/images/brands/brand1.jpg"
    echo "- $BACKEND_STATIC_DIR/images/brands/brand2.jpg"
    echo "- $BACKEND_STATIC_DIR/images/brands/brand3.jpg"
    echo "- $BACKEND_STATIC_DIR/images/avatars/user1.jpg"
    echo "- $BACKEND_STATIC_DIR/images/avatars/user2.jpg"
    echo "- $BACKEND_STATIC_DIR/images/avatars/user3.jpg"
fi

# 创建默认图标
echo "5. 创建默认图标..."
mkdir -p "$BACKEND_STATIC_DIR/images/icons"

# 如果有ImageMagick，创建图标
if command -v convert >/dev/null 2>&1; then
    convert -size 32x32 xc:"#409EFF" -gravity center -pointsize 16 -fill white -annotate +0+0 "🏠" "$BACKEND_STATIC_DIR/images/icons/home.png"
    convert -size 32x32 xc:"#67C23A" -gravity center -pointsize 16 -fill white -annotate +0+0 "🛒" "$BACKEND_STATIC_DIR/images/icons/cart.png"
    convert -size 32x32 xc:"#E6A23C" -gravity center -pointsize 16 -fill white -annotate +0+0 "🏪" "$BACKEND_STATIC_DIR/images/icons/shop.png"
    convert -size 32x32 xc:"#F56C6C" -gravity center -pointsize 16 -fill white -annotate +0+0 "🔍" "$BACKEND_STATIC_DIR/images/icons/search.png"
    convert -size 32x32 xc:"#909399" -gravity center -pointsize 16 -fill white -annotate +0+0 "⭐" "$BACKEND_STATIC_DIR/images/icons/star.png"
fi

# 设置权限
echo "6. 设置文件权限..."
chmod -R 755 "$BACKEND_STATIC_DIR"

# 创建nginx配置建议
echo "7. 创建nginx配置建议..."
cat > "nginx-static-config.conf" << 'EOF'
# Nginx静态资源配置建议
# 添加到你的nginx配置文件中

# 静态资源缓存配置
location /static/ {
    alias /path/to/your/backend/static/;
    expires 30d;
    add_header Cache-Control "public, immutable";
    
    # 图片文件
    location ~* \.(jpg|jpeg|png|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # 处理404错误，返回默认图片
    error_page 404 = @fallback_image;
}

# 404图片回退
location @fallback_image {
    return 302 /static/images/default-placeholder.jpg;
}

# 跨域配置（如果需要）
location /static/ {
    add_header Access-Control-Allow-Origin *;
    add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
    add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type';
}
EOF

echo ""
echo "=== 静态资源创建完成 ==="
echo "✅ 目录结构已创建"
echo "✅ 占位图片已生成"
echo "✅ 权限已设置"
echo ""
echo "📋 下一步操作："
echo "1. 将 $BACKEND_STATIC_DIR 目录部署到生产服务器"
echo "2. 确保nginx配置正确指向静态资源目录"
echo "3. 重启后端服务以应用更改"
echo "4. 测试静态资源访问：http://120.26.140.157:8081/static/images/brands/brand1.jpg"
echo ""
echo "📄 nginx配置建议已保存到: nginx-static-config.conf"
