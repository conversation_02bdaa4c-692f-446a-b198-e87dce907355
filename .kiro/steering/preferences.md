# SFAP 用户偏好与项目记忆

## 用户偏好设置

### 语言偏好
- **主要语言**: 中文
- **文档语言**: 中文优先，技术文档可使用中英文混合

### 技术偏好

#### 前端开发
- **框架**: Vue 2 + Element UI + SCSS
- **设计风格**: 现代化扁平设计，流畅CSS动画
- **响应式设计**: 支持多端适配
  - 桌面端: ≥1200px
  - 平板端: 768px-1199px
  - 移动端: <768px
- **代码规范**: 严格遵循ESLint，无未使用变量
- **数据处理**: 真实后端API调用，避免mock数据

#### 后端开发
- **架构**: Spring Boot + MyBatis Plus
- **开发方式**: 系统化分阶段方法
  1. 需求分析
  2. 基于角色的权限映射
  3. 功能识别
  4. 优先级实现

#### 问题解决方式
- **根本解决**: 修复技术根本问题，不使用临时方案
- **详细调试**: 需要详细的调试日志和错误信息
- **全面解决**: 系统性解决方案，不针对特定用户
- **直接修复**: 遇到错误直接修复项目代码

### 开发流程偏好

#### 服务启动
- **自主启动**: 用户自己启动SFAP前后端服务
- **端口配置**: 前端8080，后端8081

#### CORS处理
- **功能优先**: 优先保证系统功能正常
- **渐进优化**: 基本功能正常后再优化CORS安全配置
- **系统化调试**: 
  1. 数据库级别验证
  2. 审计所有后端配置
  3. 逐一隔离配置
  4. 系统化测试前后端集成

#### 测试策略
- **不生成测试接口**: 未来开发中不需要生成测试接口
- **不生成测试工具**: 不需要生成测试用例或测试工具

### 角色设计偏好

#### UI设计原则
- **组件组合模式**: 基于角色的UI设计
- **利用现有组件**: SellerApplicationButton, QuickActionsPanel, GlobalStatsPanel
- **专门功能区域**: 每个角色有专门的功能区域，不仅仅是菜单差异

#### 角色差异化
- **普通用户**: 查询/扫码溯源，个人中心，申请状态，用户活动
- **销售者**: CRUD溯源记录，商品集成，店铺管理，商品操作，销售数据
- **管理员**: 审计工作流，系统监控，用户管理，数据统计

#### 权限控制
- **严格权限**: 销售者只能操作自己的店铺和商品
- **管理员排除**: 管理员用户排除在销售者申请管理界面外
- **销售者中心**: 销售者只能访问自己的溯源记录

### 技术架构偏好

#### API设计
- **RESTful模式**: 使用特定API模式 (/api/seller/products)
- **完整映射**: 数据库-实体字段完整映射
- **字段处理**: 添加缺失字段到数据库表，不删除实体类字段

#### 数据库操作
- **结构分析**: 修改前进行完整数据库结构分析
- **Java Bean标准**: Java实体遵循标准getter/setter方法
- **不自动初始化**: 后端启动时不自动初始化数据库

### 项目配置记忆

#### 路径配置
- **项目根目录**: E:\计算机设计大赛2\V4.0\新建文件夹\SFAP
- **后端文件**: E:\计算机设计大赛2\V4.0\新建文件夹\SFAP\backend\main\src\main\java\com\agriculture
- **二维码目录**: E:\计算机设计大赛2\V4.0\新建文件夹\SFAP\uploads\qrcodes

#### 数据库配置
- **数据库**: agriculture_mall
- **用户**: root
- **密码**: fan13965711955
- **主机**: localhost

#### 跨域支持
- **开发环境**: localhost:8080 ↔ localhost:8081
- **生产环境**: 根据实际域名配置

### 系统问题记忆

#### 权限系统问题
- **字段不一致**: user表的role和user_type字段数据不一致
- **同步问题**: 管理员用户被错误识别为普通用户

#### 开发需求记忆
- **溯源系统**: 完整的"一品一码"溯源系统
- **QR码生成**: 二维码生成和验证
- **销售者管理**: 完整的CRUD操作、图片上传、溯源集成
- **Jackson问题**: 需要jackson-datatype-jsr310模块依赖
- **登录系统**: 需要可靠的认证逻辑
- **基于角色路由**: 溯源中心需要基于用户角色的路由和UI

### 溯源系统状态记忆
- **完整事件记录**: 60个商品都有4-5个标准化事件记录
- **差异化事件**: 支持管理员直采和销售者上传业务流程
- **时间顺序**: 事件按时间顺序排列形成完整生产时间线

## 文档更新机制

### 自动更新触发条件
1. **项目结构变化**: 新增/删除文件或目录
2. **数据库结构变化**: 表结构修改、新增表
3. **API接口变化**: 新增/修改接口
4. **用户偏好变化**: 新的偏好设置或要求
5. **技术栈变化**: 依赖更新、框架升级
6. **业务逻辑变化**: 新功能开发、现有功能修改

### 需要更新的文档
- **product.md**: 产品功能变化
- **tech.md**: 技术栈和构建系统变化
- **structure.md**: 项目结构变化
- **database.md**: 数据库结构变化
- **preferences.md**: 用户偏好变化

### 更新原则
1. **实时性**: 项目变化后立即更新相关文档
2. **准确性**: 确保文档内容与实际项目状态一致
3. **完整性**: 保持文档的完整性和关联性
4. **版本控制**: 记录重要变更的时间和原因

## 记忆保持策略

### 短期记忆 (会话内)
- 当前开发任务的上下文
- 临时的配置变更
- 调试过程中的发现

### 长期记忆 (跨会话)
- 用户偏好设置
- 项目架构决策
- 重要的技术选型
- 已知问题和解决方案

### 记忆验证机制
- 定期检查文档与实际项目的一致性
- 用户反馈驱动的记忆更新
- 自动检测项目变化并提醒更新

---

**最后更新时间**: 2024年12月  
**更新频率**: 实时更新  
**维护状态**: 活跃维护中