# SFAP 技术栈与构建系统

## 技术架构

### 前端技术栈
- **框架**: Vue.js 2.6.11
- **UI组件库**: Element UI 2.15.14
- **状态管理**: Vuex 3.4.0
- **路由管理**: Vue Router 3.2.0
- **样式预处理**: SCSS/Sass
- **HTTP客户端**: Axios 1.8.4
- **图表库**: ECharts 4.9.0
- **动画库**: Animate.css 4.1.1
- **构建工具**: Vue CLI 4.5.0

### 后端技术栈
- **框架**: Spring Boot 2.x
- **数据访问**: MyBatis-Plus
- **安全框架**: Spring Security + JWT
- **数据库**: MySQL 8.0
- **缓存**: Redis
- **构建工具**: Maven
- **JDK版本**: Java 8+

### Python服务
- **爬虫框架**: Scrapy
- **数据处理**: Pandas, NumPy
- **机器学习**: Scikit-learn
- **任务调度**: APScheduler

## 开发环境配置

### 端口配置
- **前端开发服务器**: localhost:8080
- **后端API服务**: localhost:8081
- **数据库**: localhost:3306
- **Redis**: localhost:6379

### 项目路径
- **项目根目录**: `E:\计算机设计大赛2\V4.0\新建文件夹\SFAP`
- **后端源码**: `backend\main\src\main\java\com\agriculture`
- **前端源码**: `src\`
- **文件上传目录**: `uploads\`

## 常用命令

### 前端开发
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run serve

# 构建生产版本
npm run build

# 代码检查
npm run lint
```

### 后端开发
```bash
# 编译项目
mvn compile

# 运行测试
mvn test

# 启动应用
mvn spring-boot:run

# 打包应用
mvn package
```

### 数据库配置
- **数据库名**: agriculture_mall
- **用户名**: root
- **密码**: fan13965711955
- **主机**: localhost

## 代码规范

### ESLint配置
- 严格遵循ESLint规范
- 禁止未使用的变量
- 使用真实API调用，避免mock数据
- 完善的错误处理机制

### 响应式设计断点
- **桌面端**: ≥1200px
- **平板端**: 768px-1199px  
- **移动端**: <768px

## API设计规范

### RESTful API模式
- **商城模块**: `/api/mall/`
- **销售者模块**: `/api/seller/`
- **管理员模块**: `/api/admin/`
- **溯源模块**: `/api/traceability/`
- **用户模块**: `/api/user/`

### 响应格式
```json
{
  "code": 200,
  "message": "success", 
  "data": {},
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## 跨域配置
- 开发环境支持 localhost:8080 ↔ localhost:8081
- 生产环境根据实际域名配置
- 优先保证功能正常，后续优化CORS安全配置