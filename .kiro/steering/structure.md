# SFAP 项目结构与组织

## 项目根目录结构

```
SFAP/
├── backend/                    # 后端项目
│   ├── main/                   # Spring Boot主项目
│   ├── python/                 # Python爬虫服务
│   └── python-price/           # 价格预测服务
├── src/                        # 前端Vue.js源码
├── public/                     # 静态资源
├── uploads/                    # 文件上传目录
│   ├── avatars/                # 用户头像
│   ├── products/               # 商品图片
│   ├── qrcodes/                # 溯源二维码
│   └── seller/                 # 销售者相关文件
├── docs/                       # 项目文档
├── 任务/                       # 开发任务文档
├── 后端开发/                   # 后端开发文档
├── 数据库设计/                 # 数据库设计文档
└── 架构文档/                   # 系统架构文档
```

## 前端项目结构

```
src/
├── api/                        # API接口层
│   ├── index.js                # 主要API配置
│   ├── auth.js                 # 认证接口
│   ├── products.js             # 商品接口
│   ├── seller.js               # 销售者接口
│   └── traceability.js         # 溯源接口
├── components/                 # 可复用组件
│   ├── Header.vue              # 页面头部
│   ├── Navbar.vue              # 导航栏
│   ├── shop/                   # 商城组件
│   │   ├── ProductCard.vue     # 商品卡片
│   │   └── MyProducts.vue      # 我的商品
│   └── traceability/           # 溯源组件
│       ├── TraceabilityCenter.vue
│       └── TraceabilitySearch.vue
├── views/                      # 页面组件
│   ├── Home.vue                # 首页
│   ├── Shop.vue                # 商城
│   ├── ProductDetail.vue       # 商品详情
│   ├── TraceabilityCenter.vue  # 溯源中心
│   └── admin/                  # 管理员页面
├── router/                     # 路由配置
├── store/                      # 状态管理
├── utils/                      # 工具函数
└── styles/                     # 全局样式
```

## 后端项目结构

```
backend/main/src/main/java/com/agriculture/
├── controller/                 # 控制器层
│   ├── ProductController.java  # 商品控制器
│   ├── UserController.java     # 用户控制器
│   ├── SellerController.java   # 销售者控制器
│   └── TraceabilityController.java # 溯源控制器
├── service/                    # 服务层
│   ├── impl/                   # 服务实现
│   ├── ProductService.java     # 商品服务
│   ├── UserService.java        # 用户服务
│   └── TraceabilityService.java # 溯源服务
├── mapper/                     # 数据访问层
│   ├── ProductMapper.java      # 商品Mapper
│   ├── UserMapper.java         # 用户Mapper
│   └── TraceabilityMapper.java # 溯源Mapper
├── entity/                     # 实体类
│   ├── Product.java            # 商品实体
│   ├── User.java               # 用户实体
│   └── TraceabilityRecord.java # 溯源记录实体
├── config/                     # 配置类
├── utils/                      # 工具类
└── Application.java            # 启动类
```

## 角色权限设计

### 用户角色分类
- **普通用户 (USER)**: 基础购买和查询功能
- **销售者 (SELLER)**: 商品管理和溯源记录维护
- **管理员 (ADMIN)**: 系统管理和数据统计

### 权限控制模式
- 基于角色的访问控制 (RBAC)
- 前端路由守卫 + 后端接口权限验证
- 销售者只能操作自己的商品和店铺
- 管理员排除在销售者申请流程之外

## 数据库表结构

### 核心业务表
- **users**: 用户表 (包含role和user_type字段)
- **products**: 商品表
- **orders**: 订单表
- **traceability_records**: 溯源记录表
- **traceability_events**: 溯源事件表
- **seller_applications**: 销售者申请表

### 关键字段说明
- **user.role**: 用户角色枚举
- **user.user_type**: 用户类型 (需与role保持同步)
- **product.seller_id**: 关联销售者ID
- **traceability_records.product_id**: 关联商品ID

## 文件存储规范

### 上传目录结构
```
uploads/
├── avatars/                    # 用户头像
├── products/                   # 商品图片
├── qrcodes/                    # 溯源二维码
├── seller/
│   └── certificates/           # 销售者证书
└── traceability/
    ├── events/                 # 溯源事件附件
    └── certificates/           # 认证证书
```

### 访问路径规范
- 开发环境: `http://localhost:8081/uploads/**`
- 生产环境: `http://域名/uploads/**`

## 组件设计原则

### 角色差异化UI
- 每个角色有专门的功能组件
- 销售者: 店铺管理、商品操作、销售数据组件
- 管理员: 系统监控、用户管理、数据统计组件
- 普通用户: 个人中心、申请状态、用户活动组件

### 组件复用策略
- 基础UI组件全角色共享
- 业务组件按角色分类
- 权限控制在组件内部实现

## 溯源系统架构

### "一品一码"设计
- 每个商品对应唯一溯源记录
- 二维码存储在 `uploads/qrcodes/` 目录
- 支持扫码查询和手动输入查询

### 事件记录系统
- 标准化事件类型 (生产、加工、检测、运输等)
- 时间顺序排列形成完整生产时间线
- 支持管理员直采和销售者上传两种业务流程