{"mcpServers": {"fetch": {"command": "uvx", "args": ["mcp-server-fetch"], "env": {}, "disabled": true, "autoApprove": []}, "mysql": {"command": "npx", "args": ["-y", "@f4ww4z/mcp-mysql-server"], "env": {"MYSQL_HOST": "localhost", "MYSQL_USER": "root", "MYSQL_PASSWORD": "fan13965711955", "MYSQL_DATABASE": "agriculture_mall"}, "disabled": false, "autoApprove": ["connect_db", "list_tables", "describe_table", "describe_table", "query", "query"]}}}