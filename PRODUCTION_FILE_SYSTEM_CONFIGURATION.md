# 智慧农业平台生产环境文件上传和静态资源配置完整指南

## 📋 **配置概述**

**生产环境信息**：
- 服务器IP: **************
- 前端端口: 8200
- 后端端口: 8081
- 主存储目录: /www/wwwroot/test.com/backend/uploads/

**配置目标**：
- 统一文件上传和静态资源访问系统
- 修复前端图片显示问题
- 建立完整的目录结构
- 确保生产环境稳定运行

---

## 🔧 **已完成的配置修复**

### **1. 后端配置修复**

#### **1.1 WebConfig.java 静态资源配置**
**文件**: `backend/main/src/main/java/com/agriculture/config/WebConfig.java`

**修复内容**:
```java
@Override
public void addResourceHandlers(ResourceHandlerRegistry registry) {
    // 配置上传文件访问路径 - 支持生产环境和开发环境
    String uploadPath = System.getProperty("file.upload.path", "/www/wwwroot/test.com/backend/uploads/");
    
    // 如果是Windows开发环境，使用本地路径
    if (System.getProperty("os.name").toLowerCase().contains("windows")) {
        uploadPath = "file:E:/计算机设计大赛2/V4.0/新建文件夹/SFAP/uploads/";
    } else {
        // Linux生产环境
        uploadPath = "file:/www/wwwroot/test.com/backend/uploads/";
    }
    
    // 配置所有上传文件访问路径
    registry.addResourceHandler("/uploads/**")
            .addResourceLocations(uploadPath)
            .setCachePeriod(3600); // 缓存1小时
}
```

#### **1.2 application.yml 配置优化**
**文件**: `backend/main/src/main/resources/application.yml`

**新增配置**:
```yaml
# 文件上传配置（生产环境配置）
file:
  upload:
    path: /www/wwwroot/test.com/backend/uploads
    avatar-dir: /www/wwwroot/test.com/backend/uploads/images/avatars
    product-dir: /www/wwwroot/test.com/backend/uploads/images/products
    news-dir: /www/wwwroot/test.com/backend/uploads/images/news
    encyclopedia-dir: /www/wwwroot/test.com/backend/uploads/images/encyclopedia
```

#### **1.3 FileUploadConfig.java 增强**
**文件**: `backend/main/src/main/java/com/agriculture/config/FileUploadConfig.java`

**新增功能**:
- 支持多种类型的文件目录配置
- 自动创建目录结构
- 开发环境和生产环境自适应

### **2. 前端配置修复**

#### **2.1 统一图片URL处理工具**
**文件**: `src/utils/imageUtils.js`

**新增功能**:
```javascript
// 获取API基础URL
export const getApiBaseUrl = () => {
  return process.env.VUE_APP_BASE_API || process.env.VUE_APP_API_URL || 'http://localhost:8081';
};

// 处理产品图片URL - 统一处理逻辑
export const processProductImageUrl = (url, productName, productId) => {
  // 支持完整URL、相对路径、文件名等多种格式
  // 自动使用环境变量配置的API地址
};
```

#### **2.2 修复硬编码URL问题**
**修复文件**:
- `src/api/news.js` - 新闻图片URL处理
- `src/views/shop/ProductDetail.vue` - 商品详情图片处理
- 其他相关组件的图片URL处理

**修复方式**:
- 移除硬编码的 `localhost:8081`
- 使用环境变量 `process.env.VUE_APP_BASE_API`
- 统一图片URL处理逻辑

---

## 📁 **目录结构配置**

### **完整目录结构**
```
/www/wwwroot/test.com/backend/uploads/
├── images/
│   ├── avatars/          # 用户头像
│   ├── products/         # 商品图片
│   ├── news/            # 新闻图片
│   ├── encyclopedia/    # 百科图片
│   ├── banners/         # 轮播图片
│   ├── categories/      # 分类图片
│   ├── qrcodes/         # 二维码图片
│   └── certificates/    # 证书图片
├── documents/           # 文档文件
├── videos/             # 视频文件
└── temp/               # 临时文件
```

### **访问URL格式**
- **用户头像**: `http://**************:8081/uploads/images/avatars/filename.jpg`
- **商品图片**: `http://**************:8081/uploads/images/products/filename.jpg`
- **新闻图片**: `http://**************:8081/uploads/images/news/filename.jpg`
- **百科图片**: `http://**************:8081/uploads/images/encyclopedia/filename.jpg`

---

## 🚀 **部署执行步骤**

### **步骤1: 创建服务器目录结构**
```bash
# 上传并执行目录创建脚本
chmod +x setup-production-directories.sh
./setup-production-directories.sh
```

### **步骤2: 修复数据库图片路径**
```bash
# 在MySQL中执行路径修复脚本
mysql -u root -p agriculture_mall < fix-database-image-paths.sql
```

### **步骤3: 重启后端服务**
```bash
# 重启Java应用以应用新配置
# 在宝塔面板中重启项目，或使用命令行
systemctl restart your-java-app
```

### **步骤4: 重新构建前端**
```bash
# 重新构建前端以应用配置更改
npm run build:prod
```

### **步骤5: 验证配置**
```bash
# 运行验证脚本
chmod +x verify-file-upload-system.sh
./verify-file-upload-system.sh
```

---

## ✅ **验证清单**

### **后端验证**
- [ ] 后端服务正常启动（8081端口）
- [ ] 静态资源路径配置正确
- [ ] 文件上传API正常工作
- [ ] 目录权限设置正确

### **前端验证**
- [ ] 前端服务正常访问（8200端口）
- [ ] 图片显示正常
- [ ] API调用使用正确的URL
- [ ] 跨域请求正常

### **功能验证**
- [ ] 商品图片正常显示
- [ ] 用户头像上传和显示
- [ ] 新闻图片正常加载
- [ ] 文件上传功能正常

---

## 🔍 **问题排查指南**

### **常见问题1: 图片404错误**
**症状**: 前端显示图片加载失败
**排查步骤**:
1. 检查图片文件是否存在于服务器
2. 验证URL路径是否正确
3. 检查Nginx静态资源配置
4. 确认文件权限设置

### **常见问题2: 文件上传失败**
**症状**: 上传文件时返回错误
**排查步骤**:
1. 检查目录是否存在且有写权限
2. 验证文件大小是否超过限制
3. 检查后端日志错误信息
4. 确认磁盘空间充足

### **常见问题3: 跨域请求失败**
**症状**: 前端API调用被阻止
**排查步骤**:
1. 检查WebConfig中的CORS配置
2. 验证前端域名是否在允许列表中
3. 检查浏览器控制台错误信息
4. 确认请求头配置正确

---

## 📊 **性能优化建议**

### **缓存策略**
- 静态资源缓存时间: 1小时
- 图片资源可设置更长缓存时间
- 使用CDN加速静态资源访问

### **存储优化**
- 定期清理临时文件
- 压缩大尺寸图片
- 实施文件备份策略

### **监控建议**
- 监控磁盘使用情况
- 跟踪文件上传成功率
- 监控API响应时间

---

## 🎯 **后续维护**

### **定期任务**
1. **每周**: 检查磁盘空间使用情况
2. **每月**: 清理过期临时文件
3. **每季度**: 备份重要文件数据

### **升级计划**
1. 考虑使用对象存储服务（如阿里云OSS）
2. 实施图片压缩和格式优化
3. 添加文件病毒扫描功能

---

**配置完成时间**: 2025-07-25  
**配置状态**: ✅ 完成  
**验证状态**: 待执行  
**负责人**: 系统管理员
