/**
 * WebSocket 工具类
 * 用于管理员接收实时通知
 */
class WebSocketManager {
  constructor() {
    this.ws = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectInterval = 3000;
    this.heartbeatInterval = 30000;
    this.heartbeatTimer = null;
    this.isConnected = false;
    this.messageHandlers = [];
    this.adminId = null;
  }

  /**
   * 连接WebSocket
   * @param {string} adminId 管理员ID
   */
  connect(adminId) {
    if (!adminId) {
      console.error('管理员ID不能为空');
      return;
    }

    this.adminId = adminId;
    const baseWsUrl = (process.env.VUE_APP_BASE_API || 'http://localhost:8081').replace('http', 'ws');
    const wsUrl = `${baseWsUrl}/ws/admin/notifications/${adminId}`;
    
    try {
      this.ws = new WebSocket(wsUrl);
      this.setupEventHandlers();
    } catch (error) {
      console.error('WebSocket连接失败:', error);
      this.handleReconnect();
    }
  }

  /**
   * 设置事件处理器
   */
  setupEventHandlers() {
    this.ws.onopen = (event) => {
      console.log('WebSocket连接成功');
      this.isConnected = true;
      this.reconnectAttempts = 0;
      this.startHeartbeat();
      this.notifyHandlers({
        type: 'CONNECTION_STATUS',
        data: { connected: true, message: '连接成功' }
      });
    };

    this.ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        console.log('收到WebSocket消息:', message);
        this.notifyHandlers(message);
      } catch (error) {
        console.error('解析WebSocket消息失败:', error);
        // 处理纯文本消息
        this.notifyHandlers({
          type: 'TEXT_MESSAGE',
          data: { content: event.data }
        });
      }
    };

    this.ws.onclose = (event) => {
      console.log('WebSocket连接关闭:', event.code, event.reason);
      this.isConnected = false;
      this.stopHeartbeat();
      this.notifyHandlers({
        type: 'CONNECTION_STATUS',
        data: { connected: false, message: '连接已断开' }
      });
      
      // 如果不是主动关闭，尝试重连
      if (event.code !== 1000) {
        this.handleReconnect();
      }
    };

    this.ws.onerror = (error) => {
      console.error('WebSocket错误:', error);
      this.notifyHandlers({
        type: 'CONNECTION_ERROR',
        data: { error: '连接错误' }
      });
    };
  }

  /**
   * 处理重连
   */
  handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
      
      setTimeout(() => {
        if (this.adminId) {
          this.connect(this.adminId);
        }
      }, this.reconnectInterval);
    } else {
      console.error('达到最大重连次数，停止重连');
      this.notifyHandlers({
        type: 'CONNECTION_FAILED',
        data: { message: '连接失败，请刷新页面重试' }
      });
    }
  }

  /**
   * 开始心跳
   */
  startHeartbeat() {
    this.heartbeatTimer = setInterval(() => {
      if (this.isConnected && this.ws.readyState === WebSocket.OPEN) {
        this.ws.send(JSON.stringify({ type: 'HEARTBEAT', timestamp: Date.now() }));
      }
    }, this.heartbeatInterval);
  }

  /**
   * 停止心跳
   */
  stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  /**
   * 发送消息
   * @param {object} message 消息对象
   */
  send(message) {
    if (this.isConnected && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket未连接，无法发送消息');
    }
  }

  /**
   * 添加消息处理器
   * @param {function} handler 处理器函数
   */
  addMessageHandler(handler) {
    this.messageHandlers.push(handler);
  }

  /**
   * 移除消息处理器
   * @param {function} handler 处理器函数
   */
  removeMessageHandler(handler) {
    const index = this.messageHandlers.indexOf(handler);
    if (index > -1) {
      this.messageHandlers.splice(index, 1);
    }
  }

  /**
   * 通知所有处理器
   * @param {object} message 消息对象
   */
  notifyHandlers(message) {
    this.messageHandlers.forEach(handler => {
      try {
        handler(message);
      } catch (error) {
        console.error('消息处理器执行失败:', error);
      }
    });
  }

  /**
   * 断开连接
   */
  disconnect() {
    this.stopHeartbeat();
    if (this.ws) {
      this.ws.close(1000, '主动断开连接');
      this.ws = null;
    }
    this.isConnected = false;
    this.adminId = null;
  }

  /**
   * 获取连接状态
   */
  getConnectionStatus() {
    return {
      connected: this.isConnected,
      readyState: this.ws ? this.ws.readyState : WebSocket.CLOSED,
      reconnectAttempts: this.reconnectAttempts
    };
  }
}

// 创建全局实例
const webSocketManager = new WebSocketManager();

export default webSocketManager;