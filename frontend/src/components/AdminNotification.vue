<template>
  <div class="admin-notification">
    <!-- 连接状态指示器 -->
    <div class="connection-status" :class="{ 'connected': isConnected, 'disconnected': !isConnected }">
      <el-badge :value="unreadCount" :hidden="unreadCount === 0" class="notification-badge">
        <el-button 
          :type="isConnected ? 'success' : 'danger'" 
          :icon="isConnected ? 'el-icon-connection' : 'el-icon-warning'"
          size="small"
          circle
          @click="toggleNotificationPanel"
        />
      </el-badge>
      <span class="status-text">{{ connectionStatusText }}</span>
    </div>

    <!-- 通知面板 -->
    <el-drawer
      title="实时通知"
      :visible.sync="showNotificationPanel"
      direction="rtl"
      size="400px"
      :before-close="handleClosePanel"
    >
      <div class="notification-panel">
        <!-- 操作按钮 -->
        <div class="panel-actions">
          <el-button size="mini" @click="markAllAsRead" :disabled="unreadCount === 0">
            全部已读
          </el-button>
          <el-button size="mini" @click="clearAllNotifications">
            清空通知
          </el-button>
          <el-button size="mini" @click="sendTestNotification">
            测试通知
          </el-button>
        </div>

        <!-- 通知列表 -->
        <div class="notification-list">
          <div 
            v-for="notification in notifications" 
            :key="notification.id"
            class="notification-item"
            :class="{ 'unread': !notification.read, 'high-priority': notification.priority === 'high' }"
            @click="markAsRead(notification)"
          >
            <div class="notification-header">
              <span class="notification-type" :class="getTypeClass(notification.type)">
                {{ getTypeText(notification.type) }}
              </span>
              <span class="notification-time">{{ formatTime(notification.timestamp) }}</span>
            </div>
            <div class="notification-title">{{ notification.title }}</div>
            <div class="notification-content">{{ notification.content }}</div>
            <div v-if="notification.data" class="notification-data">
              <el-button size="mini" type="primary" @click.stop="handleNotificationAction(notification)">
                查看详情
              </el-button>
            </div>
          </div>
          
          <div v-if="notifications.length === 0" class="empty-notifications">
            <el-empty description="暂无通知" :image-size="80"></el-empty>
          </div>
        </div>
      </div>
    </el-drawer>

    <!-- 通知弹窗 -->
    <el-dialog
      :title="currentNotification?.title"
      :visible.sync="showNotificationDialog"
      width="500px"
      @close="currentNotification = null"
    >
      <div v-if="currentNotification">
        <p><strong>类型：</strong>{{ getTypeText(currentNotification.type) }}</p>
        <p><strong>时间：</strong>{{ formatTime(currentNotification.timestamp) }}</p>
        <p><strong>内容：</strong>{{ currentNotification.content }}</p>
        <div v-if="currentNotification.data">
          <p><strong>相关数据：</strong></p>
          <pre>{{ JSON.stringify(currentNotification.data, null, 2) }}</pre>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showNotificationDialog = false">关闭</el-button>
        <el-button 
          v-if="currentNotification?.type === 'SELLER_APPLICATION'"
          type="primary" 
          @click="goToApplicationDetail"
        >
          去审核
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import webSocketManager from '@/utils/websocket'
import { getUserInfo } from '@/utils/auth'

export default {
  name: 'AdminNotification',
  data() {
    return {
      isConnected: false,
      notifications: [],
      unreadCount: 0,
      showNotificationPanel: false,
      showNotificationDialog: false,
      currentNotification: null,
      nextNotificationId: 1
    }
  },
  computed: {
    connectionStatusText() {
      return this.isConnected ? '已连接' : '未连接'
    }
  },
  mounted() {
    this.initWebSocket()
  },
  beforeDestroy() {
    this.disconnectWebSocket()
  },
  methods: {
    /**
     * 初始化WebSocket连接
     */
    initWebSocket() {
      const userInfo = getUserInfo()
      if (!userInfo || !userInfo.id) {
        console.warn('用户信息不存在，无法建立WebSocket连接')
        return
      }

      // 添加消息处理器
      webSocketManager.addMessageHandler(this.handleWebSocketMessage)
      
      // 连接WebSocket
      webSocketManager.connect(userInfo.id.toString())
    },

    /**
     * 断开WebSocket连接
     */
    disconnectWebSocket() {
      webSocketManager.removeMessageHandler(this.handleWebSocketMessage)
      webSocketManager.disconnect()
    },

    /**
     * 处理WebSocket消息
     */
    handleWebSocketMessage(message) {
      console.log('收到通知消息:', message)
      
      switch (message.type) {
        case 'CONNECTION_STATUS':
          this.isConnected = message.data.connected
          if (message.data.connected) {
            this.$message.success('通知服务已连接')
          } else {
            this.$message.warning('通知服务已断开')
          }
          break
          
        case 'CONNECTION_ERROR':
        case 'CONNECTION_FAILED':
          this.isConnected = false
          this.$message.error(message.data.message || '连接失败')
          break
          
        case 'SELLER_APPLICATION':
        case 'APPLICATION_STATUS_UPDATE':
        case 'TEST':
          this.addNotification(message)
          break
          
        case 'TEXT_MESSAGE':
          this.addNotification({
            type: 'SYSTEM',
            title: '系统消息',
            content: message.data.content,
            timestamp: new Date().toISOString(),
            priority: 'medium'
          })
          break
          
        default:
          console.log('未知消息类型:', message.type)
      }
    },

    /**
     * 添加通知
     */
    addNotification(notification) {
      const newNotification = {
        id: this.nextNotificationId++,
        ...notification,
        read: false,
        timestamp: notification.timestamp || new Date().toISOString()
      }
      
      this.notifications.unshift(newNotification)
      this.updateUnreadCount()
      
      // 显示桌面通知
      this.showDesktopNotification(newNotification)
      
      // 高优先级通知自动弹窗
      if (notification.priority === 'high' && notification.requireConfirmation) {
        this.showNotificationPopup(newNotification)
      }
    },

    /**
     * 显示桌面通知
     */
    showDesktopNotification(notification) {
      if ('Notification' in window && Notification.permission === 'granted') {
        new Notification(notification.title, {
          body: notification.content,
          icon: '/favicon.ico'
        })
      } else if ('Notification' in window && Notification.permission !== 'denied') {
        Notification.requestPermission().then(permission => {
          if (permission === 'granted') {
            new Notification(notification.title, {
              body: notification.content,
              icon: '/favicon.ico'
            })
          }
        })
      }
    },

    /**
     * 显示通知弹窗
     */
    showNotificationPopup(notification) {
      this.currentNotification = notification
      this.showNotificationDialog = true
      this.markAsRead(notification)
    },

    /**
     * 标记为已读
     */
    markAsRead(notification) {
      if (!notification.read) {
        notification.read = true
        this.updateUnreadCount()
      }
    },

    /**
     * 全部标记为已读
     */
    markAllAsRead() {
      this.notifications.forEach(notification => {
        notification.read = true
      })
      this.updateUnreadCount()
    },

    /**
     * 清空所有通知
     */
    clearAllNotifications() {
      this.$confirm('确定要清空所有通知吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.notifications = []
        this.updateUnreadCount()
        this.$message.success('已清空所有通知')
      })
    },

    /**
     * 发送测试通知
     */
    async sendTestNotification() {
      try {
        await this.$http.post('/api/admin/notifications/test', null, {
          params: { message: '这是一条测试通知 - ' + new Date().toLocaleTimeString() }
        })
        this.$message.success('测试通知已发送')
      } catch (error) {
        this.$message.error('发送测试通知失败')
      }
    },

    /**
     * 更新未读数量
     */
    updateUnreadCount() {
      this.unreadCount = this.notifications.filter(n => !n.read).length
    },

    /**
     * 切换通知面板
     */
    toggleNotificationPanel() {
      this.showNotificationPanel = !this.showNotificationPanel
    },

    /**
     * 关闭通知面板
     */
    handleClosePanel() {
      this.showNotificationPanel = false
    },

    /**
     * 处理通知操作
     */
    handleNotificationAction(notification) {
      this.currentNotification = notification
      this.showNotificationDialog = true
      this.markAsRead(notification)
    },

    /**
     * 跳转到申请详情
     */
    goToApplicationDetail() {
      if (this.currentNotification?.data) {
        this.$router.push(`/admin/seller-applications/${this.currentNotification.data}`)
        this.showNotificationDialog = false
        this.showNotificationPanel = false
      }
    },

    /**
     * 获取类型样式类
     */
    getTypeClass(type) {
      const typeClasses = {
        'SELLER_APPLICATION': 'type-application',
        'APPLICATION_STATUS_UPDATE': 'type-status',
        'TEST': 'type-test',
        'SYSTEM': 'type-system'
      }
      return typeClasses[type] || 'type-default'
    },

    /**
     * 获取类型文本
     */
    getTypeText(type) {
      const typeTexts = {
        'SELLER_APPLICATION': '销售者申请',
        'APPLICATION_STATUS_UPDATE': '状态更新',
        'TEST': '测试通知',
        'SYSTEM': '系统消息'
      }
      return typeTexts[type] || '未知类型'
    },

    /**
     * 格式化时间
     */
    formatTime(timestamp) {
      if (!timestamp) return ''
      const date = new Date(timestamp)
      const now = new Date()
      const diff = now - date
      
      if (diff < 60000) { // 1分钟内
        return '刚刚'
      } else if (diff < 3600000) { // 1小时内
        return Math.floor(diff / 60000) + '分钟前'
      } else if (diff < 86400000) { // 24小时内
        return Math.floor(diff / 3600000) + '小时前'
      } else {
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString()
      }
    }
  }
}
</script>

<style scoped>
.admin-notification {
  position: relative;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.connection-status.connected .status-text {
  color: #67c23a;
}

.connection-status.disconnected .status-text {
  color: #f56c6c;
}

.status-text {
  font-size: 12px;
  font-weight: 500;
}

.notification-badge {
  cursor: pointer;
}

.notification-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.panel-actions {
  padding: 16px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.notification-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.notification-item {
  padding: 12px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.notification-item:hover {
  background-color: #f5f7fa;
  border-color: #c0c4cc;
}

.notification-item.unread {
  border-left: 4px solid #409eff;
  background-color: #ecf5ff;
}

.notification-item.high-priority {
  border-left-color: #f56c6c;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.notification-type {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 2px;
  color: white;
  font-weight: 500;
}

.type-application {
  background-color: #409eff;
}

.type-status {
  background-color: #67c23a;
}

.type-test {
  background-color: #e6a23c;
}

.type-system {
  background-color: #909399;
}

.type-default {
  background-color: #c0c4cc;
}

.notification-time {
  font-size: 12px;
  color: #909399;
}

.notification-title {
  font-weight: 500;
  margin-bottom: 4px;
  color: #303133;
}

.notification-content {
  font-size: 14px;
  color: #606266;
  line-height: 1.4;
  margin-bottom: 8px;
}

.notification-data {
  text-align: right;
}

.empty-notifications {
  text-align: center;
  padding: 40px 20px;
}

.dialog-footer {
  text-align: right;
}

pre {
  background-color: #f5f7fa;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}
</style>