# SFAP农品汇平台数据库部署说明

## 📋 部署概述

**文件名**: `SFAP_database_deployment.sql`  
**版本**: v4.0  
**创建日期**: 2025-01-25  
**目标数据库**: MySQL 8.0+  
**字符集**: utf8mb4  
**排序规则**: utf8mb4_unicode_ci  

---

## 🎯 部署目标

本SQL文件用于在阿里云服务器上部署SFAP农品汇平台的完整数据库结构，包含：

- ✅ 完整的数据库和表结构
- ✅ 索引和约束定义
- ✅ 视图定义
- ✅ 触发器和存储过程
- ✅ 初始数据和配置
- ✅ 用户权限设置

---

## 📊 数据库结构概览

### 核心表统计
| 模块 | 表数量 | 主要功能 |
|------|--------|----------|
| 用户管理 | 3张表 | 用户信息、地址、行为记录 |
| 商品管理 | 5张表 | 商品、分类、图片、收藏、评论 |
| 订单管理 | 2张表 | 订单主表、订单项 |
| 溯源系统 | 3张表 | 溯源记录、查询记录、事件 |
| 销售者管理 | 2张表 | 申请、店铺信息 |
| 价格数据 | 2张表 | 实时价格、历史价格 |
| AI预测 | 3张表 | 模型、预测结果、评估 |
| 新闻百科 | 4张表 | 新闻、图片、百科、分类 |
| 系统管理 | 3张表 | 配置、访问日志、错误日志 |
| **总计** | **27张表** | **完整业务覆盖** |

### 视图统计
- `v_product_traceability` - 产品溯源视图
- `v_user_behavior_analysis` - 用户行为分析视图
- `v_seller_basic_stats` - 销售者统计视图
- `v_latest_prices` - 最新价格视图
- `v_system_realtime_status` - 系统实时状态视图

### 触发器统计
- 用户统计更新触发器（2个）
- 商品统计更新触发器（2个）
- 溯源查询计数触发器（1个）

### 存储过程统计
- `GetUserStatistics` - 获取用户统计信息
- `GetProductRecommendations` - 获取商品推荐

---

## 🚀 部署步骤

### 1. 环境准备

#### 1.1 MySQL服务器要求
```bash
# 最低要求
MySQL版本: 8.0+
内存: 4GB+
磁盘空间: 20GB+
字符集支持: utf8mb4
```

#### 1.2 连接到MySQL服务器
```bash
# 使用root用户连接
mysql -u root -p

# 或者使用MySQL客户端工具连接
# 主机: 120.26.140.157
# 端口: 3306
# 用户: root
# 密码: [您的root密码]
```

### 2. 执行部署SQL

#### 2.1 上传SQL文件
```bash
# 将SFAP_database_deployment.sql上传到服务器
scp SFAP_database_deployment.sql root@120.26.140.157:/tmp/
```

#### 2.2 执行SQL文件
```bash
# 方法1: 命令行执行
mysql -u root -p < /tmp/SFAP_database_deployment.sql

# 方法2: MySQL客户端执行
mysql -u root -p
mysql> source /tmp/SFAP_database_deployment.sql;

# 方法3: 使用图形化工具
# 在Navicat、phpMyAdmin等工具中导入并执行SQL文件
```

### 3. 验证部署结果

#### 3.1 检查数据库创建
```sql
SHOW DATABASES LIKE 'agriculture_mall';
USE agriculture_mall;
```

#### 3.2 检查表结构
```sql
-- 查看所有表
SHOW TABLES;

-- 查看表数量
SELECT COUNT(*) AS table_count 
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'agriculture_mall' 
  AND TABLE_TYPE = 'BASE TABLE';
```

#### 3.3 检查视图
```sql
-- 查看所有视图
SELECT TABLE_NAME AS view_name 
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'agriculture_mall' 
  AND TABLE_TYPE = 'VIEW';
```

#### 3.4 检查用户权限
```sql
-- 检查应用用户是否创建成功
SELECT User, Host FROM mysql.user WHERE User = 'sfap_user';

-- 检查权限
SHOW GRANTS FOR 'sfap_user'@'localhost';
```

#### 3.5 检查初始数据
```sql
-- 检查管理员用户
SELECT id, username, role FROM user WHERE role = 'admin';

-- 检查商品分类
SELECT id, name, parent_id, level FROM category ORDER BY sort_order;

-- 检查系统配置
SELECT config_key, config_value FROM system_configs;
```

---

## 🔧 配置说明

### 1. 默认用户账号

#### 系统管理员
- **用户名**: `admin`
- **密码**: `admin123`
- **角色**: `admin`
- **说明**: 部署后请立即修改密码

### 2. 数据库用户

#### 应用专用用户
- **用户名**: `sfap_user`
- **密码**: `sfap_secure_password_2025`
- **权限**: 数据库完整操作权限
- **说明**: 生产环境请修改为更安全的密码

### 3. 重要配置项

#### 系统配置
```sql
-- 网站基本信息
site_name: SFAP农品汇
site_description: 智慧农业综合服务平台

-- 上传配置
max_upload_size: 10MB
allowed_image_types: jpg,jpeg,png,gif,webp

-- AI预测配置
ai_prediction_enabled: true

-- 溯源配置
traceability_qr_domain: https://sfap.com/trace/
```

---

## ⚠️ 安全注意事项

### 1. 密码安全
```sql
-- 部署后立即修改管理员密码
UPDATE user SET password = '$2a$10$新的加密密码' WHERE username = 'admin';

-- 修改数据库用户密码
ALTER USER 'sfap_user'@'localhost' IDENTIFIED BY '新的安全密码';
ALTER USER 'sfap_user'@'%' IDENTIFIED BY '新的安全密码';
```

### 2. 权限控制
```sql
-- 生产环境建议移除远程访问权限
DROP USER 'sfap_user'@'%';

-- 只保留本地访问
-- 'sfap_user'@'localhost' 已足够
```

### 3. 防火墙配置
```bash
# 限制MySQL端口访问
firewall-cmd --permanent --add-rich-rule="rule family='ipv4' source address='127.0.0.1' port protocol='tcp' port='3306' accept"
firewall-cmd --reload
```

---

## 🔍 故障排查

### 1. 常见错误

#### 字符集错误
```sql
-- 检查字符集设置
SHOW VARIABLES LIKE 'character_set%';
SHOW VARIABLES LIKE 'collation%';

-- 如果字符集不正确，修改my.cnf
[mysqld]
character-set-server=utf8mb4
collation-server=utf8mb4_unicode_ci
```

#### 权限错误
```sql
-- 检查用户权限
SHOW GRANTS FOR 'sfap_user'@'localhost';

-- 重新授权
GRANT ALL PRIVILEGES ON agriculture_mall.* TO 'sfap_user'@'localhost';
FLUSH PRIVILEGES;
```

#### 外键约束错误
```sql
-- 临时禁用外键检查
SET FOREIGN_KEY_CHECKS = 0;
-- 执行SQL
SET FOREIGN_KEY_CHECKS = 1;
```

### 2. 性能优化

#### 索引优化
```sql
-- 检查索引使用情况
SHOW INDEX FROM product;
SHOW INDEX FROM `order`;

-- 分析查询性能
EXPLAIN SELECT * FROM product WHERE category_id = 1 AND status = 1;
```

#### 配置优化
```sql
-- 检查重要配置
SHOW VARIABLES LIKE 'innodb_buffer_pool_size';
SHOW VARIABLES LIKE 'max_connections';
SHOW VARIABLES LIKE 'query_cache_size';
```

---

## 📈 监控建议

### 1. 数据库监控
```sql
-- 监控表大小
SELECT 
    TABLE_NAME,
    ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'agriculture_mall'
ORDER BY (DATA_LENGTH + INDEX_LENGTH) DESC;

-- 监控连接数
SHOW STATUS LIKE 'Threads_connected';
SHOW STATUS LIKE 'Max_used_connections';
```

### 2. 性能监控
```sql
-- 慢查询监控
SHOW VARIABLES LIKE 'slow_query_log';
SHOW VARIABLES LIKE 'long_query_time';

-- 查看慢查询
SELECT * FROM mysql.slow_log ORDER BY start_time DESC LIMIT 10;
```

---

## 📞 技术支持

### 联系信息
- **项目**: SFAP农品汇平台
- **版本**: v4.0
- **部署日期**: 2025-01-25
- **技术支持**: 开发团队

### 相关文档
- 项目部署指南: `SFAP农品汇平台阿里云部署指南.md`
- 配置修改总结: `SFAP部署配置修改总结.md`
- API文档: 项目内API文档

---

**部署完成后，请保存此文档作为运维参考！**
