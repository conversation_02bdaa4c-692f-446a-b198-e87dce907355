/**
 * 溯源查询功能测试脚本
 * 用于测试前后端溯源查询接口的连通性和响应
 */

const axios = require('axios');

// 测试配置
const TEST_CONFIG = {
  baseURL: 'http://120.26.140.157:8081',
  timeout: 60000,
  testTraceCodes: [
    'SFAPA2401130900500214BB0',
    'SFAPA24011009002008E8BC9'
  ]
};

// 创建axios实例
const api = axios.create({
  baseURL: TEST_CONFIG.baseURL,
  timeout: TEST_CONFIG.timeout,
  headers: {
    'Content-Type': 'application/json',
    'User-Agent': 'TraceabilityTest/1.0'
  }
});

/**
 * 测试溯源查询接口
 */
async function testTraceabilityQuery(traceCode) {
  console.log(`\n🔍 测试溯源码: ${traceCode}`);
  console.log('=' .repeat(50));
  
  const startTime = Date.now();
  
  try {
    const requestData = {
      traceCode: traceCode,
      source: 'test',
      location: '测试位置',
      ipAddress: '127.0.0.1',
      deviceInfo: 'Test Browser',
      userId: null
    };
    
    console.log('📤 发送请求:', JSON.stringify(requestData, null, 2));
    
    const response = await api.post('/api/traceability/query', requestData);
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log(`⏱️  响应时间: ${duration}ms`);
    console.log('📥 响应状态:', response.status);
    console.log('📥 响应数据:', JSON.stringify(response.data, null, 2));
    
    if (response.data.success) {
      console.log('✅ 查询成功');
      return { success: true, duration, data: response.data };
    } else {
      console.log('❌ 查询失败:', response.data.message);
      return { success: false, duration, error: response.data.message };
    }
    
  } catch (error) {
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log(`⏱️  响应时间: ${duration}ms`);
    console.log('❌ 请求失败:', error.message);
    
    if (error.response) {
      console.log('📥 错误状态:', error.response.status);
      console.log('📥 错误数据:', JSON.stringify(error.response.data, null, 2));
    }
    
    return { success: false, duration, error: error.message };
  }
}

/**
 * 测试服务器连通性
 */
async function testServerConnectivity() {
  console.log('🌐 测试服务器连通性...');
  
  try {
    const response = await api.get('/api/health', { timeout: 5000 });
    console.log('✅ 服务器连接正常');
    return true;
  } catch (error) {
    console.log('❌ 服务器连接失败:', error.message);
    return false;
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🚀 开始溯源查询功能测试');
  console.log('测试服务器:', TEST_CONFIG.baseURL);
  console.log('超时设置:', TEST_CONFIG.timeout + 'ms');
  
  // 测试服务器连通性
  const isServerOnline = await testServerConnectivity();
  if (!isServerOnline) {
    console.log('❌ 服务器不可用，终止测试');
    return;
  }
  
  // 测试溯源查询
  const results = [];
  for (const traceCode of TEST_CONFIG.testTraceCodes) {
    const result = await testTraceabilityQuery(traceCode);
    results.push({ traceCode, ...result });
  }
  
  // 输出测试总结
  console.log('\n📊 测试总结');
  console.log('=' .repeat(50));
  
  const successCount = results.filter(r => r.success).length;
  const totalCount = results.length;
  const avgDuration = results.reduce((sum, r) => sum + r.duration, 0) / totalCount;
  
  console.log(`总测试数: ${totalCount}`);
  console.log(`成功数: ${successCount}`);
  console.log(`失败数: ${totalCount - successCount}`);
  console.log(`成功率: ${(successCount / totalCount * 100).toFixed(1)}%`);
  console.log(`平均响应时间: ${avgDuration.toFixed(0)}ms`);
  
  // 详细结果
  console.log('\n📋 详细结果:');
  results.forEach(result => {
    const status = result.success ? '✅' : '❌';
    console.log(`${status} ${result.traceCode}: ${result.duration}ms`);
    if (!result.success) {
      console.log(`   错误: ${result.error}`);
    }
  });
  
  if (successCount === totalCount) {
    console.log('\n🎉 所有测试通过！');
  } else {
    console.log('\n⚠️  部分测试失败，请检查日志');
  }
}

// 运行测试
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testTraceabilityQuery,
  testServerConnectivity,
  runAllTests
};
