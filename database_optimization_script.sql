-- =====================================================
-- SFAP平台农品汇模块用户互动功能系统优化脚本
-- 版本: V1.0
-- 创建时间: 2025-01-17
-- 说明: 基于现有优秀表结构进行精细化优化
-- =====================================================

-- 1. 收藏夹分类功能增强
-- =====================================================

-- 1.1 为product_favorite表添加收藏夹分类字段
ALTER TABLE product_favorite 
ADD COLUMN folder_name VARCHAR(50) DEFAULT '默认收藏夹' COMMENT '收藏夹名称';

-- 1.2 添加收藏夹相关索引
CREATE INDEX idx_user_folder ON product_favorite(user_id, folder_name);
CREATE INDEX idx_folder_created ON product_favorite(folder_name, created_at);

-- 1.3 创建用户收藏夹管理表
CREATE TABLE user_favorite_folders (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    folder_name VARCHAR(50) NOT NULL COMMENT '收藏夹名称',
    description VARCHAR(200) COMMENT '收藏夹描述',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    is_default TINYINT DEFAULT 0 COMMENT '是否默认收藏夹：0-否，1-是',
    product_count INT DEFAULT 0 COMMENT '收藏商品数量',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_user_folder (user_id, folder_name),
    INDEX idx_user_sort (user_id, sort_order),
    INDEX idx_user_default (user_id, is_default),
    
    FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户收藏夹管理表';

-- 1.4 为每个用户创建默认收藏夹
INSERT INTO user_favorite_folders (user_id, folder_name, is_default, product_count)
SELECT 
    u.id,
    '默认收藏夹',
    1,
    COALESCE(f.folder_count, 0)
FROM user u
LEFT JOIN (
    SELECT user_id, COUNT(*) as folder_count 
    FROM product_favorite 
    WHERE deleted = 0 
    GROUP BY user_id
) f ON u.id = f.user_id
WHERE u.deleted = 0;

-- 2. 评价系统优化
-- =====================================================

-- 2.1 为product_review表添加评价类型字段
ALTER TABLE product_review 
ADD COLUMN review_type TINYINT DEFAULT 1 COMMENT '评价类型：1-商品评价，2-服务评价，3-物流评价';

-- 2.2 添加评价追加功能字段
ALTER TABLE product_review 
ADD COLUMN is_additional TINYINT DEFAULT 0 COMMENT '是否追加评价：0-否，1-是',
ADD COLUMN parent_review_id BIGINT NULL COMMENT '父评价ID（追加评价时使用）',
ADD COLUMN additional_days INT DEFAULT 0 COMMENT '追加评价距离首次评价天数';

-- 2.3 添加评价相关索引
CREATE INDEX idx_review_type ON product_review(review_type, created_at);
CREATE INDEX idx_parent_review ON product_review(parent_review_id);
CREATE INDEX idx_additional ON product_review(is_additional, created_at);

-- 3. 性能优化索引
-- =====================================================

-- 3.1 商品表性能索引优化
CREATE INDEX idx_product_stats ON product(like_count, favorite_count, review_count);
CREATE INDEX idx_product_rating_sales ON product(rating, sales_count);
CREATE INDEX idx_product_seller_status ON product(seller_id, status, created_at);

-- 3.2 用户行为分析索引
CREATE INDEX idx_user_stats ON user(total_reviews, total_likes_given, total_favorites);

-- 3.3 评价时间范围查询优化
CREATE INDEX idx_review_time_range ON product_review(product_id, created_at, rating);
CREATE INDEX idx_review_user_time ON product_review(user_id, created_at, rating);

-- 4. 数据一致性触发器
-- =====================================================

-- 4.1 商品点赞数量同步触发器
DELIMITER $$
CREATE TRIGGER tr_product_likes_insert 
AFTER INSERT ON product_likes
FOR EACH ROW
BEGIN
    UPDATE product SET like_count = like_count + 1 WHERE id = NEW.product_id;
    UPDATE user SET total_likes_given = total_likes_given + 1 WHERE id = NEW.user_id;
END$$

CREATE TRIGGER tr_product_likes_delete 
AFTER DELETE ON product_likes
FOR EACH ROW
BEGIN
    UPDATE product SET like_count = like_count - 1 WHERE id = OLD.product_id;
    UPDATE user SET total_likes_given = total_likes_given - 1 WHERE id = OLD.user_id;
END$$

-- 4.2 商品收藏数量同步触发器
CREATE TRIGGER tr_product_favorite_insert 
AFTER INSERT ON product_favorite
FOR EACH ROW
BEGIN
    UPDATE product SET favorite_count = favorite_count + 1 WHERE id = NEW.product_id;
    UPDATE user SET total_favorites = total_favorites + 1 WHERE id = NEW.user_id;
    UPDATE user_favorite_folders SET product_count = product_count + 1 
    WHERE user_id = NEW.user_id AND folder_name = NEW.folder_name;
END$$

CREATE TRIGGER tr_product_favorite_delete 
AFTER DELETE ON product_favorite
FOR EACH ROW
BEGIN
    UPDATE product SET favorite_count = favorite_count - 1 WHERE id = OLD.product_id;
    UPDATE user SET total_favorites = total_favorites - 1 WHERE id = OLD.user_id;
    UPDATE user_favorite_folders SET product_count = product_count - 1 
    WHERE user_id = OLD.user_id AND folder_name = OLD.folder_name;
END$$

-- 4.3 商品评价数量和评分同步触发器
CREATE TRIGGER tr_product_review_insert 
AFTER INSERT ON product_review
FOR EACH ROW
BEGIN
    DECLARE avg_rating DECIMAL(3,2);
    
    UPDATE product SET review_count = review_count + 1 WHERE id = NEW.product_id;
    UPDATE user SET total_reviews = total_reviews + 1 WHERE id = NEW.user_id;
    
    SELECT AVG(rating) INTO avg_rating 
    FROM product_review 
    WHERE product_id = NEW.product_id AND status = 1;
    
    UPDATE product SET rating = COALESCE(avg_rating, 0) WHERE id = NEW.product_id;
END$$

CREATE TRIGGER tr_product_review_update 
AFTER UPDATE ON product_review
FOR EACH ROW
BEGIN
    DECLARE avg_rating DECIMAL(3,2);
    
    IF OLD.rating != NEW.rating THEN
        SELECT AVG(rating) INTO avg_rating 
        FROM product_review 
        WHERE product_id = NEW.product_id AND status = 1;
        
        UPDATE product SET rating = COALESCE(avg_rating, 0) WHERE id = NEW.product_id;
    END IF;
END$$

CREATE TRIGGER tr_product_review_delete 
AFTER DELETE ON product_review
FOR EACH ROW
BEGIN
    DECLARE avg_rating DECIMAL(3,2);
    
    UPDATE product SET review_count = review_count - 1 WHERE id = OLD.product_id;
    UPDATE user SET total_reviews = total_reviews - 1 WHERE id = OLD.user_id;
    
    SELECT AVG(rating) INTO avg_rating 
    FROM product_review 
    WHERE product_id = OLD.product_id AND status = 1;
    
    UPDATE product SET rating = COALESCE(avg_rating, 0) WHERE id = OLD.product_id;
END$$

DELIMITER ;

-- 5. 数据修复和初始化
-- =====================================================

-- 5.1 修复现有收藏数据的folder_name
UPDATE product_favorite SET folder_name = '默认收藏夹' WHERE folder_name IS NULL;

-- 5.2 重新计算商品统计数据
UPDATE product p SET 
    like_count = (SELECT COUNT(*) FROM product_likes pl WHERE pl.product_id = p.id),
    favorite_count = (SELECT COUNT(*) FROM product_favorite pf WHERE pf.product_id = p.id AND pf.deleted = 0),
    review_count = (SELECT COUNT(*) FROM product_review pr WHERE pr.product_id = p.id),
    rating = (SELECT COALESCE(AVG(rating), 0) FROM product_review pr WHERE pr.product_id = p.id AND pr.status = 1);

-- 5.3 重新计算用户统计数据
UPDATE user u SET 
    total_likes_given = (SELECT COUNT(*) FROM product_likes pl WHERE pl.user_id = u.id),
    total_favorites = (SELECT COUNT(*) FROM product_favorite pf WHERE pf.user_id = u.id AND pf.deleted = 0),
    total_reviews = (SELECT COUNT(*) FROM product_review pr WHERE pr.user_id = u.id);

-- 6. 创建数据分析视图
-- =====================================================

-- 6.1 商品互动统计视图
CREATE VIEW v_product_interaction_stats AS
SELECT 
    p.id,
    p.name,
    p.like_count,
    p.favorite_count,
    p.review_count,
    p.rating,
    p.view_count,
    p.sales_count,
    ROUND((p.like_count + p.favorite_count + p.review_count) / 3.0, 2) as interaction_score,
    CASE 
        WHEN p.rating >= 4.5 THEN '优秀'
        WHEN p.rating >= 4.0 THEN '良好'
        WHEN p.rating >= 3.0 THEN '一般'
        ELSE '待改进'
    END as rating_level
FROM product p
WHERE p.deleted = 0;

-- 6.2 用户活跃度统计视图
CREATE VIEW v_user_activity_stats AS
SELECT 
    u.id,
    u.username,
    u.total_likes_given,
    u.total_favorites,
    u.total_reviews,
    (u.total_likes_given + u.total_favorites + u.total_reviews) as total_interactions,
    CASE 
        WHEN (u.total_likes_given + u.total_favorites + u.total_reviews) >= 50 THEN '高活跃'
        WHEN (u.total_likes_given + u.total_favorites + u.total_reviews) >= 20 THEN '中活跃'
        WHEN (u.total_likes_given + u.total_favorites + u.total_reviews) >= 5 THEN '低活跃'
        ELSE '新用户'
    END as activity_level
FROM user u
WHERE u.deleted = 0;

-- =====================================================
-- 优化脚本执行完成
-- =====================================================
