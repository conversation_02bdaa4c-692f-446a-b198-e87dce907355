-- =====================================================
-- SFAP用户认证系统修复SQL脚本
-- 解决登录认证失败和管理员权限异常问题
-- =====================================================

-- 1. 检查当前用户数据状态
SELECT 
    '=== 当前用户数据状态检查 ===' as section,
    NULL as id, NULL as username, NULL as role, NULL as user_type, NULL as password_length, NULL as status;

SELECT 
    id,
    username,
    role,
    user_type,
    CHAR_LENGTH(password) as password_length,
    status,
    created_at,
    updated_at,
    CASE 
        WHEN role != COALESCE(user_type, 'user') THEN '角色不一致'
        WHEN CHAR_LENGTH(password) < 50 THEN '密码可能未加密'
        WHEN status != 1 THEN '用户被禁用'
        ELSE '正常'
    END as issue_status
FROM user 
WHERE deleted = 0
ORDER BY 
    CASE WHEN username LIKE '%admin%' THEN 0 ELSE 1 END,
    id;

-- 2. 专门检查test_user用户
SELECT 
    '=== test_user用户检查 ===' as section,
    NULL as id, NULL as username, NULL as password_info, NULL as role, NULL as user_type, NULL as status;

SELECT 
    id,
    username,
    CONCAT('长度:', CHAR_LENGTH(password), ' 前缀:', LEFT(password, 10), '...') as password_info,
    role,
    user_type,
    status,
    created_at,
    updated_at
FROM user 
WHERE username = 'test_user' AND deleted = 0;

-- 3. 检查ID为18的管理员用户
SELECT 
    '=== ID为18的管理员用户检查 ===' as section,
    NULL as id, NULL as username, NULL as role, NULL as user_type, NULL as status, NULL as issue;

SELECT 
    id,
    username,
    role,
    user_type,
    status,
    created_at,
    updated_at,
    CASE 
        WHEN role != 'admin' THEN CONCAT('角色错误: ', role, ' (应为admin)')
        WHEN user_type != 'admin' THEN CONCAT('用户类型错误: ', COALESCE(user_type, 'NULL'), ' (应为admin)')
        WHEN status != 1 THEN CONCAT('状态异常: ', status, ' (应为1)')
        ELSE '正常'
    END as issue
FROM user 
WHERE id = 18 AND deleted = 0;

-- 4. 修复ID为18的管理员用户权限
UPDATE user 
SET role = 'admin', 
    user_type = 'admin', 
    status = 1,
    updated_at = NOW() 
WHERE id = 18 AND deleted = 0;

-- 5. 修复所有管理员用户权限
UPDATE user 
SET role = 'admin', 
    user_type = 'admin', 
    status = 1,
    updated_at = NOW() 
WHERE (username LIKE '%admin%' OR role = 'admin' OR user_type = 'admin')
  AND deleted = 0;

-- 6. 修复角色不一致的用户
UPDATE user 
SET role = CASE 
    WHEN username LIKE '%admin%' THEN 'admin'
    WHEN user_type = 'seller' OR role = 'seller' THEN 'seller'
    ELSE 'user'
END,
user_type = CASE 
    WHEN username LIKE '%admin%' THEN 'admin'
    WHEN user_type = 'seller' OR role = 'seller' THEN 'seller'
    ELSE 'normal'
END,
updated_at = NOW()
WHERE role != COALESCE(user_type, 'user') 
  AND deleted = 0;

-- 7. 确保所有用户状态正常
UPDATE user 
SET status = 1, 
    updated_at = NOW() 
WHERE status != 1 AND deleted = 0;

-- 8. 检查密码加密情况
SELECT 
    '=== 密码加密状态检查 ===' as section,
    NULL as username, NULL as password_length, NULL as is_bcrypt, NULL as needs_fix;

SELECT 
    username,
    CHAR_LENGTH(password) as password_length,
    CASE 
        WHEN password LIKE '$2a$%' OR password LIKE '$2b$%' OR password LIKE '$2y$%' THEN 'BCrypt加密'
        WHEN CHAR_LENGTH(password) = 32 THEN '可能MD5加密'
        WHEN CHAR_LENGTH(password) < 20 THEN '明文密码'
        ELSE '未知格式'
    END as is_bcrypt,
    CASE 
        WHEN NOT (password LIKE '$2a$%' OR password LIKE '$2b$%' OR password LIKE '$2y$%') THEN '需要重新加密'
        ELSE '正常'
    END as needs_fix
FROM user 
WHERE deleted = 0
ORDER BY username;

-- 9. 验证修复结果
SELECT 
    '=== 修复结果验证 ===' as section,
    NULL as id, NULL as username, NULL as role, NULL as user_type, NULL as status, NULL as role_status;

SELECT 
    id,
    username,
    role,
    user_type,
    status,
    CASE 
        WHEN role = 'admin' AND user_type = 'admin' THEN '✅ 管理员'
        WHEN role = 'seller' AND user_type = 'seller' THEN '✅ 销售者'
        WHEN role = 'user' AND user_type = 'normal' THEN '✅ 普通用户'
        ELSE '❌ 角色不一致'
    END as role_status
FROM user 
WHERE deleted = 0
ORDER BY 
    CASE WHEN username LIKE '%admin%' THEN 0 ELSE 1 END,
    id;

-- 10. 特别确认关键用户
SELECT 
    '=== 关键用户确认 ===' as section,
    NULL as username, NULL as id, NULL as role, NULL as user_type, NULL as status, NULL as admin_status;

SELECT 
    username,
    id,
    role,
    user_type,
    status,
    CASE 
        WHEN username = 'admin_new' AND role = 'admin' AND user_type = 'admin' THEN '✅ admin_new权限正确'
        WHEN username = 'test_user' AND status = 1 THEN '✅ test_user状态正常'
        WHEN id = 18 AND role = 'admin' AND user_type = 'admin' THEN '✅ ID18管理员权限正确'
        ELSE '❌ 需要检查'
    END as admin_status
FROM user 
WHERE (username IN ('admin_new', 'test_user') OR id = 18)
  AND deleted = 0
ORDER BY username;

-- 11. 统计修复后的用户角色分布
SELECT 
    '=== 用户角色分布统计 ===' as section,
    NULL as role_type, NULL as count, NULL as percentage;

SELECT 
    CONCAT(role, '/', user_type) as role_type,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM user WHERE deleted = 0), 2) as percentage
FROM user 
WHERE deleted = 0
GROUP BY role, user_type
ORDER BY count DESC;
