# 📊 SFAP价格行情实时数据功能数据库表结构优化方案

> **优化时间**：2025-01-19  
> **数据库**：agriculture_mall  
> **目标**：支持惠农网全量农产品价格数据爬取和实时分析  

## 🔍 当前数据库状态分析

### 现有表结构检查结果

#### 1. product表（产品基础信息）✅
- **状态**：结构完整，数据充足
- **记录数**：60个农产品
- **分类覆盖**：12个主要农产品分类
- **字段完整性**：✅ 包含id、name、price、unit、origin等核心字段
- **优化建议**：结构良好，无需修改

#### 2. product_price_history表（价格历史）⚠️
- **状态**：表结构存在，但数据为空
- **记录数**：0条（需要填充历史数据）
- **字段分析**：
  - ✅ 基础字段完整（id、product_id、price、effective_date等）
  - ⚠️ 缺少爬取来源标识字段
  - ⚠️ 缺少市场信息字段
  - ⚠️ 缺少地区信息字段

#### 3. category表（产品分类）✅
- **状态**：结构完整，数据充足
- **分类数**：12个主要分类
- **覆盖范围**：叶菜类、根茎类、柑橘类、瓜果类等
- **优化建议**：结构良好，无需修改

### 缺失的表结构

根据任务拆解清单，需要新增以下4个表：

1. ❌ `price_market_data`表（市场价格数据）
2. ❌ `price_user_reports`表（用户上报价格）
3. ❌ `price_anomaly_alerts`表（价格异常预警）
4. ❌ `price_forecast_cache`表（预测结果缓存）

---

## 🛠️ 数据库优化方案

### 第一步：优化现有product_price_history表

#### 添加缺失字段
```sql
-- 添加爬取来源字段
ALTER TABLE product_price_history ADD COLUMN source VARCHAR(50) DEFAULT 'manual' COMMENT '数据来源：manual-手动，huinong-惠农网，user-用户上报';

-- 添加市场信息字段
ALTER TABLE product_price_history ADD COLUMN market_name VARCHAR(100) DEFAULT NULL COMMENT '市场名称';

-- 添加地区信息字段
ALTER TABLE product_price_history ADD COLUMN region VARCHAR(100) DEFAULT NULL COMMENT '地区信息';

-- 添加单位字段
ALTER TABLE product_price_history ADD COLUMN unit VARCHAR(20) DEFAULT '斤' COMMENT '价格单位';

-- 添加数据质量评分字段
ALTER TABLE product_price_history ADD COLUMN quality_score DECIMAL(3,2) DEFAULT 1.00 COMMENT '数据质量评分(0-1)';

-- 添加爬取时间字段
ALTER TABLE product_price_history ADD COLUMN crawl_time DATETIME DEFAULT NULL COMMENT '数据爬取时间';
```

### 第二步：创建新增表结构

#### 1. price_market_data表（市场价格数据）
```sql
CREATE TABLE price_market_data (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    product_name VARCHAR(100) NOT NULL COMMENT '产品名称',
    price DECIMAL(10,2) NOT NULL COMMENT '价格',
    unit VARCHAR(20) DEFAULT '斤' COMMENT '单位',
    market_name VARCHAR(100) DEFAULT NULL COMMENT '市场名称',
    region VARCHAR(100) DEFAULT NULL COMMENT '地区',
    province VARCHAR(50) DEFAULT NULL COMMENT '省份',
    city VARCHAR(50) DEFAULT NULL COMMENT '城市',
    source VARCHAR(50) DEFAULT 'huinong' COMMENT '数据来源',
    source_url TEXT DEFAULT NULL COMMENT '来源URL',
    crawl_time DATETIME NOT NULL COMMENT '爬取时间',
    price_date DATE NOT NULL COMMENT '价格日期',
    quality_score DECIMAL(3,2) DEFAULT 1.00 COMMENT '数据质量评分',
    is_processed TINYINT(1) DEFAULT 0 COMMENT '是否已处理',
    matched_product_id BIGINT DEFAULT NULL COMMENT '匹配的产品ID',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_product_name (product_name),
    INDEX idx_price_date (price_date),
    INDEX idx_crawl_time (crawl_time),
    INDEX idx_region (region),
    INDEX idx_source (source),
    INDEX idx_matched_product_id (matched_product_id),
    INDEX idx_is_processed (is_processed)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='市场价格数据表';
```

#### 2. price_user_reports表（用户上报价格）
```sql
CREATE TABLE price_user_reports (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    product_id BIGINT NOT NULL COMMENT '产品ID',
    reported_price DECIMAL(10,2) NOT NULL COMMENT '上报价格',
    unit VARCHAR(20) DEFAULT '斤' COMMENT '单位',
    market_name VARCHAR(100) DEFAULT NULL COMMENT '市场名称',
    region VARCHAR(100) DEFAULT NULL COMMENT '地区',
    report_time DATETIME NOT NULL COMMENT '上报时间',
    description TEXT DEFAULT NULL COMMENT '价格描述',
    image_url VARCHAR(500) DEFAULT NULL COMMENT '图片证明URL',
    verification_status TINYINT(1) DEFAULT 0 COMMENT '验证状态：0-待验证，1-已验证，2-已拒绝',
    verification_time DATETIME DEFAULT NULL COMMENT '验证时间',
    verifier_id BIGINT DEFAULT NULL COMMENT '验证人ID',
    credibility_score DECIMAL(3,2) DEFAULT 0.50 COMMENT '可信度评分',
    is_adopted TINYINT(1) DEFAULT 0 COMMENT '是否被采纳',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_user_id (user_id),
    INDEX idx_product_id (product_id),
    INDEX idx_report_time (report_time),
    INDEX idx_verification_status (verification_status),
    INDEX idx_is_adopted (is_adopted),
    FOREIGN KEY (product_id) REFERENCES product(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户上报价格表';
```

#### 3. price_anomaly_alerts表（价格异常预警）
```sql
CREATE TABLE price_anomaly_alerts (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    product_id BIGINT NOT NULL COMMENT '产品ID',
    alert_type VARCHAR(50) NOT NULL COMMENT '预警类型：price_spike-价格暴涨，price_drop-价格暴跌，abnormal_change-异常变化',
    current_price DECIMAL(10,2) NOT NULL COMMENT '当前价格',
    previous_price DECIMAL(10,2) NOT NULL COMMENT '之前价格',
    change_rate DECIMAL(5,2) NOT NULL COMMENT '变化率(%)',
    threshold_value DECIMAL(5,2) NOT NULL COMMENT '阈值',
    alert_level TINYINT(1) DEFAULT 1 COMMENT '预警级别：1-低，2-中，3-高',
    alert_message TEXT NOT NULL COMMENT '预警消息',
    data_source VARCHAR(50) DEFAULT NULL COMMENT '数据来源',
    region VARCHAR(100) DEFAULT NULL COMMENT '地区',
    alert_time DATETIME NOT NULL COMMENT '预警时间',
    is_processed TINYINT(1) DEFAULT 0 COMMENT '是否已处理',
    process_time DATETIME DEFAULT NULL COMMENT '处理时间',
    processor_id BIGINT DEFAULT NULL COMMENT '处理人ID',
    process_note TEXT DEFAULT NULL COMMENT '处理备注',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_product_id (product_id),
    INDEX idx_alert_type (alert_type),
    INDEX idx_alert_level (alert_level),
    INDEX idx_alert_time (alert_time),
    INDEX idx_is_processed (is_processed),
    FOREIGN KEY (product_id) REFERENCES product(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='价格异常预警表';
```

#### 4. price_forecast_cache表（预测结果缓存）
```sql
CREATE TABLE price_forecast_cache (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    product_id BIGINT NOT NULL COMMENT '产品ID',
    forecast_type VARCHAR(50) NOT NULL COMMENT '预测类型：simple-简单预测，arima-ARIMA模型，lstm-LSTM模型，ensemble-集成模型',
    forecast_days INT NOT NULL COMMENT '预测天数',
    base_price DECIMAL(10,2) NOT NULL COMMENT '基准价格',
    forecast_data JSON NOT NULL COMMENT '预测数据(JSON格式)',
    confidence_score DECIMAL(3,2) DEFAULT NULL COMMENT '置信度评分',
    model_version VARCHAR(50) DEFAULT NULL COMMENT '模型版本',
    forecast_time DATETIME NOT NULL COMMENT '预测时间',
    expire_time DATETIME NOT NULL COMMENT '过期时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否有效',
    accuracy_score DECIMAL(3,2) DEFAULT NULL COMMENT '准确度评分',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_product_id (product_id),
    INDEX idx_forecast_type (forecast_type),
    INDEX idx_forecast_time (forecast_time),
    INDEX idx_expire_time (expire_time),
    INDEX idx_is_active (is_active),
    UNIQUE KEY uk_product_forecast (product_id, forecast_type, forecast_days),
    FOREIGN KEY (product_id) REFERENCES product(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预测结果缓存表';
```

### 第三步：创建索引优化

#### 为product_price_history表添加复合索引
```sql
-- 产品ID和时间的复合索引
CREATE INDEX idx_product_time ON product_price_history(product_id, effective_date DESC);

-- 来源和时间的复合索引
CREATE INDEX idx_source_time ON product_price_history(source, created_at DESC);

-- 地区和时间的复合索引
CREATE INDEX idx_region_time ON product_price_history(region, effective_date DESC);
```

---

## 📈 数据迁移和初始化方案

### 1. 历史数据生成
为现有60个产品生成90天的历史价格数据，模拟真实的价格波动。

### 2. 分类扩展
根据惠农网的农产品分类，可能需要扩展category表以支持更多产品类型。

### 3. 数据验证
确保所有新增表的外键关系正确，数据类型合适。

---

## 🔧 执行计划

1. **第一阶段**：优化现有表结构
2. **第二阶段**：创建新增表
3. **第三阶段**：添加索引和约束
4. **第四阶段**：数据验证和测试
5. **第五阶段**：生成初始数据

---

**下一步**：开始执行数据库结构优化
