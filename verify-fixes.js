/**
 * 农品汇系统修复验证脚本
 * 用于验证所有修复是否生效
 */

// 生产环境配置
const PRODUCTION_CONFIG = {
  frontend: 'http://120.26.140.157:8200',
  backend: 'http://120.26.140.157:8081',
  staticResources: [
    '/static/images/brands/brand1.jpg',
    '/static/images/brands/brand2.jpg', 
    '/static/images/brands/brand3.jpg',
    '/static/images/avatars/user1.jpg',
    '/static/images/avatars/user2.jpg',
    '/static/images/avatars/user3.jpg'
  ],
  apiEndpoints: [
    '/api/mall/products/shop/featured-brands',
    '/api/mall/products/shop/featured-reviews',
    '/api/encyclopedia/list',
    '/api/encyclopedia/categories/level/1'
  ]
}

/**
 * 验证静态资源是否可访问
 */
async function verifyStaticResources() {
  console.log('🔍 验证静态资源...')
  
  const results = []
  
  for (const resource of PRODUCTION_CONFIG.staticResources) {
    const url = PRODUCTION_CONFIG.backend + resource
    
    try {
      const response = await fetch(url, { method: 'HEAD' })
      const status = response.status
      
      results.push({
        resource,
        url,
        status,
        success: status === 200,
        message: status === 200 ? '✅ 正常' : `❌ 错误 (${status})`
      })
      
      console.log(`  ${resource}: ${status === 200 ? '✅' : '❌'} ${status}`)
    } catch (error) {
      results.push({
        resource,
        url,
        status: 'ERROR',
        success: false,
        message: `❌ 网络错误: ${error.message}`
      })
      
      console.log(`  ${resource}: ❌ 网络错误`)
    }
  }
  
  return results
}

/**
 * 验证API接口是否正常
 */
async function verifyApiEndpoints() {
  console.log('🔍 验证API接口...')
  
  const results = []
  
  for (const endpoint of PRODUCTION_CONFIG.apiEndpoints) {
    const url = PRODUCTION_CONFIG.backend + endpoint
    
    try {
      const response = await fetch(url)
      const status = response.status
      const data = await response.json()
      
      results.push({
        endpoint,
        url,
        status,
        success: status === 200 && data.code === 200,
        message: status === 200 ? '✅ 正常' : `❌ 错误 (${status})`,
        data: data
      })
      
      console.log(`  ${endpoint}: ${status === 200 ? '✅' : '❌'} ${status}`)
    } catch (error) {
      results.push({
        endpoint,
        url,
        status: 'ERROR',
        success: false,
        message: `❌ 网络错误: ${error.message}`
      })
      
      console.log(`  ${endpoint}: ❌ 网络错误`)
    }
  }
  
  return results
}

/**
 * 验证前端页面是否正常加载
 */
async function verifyFrontendPages() {
  console.log('🔍 验证前端页面...')
  
  const pages = [
    '/',
    '/shop',
    '/encyclopedia',
    '/news',
    '/price'
  ]
  
  const results = []
  
  for (const page of pages) {
    const url = PRODUCTION_CONFIG.frontend + page
    
    try {
      const response = await fetch(url)
      const status = response.status
      
      results.push({
        page,
        url,
        status,
        success: status === 200,
        message: status === 200 ? '✅ 正常' : `❌ 错误 (${status})`
      })
      
      console.log(`  ${page}: ${status === 200 ? '✅' : '❌'} ${status}`)
    } catch (error) {
      results.push({
        page,
        url,
        status: 'ERROR',
        success: false,
        message: `❌ 网络错误: ${error.message}`
      })
      
      console.log(`  ${page}: ❌ 网络错误`)
    }
  }
  
  return results
}

/**
 * 生成验证报告
 */
function generateReport(staticResults, apiResults, frontendResults) {
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      staticResources: {
        total: staticResults.length,
        success: staticResults.filter(r => r.success).length,
        failed: staticResults.filter(r => !r.success).length
      },
      apiEndpoints: {
        total: apiResults.length,
        success: apiResults.filter(r => r.success).length,
        failed: apiResults.filter(r => !r.success).length
      },
      frontendPages: {
        total: frontendResults.length,
        success: frontendResults.filter(r => r.success).length,
        failed: frontendResults.filter(r => !r.success).length
      }
    },
    details: {
      staticResources: staticResults,
      apiEndpoints: apiResults,
      frontendPages: frontendResults
    }
  }
  
  // 计算总体成功率
  const totalTests = staticResults.length + apiResults.length + frontendResults.length
  const totalSuccess = report.summary.staticResources.success + 
                      report.summary.apiEndpoints.success + 
                      report.summary.frontendPages.success
  
  report.summary.overall = {
    total: totalTests,
    success: totalSuccess,
    failed: totalTests - totalSuccess,
    successRate: ((totalSuccess / totalTests) * 100).toFixed(2) + '%'
  }
  
  return report
}

/**
 * 主验证函数
 */
async function runVerification() {
  console.log('🚀 开始验证农品汇系统修复效果...')
  console.log('=' .repeat(50))
  
  try {
    // 验证静态资源
    const staticResults = await verifyStaticResources()
    console.log('')
    
    // 验证API接口
    const apiResults = await verifyApiEndpoints()
    console.log('')
    
    // 验证前端页面
    const frontendResults = await verifyFrontendPages()
    console.log('')
    
    // 生成报告
    const report = generateReport(staticResults, apiResults, frontendResults)
    
    // 显示总结
    console.log('📊 验证总结:')
    console.log(`  静态资源: ${report.summary.staticResources.success}/${report.summary.staticResources.total} 成功`)
    console.log(`  API接口: ${report.summary.apiEndpoints.success}/${report.summary.apiEndpoints.total} 成功`)
    console.log(`  前端页面: ${report.summary.frontendPages.success}/${report.summary.frontendPages.total} 成功`)
    console.log(`  总体成功率: ${report.summary.overall.successRate}`)
    
    // 保存报告
    if (typeof require !== 'undefined') {
      const fs = require('fs')
      fs.writeFileSync('verification-report.json', JSON.stringify(report, null, 2))
      console.log('📄 详细报告已保存到: verification-report.json')
    }
    
    // 修复建议
    if (report.summary.overall.success < report.summary.overall.total) {
      console.log('')
      console.log('🔧 修复建议:')
      
      if (report.summary.staticResources.failed > 0) {
        console.log('  - 静态资源问题: 执行 bash create-static-resources.sh')
      }
      
      if (report.summary.apiEndpoints.failed > 0) {
        console.log('  - API接口问题: 检查后端服务状态')
      }
      
      if (report.summary.frontendPages.failed > 0) {
        console.log('  - 前端页面问题: 检查nginx配置和前端部署')
      }
    } else {
      console.log('')
      console.log('🎉 所有测试通过！农品汇系统修复成功！')
    }
    
  } catch (error) {
    console.error('❌ 验证过程中发生错误:', error)
  }
  
  console.log('=' .repeat(50))
  console.log('✅ 验证完成')
}

// 如果在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    runVerification,
    verifyStaticResources,
    verifyApiEndpoints,
    verifyFrontendPages
  }
  
  // 如果直接运行此脚本
  if (require.main === module) {
    runVerification()
  }
} else {
  // 浏览器环境
  window.agricultureVerification = {
    runVerification,
    verifyStaticResources,
    verifyApiEndpoints,
    verifyFrontendPages
  }
  
  // 自动运行验证
  runVerification()
}
