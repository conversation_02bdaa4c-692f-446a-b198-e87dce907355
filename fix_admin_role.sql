-- 修复管理员用户角色数据
-- 用户ID 18 (admin_new) 应该是管理员，但当前是seller

-- 1. 修复用户ID 18 (admin_new)
UPDATE user 
SET role = 'admin', 
    user_type = 'admin', 
    updated_at = NOW()
WHERE id = 18 AND username = 'admin_new';

-- 2. 修复所有包含admin的用户名
UPDATE user 
SET role = 'admin', 
    user_type = 'admin', 
    updated_at = NOW()
WHERE username LIKE '%admin%' 
  AND deleted = 0;

-- 3. 验证修复结果
SELECT 
    id,
    username,
    role,
    user_type,
    status,
    updated_at
FROM user 
WHERE username LIKE '%admin%' OR id = 18
  AND deleted = 0
ORDER BY id;
