#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成销售者产品二维码脚本
"""

import qrcode
from PIL import Image, ImageDraw, ImageFont
import os
import sys

def generate_qr_code_with_logo(trace_code, output_path):
    """
    生成带SFAP logo和底部文字的二维码
    """
    try:
        # 创建二维码
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_M,
            box_size=10,
            border=4,
        )
        
        # 二维码内容为查询URL
        qr_data = f"https://sfap.com/trace/{trace_code}"
        qr.add_data(qr_data)
        qr.make(fit=True)
        
        # 生成二维码图片
        qr_img = qr.make_image(fill_color="black", back_color="white")
        
        # 创建最终图片 (300x360, 为底部文字留出空间)
        final_img = Image.new('RGB', (300, 360), 'white')
        
        # 调整二维码大小并粘贴到上方
        qr_img = qr_img.resize((280, 280))
        final_img.paste(qr_img, (10, 10))
        
        # 添加底部文字
        draw = ImageDraw.Draw(final_img)
        
        try:
            # 尝试使用系统字体
            font_large = ImageFont.truetype("arial.ttf", 16)
            font_small = ImageFont.truetype("arial.ttf", 12)
        except:
            # 如果没有找到字体，使用默认字体
            font_large = ImageFont.load_default()
            font_small = ImageFont.load_default()
        
        # 绘制文字
        text1 = "SFAP农产品溯源"
        text2 = f"溯源码: {trace_code}"
        text3 = "扫码查看详细信息"
        
        # 计算文字位置（居中）
        bbox1 = draw.textbbox((0, 0), text1, font=font_large)
        bbox2 = draw.textbbox((0, 0), text2, font=font_small)
        bbox3 = draw.textbbox((0, 0), text3, font=font_small)
        
        text1_width = bbox1[2] - bbox1[0]
        text2_width = bbox2[2] - bbox2[0]
        text3_width = bbox3[2] - bbox3[0]
        
        # 绘制文字
        draw.text(((300 - text1_width) // 2, 300), text1, fill="black", font=font_large)
        draw.text(((300 - text2_width) // 2, 320), text2, fill="black", font=font_small)
        draw.text(((300 - text3_width) // 2, 340), text3, fill="gray", font=font_small)
        
        # 保存图片
        final_img.save(output_path, 'PNG', quality=95)
        print(f"✅ 成功生成二维码: {output_path}")
        return True
        
    except Exception as e:
        print(f"❌ 生成二维码失败 {trace_code}: {str(e)}")
        return False

def main():
    # 销售者产品的溯源码
    seller_trace_codes = [
        "SFAPS25071622096012CD351",
        "SFAPS25071622096013C80DE", 
        "SFAPS2507162209601459927"
    ]
    
    # 输出目录
    output_dir = "uploads/qrcodes"
    
    # 确保输出目录存在
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"创建目录: {output_dir}")
    
    print("开始生成销售者产品二维码...")
    success_count = 0
    
    for trace_code in seller_trace_codes:
        output_path = os.path.join(output_dir, f"qr_{trace_code}.png")
        
        if generate_qr_code_with_logo(trace_code, output_path):
            success_count += 1
    
    print(f"\n生成完成！成功: {success_count}/{len(seller_trace_codes)}")
    
    # 检查最终文件数量
    if os.path.exists(output_dir):
        total_files = len([f for f in os.listdir(output_dir) if f.endswith('.png')])
        print(f"二维码目录总文件数: {total_files}")

if __name__ == "__main__":
    main()
