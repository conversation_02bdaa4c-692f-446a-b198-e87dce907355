<mxfile host="app.diagrams.net" modified="2023-06-10T09:30:00.000Z" agent="5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36" etag="2345678901" version="14.7.7" type="device">
  <diagram id="agriculture-business-architecture" name="农智汇平台业务架构">
    <mxGraphModel dx="1422" dy="762" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- 标题 -->
        <mxCell id="title" value="农智汇平台业务架构图" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=24;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="359.5" y="20" width="450" height="40" as="geometry" />
        </mxCell>
        
        <!-- 用户接入层 -->
        <mxCell id="user_access_layer" value="用户接入层" style="swimlane;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;rounded=1;fontSize=16;fontStyle=1;strokeWidth=2;resizeParent=0;resizeLast=1;shadow=0;dashed=0;align=center;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="80" y="80" width="1010" height="100" as="geometry" />
        </mxCell>
        
        <!-- 用户接入模块 -->
        <mxCell id="web_access" value="Web端访问" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="160" y="120" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="mobile_access" value="移动端访问" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="360" y="120" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="admin_access" value="管理员访问" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="560" y="120" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="api_access" value="API访问" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="760" y="120" width="120" height="40" as="geometry" />
        </mxCell>
        
        <!-- 业务功能层 -->
        <mxCell id="business_layer" value="业务功能层" style="swimlane;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;rounded=1;fontSize=16;fontStyle=1;strokeWidth=2;resizeParent=0;resizeLast=1;shadow=0;dashed=0;align=center;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="80" y="210" width="1010" height="330" as="geometry" />
        </mxCell>
        
        <!-- 电商系统 -->
        <mxCell id="ecommerce_system" value="电商系统" style="swimlane;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;rounded=1;fontSize=14;fontStyle=1;strokeWidth=2;resizeParent=0;resizeLast=1;shadow=0;dashed=0;align=center;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="100" y="260" width="220" height="260" as="geometry" />
        </mxCell>
        <mxCell id="product_management" value="商品管理" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="120" y="300" width="180" height="30" as="geometry" />
        </mxCell>
        <mxCell id="shopping_cart" value="购物车" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="120" y="340" width="180" height="30" as="geometry" />
        </mxCell>
        <mxCell id="order_management" value="订单管理" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="120" y="380" width="180" height="30" as="geometry" />
        </mxCell>
        <mxCell id="payment" value="支付系统" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="120" y="420" width="180" height="30" as="geometry" />
        </mxCell>
        <mxCell id="review" value="评价系统" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="120" y="460" width="180" height="30" as="geometry" />
        </mxCell>
        
        <!-- 数据分析系统 -->
        <mxCell id="analysis_system" value="数据分析系统" style="swimlane;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;rounded=1;fontSize=14;fontStyle=1;strokeWidth=2;resizeParent=0;resizeLast=1;shadow=0;dashed=0;align=center;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="350" y="260" width="220" height="260" as="geometry" />
        </mxCell>
        <mxCell id="price_analysis" value="价格分析" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="370" y="300" width="180" height="30" as="geometry" />
        </mxCell>
        <mxCell id="market_analysis" value="市场分析" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="370" y="340" width="180" height="30" as="geometry" />
        </mxCell>
        <mxCell id="price_forecast" value="价格预测" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="370" y="380" width="180" height="30" as="geometry" />
        </mxCell>
        <mxCell id="data_visualization" value="数据可视化" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="370" y="420" width="180" height="30" as="geometry" />
        </mxCell>
        <mxCell id="report_generation" value="报告生成" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="370" y="460" width="180" height="30" as="geometry" />
        </mxCell>
        
        <!-- 农业百科系统 -->
        <mxCell id="encyclopedia_system" value="农业百科系统" style="swimlane;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;rounded=1;fontSize=14;fontStyle=1;strokeWidth=2;resizeParent=0;resizeLast=1;shadow=0;dashed=0;align=center;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="600" y="260" width="220" height="260" as="geometry" />
        </mxCell>
        <mxCell id="knowledge_management" value="知识管理" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="620" y="300" width="180" height="30" as="geometry" />
        </mxCell>
        <mxCell id="category_management" value="分类管理" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="620" y="340" width="180" height="30" as="geometry" />
        </mxCell>
        <mxCell id="search_system" value="搜索系统" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="620" y="380" width="180" height="30" as="geometry" />
        </mxCell>
        <mxCell id="comment_system" value="评论系统" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="620" y="420" width="180" height="30" as="geometry" />
        </mxCell>
        <mxCell id="favorite_system" value="收藏系统" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="620" y="460" width="180" height="30" as="geometry" />
        </mxCell>
        
        <!-- 智能服务系统 -->
        <mxCell id="intelligent_service_system" value="智能服务系统" style="swimlane;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;rounded=1;fontSize=14;fontStyle=1;strokeWidth=2;resizeParent=0;resizeLast=1;shadow=0;dashed=0;align=center;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="850" y="260" width="220" height="260" as="geometry" />
        </mxCell>
        <mxCell id="weather_forecast" value="天气预报" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="870" y="300" width="180" height="30" as="geometry" />
        </mxCell>
        <mxCell id="agricultural_advice" value="农事建议" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="870" y="340" width="180" height="30" as="geometry" />
        </mxCell>
        <mxCell id="ai_assistant" value="AI智能助手" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="870" y="380" width="180" height="30" as="geometry" />
        </mxCell>
        <mxCell id="weather_alert" value="天气预警" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="870" y="420" width="180" height="30" as="geometry" />
        </mxCell>
        <mxCell id="ai_chat" value="智能对话" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="870" y="460" width="180" height="30" as="geometry" />
        </mxCell>
        
        <!-- 公共服务层 -->
        <mxCell id="common_service_layer" value="公共服务层" style="swimlane;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;rounded=1;fontSize=16;fontStyle=1;strokeWidth=2;resizeParent=0;resizeLast=1;shadow=0;dashed=0;align=center;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="80" y="570" width="1010" height="100" as="geometry" />
        </mxCell>
        
        <!-- 公共服务 -->
        <mxCell id="user_management" value="用户管理" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="100" y="610" width="140" height="40" as="geometry" />
        </mxCell>
        <mxCell id="auth_service" value="认证授权" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="260" y="610" width="140" height="40" as="geometry" />
        </mxCell>
        <mxCell id="file_service" value="文件服务" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="420" y="610" width="140" height="40" as="geometry" />
        </mxCell>
        <mxCell id="notification_service" value="消息通知" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="580" y="610" width="140" height="40" as="geometry" />
        </mxCell>
        <mxCell id="search_service" value="搜索服务" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="740" y="610" width="140" height="40" as="geometry" />
        </mxCell>
        <mxCell id="log_service" value="日志服务" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="900" y="610" width="140" height="40" as="geometry" />
        </mxCell>
        
        <!-- 数据存储层 -->
        <mxCell id="data_storage_layer" value="数据存储层" style="swimlane;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;rounded=1;fontSize=16;fontStyle=1;strokeWidth=2;resizeParent=0;resizeLast=1;shadow=0;dashed=0;align=center;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" vertex="1" parent="1">
          <mxGeometry x="80" y="700" width="1010" height="100" as="geometry" />
        </mxCell>
        
        <!-- 数据存储 -->
        <mxCell id="product_data" value="商品数据" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fontSize=12;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" vertex="1" parent="1">
          <mxGeometry x="110" y="740" width="100" height="50" as="geometry" />
        </mxCell>
        <mxCell id="order_data" value="订单数据" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fontSize=12;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" vertex="1" parent="1">
          <mxGeometry x="230" y="740" width="100" height="50" as="geometry" />
        </mxCell>
        <mxCell id="user_data" value="用户数据" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fontSize=12;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" vertex="1" parent="1">
          <mxGeometry x="350" y="740" width="100" height="50" as="geometry" />
        </mxCell>
        <mxCell id="price_data" value="价格数据" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fontSize=12;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" vertex="1" parent="1">
          <mxGeometry x="470" y="740" width="100" height="50" as="geometry" />
        </mxCell>
        <mxCell id="encyclopedia_data" value="百科数据" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fontSize=12;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" vertex="1" parent="1">
          <mxGeometry x="590" y="740" width="100" height="50" as="geometry" />
        </mxCell>
        <mxCell id="weather_data" value="天气数据" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fontSize=12;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" vertex="1" parent="1">
          <mxGeometry x="710" y="740" width="100" height="50" as="geometry" />
        </mxCell>
        <mxCell id="ai_data" value="AI会话数据" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fontSize=12;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" vertex="1" parent="1">
          <mxGeometry x="830" y="740" width="100" height="50" as="geometry" />
        </mxCell>
        <mxCell id="file_data" value="文件数据" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fontSize=12;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" vertex="1" parent="1">
          <mxGeometry x="950" y="740" width="100" height="50" as="geometry" />
        </mxCell>
        
        <!-- 连接关系 -->
        <!-- 用户接入层到业务功能层 -->
        <mxCell id="access_to_business" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="user_access_layer" target="business_layer">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="410" as="sourcePoint" />
            <mxPoint x="610" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 业务功能层到公共服务层 -->
        <mxCell id="business_to_common" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="business_layer" target="common_service_layer">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="410" as="sourcePoint" />
            <mxPoint x="610" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 公共服务层到数据存储层 -->
        <mxCell id="common_to_data" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="common_service_layer" target="data_storage_layer">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="410" as="sourcePoint" />
            <mxPoint x="610" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 业务系统之间的交互 -->
        <mxCell id="ecommerce_to_analysis" value="" style="endArrow=classic;startArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;dashed=1;" edge="1" parent="1" source="ecommerce_system" target="analysis_system">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="410" as="sourcePoint" />
            <mxPoint x="610" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="analysis_to_encyclopedia" value="" style="endArrow=classic;startArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;dashed=1;" edge="1" parent="1" source="analysis_system" target="encyclopedia_system">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="410" as="sourcePoint" />
            <mxPoint x="610" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="encyclopedia_to_intelligent" value="" style="endArrow=classic;startArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;dashed=1;" edge="1" parent="1" source="encyclopedia_system" target="intelligent_service_system">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="410" as="sourcePoint" />
            <mxPoint x="610" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile> 