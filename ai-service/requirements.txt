# SFAP农品汇平台AI预测服务依赖包
# 更新日期: 2025-01-24
# Python版本: 3.13.3

# Web框架
flask==2.3.3
flask-cors==4.0.0
flask-restful==0.3.10

# 数据处理
pandas>=1.5.0
numpy>=1.21.0
scikit-learn>=1.0.0

# 深度学习框架
# tensorflow>=2.10.0,<3.0.0  # Python 3.13不支持，使用PyTorch替代
torch>=2.0.0  # 已安装 2.6.0，用于深度学习

# 时间序列分析
statsmodels>=0.13.0
# pmdarima>=1.8.0  # 编译失败，使用statsmodels.tsa.arima替代

# 数据库连接
pymysql==1.1.0
mysql-connector-python==8.2.0
sqlalchemy==2.0.20

# 配置管理
python-dotenv==1.0.0

# 日志和监控
loguru==0.7.0

# 数据可视化
matplotlib>=3.7.0  # 已安装 3.10.1
seaborn==0.12.2

# 工具库
requests==2.31.0
joblib==1.3.2
tqdm==4.65.0
psutil==5.9.5

# 开发工具
pytest==7.4.0
black==23.7.0
flake8==6.0.0
