#!/bin/bash

# SFAP农品汇平台AI预测服务部署脚本
# 适用于云服务器环境

echo "=== SFAP AI服务部署开始 ==="

# 检查Python版本
echo "检查Python版本..."
python3 --version

# 创建虚拟环境（推荐）
echo "创建Python虚拟环境..."
python3 -m venv venv
source venv/bin/activate

# 升级pip
echo "升级pip..."
pip install --upgrade pip

# 安装依赖
echo "安装Python依赖包..."
pip install -r requirements.txt

# 检查关键依赖是否安装成功
echo "验证关键依赖..."
python3 -c "import pymysql; print('✓ pymysql 安装成功')"
python3 -c "import mysql.connector; print('✓ mysql-connector-python 安装成功')" 2>/dev/null || echo "⚠ mysql-connector-python 安装失败，但不影响运行"
python3 -c "import flask; print('✓ Flask 安装成功')"
python3 -c "import pandas; print('✓ Pandas 安装成功')"
python3 -c "import numpy; print('✓ Numpy 安装成功')"

# 创建必要的目录
echo "创建必要的目录..."
mkdir -p logs
mkdir -p saved_models/arima
mkdir -p saved_models/rnn
mkdir -p temp

# 设置环境变量文件
echo "配置环境变量..."
if [ ! -f .env ]; then
    cat > .env << EOF
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=agriculture_mall

# 服务配置
FLASK_ENV=production
FLASK_DEBUG=False
AI_SERVICE_PORT=5000

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/ai_service.log
EOF
    echo "✓ 已创建 .env 配置文件，请根据实际情况修改数据库配置"
else
    echo "✓ .env 文件已存在"
fi

# 测试服务启动
echo "测试服务启动..."
python3 -c "
try:
    from app import app
    print('✓ AI服务模块导入成功')
except ImportError as e:
    print(f'✗ 模块导入失败: {e}')
    exit(1)
except Exception as e:
    print(f'⚠ 其他错误: {e}')
"

echo "=== 部署完成 ==="
echo ""
echo "启动服务命令："
echo "  source venv/bin/activate"
echo "  python3 app.py"
echo ""
echo "或使用后台运行："
echo "  nohup python3 app.py > logs/ai_service.log 2>&1 &"
echo ""
echo "检查服务状态："
echo "  curl http://localhost:5000/health"
