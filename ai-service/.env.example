# SFAP农品汇平台AI预测服务环境变量配置示例
# 复制此文件为 .env 并根据实际环境修改配置

# 数据库配置
DB_HOST=**************
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_database_password
DB_NAME=agriculture_mall

# 服务配置
FLASK_ENV=production
FLASK_DEBUG=False
AI_SERVICE_PORT=5000
AI_SERVICE_HOST=0.0.0.0

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/ai_service.log
ERROR_LOG_FILE=logs/ai_service_error.log

# 模型配置
MODEL_SAVE_PATH=saved_models
ARIMA_MODEL_PATH=saved_models/arima
RNN_MODEL_PATH=saved_models/rnn

# 数据处理配置
MAX_PREDICTION_DAYS=30
MIN_HISTORICAL_DAYS=30
DEFAULT_BATCH_SIZE=32

# API配置
API_TIMEOUT=30
MAX_REQUESTS_PER_MINUTE=100

# 缓存配置
ENABLE_CACHE=True
CACHE_TIMEOUT=3600
