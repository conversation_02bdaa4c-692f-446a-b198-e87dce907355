"""
RNN神经网络价格预测模型（简化版本）
注意：由于TensorFlow在Python 3.13中不可用，此模块提供基础功能
实际深度学习功能需要在兼容环境中运行
"""

import numpy as np
import pandas as pd
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_absolute_error, mean_squared_error
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import LinearRegression
import joblib
import os
from datetime import datetime, timedelta
import uuid
import warnings
warnings.filterwarnings('ignore')

from utils.logger import setup_logger
from utils.database import DatabaseManager

logger = setup_logger()

class RNNPredictor:
    """
    RNN预测器（简化版本）
    使用随机森林作为临时替代方案
    """
    def __init__(self):
        self.model = None
        self.scaler = None
        self.db_manager = DatabaseManager()
        self.model_dir = "saved_models/rnn"
        os.makedirs(self.model_dir, exist_ok=True)
        logger.warning("RNN模型使用简化版本（随机森林替代），建议在支持TensorFlow的环境中使用完整版本")
        
    def _create_model(self, input_size, config):
        """创建简化模型（随机森林）"""
        return RandomForestRegressor(
            n_estimators=config.get('n_estimators', 100),
            max_depth=config.get('max_depth', 10),
            random_state=42
        )

    def _create_temp_model(self, history_data):
        """创建临时模型用于预测"""
        try:
            logger.info("创建临时模型进行预测")

            # 简单处理：使用线性回归和基础特征
            df = pd.DataFrame(history_data)
            if len(df) < 3:
                # 数据太少，使用最后一个价格
                self.model = None
                return

            # 创建简单的时间序列特征
            df['index'] = range(len(df))
            X = df[['index']].values
            y = df['price'].values

            # 使用线性回归
            self.model = LinearRegression()
            self.scaler = MinMaxScaler()

            X_scaled = self.scaler.fit_transform(X)
            self.model.fit(X_scaled, y)

            logger.info("临时模型创建成功")

        except Exception as e:
            logger.error(f"创建临时模型失败: {e}")
            self.model = None

    def _simple_prediction(self, df, forecast_days):
        """简单预测方法，当模型创建失败时使用"""
        try:
            # 使用最后几个价格的平均值作为预测
            last_prices = df['price'].tail(3).values
            avg_price = np.mean(last_prices)

            predictions = []
            for i in range(forecast_days):
                pred_date = df['date'].iloc[-1] + timedelta(days=i+1)
                # 添加小幅随机波动
                pred_price = avg_price * (1 + np.random.uniform(-0.02, 0.02))

                predictions.append({
                    'date': pred_date.strftime('%Y-%m-%d'),
                    'predicted_price': float(pred_price),
                    'confidence_lower': float(pred_price * 0.95),
                    'confidence_upper': float(pred_price * 1.05)
                })

            # 计算汇总信息
            avg_price_pred = np.mean([p['predicted_price'] for p in predictions])
            price_change = (predictions[-1]['predicted_price'] - df['price'].iloc[-1]) / df['price'].iloc[-1]
            trend_direction = "上涨" if price_change > 0.02 else "下跌" if price_change < -0.02 else "稳定"

            return {
                'predictions': predictions,
                'summary': {
                    'avg_price': avg_price_pred,
                    'price_change': price_change,
                    'trend_direction': trend_direction,
                    'forecast_days': forecast_days
                },
                'model_info': {
                    'model_type': 'RNN_SIMPLE_FALLBACK',
                    'model_id': None
                }
            }

        except Exception as e:
            logger.error(f"简单预测失败: {e}")
            raise
    
    def _prepare_sequences(self, data, sequence_length=30):
        """准备时间序列数据"""
        X, y = [], []
        
        for i in range(sequence_length, len(data)):
            X.append(data[i-sequence_length:i].flatten())
            y.append(data[i])
        
        return np.array(X), np.array(y)
    
    def _create_features(self, df):
        """创建特征"""
        # 基础价格特征
        df['price_lag_1'] = df['price'].shift(1)
        df['price_lag_3'] = df['price'].shift(3)
        df['price_lag_7'] = df['price'].shift(7)
        
        # 滑动窗口统计特征
        df['price_ma_7'] = df['price'].rolling(window=7).mean()
        df['price_ma_14'] = df['price'].rolling(window=14).mean()
        df['price_std_7'] = df['price'].rolling(window=7).std()
        
        # 时间特征
        df['month'] = pd.to_datetime(df['date']).dt.month
        df['day_of_week'] = pd.to_datetime(df['date']).dt.dayofweek
        
        return df.dropna()
    
    def train(self, category, region, history_data, model_params=None):
        """训练模型"""
        try:
            logger.info(f"开始训练RNN模型（简化版）: {category} - {region}")
            
            # 数据预处理
            df = pd.DataFrame(history_data)
            df['date'] = pd.to_datetime(df['date'])
            df = df.sort_values('date')
            
            # 创建特征
            df = self._create_features(df)
            
            if len(df) < 30:
                raise ValueError("训练数据不足，至少需要30个数据点")
            
            # 准备训练数据
            feature_cols = [col for col in df.columns if col not in ['date', 'price']]
            X = df[feature_cols].values
            y = df['price'].values
            
            # 数据标准化
            self.scaler = MinMaxScaler()
            X_scaled = self.scaler.fit_transform(X)
            
            # 创建和训练模型
            config = model_params or {}
            self.model = self._create_model(X_scaled.shape[1], config)
            self.model.fit(X_scaled, y)
            
            # 计算训练指标
            y_pred = self.model.predict(X_scaled)
            mape = np.mean(np.abs((y - y_pred) / y)) * 100
            mae = mean_absolute_error(y, y_pred)
            mse = mean_squared_error(y, y_pred)
            
            # 保存模型
            model_id = str(uuid.uuid4())
            model_path = os.path.join(self.model_dir, f"{model_id}.joblib")
            scaler_path = os.path.join(self.model_dir, f"{model_id}_scaler.joblib")
            
            joblib.dump(self.model, model_path)
            joblib.dump(self.scaler, scaler_path)
            
            # 保存模型信息到数据库
            model_info = {
                'model_id': model_id,
                'model_type': 'RNN_SIMPLIFIED',
                'category': category,
                'region': region,
                'training_data_size': len(df),
                'mape': mape,
                'mae': mae,
                'mse': mse,
                'model_path': model_path,
                'scaler_path': scaler_path,
                'created_at': datetime.now(),
                'model_params': config
            }
            
            logger.info(f"RNN模型训练完成: MAPE={mape:.2f}%, MAE={mae:.2f}")
            
            return {
                'model_id': model_id,
                'training_metrics': {
                    'mape': mape,
                    'mae': mae,
                    'mse': mse
                },
                'model_info': model_info
            }
            
        except Exception as e:
            logger.error(f"RNN模型训练失败: {e}")
            raise
    
    def predict(self, category, region, history_data, forecast_days=7, model_id=None):
        """预测价格"""
        try:
            logger.info(f"开始RNN预测（简化版）: {category} - {region}, 预测{forecast_days}天")
            
            # 加载模型
            if model_id:
                model_path = os.path.join(self.model_dir, f"{model_id}.joblib")
                scaler_path = os.path.join(self.model_dir, f"{model_id}_scaler.joblib")
                
                if os.path.exists(model_path) and os.path.exists(scaler_path):
                    self.model = joblib.load(model_path)
                    self.scaler = joblib.load(scaler_path)
                else:
                    raise ValueError(f"模型文件不存在: {model_id}")
            
            if self.model is None:
                # 如果没有预训练模型，创建一个临时模型进行预测
                logger.warning("没有预训练模型，使用临时模型进行预测")
                self._create_temp_model(history_data)
            
            # 数据预处理
            df = pd.DataFrame(history_data)
            df['date'] = pd.to_datetime(df['date'])
            df = df.sort_values('date')

            # 如果模型仍然为None，使用简单预测
            if self.model is None:
                return self._simple_prediction(df, forecast_days)

            # 预测
            predictions = []

            for i in range(forecast_days):
                # 简单预测：使用时间索引
                last_index = len(df) - 1 + i
                X = np.array([[last_index]])
                X_scaled = self.scaler.transform(X)

                # 预测
                pred_price = self.model.predict(X_scaled)[0]
                pred_date = df['date'].iloc[-1] + timedelta(days=i+1)
                
                predictions.append({
                    'date': pred_date.strftime('%Y-%m-%d'),
                    'predicted_price': float(pred_price),
                    'confidence_lower': float(pred_price * 0.95),
                    'confidence_upper': float(pred_price * 1.05)
                })
            
            # 计算汇总信息
            avg_price = np.mean([p['predicted_price'] for p in predictions])
            price_change = (predictions[-1]['predicted_price'] - df['price'].iloc[-1]) / df['price'].iloc[-1]
            trend_direction = "上涨" if price_change > 0.02 else "下跌" if price_change < -0.02 else "稳定"
            
            result = {
                'predictions': predictions,
                'summary': {
                    'avg_price': avg_price,
                    'price_change': price_change,
                    'trend_direction': trend_direction,
                    'forecast_days': forecast_days
                },
                'model_info': {
                    'model_type': 'RNN_SIMPLIFIED',
                    'model_id': model_id
                }
            }
            
            logger.info(f"RNN预测完成: 平均价格={avg_price:.2f}, 趋势={trend_direction}")
            return result
            
        except Exception as e:
            logger.error(f"RNN预测失败: {e}")
            raise
    
    def load_model(self, model_id):
        """加载模型"""
        try:
            model_path = os.path.join(self.model_dir, f"{model_id}.joblib")
            scaler_path = os.path.join(self.model_dir, f"{model_id}_scaler.joblib")
            
            if os.path.exists(model_path) and os.path.exists(scaler_path):
                self.model = joblib.load(model_path)
                self.scaler = joblib.load(scaler_path)
                logger.info(f"RNN模型加载成功: {model_id}")
                return True
            else:
                logger.error(f"RNN模型文件不存在: {model_id}")
                return False
                
        except Exception as e:
            logger.error(f"RNN模型加载失败: {e}")
            return False
