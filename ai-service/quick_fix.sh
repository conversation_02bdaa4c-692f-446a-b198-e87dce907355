#!/bin/bash

# SFAP AI服务快速修复脚本
# 解决 ModuleNotFoundError: No module named 'mysql' 问题

echo "=== SFAP AI服务快速修复开始 ==="

# 检查当前目录
if [ ! -f "app.py" ]; then
    echo "❌ 错误：请在AI服务根目录下运行此脚本"
    exit 1
fi

# 停止现有服务
echo "停止现有AI服务进程..."
pkill -f "python3 app.py" 2>/dev/null || echo "没有运行中的AI服务"

# 激活虚拟环境（如果存在）
if [ -d "venv" ]; then
    echo "激活虚拟环境..."
    source venv/bin/activate
else
    echo "创建新的虚拟环境..."
    python3 -m venv venv
    source venv/bin/activate
fi

# 升级pip
echo "升级pip..."
pip install --upgrade pip

# 安装缺失的关键依赖
echo "安装缺失的MySQL连接器..."
pip install mysql-connector-python==8.2.0

echo "安装系统监控库..."
pip install psutil==5.9.5

# 安装所有依赖
echo "安装完整依赖列表..."
pip install -r requirements.txt

# 验证关键模块
echo "验证关键模块安装..."
python3 -c "
import sys
modules_to_check = [
    ('pymysql', 'PyMySQL'),
    ('mysql.connector', 'MySQL Connector'),
    ('flask', 'Flask'),
    ('pandas', 'Pandas'),
    ('numpy', 'NumPy'),
    ('psutil', 'psutil')
]

failed_modules = []
for module, name in modules_to_check:
    try:
        __import__(module)
        print(f'✓ {name} 安装成功')
    except ImportError:
        print(f'✗ {name} 安装失败')
        failed_modules.append(name)

if failed_modules:
    print(f'\\n❌ 以下模块安装失败: {failed_modules}')
    sys.exit(1)
else:
    print('\\n✅ 所有关键模块安装成功')
"

if [ $? -ne 0 ]; then
    echo "❌ 模块验证失败，请检查安装日志"
    exit 1
fi

# 测试应用导入
echo "测试应用模块导入..."
python3 -c "
try:
    from app import app
    print('✅ AI服务应用导入成功')
except Exception as e:
    print(f'❌ 应用导入失败: {e}')
    import traceback
    traceback.print_exc()
    exit(1)
"

if [ $? -ne 0 ]; then
    echo "❌ 应用导入测试失败"
    exit 1
fi

# 创建必要目录
echo "创建必要目录..."
mkdir -p logs
mkdir -p saved_models/arima
mkdir -p saved_models/rnn
mkdir -p temp

# 检查环境变量文件
if [ ! -f ".env" ]; then
    echo "创建环境变量文件..."
    cp .env.example .env 2>/dev/null || cat > .env << 'EOF'
DB_HOST=**************
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=agriculture_mall
AI_SERVICE_PORT=5000
FLASK_ENV=production
FLASK_DEBUG=False
LOG_LEVEL=INFO
EOF
    echo "⚠️  请编辑 .env 文件设置正确的数据库密码"
fi

# 启动服务
echo "启动AI服务..."
nohup python3 app.py > logs/ai_service.log 2>&1 &
SERVICE_PID=$!

# 等待服务启动
echo "等待服务启动..."
sleep 5

# 检查服务是否正在运行
if ps -p $SERVICE_PID > /dev/null; then
    echo "✅ AI服务启动成功，PID: $SERVICE_PID"
    
    # 测试健康检查
    echo "测试服务健康检查..."
    sleep 2
    curl -s http://localhost:5000/api/v1/health > /dev/null
    if [ $? -eq 0 ]; then
        echo "✅ 服务健康检查通过"
    else
        echo "⚠️  健康检查失败，请检查服务日志"
    fi
else
    echo "❌ 服务启动失败，请检查日志："
    tail -20 logs/ai_service.log
    exit 1
fi

echo ""
echo "=== 修复完成 ==="
echo "服务状态："
echo "  PID: $SERVICE_PID"
echo "  日志: tail -f logs/ai_service.log"
echo "  健康检查: curl http://localhost:5000/api/v1/health"
echo "  停止服务: kill $SERVICE_PID"
