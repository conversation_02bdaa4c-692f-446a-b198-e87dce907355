#!/bin/bash

# SFAP AI服务NGINX代理测试脚本

echo "=== SFAP AI服务NGINX代理测试 ==="

# 测试配置
AI_SERVICE_PORT=5000
NGINX_PORT=8200
SERVER_IP="**************"

echo "测试环境："
echo "  AI服务地址: http://${SERVER_IP}:${AI_SERVICE_PORT}"
echo "  NGINX地址: http://${SERVER_IP}:${NGINX_PORT}"
echo "  代理路径: /api/prediction/"
echo ""

# 1. 测试AI服务直接访问
echo "1. 测试AI服务直接访问..."
echo "   URL: http://${SERVER_IP}:${AI_SERVICE_PORT}/api/v1/health"

curl -s -w "\n状态码: %{http_code}\n响应时间: %{time_total}s\n" \
  -H "Content-Type: application/json" \
  "http://${SERVER_IP}:${AI_SERVICE_PORT}/api/v1/health" || echo "❌ 直接访问失败"

echo ""

# 2. 测试NGINX代理访问
echo "2. 测试NGINX代理访问..."
echo "   URL: http://${SERVER_IP}:${NGINX_PORT}/api/prediction/api/v1/health"

curl -s -w "\n状态码: %{http_code}\n响应时间: %{time_total}s\n" \
  -H "Content-Type: application/json" \
  "http://${SERVER_IP}:${NGINX_PORT}/api/prediction/api/v1/health" || echo "❌ 代理访问失败"

echo ""

# 3. 测试本地访问（如果在服务器上运行）
if [ "$HOSTNAME" = "iZbp1bum5o9ks9h2qb1kkaZ" ] || [ -f "/etc/nginx/nginx.conf" ]; then
    echo "3. 测试本地访问..."
    echo "   URL: http://localhost:${NGINX_PORT}/api/prediction/api/v1/health"
    
    curl -s -w "\n状态码: %{http_code}\n响应时间: %{time_total}s\n" \
      -H "Content-Type: application/json" \
      "http://localhost:${NGINX_PORT}/api/prediction/api/v1/health" || echo "❌ 本地访问失败"
    
    echo ""
fi

# 4. 检查AI服务进程
echo "4. 检查AI服务进程..."
if command -v ps >/dev/null 2>&1; then
    AI_PROCESS=$(ps aux | grep "python.*app.py" | grep -v grep)
    if [ -n "$AI_PROCESS" ]; then
        echo "✅ AI服务进程运行中:"
        echo "$AI_PROCESS"
    else
        echo "❌ AI服务进程未运行"
    fi
else
    echo "⚠️  无法检查进程状态"
fi

echo ""

# 5. 检查端口监听
echo "5. 检查端口监听..."
if command -v netstat >/dev/null 2>&1; then
    echo "检查端口 ${AI_SERVICE_PORT}:"
    netstat -tlnp | grep ":${AI_SERVICE_PORT}" || echo "❌ 端口 ${AI_SERVICE_PORT} 未监听"
    
    echo "检查端口 ${NGINX_PORT}:"
    netstat -tlnp | grep ":${NGINX_PORT}" || echo "❌ 端口 ${NGINX_PORT} 未监听"
elif command -v ss >/dev/null 2>&1; then
    echo "检查端口 ${AI_SERVICE_PORT}:"
    ss -tlnp | grep ":${AI_SERVICE_PORT}" || echo "❌ 端口 ${AI_SERVICE_PORT} 未监听"
    
    echo "检查端口 ${NGINX_PORT}:"
    ss -tlnp | grep ":${NGINX_PORT}" || echo "❌ 端口 ${NGINX_PORT} 未监听"
else
    echo "⚠️  无法检查端口状态"
fi

echo ""

# 6. 测试预测接口
echo "6. 测试预测接口..."
echo "   URL: http://${SERVER_IP}:${NGINX_PORT}/api/prediction/api/v1/predict_rnn"

PREDICTION_DATA='{
  "category": "苹果",
  "region": "山东",
  "product_name": "苹果",
  "history_data": [
    {"date": "2024-01-01", "price": 5.0},
    {"date": "2024-01-02", "price": 5.2},
    {"date": "2024-01-03", "price": 5.1}
  ],
  "forecast_days": 3,
  "confidence_level": 0.95
}'

curl -s -w "\n状态码: %{http_code}\n响应时间: %{time_total}s\n" \
  -X POST \
  -H "Content-Type: application/json" \
  -d "$PREDICTION_DATA" \
  "http://${SERVER_IP}:${NGINX_PORT}/api/prediction/api/v1/predict_rnn" || echo "❌ 预测接口测试失败"

echo ""

# 7. 检查日志
echo "7. 检查AI服务日志..."
if [ -f "logs/ai_service.log" ]; then
    echo "最近的日志条目:"
    tail -5 logs/ai_service.log
elif [ -f "/www/wwwroot/test.com/ai-service/logs/ai_service.log" ]; then
    echo "最近的日志条目:"
    tail -5 /www/wwwroot/test.com/ai-service/logs/ai_service.log
else
    echo "⚠️  未找到AI服务日志文件"
fi

echo ""
echo "=== 测试完成 ==="

# 8. 提供修复建议
echo ""
echo "如果测试失败，请检查："
echo "1. AI服务是否正常运行: python3 app.py"
echo "2. NGINX配置是否正确: nginx -t"
echo "3. 防火墙是否开放端口: iptables -L"
echo "4. 服务器资源是否充足: free -h && df -h"
echo ""
echo "重启服务命令："
echo "  AI服务: pkill -f 'python.*app.py' && cd /www/wwwroot/test.com/ai-service && nohup python3 app.py > logs/ai_service.log 2>&1 &"
echo "  NGINX: nginx -s reload"
