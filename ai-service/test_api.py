#!/usr/bin/env python3
"""
SFAP农品汇平台AI预测服务API测试脚本
"""

import requests
import json
from datetime import datetime, timedelta
import random

# 服务配置
BASE_URL = "http://localhost:5000"
API_BASE = f"{BASE_URL}/api/v1"

def generate_test_data(days=100):
    """生成测试数据"""
    data = []
    base_price = 5.0
    start_date = datetime.now() - timedelta(days=days)
    
    for i in range(days):
        date = start_date + timedelta(days=i)
        # 模拟价格波动
        price = base_price + random.uniform(-1, 1) + 0.1 * i / days
        price = max(price, 1.0)  # 确保价格为正
        
        data.append({
            "date": date.strftime("%Y-%m-%d"),
            "price": round(price, 2),
            "volume": random.randint(500, 2000)
        })
    
    return data

def test_health_check():
    """测试健康检查"""
    print("测试健康检查...")
    try:
        response = requests.get(f"{API_BASE}/health")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"健康检查失败: {e}")
        return False

def test_data_validation():
    """测试数据验证"""
    print("\n测试数据验证...")
    test_data = generate_test_data(30)
    
    payload = {
        "data": test_data
    }
    
    try:
        response = requests.post(f"{API_BASE}/data/validate", json=payload)
        print(f"状态码: {response.status_code}")
        result = response.json()
        print(f"数据质量评分: {result['data']['quality_score']}")
        print(f"有效记录: {result['data']['valid_records']}/{result['data']['total_records']}")
        return response.status_code == 200
    except Exception as e:
        print(f"数据验证失败: {e}")
        return False

def test_rnn_training():
    """测试RNN模型训练"""
    print("\n测试RNN模型训练...")
    test_data = generate_test_data(60)
    
    payload = {
        "category": "apple",
        "region": "山东省",
        "history_data": test_data,
        "model_params": {
            "sequence_length": 30,
            "hidden_size": 64,
            "epochs": 10,  # 减少训练轮数用于测试
            "batch_size": 16
        }
    }
    
    try:
        print("开始训练RNN模型（这可能需要几分钟）...")
        response = requests.post(f"{API_BASE}/train_rnn", json=payload, timeout=300)
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"模型ID: {result['data']['model_id']}")
            print(f"训练MAPE: {result['data']['training_metrics']['mape']:.2f}%")
            return result['data']['model_id']
        else:
            print(f"训练失败: {response.text}")
            return None
    except Exception as e:
        print(f"RNN训练失败: {e}")
        return None

def test_arima_training():
    """测试ARIMA模型训练"""
    print("\n测试ARIMA模型训练...")
    test_data = generate_test_data(60)
    
    payload = {
        "category": "apple",
        "region": "山东省",
        "history_data": test_data
    }
    
    try:
        print("开始训练ARIMA模型...")
        response = requests.post(f"{API_BASE}/train_arima", json=payload, timeout=180)
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"模型ID: {result['data']['model_id']}")
            print(f"训练MAPE: {result['data']['training_metrics']['mape']:.2f}%")
            return result['data']['model_id']
        else:
            print(f"训练失败: {response.text}")
            return None
    except Exception as e:
        print(f"ARIMA训练失败: {e}")
        return None

def test_prediction(model_id, model_type):
    """测试预测"""
    print(f"\n测试{model_type}预测...")
    test_data = generate_test_data(30)
    
    endpoint = f"/predict_{model_type.lower()}"
    payload = {
        "category": "apple",
        "region": "山东省",
        "history_data": test_data,
        "forecast_days": 7,
        "confidence_level": 0.95,
        "model_id": model_id
    }
    
    try:
        response = requests.post(f"{API_BASE}{endpoint}", json=payload, timeout=60)
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            predictions = result['data']['predictions']
            print(f"预测天数: {len(predictions)}")
            print(f"平均预测价格: {result['data']['summary']['avg_price']:.2f}")
            print(f"价格变化: {result['data']['summary']['price_change']:.2%}")
            print(f"趋势方向: {result['data']['summary']['trend_direction']}")
            return True
        else:
            print(f"预测失败: {response.text}")
            return False
    except Exception as e:
        print(f"{model_type}预测失败: {e}")
        return False

def test_model_list():
    """测试模型列表"""
    print("\n测试模型列表...")
    try:
        response = requests.get(f"{API_BASE}/models?category=apple&region=山东省")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            models = result['data']['models']
            print(f"模型数量: {len(models)}")
            for model in models:
                print(f"- {model['model_id']} ({model['model_type']}) - 准确率: {model.get('accuracy', 'N/A')}%")
            return True
        else:
            print(f"获取模型列表失败: {response.text}")
            return False
    except Exception as e:
        print(f"获取模型列表失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("SFAP农品汇平台AI预测服务API测试")
    print("=" * 60)
    
    # 测试健康检查
    if not test_health_check():
        print("服务未启动或不可用，请先启动AI服务")
        return
    
    # 测试数据验证
    test_data_validation()
    
    # 测试ARIMA训练（较快）
    arima_model_id = test_arima_training()
    
    # 测试RNN训练（较慢，可选）
    print("\n是否测试RNN模型训练？这可能需要几分钟时间 (y/n): ", end="")
    if input().lower() == 'y':
        rnn_model_id = test_rnn_training()
    else:
        rnn_model_id = None
    
    # 测试预测
    if arima_model_id:
        test_prediction(arima_model_id, "ARIMA")
    
    if rnn_model_id:
        test_prediction(rnn_model_id, "RNN")
    
    # 测试模型列表
    test_model_list()
    
    print("\n" + "=" * 60)
    print("API测试完成")
    print("=" * 60)

if __name__ == '__main__':
    main()
