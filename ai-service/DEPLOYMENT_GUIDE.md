# SFAP AI预测服务云服务器部署指南

## 问题诊断

### 原始错误
```
ModuleNotFoundError: No module named 'mysql'
```

### 根本原因
1. 缺少 `mysql-connector-python` 依赖包
2. 代码中混用了 `pymysql` 和 `mysql.connector` 两种MySQL连接方式
3. 云服务器环境缺少必要的Python依赖

## 修复方案

### 1. 依赖问题修复
已更新 `requirements.txt` 文件，添加了缺失的依赖：
- `mysql-connector-python==8.2.0`
- `psutil==5.9.5`

### 2. 代码兼容性修复
已修改 `utils/crawl_data_provider.py`：
- 移除 `import mysql.connector`
- 统一使用 `pymysql` 连接数据库

## 部署步骤

### 步骤1：上传代码到云服务器
```bash
# 确保代码已上传到 /www/wwwroot/test.com/ai-service/
cd /www/wwwroot/test.com/ai-service/
```

### 步骤2：创建Python虚拟环境（推荐）
```bash
# 创建虚拟环境
python3 -m venv venv

# 激活虚拟环境
source venv/bin/activate

# 升级pip
pip install --upgrade pip
```

### 步骤3：安装依赖
```bash
# 安装所有依赖
pip install -r requirements.txt

# 验证关键依赖安装
python3 -c "import pymysql; print('✓ pymysql OK')"
python3 -c "import flask; print('✓ Flask OK')"
python3 -c "import pandas; print('✓ Pandas OK')"
python3 -c "import psutil; print('✓ psutil OK')"
```

### 步骤4：配置环境变量
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑配置文件
nano .env
```

配置示例：
```env
DB_HOST=**************
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_actual_password
DB_NAME=agriculture_mall
AI_SERVICE_PORT=5000
FLASK_ENV=production
```

### 步骤5：创建必要目录
```bash
mkdir -p logs
mkdir -p saved_models/arima
mkdir -p saved_models/rnn
mkdir -p temp
```

### 步骤6：测试服务启动
```bash
# 测试导入
python3 -c "from app import app; print('✓ 服务模块导入成功')"

# 启动服务（前台测试）
python3 app.py
```

### 步骤7：后台运行服务
```bash
# 后台运行
nohup python3 app.py > logs/ai_service.log 2>&1 &

# 查看进程
ps aux | grep python3

# 查看日志
tail -f logs/ai_service.log
```

## 验证部署

### 1. 健康检查
```bash
curl http://localhost:5000/api/v1/health
```

期望响应：
```json
{
  "code": 200,
  "message": "服务正常",
  "data": {
    "status": "healthy",
    "version": "1.0.0",
    "uptime": 3600,
    "models_loaded": 0,
    "memory_usage": "256MB",
    "cpu_usage": "5.2%"
  }
}
```

### 2. 测试预测接口
```bash
curl -X POST http://localhost:5000/api/v1/predict \
  -H "Content-Type: application/json" \
  -d '{
    "product_name": "苹果",
    "region": "山东",
    "prediction_days": 7
  }'
```

### 3. 检查NGINX代理
确认NGINX配置中的代理设置：
```nginx
location /api/prediction/ {
    proxy_pass http://**************:5000/;
    # ... 其他配置
}
```

## 故障排除

### 常见问题1：端口被占用
```bash
# 查看端口占用
netstat -tlnp | grep :5000

# 杀死占用进程
kill -9 <PID>
```

### 常见问题2：数据库连接失败
```bash
# 测试数据库连接
mysql -h ************** -u root -p agriculture_mall

# 检查防火墙
iptables -L | grep 3306
```

### 常见问题3：权限问题
```bash
# 设置正确权限
chown -R www:www /www/wwwroot/test.com/ai-service/
chmod +x deploy.sh
```

## 监控和维护

### 查看服务状态
```bash
# 查看进程
ps aux | grep "python3 app.py"

# 查看日志
tail -f logs/ai_service.log
tail -f logs/ai_service_error.log
```

### 重启服务
```bash
# 停止服务
pkill -f "python3 app.py"

# 启动服务
cd /www/wwwroot/test.com/ai-service/
source venv/bin/activate
nohup python3 app.py > logs/ai_service.log 2>&1 &
```

### 更新服务
```bash
# 停止服务
pkill -f "python3 app.py"

# 更新代码
git pull  # 或重新上传文件

# 更新依赖（如有需要）
pip install -r requirements.txt

# 重启服务
nohup python3 app.py > logs/ai_service.log 2>&1 &
```
