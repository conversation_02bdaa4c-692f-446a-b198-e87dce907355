"""
爬取数据提供者

提供统一的接口访问惠农网爬取的农产品价格数据
"""

import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta
from typing import List, Dict, Any, Optional, Tuple
import pymysql
from utils.logger import setup_logger
from utils.database import DatabaseManager

logger = setup_logger()


class CrawlDataProvider:
    """爬取数据提供者"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        logger.info("爬取数据提供者初始化完成")
    
    def get_price_history(self, product_name: str, region_name: str = None, 
                         start_date: str = None, end_date: str = None, 
                         limit: int = 1000) -> List[Dict[str, Any]]:
        """
        获取产品的历史价格数据
        
        Args:
            product_name: 产品名称
            region_name: 地区名称（可选）
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            limit: 限制返回数量
            
        Returns:
            历史价格数据列表
        """
        try:
            # 构建查询SQL
            sql = """
            SELECT 
                pd.data_date as date,
                pd.price,
                pd.unit,
                pd.price_change,
                pd.price_trend,
                p.name as product_name,
                r.name as region_name,
                r.full_name as region_full_name,
                c.name as category_name
            FROM price_data pd
            JOIN products p ON pd.product_id = p.id
            JOIN regions r ON pd.region_id = r.id
            JOIN categories c ON p.category_id = c.id
            WHERE p.name LIKE %s
            """
            
            params = [f"%{product_name}%"]
            
            # 添加地区过滤
            if region_name:
                sql += " AND (r.name LIKE %s OR r.full_name LIKE %s)"
                params.extend([f"%{region_name}%", f"%{region_name}%"])
            
            # 添加日期过滤
            if start_date:
                sql += " AND pd.data_date >= %s"
                params.append(start_date)
            
            if end_date:
                sql += " AND pd.data_date <= %s"
                params.append(end_date)
            
            sql += " ORDER BY pd.data_date DESC, pd.crawl_time DESC"
            
            if limit:
                sql += f" LIMIT {limit}"
            
            # 执行查询
            results = self.db_manager.execute_query(sql, tuple(params))
            
            # 转换数据格式
            formatted_results = []
            for row in results:
                formatted_results.append({
                    'date': row['date'].strftime('%Y-%m-%d') if row['date'] else None,
                    'price': float(row['price']) if row['price'] else 0.0,
                    'unit': row['unit'] or '斤',
                    'price_change': row['price_change'] or '',
                    'price_trend': row['price_trend'],
                    'product_name': row['product_name'],
                    'region_name': row['region_name'],
                    'region_full_name': row['region_full_name'],
                    'category_name': row['category_name']
                })
            
            logger.info(f"获取历史价格数据: {product_name}, {len(formatted_results)}条记录")
            return formatted_results
            
        except Exception as e:
            logger.error(f"获取历史价格数据失败: {e}")
            return []
    
    def get_latest_prices(self, category: str = None, region: str = None, 
                         limit: int = 100) -> List[Dict[str, Any]]:
        """
        获取最新价格数据
        
        Args:
            category: 分类名称（可选）
            region: 地区名称（可选）
            limit: 限制返回数量
            
        Returns:
            最新价格数据列表
        """
        try:
            sql = """
            SELECT 
                pd.data_date as date,
                pd.price,
                pd.unit,
                pd.price_change,
                pd.price_trend,
                p.name as product_name,
                r.name as region_name,
                r.full_name as region_full_name,
                c.name as category_name
            FROM price_data pd
            JOIN products p ON pd.product_id = p.id
            JOIN regions r ON pd.region_id = r.id
            JOIN categories c ON p.category_id = c.id
            WHERE pd.data_date = (
                SELECT MAX(data_date) 
                FROM price_data pd2 
                WHERE pd2.product_id = pd.product_id 
                AND pd2.region_id = pd.region_id
            )
            """
            
            params = []
            
            # 添加分类过滤
            if category:
                sql += " AND c.name LIKE %s"
                params.append(f"%{category}%")
            
            # 添加地区过滤
            if region:
                sql += " AND (r.name LIKE %s OR r.full_name LIKE %s)"
                params.extend([f"%{region}%", f"%{region}%"])
            
            sql += " ORDER BY pd.data_date DESC, p.name, r.name"
            
            if limit:
                sql += f" LIMIT {limit}"
            
            results = self.db_manager.execute_query(sql, tuple(params))
            
            # 转换数据格式
            formatted_results = []
            for row in results:
                formatted_results.append({
                    'date': row['date'].strftime('%Y-%m-%d') if row['date'] else None,
                    'price': float(row['price']) if row['price'] else 0.0,
                    'unit': row['unit'] or '斤',
                    'price_change': row['price_change'] or '',
                    'price_trend': row['price_trend'],
                    'product_name': row['product_name'],
                    'region_name': row['region_name'],
                    'region_full_name': row['region_full_name'],
                    'category_name': row['category_name']
                })
            
            logger.info(f"获取最新价格数据: {len(formatted_results)}条记录")
            return formatted_results
            
        except Exception as e:
            logger.error(f"获取最新价格数据失败: {e}")
            return []
    
    def get_price_statistics(self, product_name: str, region_name: str = None, 
                           days: int = 30) -> Dict[str, Any]:
        """
        获取价格统计信息
        
        Args:
            product_name: 产品名称
            region_name: 地区名称（可选）
            days: 统计天数
            
        Returns:
            价格统计信息
        """
        try:
            sql = """
            SELECT 
                COUNT(*) as count,
                AVG(pd.price) as avg_price,
                MIN(pd.price) as min_price,
                MAX(pd.price) as max_price,
                STDDEV(pd.price) as std_price,
                MIN(pd.data_date) as start_date,
                MAX(pd.data_date) as end_date
            FROM price_data pd
            JOIN products p ON pd.product_id = p.id
            JOIN regions r ON pd.region_id = r.id
            WHERE p.name LIKE %s
            AND pd.data_date >= DATE_SUB(CURDATE(), INTERVAL %s DAY)
            """
            
            params = [f"%{product_name}%", days]
            
            if region_name:
                sql += " AND (r.name LIKE %s OR r.full_name LIKE %s)"
                params.extend([f"%{region_name}%", f"%{region_name}%"])
            
            results = self.db_manager.execute_query(sql, tuple(params))
            
            if results:
                row = results[0]
                return {
                    'count': row['count'] or 0,
                    'avg_price': float(row['avg_price']) if row['avg_price'] else 0.0,
                    'min_price': float(row['min_price']) if row['min_price'] else 0.0,
                    'max_price': float(row['max_price']) if row['max_price'] else 0.0,
                    'std_price': float(row['std_price']) if row['std_price'] else 0.0,
                    'start_date': row['start_date'].strftime('%Y-%m-%d') if row['start_date'] else None,
                    'end_date': row['end_date'].strftime('%Y-%m-%d') if row['end_date'] else None,
                    'days': days
                }
            else:
                return {}
                
        except Exception as e:
            logger.error(f"获取价格统计信息失败: {e}")
            return {}
    
    def get_price_trend(self, product_name: str, region_name: str = None, 
                       days: int = 30) -> List[Dict[str, Any]]:
        """
        获取价格趋势数据
        
        Args:
            product_name: 产品名称
            region_name: 地区名称（可选）
            days: 天数
            
        Returns:
            价格趋势数据
        """
        try:
            sql = """
            SELECT 
                pd.data_date as date,
                AVG(pd.price) as avg_price,
                MIN(pd.price) as min_price,
                MAX(pd.price) as max_price,
                COUNT(*) as count
            FROM price_data pd
            JOIN products p ON pd.product_id = p.id
            JOIN regions r ON pd.region_id = r.id
            WHERE p.name LIKE %s
            AND pd.data_date >= DATE_SUB(CURDATE(), INTERVAL %s DAY)
            """
            
            params = [f"%{product_name}%", days]
            
            if region_name:
                sql += " AND (r.name LIKE %s OR r.full_name LIKE %s)"
                params.extend([f"%{region_name}%", f"%{region_name}%"])
            
            sql += " GROUP BY pd.data_date ORDER BY pd.data_date"
            
            results = self.db_manager.execute_query(sql, tuple(params))
            
            # 转换数据格式
            formatted_results = []
            for row in results:
                formatted_results.append({
                    'date': row['date'].strftime('%Y-%m-%d') if row['date'] else None,
                    'avg_price': float(row['avg_price']) if row['avg_price'] else 0.0,
                    'min_price': float(row['min_price']) if row['min_price'] else 0.0,
                    'max_price': float(row['max_price']) if row['max_price'] else 0.0,
                    'count': row['count'] or 0
                })
            
            logger.info(f"获取价格趋势数据: {product_name}, {len(formatted_results)}条记录")
            return formatted_results
            
        except Exception as e:
            logger.error(f"获取价格趋势数据失败: {e}")
            return []
    
    def search_products(self, keyword: str, category: str = None, 
                       limit: int = 50) -> List[Dict[str, Any]]:
        """
        搜索产品
        
        Args:
            keyword: 搜索关键词
            category: 分类名称（可选）
            limit: 限制返回数量
            
        Returns:
            产品列表
        """
        try:
            sql = """
            SELECT DISTINCT
                p.id,
                p.name as product_name,
                p.unit,
                c.name as category_name,
                COUNT(pd.id) as price_count,
                MAX(pd.data_date) as latest_date
            FROM products p
            JOIN categories c ON p.category_id = c.id
            LEFT JOIN price_data pd ON p.id = pd.product_id
            WHERE p.name LIKE %s
            """
            
            params = [f"%{keyword}%"]
            
            if category:
                sql += " AND c.name LIKE %s"
                params.append(f"%{category}%")
            
            sql += " GROUP BY p.id, p.name, p.unit, c.name"
            sql += " ORDER BY price_count DESC, p.name"
            
            if limit:
                sql += f" LIMIT {limit}"
            
            results = self.db_manager.execute_query(sql, tuple(params))
            
            # 转换数据格式
            formatted_results = []
            for row in results:
                formatted_results.append({
                    'id': row['id'],
                    'product_name': row['product_name'],
                    'unit': row['unit'] or '斤',
                    'category_name': row['category_name'],
                    'price_count': row['price_count'] or 0,
                    'latest_date': row['latest_date'].strftime('%Y-%m-%d') if row['latest_date'] else None
                })
            
            logger.info(f"搜索产品: {keyword}, {len(formatted_results)}条记录")
            return formatted_results
            
        except Exception as e:
            logger.error(f"搜索产品失败: {e}")
            return []
    
    def get_categories(self) -> List[Dict[str, Any]]:
        """
        获取所有分类
        
        Returns:
            分类列表
        """
        try:
            sql = """
            SELECT
                c.id,
                c.name,
                c.pinyin,
                COALESCE(p_count.product_count, 0) as product_count,
                COALESCE(pd_count.price_count, 0) as price_count
            FROM categories c
            LEFT JOIN (
                SELECT category_id, COUNT(*) as product_count
                FROM products
                GROUP BY category_id
            ) p_count ON c.id = p_count.category_id
            LEFT JOIN (
                SELECT p.category_id, COUNT(pd.id) as price_count
                FROM products p
                JOIN price_data pd ON p.id = pd.product_id
                GROUP BY p.category_id
            ) pd_count ON c.id = pd_count.category_id
            WHERE c.is_active = 1
            ORDER BY c.sort_order, c.name
            """
            
            results = self.db_manager.execute_query(sql)
            
            # 转换数据格式
            formatted_results = []
            for row in results:
                formatted_results.append({
                    'id': row['id'],
                    'name': row['name'],
                    'pinyin': row['pinyin'],
                    'product_count': row['product_count'] or 0,
                    'price_count': row['price_count'] or 0
                })
            
            logger.info(f"获取分类列表: {len(formatted_results)}条记录")
            return formatted_results
            
        except Exception as e:
            logger.error(f"获取分类列表失败: {e}")
            return []
    
    def get_regions(self, level: int = None) -> List[Dict[str, Any]]:
        """
        获取地区列表
        
        Args:
            level: 地区层级（1省2市3县）
            
        Returns:
            地区列表
        """
        try:
            sql = """
            SELECT 
                r.id,
                r.name,
                r.full_name,
                r.level,
                COUNT(pd.id) as price_count
            FROM regions r
            LEFT JOIN price_data pd ON r.id = pd.region_id
            WHERE r.is_active = 1
            """
            
            params = []
            
            if level:
                sql += " AND r.level = %s"
                params.append(level)
            
            sql += " GROUP BY r.id, r.name, r.full_name, r.level"
            sql += " ORDER BY r.level, r.name"
            
            results = self.db_manager.execute_query(sql, tuple(params))
            
            # 转换数据格式
            formatted_results = []
            for row in results:
                formatted_results.append({
                    'id': row['id'],
                    'name': row['name'],
                    'full_name': row['full_name'],
                    'level': row['level'],
                    'price_count': row['price_count'] or 0
                })
            
            logger.info(f"获取地区列表: {len(formatted_results)}条记录")
            return formatted_results
            
        except Exception as e:
            logger.error(f"获取地区列表失败: {e}")
            return []
