"""
SFAP农品汇平台AI服务数据库管理工具
"""

import pymysql
import json
from datetime import datetime
from typing import Dict, List, Optional, Any
import os
from dotenv import load_dotenv
from utils.logger import setup_logger

load_dotenv()
logger = setup_logger()

class DatabaseManager:
    def __init__(self):
        self.connection_config = {
            'host': os.getenv('DB_HOST', 'localhost'),
            'port': int(os.getenv('DB_PORT', 3306)),
            'user': os.getenv('DB_USER', 'root'),
            'password': os.getenv('DB_PASSWORD', 'fan13965711955'),
            'database': os.getenv('DB_NAME', 'agriculture_mall'),
            'charset': 'utf8mb4',
            'autocommit': True
        }
        
    def get_connection(self):
        """获取数据库连接"""
        try:
            connection = pymysql.connect(**self.connection_config)
            return connection
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise e

    def execute_query(self, sql: str, params: tuple = None) -> List[Dict[str, Any]]:
        """执行查询SQL"""
        try:
            connection = self.get_connection()
            cursor = connection.cursor(pymysql.cursors.DictCursor)

            if params:
                cursor.execute(sql, params)
            else:
                cursor.execute(sql)

            results = cursor.fetchall()
            cursor.close()
            connection.close()

            return results

        except Exception as e:
            logger.error(f"执行查询失败: {e}")
            logger.error(f"SQL: {sql}")
            logger.error(f"参数: {params}")
            raise e

    def execute_update(self, sql: str, params: tuple = None) -> int:
        """执行更新SQL"""
        try:
            connection = self.get_connection()
            cursor = connection.cursor()

            if params:
                affected_rows = cursor.execute(sql, params)
            else:
                affected_rows = cursor.execute(sql)

            connection.commit()
            cursor.close()
            connection.close()

            return affected_rows

        except Exception as e:
            logger.error(f"执行更新失败: {e}")
            logger.error(f"SQL: {sql}")
            logger.error(f"参数: {params}")
            raise e
    
    def init_database(self):
        """初始化AI预测相关数据库表"""
        try:
            connection = self.get_connection()
            cursor = connection.cursor()
            
            # 创建历史价格数据表
            create_price_history_sql = """
            CREATE TABLE IF NOT EXISTS price_history (
                id BIGINT PRIMARY KEY AUTO_INCREMENT,
                category VARCHAR(50) NOT NULL COMMENT '农产品类别',
                product_name VARCHAR(100) NOT NULL COMMENT '具体产品名称',
                region VARCHAR(100) NOT NULL COMMENT '地区',
                market_name VARCHAR(200) COMMENT '市场名称',
                price DECIMAL(10,2) NOT NULL COMMENT '价格(元/kg)',
                volume INT COMMENT '交易量(kg)',
                quality_grade VARCHAR(10) COMMENT '质量等级',
                date DATE NOT NULL COMMENT '日期',
                data_source VARCHAR(50) COMMENT '数据来源',
                weather VARCHAR(50) COMMENT '天气情况',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                
                INDEX idx_category_region_date (category, region, date),
                INDEX idx_product_date (product_name, date),
                INDEX idx_date (date),
                UNIQUE KEY uk_category_region_date (category, region, date, market_name)
            ) COMMENT '历史价格数据表'
            """
            
            # 创建模型信息表
            create_models_sql = """
            CREATE TABLE IF NOT EXISTS prediction_models (
                id BIGINT PRIMARY KEY AUTO_INCREMENT,
                model_id VARCHAR(100) UNIQUE NOT NULL COMMENT '模型唯一标识',
                model_type ENUM('RNN', 'ARIMA') NOT NULL COMMENT '模型类型',
                category VARCHAR(50) NOT NULL COMMENT '农产品类别',
                region VARCHAR(100) NOT NULL COMMENT '地区',
                model_params JSON COMMENT '模型参数',
                training_metrics JSON COMMENT '训练指标',
                model_file_path VARCHAR(500) COMMENT '模型文件路径',
                model_size BIGINT COMMENT '模型文件大小(字节)',
                accuracy DECIMAL(5,2) COMMENT '模型准确率',
                status ENUM('training', 'active', 'deprecated') DEFAULT 'training',
                training_start_time TIMESTAMP COMMENT '训练开始时间',
                training_end_time TIMESTAMP COMMENT '训练结束时间',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                
                INDEX idx_category_region (category, region),
                INDEX idx_model_type (model_type),
                INDEX idx_status (status)
            ) COMMENT '预测模型信息表'
            """
            
            # 创建预测结果表
            create_results_sql = """
            CREATE TABLE IF NOT EXISTS prediction_results (
                id BIGINT PRIMARY KEY AUTO_INCREMENT,
                prediction_id VARCHAR(100) UNIQUE NOT NULL COMMENT '预测任务ID',
                model_id VARCHAR(100) NOT NULL COMMENT '使用的模型ID',
                category VARCHAR(50) NOT NULL COMMENT '农产品类别',
                region VARCHAR(100) NOT NULL COMMENT '地区',
                prediction_date DATE NOT NULL COMMENT '预测日期',
                predicted_price DECIMAL(10,2) NOT NULL COMMENT '预测价格',
                confidence DECIMAL(5,4) COMMENT '置信度',
                upper_bound DECIMAL(10,2) COMMENT '置信区间上界',
                lower_bound DECIMAL(10,2) COMMENT '置信区间下界',
                trend VARCHAR(20) COMMENT '趋势(up/down/stable)',
                factors JSON COMMENT '影响因素分析',
                actual_price DECIMAL(10,2) COMMENT '实际价格(用于后续验证)',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                
                INDEX idx_model_id (model_id),
                INDEX idx_category_region_date (category, region, prediction_date),
                INDEX idx_prediction_date (prediction_date)
            ) COMMENT '预测结果表'
            """
            
            # 创建模型评估表
            create_evaluations_sql = """
            CREATE TABLE IF NOT EXISTS model_evaluations (
                id BIGINT PRIMARY KEY AUTO_INCREMENT,
                model_id VARCHAR(100) NOT NULL COMMENT '模型ID',
                evaluation_date DATE NOT NULL COMMENT '评估日期',
                mape DECIMAL(8,4) COMMENT '平均绝对百分比误差',
                rmse DECIMAL(10,4) COMMENT '均方根误差',
                mae DECIMAL(10,4) COMMENT '平均绝对误差',
                r2_score DECIMAL(8,4) COMMENT 'R²决定系数',
                trend_accuracy DECIMAL(5,2) COMMENT '趋势预测准确率',
                evaluation_details JSON COMMENT '详细评估信息',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                
                INDEX idx_model_id (model_id),
                INDEX idx_evaluation_date (evaluation_date)
            ) COMMENT '模型评估表'
            """
            
            # 执行建表语句
            cursor.execute(create_price_history_sql)
            cursor.execute(create_models_sql)
            cursor.execute(create_results_sql)
            cursor.execute(create_evaluations_sql)
            
            logger.info("AI预测数据库表初始化完成")
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise e
        finally:
            if 'connection' in locals():
                connection.close()
    
    def save_model_info(self, model_info: Dict[str, Any]):
        """保存模型信息"""
        try:
            connection = self.get_connection()
            cursor = connection.cursor()
            
            sql = """
            INSERT INTO prediction_models 
            (model_id, model_type, category, region, model_params, training_metrics, 
             model_file_path, accuracy, status, training_start_time, training_end_time)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            values = (
                model_info['model_id'],
                model_info['model_type'],
                model_info['category'],
                model_info['region'],
                json.dumps(model_info.get('model_params', {})),
                json.dumps(model_info.get('training_metrics', {})),
                model_info.get('model_file_path'),
                model_info.get('accuracy'),
                model_info.get('status', 'active'),
                datetime.now(),
                datetime.now()
            )
            
            cursor.execute(sql, values)
            logger.info(f"模型信息保存成功: {model_info['model_id']}")
            
        except Exception as e:
            logger.error(f"保存模型信息失败: {e}")
            raise e
        finally:
            if 'connection' in locals():
                connection.close()
    
    def get_model_by_id(self, model_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取模型信息"""
        try:
            connection = self.get_connection()
            cursor = connection.cursor(pymysql.cursors.DictCursor)
            
            sql = "SELECT * FROM prediction_models WHERE model_id = %s"
            cursor.execute(sql, (model_id,))
            result = cursor.fetchone()
            
            if result:
                # 解析JSON字段
                if result.get('model_params'):
                    result['model_params'] = json.loads(result['model_params'])
                if result.get('training_metrics'):
                    result['training_metrics'] = json.loads(result['training_metrics'])
            
            return result
            
        except Exception as e:
            logger.error(f"获取模型信息失败: {e}")
            return None
        finally:
            if 'connection' in locals():
                connection.close()
    
    def get_latest_model(self, category: str, region: str, model_type: str) -> Optional[Dict[str, Any]]:
        """获取最新的活跃模型"""
        try:
            connection = self.get_connection()
            cursor = connection.cursor(pymysql.cursors.DictCursor)
            
            sql = """
            SELECT * FROM prediction_models 
            WHERE category = %s AND region = %s AND model_type = %s AND status = 'active'
            ORDER BY created_at DESC LIMIT 1
            """
            
            cursor.execute(sql, (category, region, model_type))
            result = cursor.fetchone()
            
            if result:
                # 解析JSON字段
                if result.get('model_params'):
                    result['model_params'] = json.loads(result['model_params'])
                if result.get('training_metrics'):
                    result['training_metrics'] = json.loads(result['training_metrics'])
            
            return result
            
        except Exception as e:
            logger.error(f"获取最新模型失败: {e}")
            return None
        finally:
            if 'connection' in locals():
                connection.close()
    
    def get_models(self, category: str = None, region: str = None, 
                   model_type: str = None, page: int = 1, size: int = 10) -> Dict[str, Any]:
        """获取模型列表"""
        try:
            connection = self.get_connection()
            cursor = connection.cursor(pymysql.cursors.DictCursor)
            
            # 构建查询条件
            conditions = []
            params = []
            
            if category:
                conditions.append("category = %s")
                params.append(category)
            if region:
                conditions.append("region = %s")
                params.append(region)
            if model_type:
                conditions.append("model_type = %s")
                params.append(model_type)
            
            where_clause = " WHERE " + " AND ".join(conditions) if conditions else ""
            
            # 查询总数
            count_sql = f"SELECT COUNT(*) as total FROM prediction_models{where_clause}"
            cursor.execute(count_sql, params)
            total = cursor.fetchone()['total']
            
            # 查询数据
            offset = (page - 1) * size
            data_sql = f"""
            SELECT * FROM prediction_models{where_clause}
            ORDER BY created_at DESC LIMIT %s OFFSET %s
            """
            
            cursor.execute(data_sql, params + [size, offset])
            models = cursor.fetchall()
            
            # 解析JSON字段
            for model in models:
                if model.get('model_params'):
                    model['model_params'] = json.loads(model['model_params'])
                if model.get('training_metrics'):
                    model['training_metrics'] = json.loads(model['training_metrics'])
            
            return {
                'models': models,
                'pagination': {
                    'page': page,
                    'size': size,
                    'total': total,
                    'pages': (total + size - 1) // size
                }
            }
            
        except Exception as e:
            logger.error(f"获取模型列表失败: {e}")
            return {'models': [], 'pagination': {'page': page, 'size': size, 'total': 0, 'pages': 0}}
        finally:
            if 'connection' in locals():
                connection.close()
    
    def count_active_models(self) -> int:
        """统计活跃模型数量"""
        try:
            connection = self.get_connection()
            cursor = connection.cursor()
            
            sql = "SELECT COUNT(*) FROM prediction_models WHERE status = 'active'"
            cursor.execute(sql)
            result = cursor.fetchone()
            
            return result[0] if result else 0
            
        except Exception as e:
            logger.error(f"统计活跃模型失败: {e}")
            return 0
        finally:
            if 'connection' in locals():
                connection.close()
