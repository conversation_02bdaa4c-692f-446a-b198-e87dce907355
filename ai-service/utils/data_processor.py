"""
SFAP农品汇平台AI服务数据预处理工具
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Any, Tuple
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.impute import SimpleImputer
import warnings
warnings.filterwarnings('ignore')

from utils.logger import setup_logger

logger = setup_logger()

class DataProcessor:
    def __init__(self):
        self.price_scaler = None
        self.feature_scaler = None
        
    def preprocess_data(self, raw_data: List[Dict[str, Any]], 
                       category: str, region: str) -> pd.DataFrame:
        """预处理原始数据"""
        try:
            logger.info(f"开始预处理数据: {category} - {region}, 数据量: {len(raw_data)}")
            
            # 转换为DataFrame
            df = pd.DataFrame(raw_data)
            
            # 基础数据清洗
            df = self._clean_basic_data(df)
            
            # 创建时间特征
            df = self._create_time_features(df)
            
            # 创建价格特征
            df = self._create_price_features(df)
            
            # 创建技术指标
            df = self._create_technical_indicators(df)
            
            # 处理缺失值
            df = self._handle_missing_values(df)
            
            # 异常值处理
            df = self._handle_outliers(df)
            
            logger.info(f"数据预处理完成: 最终数据量: {len(df)}, 特征数: {len(df.columns)}")
            
            return df
            
        except Exception as e:
            logger.error(f"数据预处理失败: {e}")
            raise e
    
    def _clean_basic_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """基础数据清洗"""
        # 确保必要字段存在
        required_fields = ['date', 'price']
        for field in required_fields:
            if field not in df.columns:
                raise ValueError(f"缺少必要字段: {field}")
        
        # 转换日期格式
        df['date'] = pd.to_datetime(df['date'])
        
        # 确保价格为数值类型
        df['price'] = pd.to_numeric(df['price'], errors='coerce')
        
        # 移除价格为空或非正数的记录
        df = df[df['price'] > 0]
        
        # 按日期排序
        df = df.sort_values('date').reset_index(drop=True)
        
        # 移除重复日期（保留最后一条）
        df = df.drop_duplicates(subset=['date'], keep='last')
        
        return df
    
    def _create_time_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建时间特征"""
        df['year'] = df['date'].dt.year
        df['month'] = df['date'].dt.month
        df['day'] = df['date'].dt.day
        df['day_of_week'] = df['date'].dt.dayofweek
        df['day_of_year'] = df['date'].dt.dayofyear
        df['week_of_year'] = df['date'].dt.isocalendar().week
        df['quarter'] = df['date'].dt.quarter
        
        # 周末标识
        df['is_weekend'] = (df['day_of_week'] >= 5).astype(int)
        
        # 季节特征（正弦余弦编码）
        df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
        df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
        df['day_sin'] = np.sin(2 * np.pi * df['day_of_year'] / 365)
        df['day_cos'] = np.cos(2 * np.pi * df['day_of_year'] / 365)
        
        # 季节标识
        df['season'] = df['month'].apply(self._get_season)
        
        return df
    
    def _get_season(self, month: int) -> int:
        """获取季节标识"""
        if month in [12, 1, 2]:
            return 0  # 冬季
        elif month in [3, 4, 5]:
            return 1  # 春季
        elif month in [6, 7, 8]:
            return 2  # 夏季
        else:
            return 3  # 秋季
    
    def _create_price_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建价格相关特征"""
        # 滞后特征
        for lag in [1, 3, 7, 14, 30]:
            df[f'price_lag_{lag}'] = df['price'].shift(lag)
        
        # 移动平均
        for window in [3, 7, 14, 30]:
            df[f'price_ma_{window}'] = df['price'].rolling(window=window).mean()
            df[f'price_std_{window}'] = df['price'].rolling(window=window).std()
            df[f'price_max_{window}'] = df['price'].rolling(window=window).max()
            df[f'price_min_{window}'] = df['price'].rolling(window=window).min()
        
        # 价格变化率
        df['price_change'] = df['price'].pct_change()
        df['price_change_abs'] = df['price_change'].abs()
        
        # 价格相对位置（在一定窗口内的位置）
        for window in [7, 14, 30]:
            df[f'price_rank_{window}'] = df['price'].rolling(window=window).rank(pct=True)
        
        # 价格趋势
        for window in [7, 14]:
            df[f'price_trend_{window}'] = df['price'].rolling(window=window).apply(
                lambda x: np.polyfit(range(len(x)), x, 1)[0] if len(x) == window else 0
            )
        
        return df
    
    def _create_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建技术指标"""
        # RSI (相对强弱指数)
        df['rsi_14'] = self._calculate_rsi(df['price'], 14)
        
        # 布林带
        df['bb_upper'], df['bb_middle'], df['bb_lower'] = self._calculate_bollinger_bands(df['price'], 20)
        df['bb_width'] = df['bb_upper'] - df['bb_lower']
        df['bb_position'] = (df['price'] - df['bb_lower']) / df['bb_width']
        
        # MACD
        df['macd'], df['macd_signal'], df['macd_histogram'] = self._calculate_macd(df['price'])
        
        # 波动率
        for window in [7, 14, 30]:
            df[f'volatility_{window}'] = df['price'].rolling(window=window).std() / df['price'].rolling(window=window).mean()
        
        return df
    
    def _calculate_rsi(self, prices: pd.Series, window: int = 14) -> pd.Series:
        """计算RSI指标"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def _calculate_bollinger_bands(self, prices: pd.Series, window: int = 20, num_std: float = 2) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """计算布林带"""
        middle = prices.rolling(window=window).mean()
        std = prices.rolling(window=window).std()
        upper = middle + (std * num_std)
        lower = middle - (std * num_std)
        return upper, middle, lower
    
    def _calculate_macd(self, prices: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """计算MACD指标"""
        ema_fast = prices.ewm(span=fast).mean()
        ema_slow = prices.ewm(span=slow).mean()
        macd = ema_fast - ema_slow
        macd_signal = macd.ewm(span=signal).mean()
        macd_histogram = macd - macd_signal
        return macd, macd_signal, macd_histogram
    
    def _handle_missing_values(self, df: pd.DataFrame) -> pd.DataFrame:
        """处理缺失值"""
        # 对于价格相关的特征，使用前向填充
        price_columns = [col for col in df.columns if 'price' in col.lower()]
        for col in price_columns:
            df[col] = df[col].fillna(method='ffill')
        
        # 对于技术指标，使用中位数填充
        technical_columns = ['rsi_14', 'bb_upper', 'bb_middle', 'bb_lower', 'bb_width', 'bb_position',
                           'macd', 'macd_signal', 'macd_histogram']
        for col in technical_columns:
            if col in df.columns:
                df[col] = df[col].fillna(df[col].median())
        
        # 对于波动率，使用0填充
        volatility_columns = [col for col in df.columns if 'volatility' in col]
        for col in volatility_columns:
            df[col] = df[col].fillna(0)
        
        return df
    
    def _handle_outliers(self, df: pd.DataFrame, method: str = 'iqr') -> pd.DataFrame:
        """处理异常值"""
        if method == 'iqr':
            # 使用IQR方法检测价格异常值
            Q1 = df['price'].quantile(0.25)
            Q3 = df['price'].quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            # 标记异常值
            df['is_outlier'] = (df['price'] < lower_bound) | (df['price'] > upper_bound)
            
            # 记录异常值数量
            outlier_count = df['is_outlier'].sum()
            if outlier_count > 0:
                logger.warning(f"检测到 {outlier_count} 个价格异常值")
            
            # 可以选择删除或修正异常值
            # 这里选择用中位数替换
            median_price = df['price'].median()
            df.loc[df['is_outlier'], 'price'] = median_price
        
        return df
    
    def normalize_features(self, df: pd.DataFrame, fit: bool = True) -> pd.DataFrame:
        """特征标准化"""
        # 价格相关特征使用MinMax标准化
        price_features = [col for col in df.columns if 'price' in col.lower()]
        
        if fit:
            self.price_scaler = MinMaxScaler()
            df[price_features] = self.price_scaler.fit_transform(df[price_features])
        else:
            if self.price_scaler is None:
                raise ValueError("价格标准化器未初始化，请先进行fit操作")
            df[price_features] = self.price_scaler.transform(df[price_features])
        
        # 其他数值特征使用标准化
        numeric_features = df.select_dtypes(include=[np.number]).columns
        other_features = [col for col in numeric_features if col not in price_features and col != 'is_outlier']
        
        if other_features:
            if fit:
                self.feature_scaler = StandardScaler()
                df[other_features] = self.feature_scaler.fit_transform(df[other_features])
            else:
                if self.feature_scaler is None:
                    raise ValueError("特征标准化器未初始化，请先进行fit操作")
                df[other_features] = self.feature_scaler.transform(df[other_features])
        
        return df
    
    def create_sequences(self, data: np.ndarray, sequence_length: int = 30, 
                        forecast_horizon: int = 1) -> Tuple[np.ndarray, np.ndarray]:
        """构建时间序列数据"""
        X, y = [], []
        
        for i in range(sequence_length, len(data) - forecast_horizon + 1):
            # 输入序列
            X.append(data[i-sequence_length:i])
            # 目标值
            y.append(data[i:i+forecast_horizon])
        
        return np.array(X), np.array(y)
    
    def get_feature_columns(self) -> List[str]:
        """获取特征列名"""
        return [
            'price', 'price_lag_1', 'price_lag_3', 'price_lag_7', 'price_lag_14',
            'price_ma_7', 'price_ma_14', 'price_ma_30', 'price_std_7', 'price_std_14',
            'price_max_7', 'price_min_7', 'price_change', 'price_rank_7', 'price_trend_7',
            'month', 'day_of_week', 'day_of_year', 'quarter', 'is_weekend',
            'month_sin', 'month_cos', 'day_sin', 'day_cos', 'season',
            'rsi_14', 'bb_position', 'bb_width', 'macd', 'volatility_14'
        ]
