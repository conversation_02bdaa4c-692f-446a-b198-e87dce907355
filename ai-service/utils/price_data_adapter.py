"""
价格数据适配器

将爬取的原始价格数据转换为AI模型所需的格式
"""

import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta
from typing import List, Dict, Any, Optional, Tuple
import warnings
warnings.filterwarnings('ignore')

from utils.logger import setup_logger

logger = setup_logger()


class PriceDataAdapter:
    """价格数据适配器"""
    
    def __init__(self):
        logger.info("价格数据适配器初始化完成")
    
    def format_for_arima(self, raw_data: List[Dict[str, Any]], 
                        date_column: str = 'date', 
                        price_column: str = 'price') -> pd.DataFrame:
        """
        格式化数据用于ARIMA模型
        
        Args:
            raw_data: 原始数据列表
            date_column: 日期列名
            price_column: 价格列名
            
        Returns:
            格式化后的DataFrame
        """
        try:
            if not raw_data:
                logger.warning("原始数据为空，返回空DataFrame")
                return pd.DataFrame()
            
            # 转换为DataFrame
            df = pd.DataFrame(raw_data)
            
            # 检查必要列是否存在
            if date_column not in df.columns or price_column not in df.columns:
                logger.error(f"缺少必要列: {date_column} 或 {price_column}")
                return pd.DataFrame()
            
            # 转换日期列
            df[date_column] = pd.to_datetime(df[date_column])
            
            # 转换价格列为数值类型
            df[price_column] = pd.to_numeric(df[price_column], errors='coerce')
            
            # 删除无效数据
            df = df.dropna(subset=[date_column, price_column])
            df = df[df[price_column] > 0]  # 删除价格为0或负数的记录
            
            # 按日期排序
            df = df.sort_values(date_column)
            
            # 设置日期为索引
            df.set_index(date_column, inplace=True)
            
            # 如果有重复日期，取平均值
            if df.index.duplicated().any():
                df = df.groupby(df.index).agg({
                    price_column: 'mean',
                    'product_name': 'first',
                    'region_name': 'first',
                    'category_name': 'first'
                })
            
            # 填充缺失的日期（可选）
            if len(df) > 1:
                date_range = pd.date_range(start=df.index.min(), end=df.index.max(), freq='D')
                df = df.reindex(date_range)
                
                # 前向填充价格数据
                df[price_column] = df[price_column].fillna(method='ffill')
                
                # 填充其他列
                for col in ['product_name', 'region_name', 'category_name']:
                    if col in df.columns:
                        df[col] = df[col].fillna(method='ffill')
            
            logger.info(f"ARIMA数据格式化完成: {len(df)}条记录")
            return df
            
        except Exception as e:
            logger.error(f"ARIMA数据格式化失败: {e}")
            return pd.DataFrame()
    
    def format_for_rnn(self, raw_data: List[Dict[str, Any]], 
                      sequence_length: int = 30,
                      features: List[str] = None) -> Tuple[np.ndarray, np.ndarray]:
        """
        格式化数据用于RNN模型
        
        Args:
            raw_data: 原始数据列表
            sequence_length: 序列长度
            features: 特征列名列表
            
        Returns:
            (X, y) 训练数据元组
        """
        try:
            if not raw_data:
                logger.warning("原始数据为空，返回空数组")
                return np.array([]), np.array([])
            
            # 转换为DataFrame
            df = pd.DataFrame(raw_data)
            
            # 默认特征列
            if features is None:
                features = ['price']
            
            # 检查必要列是否存在
            required_columns = ['date', 'price']
            for col in required_columns:
                if col not in df.columns:
                    logger.error(f"缺少必要列: {col}")
                    return np.array([]), np.array([])
            
            # 转换日期和价格
            df['date'] = pd.to_datetime(df['date'])
            df['price'] = pd.to_numeric(df['price'], errors='coerce')
            
            # 删除无效数据
            df = df.dropna(subset=['date', 'price'])
            df = df[df['price'] > 0]
            
            # 按日期排序
            df = df.sort_values('date')
            
            # 创建额外特征
            df = self._create_features(df)
            
            # 选择特征列
            available_features = [f for f in features if f in df.columns]
            if not available_features:
                logger.error("没有可用的特征列")
                return np.array([]), np.array([])
            
            # 提取特征数据
            feature_data = df[available_features].values
            
            # 数据标准化
            from sklearn.preprocessing import MinMaxScaler
            scaler = MinMaxScaler()
            feature_data = scaler.fit_transform(feature_data)
            
            # 创建序列数据
            X, y = [], []
            for i in range(sequence_length, len(feature_data)):
                X.append(feature_data[i-sequence_length:i])
                y.append(feature_data[i, 0])  # 假设第一列是价格
            
            X = np.array(X)
            y = np.array(y)
            
            logger.info(f"RNN数据格式化完成: X shape={X.shape}, y shape={y.shape}")
            return X, y
            
        except Exception as e:
            logger.error(f"RNN数据格式化失败: {e}")
            return np.array([]), np.array([])
    
    def _create_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        创建额外特征
        
        Args:
            df: 原始DataFrame
            
        Returns:
            包含额外特征的DataFrame
        """
        try:
            # 价格滞后特征
            df['price_lag_1'] = df['price'].shift(1)
            df['price_lag_3'] = df['price'].shift(3)
            df['price_lag_7'] = df['price'].shift(7)
            
            # 移动平均特征
            df['price_ma_3'] = df['price'].rolling(window=3).mean()
            df['price_ma_7'] = df['price'].rolling(window=7).mean()
            df['price_ma_14'] = df['price'].rolling(window=14).mean()
            
            # 移动标准差特征
            df['price_std_7'] = df['price'].rolling(window=7).std()
            df['price_std_14'] = df['price'].rolling(window=14).std()
            
            # 价格变化特征
            df['price_change_1d'] = df['price'].pct_change(1)
            df['price_change_3d'] = df['price'].pct_change(3)
            df['price_change_7d'] = df['price'].pct_change(7)
            
            # 时间特征
            df['month'] = df['date'].dt.month
            df['day_of_week'] = df['date'].dt.dayofweek
            df['day_of_month'] = df['date'].dt.day
            df['quarter'] = df['date'].dt.quarter
            
            # 季节性特征
            df['is_weekend'] = (df['day_of_week'] >= 5).astype(int)
            df['season'] = ((df['month'] % 12 + 3) // 3).astype(int)
            
            return df
            
        except Exception as e:
            logger.error(f"创建特征失败: {e}")
            return df
    
    def validate_data_quality(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        验证数据质量
        
        Args:
            data: 数据列表
            
        Returns:
            数据质量报告
        """
        try:
            if not data:
                return {
                    'total_records': 0,
                    'valid_records': 0,
                    'invalid_records': 0,
                    'quality_score': 0.0,
                    'issues': ['数据为空']
                }
            
            total_records = len(data)
            valid_records = 0
            issues = []
            
            # 检查每条记录
            for i, record in enumerate(data):
                is_valid = True
                
                # 检查必要字段
                required_fields = ['date', 'price', 'product_name']
                for field in required_fields:
                    if field not in record or record[field] is None:
                        issues.append(f"记录{i}: 缺少字段 {field}")
                        is_valid = False
                
                # 检查价格合理性
                if 'price' in record:
                    try:
                        price = float(record['price'])
                        if price <= 0:
                            issues.append(f"记录{i}: 价格不合理 {price}")
                            is_valid = False
                        elif price > 10000:
                            issues.append(f"记录{i}: 价格过高 {price}")
                            is_valid = False
                    except (ValueError, TypeError):
                        issues.append(f"记录{i}: 价格格式错误")
                        is_valid = False
                
                # 检查日期格式
                if 'date' in record:
                    try:
                        pd.to_datetime(record['date'])
                    except:
                        issues.append(f"记录{i}: 日期格式错误")
                        is_valid = False
                
                if is_valid:
                    valid_records += 1
            
            invalid_records = total_records - valid_records
            quality_score = (valid_records / total_records * 100) if total_records > 0 else 0
            
            # 检查数据连续性
            if valid_records > 1:
                df = pd.DataFrame(data)
                df['date'] = pd.to_datetime(df['date'], errors='coerce')
                df = df.dropna(subset=['date']).sort_values('date')
                
                if len(df) > 1:
                    date_diff = (df['date'].max() - df['date'].min()).days
                    expected_records = date_diff + 1
                    continuity_score = len(df) / expected_records * 100
                    
                    if continuity_score < 80:
                        issues.append(f"数据连续性不足: {continuity_score:.1f}%")
            
            report = {
                'total_records': total_records,
                'valid_records': valid_records,
                'invalid_records': invalid_records,
                'quality_score': round(quality_score, 2),
                'issues': issues[:10]  # 只返回前10个问题
            }
            
            logger.info(f"数据质量验证完成: 质量分数={quality_score:.2f}%")
            return report
            
        except Exception as e:
            logger.error(f"数据质量验证失败: {e}")
            return {
                'total_records': 0,
                'valid_records': 0,
                'invalid_records': 0,
                'quality_score': 0.0,
                'issues': [f'验证失败: {str(e)}']
            }
    
    def clean_data(self, data: List[Dict[str, Any]], 
                   remove_outliers: bool = True,
                   fill_missing: bool = True) -> List[Dict[str, Any]]:
        """
        清洗数据
        
        Args:
            data: 原始数据
            remove_outliers: 是否移除异常值
            fill_missing: 是否填充缺失值
            
        Returns:
            清洗后的数据
        """
        try:
            if not data:
                return []
            
            df = pd.DataFrame(data)
            
            # 转换数据类型
            if 'date' in df.columns:
                df['date'] = pd.to_datetime(df['date'], errors='coerce')
            if 'price' in df.columns:
                df['price'] = pd.to_numeric(df['price'], errors='coerce')
            
            # 删除完全无效的记录
            df = df.dropna(subset=['date', 'price'])
            df = df[df['price'] > 0]
            
            # 移除异常值
            if remove_outliers and 'price' in df.columns:
                Q1 = df['price'].quantile(0.25)
                Q3 = df['price'].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                
                before_count = len(df)
                df = df[(df['price'] >= lower_bound) & (df['price'] <= upper_bound)]
                after_count = len(df)
                
                if before_count > after_count:
                    logger.info(f"移除异常值: {before_count - after_count}条")
            
            # 按日期排序
            df = df.sort_values('date')
            
            # 填充缺失值
            if fill_missing:
                # 前向填充
                df = df.fillna(method='ffill')
                # 后向填充
                df = df.fillna(method='bfill')
            
            # 转换回字典列表
            cleaned_data = df.to_dict('records')
            
            # 转换日期格式
            for record in cleaned_data:
                if 'date' in record and pd.notna(record['date']):
                    record['date'] = record['date'].strftime('%Y-%m-%d')
            
            logger.info(f"数据清洗完成: {len(data)} -> {len(cleaned_data)}条")
            return cleaned_data
            
        except Exception as e:
            logger.error(f"数据清洗失败: {e}")
            return data
    
    def aggregate_by_period(self, data: List[Dict[str, Any]], 
                           period: str = 'D') -> List[Dict[str, Any]]:
        """
        按时间周期聚合数据
        
        Args:
            data: 原始数据
            period: 聚合周期 ('D'日, 'W'周, 'M'月)
            
        Returns:
            聚合后的数据
        """
        try:
            if not data:
                return []
            
            df = pd.DataFrame(data)
            
            # 转换日期
            df['date'] = pd.to_datetime(df['date'])
            df['price'] = pd.to_numeric(df['price'], errors='coerce')
            
            # 删除无效数据
            df = df.dropna(subset=['date', 'price'])
            
            # 设置日期为索引
            df.set_index('date', inplace=True)
            
            # 按周期聚合
            agg_df = df.resample(period).agg({
                'price': ['mean', 'min', 'max', 'count'],
                'product_name': 'first',
                'region_name': 'first',
                'category_name': 'first'
            }).reset_index()
            
            # 扁平化列名
            agg_df.columns = ['date', 'avg_price', 'min_price', 'max_price', 'count',
                             'product_name', 'region_name', 'category_name']
            
            # 转换为字典列表
            result = []
            for _, row in agg_df.iterrows():
                result.append({
                    'date': row['date'].strftime('%Y-%m-%d'),
                    'price': float(row['avg_price']),
                    'min_price': float(row['min_price']),
                    'max_price': float(row['max_price']),
                    'count': int(row['count']),
                    'product_name': row['product_name'],
                    'region_name': row['region_name'],
                    'category_name': row['category_name']
                })
            
            logger.info(f"数据聚合完成: {len(data)} -> {len(result)}条 (周期: {period})")
            return result
            
        except Exception as e:
            logger.error(f"数据聚合失败: {e}")
            return data
