#!/usr/bin/env python3
"""
SFAP农品汇平台AI预测服务启动脚本
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("错误: 需要Python 3.8或更高版本")
        sys.exit(1)
    print(f"✓ Python版本: {sys.version}")

def check_dependencies():
    """检查依赖包"""
    print("检查依赖包...")

    # 核心必需包
    required_packages = [
        'flask', 'pandas', 'numpy', 'statsmodels', 'pymysql'
    ]

    # 可选包（有替代方案）
    optional_packages = {
        'scikit-learn': '已安装',
        'tensorflow': '使用PyTorch替代',
        'pmdarima': '使用statsmodels替代',
        'python-dotenv': '已安装',
        'seaborn': '已安装'
    }

    missing_packages = []

    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"✗ {package} (缺失)")

    # 检查可选包
    for package, status in optional_packages.items():
        try:
            __import__(package)
            print(f"✓ {package}")
        except ImportError:
            print(f"○ {package} ({status})")

    if missing_packages:
        print(f"\n缺少核心依赖包: {', '.join(missing_packages)}")
        print("请安装缺失的包后重试")
        return False

    print("\n✓ 依赖检查通过（使用简化配置）")
    return True

def create_directories():
    """创建必要的目录"""
    directories = [
        'logs',
        'saved_models',
        'saved_models/rnn',
        'saved_models/arima',
        'data',
        'temp'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✓ 目录: {directory}")

def check_database_connection():
    """检查数据库连接"""
    try:
        from utils.database import DatabaseManager
        db_manager = DatabaseManager()
        connection = db_manager.get_connection()
        connection.close()
        print("✓ 数据库连接正常")
        return True
    except Exception as e:
        print(f"✗ 数据库连接失败: {e}")
        return False

def install_dependencies():
    """安装依赖包"""
    print("正在安装依赖包...")
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
        print("✓ 依赖包安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 依赖包安装失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("SFAP农品汇平台AI预测服务启动检查")
    print("=" * 50)
    
    # 检查Python版本
    check_python_version()
    
    # 创建必要目录
    print("\n创建目录...")
    create_directories()
    
    # 检查依赖包
    print("\n检查依赖包...")
    if not check_dependencies():
        print("\n是否自动安装缺失的依赖包? (y/n): ", end="")
        if input().lower() == 'y':
            if not install_dependencies():
                sys.exit(1)
        else:
            sys.exit(1)
    
    # 检查数据库连接
    print("\n检查数据库连接...")
    if not check_database_connection():
        print("请检查数据库配置和连接")
        sys.exit(1)
    
    print("\n" + "=" * 50)
    print("✓ 所有检查通过，启动AI预测服务...")
    print("=" * 50)
    
    # 启动服务
    try:
        from app import app
        import time
        
        # 设置启动时间
        globals()['start_time'] = time.time()
        
        # 获取配置
        port = int(os.getenv('PORT', 5000))
        debug = os.getenv('DEBUG', 'False').lower() == 'true'
        
        print(f"服务地址: http://localhost:{port}")
        print(f"调试模式: {debug}")
        print(f"API文档: http://localhost:{port}/api/v1/health")
        print("\n按 Ctrl+C 停止服务")
        
        app.run(
            host='0.0.0.0',
            port=port,
            debug=debug,
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\n服务已停止")
    except Exception as e:
        print(f"启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
