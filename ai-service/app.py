"""
SFAP农品汇平台AI预测服务主应用
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
from datetime import datetime, timedelta
import logging
import traceback
import os
from dotenv import load_dotenv

# 导入自定义模块
from models.rnn_model import RNNPredictor
from models.arima_model import ARIMAPredictor
from utils.data_processor import DataProcessor
from utils.database import DatabaseManager
from utils.logger import setup_logger
from utils.crawl_data_provider import CrawlDataProvider
from utils.price_data_adapter import PriceDataAdapter

# 加载环境变量
load_dotenv()

# 创建Flask应用
app = Flask(__name__)
CORS(app)  # 启用跨域支持

# 设置日志
logger = setup_logger()

# 初始化组件
db_manager = DatabaseManager()
data_processor = DataProcessor()
rnn_predictor = RNNPredictor()
arima_predictor = ARIMAPredictor()
crawl_data_provider = CrawlDataProvider()
price_data_adapter = PriceDataAdapter()

# 全局响应格式
def create_response(code=200, message="success", data=None):
    """创建标准响应格式"""
    return jsonify({
        "code": code,
        "message": message,
        "data": data,
        "timestamp": datetime.now().isoformat()
    })

def handle_error(error, message="服务器内部错误"):
    """统一错误处理"""
    logger.error(f"{message}: {str(error)}")
    logger.error(traceback.format_exc())
    return create_response(500, message, None), 500

# ==================== RNN模型接口 ====================

@app.route('/api/v1/train_rnn', methods=['POST'])
def train_rnn():
    """训练RNN模型"""
    try:
        data = request.get_json()
        
        # 参数验证
        required_fields = ['category', 'region', 'history_data']
        for field in required_fields:
            if field not in data:
                return create_response(400, f"缺少必需参数: {field}"), 400
        
        logger.info(f"开始训练RNN模型: {data['category']} - {data['region']}")
        
        # 数据预处理
        processed_data = data_processor.preprocess_data(
            data['history_data'], 
            data['category'], 
            data['region']
        )
        
        # 训练模型
        model_params = data.get('model_params', {})
        training_result = rnn_predictor.train(
            processed_data, 
            data['category'], 
            data['region'],
            model_params
        )
        
        logger.info(f"RNN模型训练完成: {training_result['model_id']}")
        
        return create_response(200, "RNN模型训练成功", training_result)
        
    except Exception as e:
        return handle_error(e, "RNN模型训练失败")

@app.route('/api/v1/predict_rnn', methods=['POST'])
def predict_rnn():
    """RNN模型预测"""
    try:
        data = request.get_json()
        
        # 参数验证
        required_fields = ['category', 'region', 'forecast_days']
        for field in required_fields:
            if field not in data:
                return create_response(400, f"缺少必需参数: {field}"), 400

        logger.info(f"开始RNN预测: {data['category']} - {data['region']} - {data['forecast_days']}天")

        # 获取历史数据（优先使用爬取数据）
        if 'history_data' in data and data['history_data']:
            # 使用用户提供的历史数据
            history_data = data['history_data']
            logger.info("使用用户提供的历史数据")
        else:
            # 自动获取爬取的历史数据
            product_name = data.get('product_name', data['category'])
            region_name = data['region']
            days = data.get('history_days', 90)  # 默认获取90天数据

            history_data = crawl_data_provider.get_price_history(
                product_name=product_name,
                region_name=region_name,
                start_date=(datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d'),
                end_date=datetime.now().strftime('%Y-%m-%d'),
                limit=days
            )

            if not history_data:
                return create_response(400, f"未找到 {product_name} 在 {region_name} 的历史价格数据")

            logger.info(f"自动获取爬取数据: {len(history_data)}条记录")

        # 数据质量验证和清洗
        quality_report = price_data_adapter.validate_data_quality(history_data)
        if quality_report['quality_score'] < 60:
            logger.warning(f"数据质量较低: {quality_report['quality_score']}%")

        cleaned_data = price_data_adapter.clean_data(history_data)

        # 数据预处理
        processed_data = data_processor.preprocess_data(
            cleaned_data,
            data['category'],
            data['region']
        )
        
        # 执行预测（简化版RNN模型）
        prediction_result = rnn_predictor.predict(
            category=data['category'],
            region=data['region'],
            history_data=processed_data,
            forecast_days=data['forecast_days'],
            model_id=data.get('model_id')
        )
        
        logger.info(f"RNN预测完成: {len(prediction_result['predictions'])}个预测点")
        
        return create_response(200, "RNN预测成功", prediction_result)
        
    except Exception as e:
        return handle_error(e, "RNN预测失败")

# ==================== ARIMA模型接口 ====================

@app.route('/api/v1/train_arima', methods=['POST'])
def train_arima():
    """训练ARIMA模型"""
    try:
        data = request.get_json()
        
        # 参数验证
        required_fields = ['category', 'region', 'history_data']
        for field in required_fields:
            if field not in data:
                return create_response(400, f"缺少必需参数: {field}"), 400
        
        logger.info(f"开始训练ARIMA模型: {data['category']} - {data['region']}")
        
        # 数据预处理
        processed_data = data_processor.preprocess_data(
            data['history_data'], 
            data['category'], 
            data['region']
        )
        
        # 训练模型
        model_params = data.get('model_params', {})
        training_result = arima_predictor.train(
            processed_data, 
            data['category'], 
            data['region'],
            model_params
        )
        
        logger.info(f"ARIMA模型训练完成: {training_result['model_id']}")
        
        return create_response(200, "ARIMA模型训练成功", training_result)
        
    except Exception as e:
        return handle_error(e, "ARIMA模型训练失败")

@app.route('/api/v1/predict_arima', methods=['POST'])
def predict_arima():
    """ARIMA模型预测"""
    try:
        data = request.get_json()

        # 参数验证
        required_fields = ['category', 'region', 'forecast_days']
        for field in required_fields:
            if field not in data:
                return create_response(400, f"缺少必需参数: {field}"), 400

        logger.info(f"开始ARIMA预测: {data['category']} - {data['region']} - {data['forecast_days']}天")

        # 获取历史数据（优先使用爬取数据）
        if 'history_data' in data and data['history_data']:
            # 使用用户提供的历史数据
            history_data = data['history_data']
            logger.info("使用用户提供的历史数据")
        else:
            # 自动获取爬取的历史数据
            product_name = data.get('product_name', data['category'])
            region_name = data['region']
            days = data.get('history_days', 90)  # 默认获取90天数据

            history_data = crawl_data_provider.get_price_history(
                product_name=product_name,
                region_name=region_name,
                start_date=(datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d'),
                end_date=datetime.now().strftime('%Y-%m-%d'),
                limit=days
            )

            if not history_data:
                return create_response(400, f"未找到 {product_name} 在 {region_name} 的历史价格数据")

            logger.info(f"自动获取爬取数据: {len(history_data)}条记录")

        # 数据质量验证和清洗
        quality_report = price_data_adapter.validate_data_quality(history_data)
        if quality_report['quality_score'] < 60:
            logger.warning(f"数据质量较低: {quality_report['quality_score']}%")

        cleaned_data = price_data_adapter.clean_data(history_data)

        # 数据预处理
        processed_data = data_processor.preprocess_data(
            cleaned_data,
            data['category'],
            data['region']
        )

        # 执行预测
        prediction_result = arima_predictor.predict(
            processed_data,
            data['category'],
            data['region'],
            data['forecast_days'],
            data.get('confidence_level', 0.95),
            data.get('model_id')
        )

        logger.info(f"ARIMA预测完成: {len(prediction_result['predictions'])}个预测点")

        return create_response(200, "ARIMA预测成功", prediction_result)

    except Exception as e:
        return handle_error(e, "ARIMA预测失败")

# ==================== 模型管理接口 ====================

@app.route('/api/v1/models', methods=['GET'])
def get_models():
    """获取模型列表"""
    try:
        category = request.args.get('category')
        region = request.args.get('region')
        model_type = request.args.get('model_type')
        page = int(request.args.get('page', 1))
        size = int(request.args.get('size', 10))
        
        models = db_manager.get_models(category, region, model_type, page, size)
        
        return create_response(200, "获取模型列表成功", models)
        
    except Exception as e:
        return handle_error(e, "获取模型列表失败")

@app.route('/api/v1/models/<model_id>/evaluate', methods=['POST'])
def evaluate_model(model_id):
    """评估模型性能"""
    try:
        data = request.get_json()
        test_data = data.get('test_data', [])
        
        # 根据模型类型选择评估器
        model_info = db_manager.get_model_by_id(model_id)
        if not model_info:
            return create_response(404, "模型不存在"), 404
        
        if model_info['model_type'] == 'RNN':
            evaluation_result = rnn_predictor.evaluate(model_id, test_data)
        else:
            evaluation_result = arima_predictor.evaluate(model_id, test_data)
        
        return create_response(200, "模型评估完成", evaluation_result)
        
    except Exception as e:
        return handle_error(e, "模型评估失败")

# ==================== 数据管理接口 ====================

@app.route('/api/v1/data/upload', methods=['POST'])
def upload_data():
    """批量上传历史价格数据"""
    try:
        data = request.get_json()

        # 参数验证
        required_fields = ['category', 'region', 'data']
        for field in required_fields:
            if field not in data:
                return create_response(400, f"缺少必需参数: {field}"), 400

        # 数据验证和处理
        processed_data = data_processor.preprocess_data(
            data['data'],
            data['category'],
            data['region']
        )

        # 保存到数据库（这里可以实现具体的保存逻辑）
        upload_result = {
            'total_records': len(data['data']),
            'valid_records': len(processed_data),
            'category': data['category'],
            'region': data['region'],
            'data_source': data.get('data_source', 'manual_upload')
        }

        logger.info(f"数据上传成功: {data['category']} - {data['region']}, {len(processed_data)}条记录")

        return create_response(200, "数据上传成功", upload_result)

    except Exception as e:
        return handle_error(e, "数据上传失败")

@app.route('/api/v1/data/validate', methods=['POST'])
def validate_data():
    """检查数据质量"""
    try:
        data = request.get_json()

        if 'data' not in data:
            return create_response(400, "缺少数据参数"), 400

        # 数据质量检查
        raw_data = data['data']
        total_records = len(raw_data)

        # 检查缺失值
        missing_issues = []
        outlier_issues = []
        valid_records = 0

        for i, record in enumerate(raw_data):
            is_valid = True

            # 检查必要字段
            if 'price' not in record or record['price'] is None:
                missing_issues.append(f"记录{i+1}: 缺少价格字段")
                is_valid = False
            elif record['price'] <= 0:
                outlier_issues.append(f"记录{i+1}: 价格异常值 {record['price']}")
                is_valid = False

            if 'date' not in record or record['date'] is None:
                missing_issues.append(f"记录{i+1}: 缺少日期字段")
                is_valid = False

            if is_valid:
                valid_records += 1

        # 计算质量评分
        quality_score = (valid_records / total_records) * 100 if total_records > 0 else 0

        issues = []
        if missing_issues:
            issues.append({
                'type': 'missing_value',
                'count': len(missing_issues),
                'fields': ['price', 'date'],
                'details': missing_issues[:5]  # 只返回前5个
            })

        if outlier_issues:
            issues.append({
                'type': 'outlier',
                'count': len(outlier_issues),
                'description': '价格异常值',
                'details': outlier_issues[:5]  # 只返回前5个
            })

        validation_result = {
            'total_records': total_records,
            'valid_records': valid_records,
            'issues': issues,
            'quality_score': round(quality_score, 1)
        }

        return create_response(200, "数据质量检查完成", validation_result)

    except Exception as e:
        return handle_error(e, "数据质量检查失败")

# ==================== 系统状态接口 ====================

@app.route('/api/v1/health', methods=['GET'])
def health_check():
    """健康检查"""
    try:
        import psutil
        import time

        # 计算服务运行时间（简化版本）
        uptime = int(time.time() - start_time) if 'start_time' in globals() else 3600

        health_data = {
            "status": "healthy",
            "version": "1.0.0",
            "uptime": uptime,
            "models_loaded": db_manager.count_active_models(),
            "memory_usage": f"{psutil.virtual_memory().percent}%",
            "cpu_usage": f"{psutil.cpu_percent()}%"
        }

        return create_response(200, "服务正常", health_data)

    except Exception as e:
        return handle_error(e, "健康检查失败")

# ==================== 爬取数据接口 ====================

@app.route('/api/v1/crawl_data/products', methods=['GET'])
def get_crawl_products():
    """获取爬取的产品列表"""
    try:
        keyword = request.args.get('keyword', '')
        category = request.args.get('category', '')
        limit = int(request.args.get('limit', 50))

        products = crawl_data_provider.search_products(
            keyword=keyword,
            category=category,
            limit=limit
        )

        return create_response(data={
            "products": products,
            "total": len(products)
        })

    except Exception as e:
        logger.error(f"获取产品列表失败: {e}")
        return create_response(500, f"获取产品列表失败: {str(e)}")


@app.route('/api/v1/crawl_data/categories', methods=['GET'])
def get_crawl_categories():
    """获取爬取的分类列表"""
    try:
        categories = crawl_data_provider.get_categories()

        return create_response(data={
            "categories": categories,
            "total": len(categories)
        })

    except Exception as e:
        logger.error(f"获取分类列表失败: {e}")
        return create_response(500, f"获取分类列表失败: {str(e)}")


@app.route('/api/v1/crawl_data/regions', methods=['GET'])
def get_crawl_regions():
    """获取爬取的地区列表"""
    try:
        level = request.args.get('level', type=int)
        regions = crawl_data_provider.get_regions(level=level)

        return create_response(data={
            "regions": regions,
            "total": len(regions)
        })

    except Exception as e:
        logger.error(f"获取地区列表失败: {e}")
        return create_response(500, f"获取地区列表失败: {str(e)}")


@app.route('/api/v1/crawl_data/prices/history', methods=['GET'])
def get_crawl_price_history():
    """获取爬取的历史价格数据"""
    try:
        product_name = request.args.get('product_name', '')
        region_name = request.args.get('region_name', '')
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')
        limit = int(request.args.get('limit', 1000))

        if not product_name:
            return create_response(400, "产品名称不能为空")

        # 获取历史数据
        history_data = crawl_data_provider.get_price_history(
            product_name=product_name,
            region_name=region_name,
            start_date=start_date,
            end_date=end_date,
            limit=limit
        )

        # 数据质量验证
        quality_report = price_data_adapter.validate_data_quality(history_data)

        return create_response(data={
            "history_data": history_data,
            "total": len(history_data),
            "quality_report": quality_report
        })

    except Exception as e:
        logger.error(f"获取历史价格数据失败: {e}")
        return create_response(500, f"获取历史价格数据失败: {str(e)}")


@app.route('/api/v1/crawl_data/prices/latest', methods=['GET'])
def get_crawl_latest_prices():
    """获取爬取的最新价格数据"""
    try:
        category = request.args.get('category', '')
        region = request.args.get('region', '')
        limit = int(request.args.get('limit', 100))

        latest_prices = crawl_data_provider.get_latest_prices(
            category=category,
            region=region,
            limit=limit
        )

        return create_response(data={
            "latest_prices": latest_prices,
            "total": len(latest_prices)
        })

    except Exception as e:
        logger.error(f"获取最新价格数据失败: {e}")
        return create_response(500, f"获取最新价格数据失败: {str(e)}")

# ==================== 错误处理 ====================

@app.errorhandler(404)
def not_found(error):
    return create_response(404, "接口不存在"), 404

@app.errorhandler(405)
def method_not_allowed(error):
    return create_response(405, "请求方法不允许"), 405

@app.errorhandler(500)
def internal_error(error):
    return create_response(500, "服务器内部错误"), 500

# ==================== 应用启动 ====================

if __name__ == '__main__':
    import time

    # 记录启动时间
    start_time = time.time()

    # 初始化数据库
    try:
        db_manager.init_database()
        logger.info("数据库初始化完成")
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")

    # 启动应用
    port = int(os.getenv('PORT', 5000))
    debug = os.getenv('DEBUG', 'False').lower() == 'true'

    logger.info(f"启动AI预测服务，端口: {port}, 调试模式: {debug}")

    app.run(
        host='0.0.0.0',
        port=port,
        debug=debug,
        threaded=True
    )
