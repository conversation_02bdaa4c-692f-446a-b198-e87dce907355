#!/bin/bash

# 智慧农业平台生产环境目录结构创建脚本
# 服务器IP: **************
# 主存储目录: /www/wwwroot/test.com/backend/uploads/

echo "=========================================="
echo "智慧农业平台生产环境目录结构创建"
echo "=========================================="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 基础配置
BASE_DIR="/www/wwwroot/test.com/backend/uploads"
IMAGES_DIR="$BASE_DIR/images"

echo -e "${BLUE}📁 创建目录结构${NC}"
echo "=================================="

# 创建主目录
echo -n "创建主目录 $BASE_DIR ... "
if mkdir -p "$BASE_DIR"; then
    echo -e "${GREEN}✅ 成功${NC}"
else
    echo -e "${RED}❌ 失败${NC}"
    exit 1
fi

# 创建images主目录
echo -n "创建images目录 $IMAGES_DIR ... "
if mkdir -p "$IMAGES_DIR"; then
    echo -e "${GREEN}✅ 成功${NC}"
else
    echo -e "${RED}❌ 失败${NC}"
    exit 1
fi

# 创建各个子目录
directories=(
    "images/avatars"
    "images/products" 
    "images/news"
    "images/encyclopedia"
    "images/banners"
    "images/categories"
    "images/qrcodes"
    "images/certificates"
    "documents"
    "videos"
    "temp"
)

echo ""
echo -e "${BLUE}📂 创建子目录${NC}"
echo "=================================="

for dir in "${directories[@]}"; do
    full_path="$BASE_DIR/$dir"
    echo -n "创建目录 $full_path ... "
    
    if mkdir -p "$full_path"; then
        echo -e "${GREEN}✅ 成功${NC}"
    else
        echo -e "${RED}❌ 失败${NC}"
    fi
done

echo ""
echo -e "${BLUE}🔐 设置目录权限${NC}"
echo "=================================="

# 设置目录权限
echo -n "设置目录权限 755 ... "
if find "$BASE_DIR" -type d -exec chmod 755 {} \;; then
    echo -e "${GREEN}✅ 成功${NC}"
else
    echo -e "${RED}❌ 失败${NC}"
fi

# 设置文件权限（如果有文件的话）
echo -n "设置文件权限 644 ... "
if find "$BASE_DIR" -type f -exec chmod 644 {} \; 2>/dev/null; then
    echo -e "${GREEN}✅ 成功${NC}"
else
    echo -e "${YELLOW}⚠️ 无文件或权限设置失败${NC}"
fi

# 设置所有者为www用户（如果存在）
echo -n "设置所有者为www用户 ... "
if id "www" &>/dev/null; then
    if chown -R www:www "$BASE_DIR"; then
        echo -e "${GREEN}✅ 成功${NC}"
    else
        echo -e "${RED}❌ 失败${NC}"
    fi
else
    echo -e "${YELLOW}⚠️ www用户不存在，跳过${NC}"
fi

echo ""
echo -e "${BLUE}📋 目录结构验证${NC}"
echo "=================================="

# 验证目录结构
echo "目录结构："
tree "$BASE_DIR" 2>/dev/null || ls -la "$BASE_DIR"

echo ""
echo -e "${BLUE}🔍 权限检查${NC}"
echo "=================================="

# 检查权限
echo "权限信息："
ls -la "$BASE_DIR"

echo ""
echo -e "${BLUE}💾 磁盘空间检查${NC}"
echo "=================================="

# 检查磁盘空间
echo "磁盘空间："
df -h "$BASE_DIR"

echo ""
echo -e "${BLUE}🌐 访问URL格式${NC}"
echo "=================================="

echo "各类图片的访问URL格式："
echo "• 用户头像: http://**************:8081/uploads/images/avatars/filename.jpg"
echo "• 商品图片: http://**************:8081/uploads/images/products/filename.jpg"
echo "• 新闻图片: http://**************:8081/uploads/images/news/filename.jpg"
echo "• 百科图片: http://**************:8081/uploads/images/encyclopedia/filename.jpg"
echo "• 轮播图片: http://**************:8081/uploads/images/banners/filename.jpg"
echo "• 分类图片: http://**************:8081/uploads/images/categories/filename.jpg"
echo "• 二维码图片: http://**************:8081/uploads/images/qrcodes/filename.jpg"
echo "• 证书图片: http://**************:8081/uploads/images/certificates/filename.jpg"

echo ""
echo -e "${BLUE}📝 后续配置建议${NC}"
echo "=================================="

echo "1. 确保Nginx配置正确映射静态资源"
echo "2. 配置适当的缓存策略"
echo "3. 设置文件上传大小限制"
echo "4. 配置定期清理临时文件"
echo "5. 设置备份策略"

echo ""
echo -e "${GREEN}🎉 目录结构创建完成！${NC}"
echo ""
echo "下一步："
echo "1. 重启后端应用以应用新配置"
echo "2. 测试文件上传功能"
echo "3. 验证图片访问是否正常"

exit 0
