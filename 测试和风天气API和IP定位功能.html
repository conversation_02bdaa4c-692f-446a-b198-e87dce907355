<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>和风天气API和IP定位功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #333;
            margin-top: 0;
        }
        button {
            background-color: #409EFF;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #66b1ff;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            background-color: #f9f9f9;
            border: 1px solid #e4e7ed;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            border-color: #67c23a;
            background-color: #f0f9ff;
        }
        .error {
            border-color: #f56c6c;
            background-color: #fef0f0;
        }
        .loading {
            color: #909399;
        }
        input[type="text"] {
            padding: 8px 12px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            margin: 5px;
            width: 200px;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            margin-left: 10px;
        }
        .status.success {
            background-color: #67c23a;
            color: white;
        }
        .status.error {
            background-color: #f56c6c;
            color: white;
        }
        .status.loading {
            background-color: #409EFF;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌤️ SFAP农品汇平台 - 和风天气API和IP定位功能测试</h1>
        <p>本页面用于测试和风天气API和IP定位服务的功能是否正常。</p>
    </div>

    <div class="container">
        <div class="test-section">
            <h3>1. IP定位服务测试 <span id="ip-status" class="status">未测试</span></h3>
            <p>测试高德地图IP定位API是否能正确获取当前位置信息</p>
            <button onclick="testIPLocation()">测试IP定位</button>
            <div id="ip-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>2. 和风天气API连通性测试 <span id="weather-status" class="status">未测试</span></h3>
            <p>测试和风天气API是否可以正常访问</p>
            <button onclick="testWeatherAPI()">测试API连通性</button>
            <div id="weather-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>3. 城市搜索功能测试 <span id="search-status" class="status">未测试</span></h3>
            <p>测试和风天气城市搜索API</p>
            <input type="text" id="search-keyword" placeholder="输入城市名称，如：北京" value="北京">
            <button onclick="testCitySearch()">搜索城市</button>
            <div id="search-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>4. 实时天气数据测试 <span id="current-status" class="status">未测试</span></h3>
            <p>测试获取实时天气数据</p>
            <input type="text" id="current-city" placeholder="输入城市名称或ID" value="101010100">
            <button onclick="testCurrentWeather()">获取实时天气</button>
            <div id="current-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>5. 天气预报数据测试 <span id="forecast-status" class="status">未测试</span></h3>
            <p>测试获取天气预报数据</p>
            <input type="text" id="forecast-city" placeholder="输入城市名称或ID" value="101010100">
            <button onclick="testWeatherForecast()">获取7天预报</button>
            <div id="forecast-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>6. 综合功能测试 <span id="integration-status" class="status">未测试</span></h3>
            <p>测试完整的天气服务流程：IP定位 → 城市搜索 → 获取天气</p>
            <button onclick="testIntegration()">运行综合测试</button>
            <div id="integration-result" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        // 配置信息
        const CONFIG = {
            QWEATHER_API_KEY: '57927154f3aa403c94dbf487871c9cc5',
            QWEATHER_HOST: 'p85xmcften.re.qweatherapi.com',
            AMAP_API_KEY: '0113a13c88697dcea6a445584d535837'
        };

        // 工具函数
        function updateStatus(elementId, status, text) {
            const element = document.getElementById(elementId);
            element.className = `status ${status}`;
            element.textContent = text || status;
        }

        function showResult(elementId, content, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${isSuccess ? 'success' : 'error'}`;
            element.textContent = typeof content === 'object' ? JSON.stringify(content, null, 2) : content;
        }

        function showLoading(elementId) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = 'result loading';
            element.textContent = '正在测试中...';
        }

        // 1. IP定位服务测试
        async function testIPLocation() {
            updateStatus('ip-status', 'loading', '测试中');
            showLoading('ip-result');

            try {
                const response = await fetch(`https://restapi.amap.com/v3/ip?key=${CONFIG.AMAP_API_KEY}`);
                const data = await response.json();

                if (data.status === '1') {
                    updateStatus('ip-status', 'success', '成功');
                    showResult('ip-result', {
                        status: '✅ IP定位成功',
                        province: data.province,
                        city: data.city,
                        adcode: data.adcode,
                        rectangle: data.rectangle,
                        message: 'IP定位服务工作正常'
                    });
                } else {
                    throw new Error(`API返回错误: ${data.info}`);
                }
            } catch (error) {
                updateStatus('ip-status', 'error', '失败');
                showResult('ip-result', {
                    status: '❌ IP定位失败',
                    error: error.message,
                    suggestion: '请检查网络连接或API密钥是否正确'
                }, false);
            }
        }

        // 2. 和风天气API连通性测试
        async function testWeatherAPI() {
            updateStatus('weather-status', 'loading', '测试中');
            showLoading('weather-result');

            try {
                // 测试API连通性，使用北京的城市ID
                const response = await fetch(`https://${CONFIG.QWEATHER_HOST}/v7/weather/now?location=101010100&key=${CONFIG.QWEATHER_API_KEY}`);
                const data = await response.json();

                if (data.code === '200') {
                    updateStatus('weather-status', 'success', '成功');
                    showResult('weather-result', {
                        status: '✅ 和风天气API连通正常',
                        api_host: CONFIG.QWEATHER_HOST,
                        response_code: data.code,
                        update_time: data.updateTime,
                        message: 'API密钥有效，服务正常'
                    });
                } else {
                    throw new Error(`API返回错误代码: ${data.code}`);
                }
            } catch (error) {
                updateStatus('weather-status', 'error', '失败');
                showResult('weather-result', {
                    status: '❌ 和风天气API连接失败',
                    error: error.message,
                    api_host: CONFIG.QWEATHER_HOST,
                    suggestion: '请检查API密钥是否正确或网络是否正常'
                }, false);
            }
        }

        // 3. 城市搜索功能测试
        async function testCitySearch() {
            const keyword = document.getElementById('search-keyword').value.trim();
            if (!keyword) {
                alert('请输入城市名称');
                return;
            }

            updateStatus('search-status', 'loading', '测试中');
            showLoading('search-result');

            try {
                const response = await fetch(`https://${CONFIG.QWEATHER_HOST}/v2/city/lookup?location=${encodeURIComponent(keyword)}&key=${CONFIG.QWEATHER_API_KEY}`);
                const data = await response.json();

                if (data.code === '200') {
                    updateStatus('search-status', 'success', '成功');
                    showResult('search-result', {
                        status: '✅ 城市搜索成功',
                        keyword: keyword,
                        found_cities: data.location.length,
                        cities: data.location.map(city => ({
                            name: city.name,
                            id: city.id,
                            adm1: city.adm1,
                            adm2: city.adm2
                        }))
                    });
                } else {
                    throw new Error(`搜索失败，错误代码: ${data.code}`);
                }
            } catch (error) {
                updateStatus('search-status', 'error', '失败');
                showResult('search-result', {
                    status: '❌ 城市搜索失败',
                    keyword: keyword,
                    error: error.message
                }, false);
            }
        }

        // 4. 实时天气数据测试
        async function testCurrentWeather() {
            const cityId = document.getElementById('current-city').value.trim();
            if (!cityId) {
                alert('请输入城市ID');
                return;
            }

            updateStatus('current-status', 'loading', '测试中');
            showLoading('current-result');

            try {
                const response = await fetch(`https://${CONFIG.QWEATHER_HOST}/v7/weather/now?location=${cityId}&key=${CONFIG.QWEATHER_API_KEY}`);
                const data = await response.json();

                if (data.code === '200') {
                    updateStatus('current-status', 'success', '成功');
                    showResult('current-result', {
                        status: '✅ 实时天气获取成功',
                        city_id: cityId,
                        update_time: data.updateTime,
                        weather: {
                            temperature: data.now.temp + '°C',
                            feels_like: data.now.feelsLike + '°C',
                            condition: data.now.text,
                            humidity: data.now.humidity + '%',
                            wind: `${data.now.windDir} ${data.now.windScale}级`,
                            pressure: data.now.pressure + 'hPa'
                        }
                    });
                } else {
                    throw new Error(`获取天气失败，错误代码: ${data.code}`);
                }
            } catch (error) {
                updateStatus('current-status', 'error', '失败');
                showResult('current-result', {
                    status: '❌ 实时天气获取失败',
                    city_id: cityId,
                    error: error.message
                }, false);
            }
        }

        // 5. 天气预报数据测试
        async function testWeatherForecast() {
            const cityId = document.getElementById('forecast-city').value.trim();
            if (!cityId) {
                alert('请输入城市ID');
                return;
            }

            updateStatus('forecast-status', 'loading', '测试中');
            showLoading('forecast-result');

            try {
                const response = await fetch(`https://${CONFIG.QWEATHER_HOST}/v7/weather/7d?location=${cityId}&key=${CONFIG.QWEATHER_API_KEY}`);
                const data = await response.json();

                if (data.code === '200') {
                    updateStatus('forecast-status', 'success', '成功');
                    showResult('forecast-result', {
                        status: '✅ 天气预报获取成功',
                        city_id: cityId,
                        update_time: data.updateTime,
                        forecast_days: data.daily.length,
                        forecast: data.daily.slice(0, 3).map(day => ({
                            date: day.fxDate,
                            condition: `${day.textDay}/${day.textNight}`,
                            temperature: `${day.tempMin}°C ~ ${day.tempMax}°C`,
                            humidity: day.humidity + '%'
                        }))
                    });
                } else {
                    throw new Error(`获取预报失败，错误代码: ${data.code}`);
                }
            } catch (error) {
                updateStatus('forecast-status', 'error', '失败');
                showResult('forecast-result', {
                    status: '❌ 天气预报获取失败',
                    city_id: cityId,
                    error: error.message
                }, false);
            }
        }

        // 6. 综合功能测试
        async function testIntegration() {
            updateStatus('integration-status', 'loading', '测试中');
            showLoading('integration-result');

            const results = [];

            try {
                // 步骤1: IP定位
                results.push('🔍 步骤1: 获取IP定位...');
                const ipResponse = await fetch(`https://restapi.amap.com/v3/ip?key=${CONFIG.AMAP_API_KEY}`);
                const ipData = await ipResponse.json();
                
                if (ipData.status !== '1') {
                    throw new Error('IP定位失败');
                }
                results.push(`✅ IP定位成功: ${ipData.province} ${ipData.city}`);

                // 步骤2: 搜索城市
                results.push('\n🔍 步骤2: 搜索城市信息...');
                const searchResponse = await fetch(`https://${CONFIG.QWEATHER_HOST}/v2/city/lookup?location=${encodeURIComponent(ipData.city)}&key=${CONFIG.QWEATHER_API_KEY}`);
                const searchData = await searchResponse.json();
                
                if (searchData.code !== '200' || !searchData.location.length) {
                    throw new Error('城市搜索失败');
                }
                const city = searchData.location[0];
                results.push(`✅ 城市搜索成功: ${city.name} (ID: ${city.id})`);

                // 步骤3: 获取天气
                results.push('\n🔍 步骤3: 获取天气信息...');
                const weatherResponse = await fetch(`https://${CONFIG.QWEATHER_HOST}/v7/weather/now?location=${city.id}&key=${CONFIG.QWEATHER_API_KEY}`);
                const weatherData = await weatherResponse.json();
                
                if (weatherData.code !== '200') {
                    throw new Error('天气数据获取失败');
                }
                results.push(`✅ 天气获取成功: ${weatherData.now.text} ${weatherData.now.temp}°C`);

                results.push('\n🎉 综合测试完成！所有功能正常工作。');
                updateStatus('integration-status', 'success', '成功');
                showResult('integration-result', results.join('\n'));

            } catch (error) {
                results.push(`\n❌ 测试失败: ${error.message}`);
                updateStatus('integration-status', 'error', '失败');
                showResult('integration-result', results.join('\n'), false);
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('和风天气API和IP定位功能测试页面已加载');
            console.log('配置信息:', CONFIG);
        });
    </script>
</body>
</html>
