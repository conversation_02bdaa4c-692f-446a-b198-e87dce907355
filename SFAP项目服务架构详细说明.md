# SFAP农品汇平台服务架构详细说明

## 📋 项目概览

SFAP农品汇平台是一个综合性的农业电商和数据分析平台，包含5个主要服务模块，每个模块都有独立的功能和部署要求。

## 🏗️ 服务架构图

```
┌─────────────────────────────────────────────────────────────────┐
│                    SFAP农品汇平台架构                            │
├─────────────────────────────────────────────────────────────────┤
│                        用户访问层                                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   Web浏览器     │  │   移动端APP     │  │   管理后台      │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
├─────────────────────────────────────────────────────────────────┤
│                        网关代理层                                │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                 Nginx (80/443)                             │ │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │ │
│  │  │ 静态资源    │ │ API代理     │ │ 负载均衡    │          │ │
│  │  └─────────────┘ └─────────────┘ └─────────────┘          │ │
│  └─────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                        应用服务层                                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │   前端服务  │ │   后端服务  │ │  AI预测服务 │ │ 新闻爬取服务│ │
│  │  Vue.js     │ │Spring Boot  │ │   Flask     │ │   Flask     │ │
│  │  (8200)     │ │   (8081)    │ │   (5000)    │ │   (5001)    │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                价格爬取服务 (命令行工具)                     │ │
│  │              定时任务 + 预留API端口(8002)                   │ │
│  └─────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                        数据存储层                                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │   MySQL     │ │    Redis    │ │  文件存储   │ │   日志存储  │ │
│  │   (3306)    │ │   (6379)    │ │  /uploads   │ │   /logs     │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## 🔧 服务详细说明

### 1. 前端服务 (Vue.js - 端口8200)

**技术栈：**
- Vue 2.x + Element UI
- SCSS样式预处理
- Webpack构建工具
- Axios HTTP客户端

**主要功能：**
- 用户界面展示
- 商品浏览和购买
- 用户管理和认证
- 数据可视化展示
- 响应式设计支持

**部署方式：**
- 开发环境：Vue DevServer (8200)
- 生产环境：Nginx静态文件服务

**关键配置文件：**
- `vue.config.js` - 构建配置
- `src/config/ai-service.js` - AI服务配置
- `.env.production` - 生产环境变量

### 2. 后端服务 (Spring Boot - 端口8081)

**技术栈：**
- Spring Boot 2.x
- MyBatis Plus ORM
- Spring Security安全框架
- Jackson JSON处理
- Maven构建工具

**主要功能：**
- RESTful API接口
- 用户认证和授权
- 商品管理CRUD
- 文件上传处理
- 数据库操作
- 跨域配置管理

**部署方式：**
- JAR包部署
- JVM参数优化
- 多环境配置支持

**关键配置文件：**
- `application.yml` - 主配置文件
- `application-prod.yml` - 生产环境配置
- `pom.xml` - Maven依赖配置

### 3. AI预测服务 (Flask - 端口5000)

**技术栈：**
- Python 3.8+
- Flask Web框架
- TensorFlow/PyTorch机器学习
- NumPy/Pandas数据处理
- SQLAlchemy数据库ORM

**主要功能：**
- 农产品价格预测
- RNN时间序列模型
- ARIMA统计模型
- 数据预处理和特征工程
- 模型训练和推理

**部署方式：**
- Python虚拟环境
- Gunicorn WSGI服务器
- 模型文件管理

**关键配置文件：**
- `app.py` - Flask应用主文件
- `requirements.txt` - Python依赖
- `.env` - 环境变量配置

### 4. 新闻爬取服务 (Flask - 端口5001)

**技术栈：**
- Python 3.8+
- Flask Web框架
- Requests HTTP库
- BeautifulSoup HTML解析
- APScheduler定时任务

**主要功能：**
- 农业新闻数据爬取
- 新闻内容解析和清洗
- 定时任务调度
- 数据存储和管理
- 爬取状态监控

**部署方式：**
- Python虚拟环境
- 后台进程运行
- 日志文件管理

**关键配置文件：**
- `app.py` - Flask应用主文件
- `db_manager.py` - 数据库管理
- `requirements.txt` - Python依赖

### 5. 价格爬取服务 (命令行工具 - 预留端口8002)

**技术栈：**
- Python 3.8+
- Scrapy爬虫框架
- Requests HTTP库
- PyMySQL数据库连接
- YAML配置管理

**主要功能：**
- 惠农网价格数据爬取
- 农产品价格监控
- 数据清洗和验证
- 定时任务执行
- 数据质量检查

**部署方式：**
- 命令行工具
- Crontab定时执行
- 日志文件记录

**关键配置文件：**
- `config/settings.yaml` - 主配置文件
- `.env` - 环境变量
- `main.py` - 爬虫入口文件

## 🔗 服务间通信

### API调用关系

```
前端服务 (8200)
    ↓ HTTP/HTTPS
后端服务 (8081)
    ↓ HTTP
AI预测服务 (5000)
    ↓ HTTP
新闻爬取服务 (5001)

价格爬取服务 (命令行)
    ↓ 直接数据库连接
MySQL数据库 (3306)
```

### 数据流向

1. **用户请求流程：**
   ```
   用户浏览器 → Nginx → 前端服务 → 后端API → 数据库
   ```

2. **AI预测流程：**
   ```
   前端请求 → 后端API → AI服务 → 模型推理 → 返回结果
   ```

3. **数据爬取流程：**
   ```
   定时任务 → 爬虫服务 → 外部网站 → 数据清洗 → 数据库存储
   ```

## 📊 端口分配总表

| 服务名称 | 端口 | 协议 | 状态 | 说明 |
|----------|------|------|------|------|
| Nginx主服务 | 80 | HTTP | 生产 | Web服务器 |
| Nginx SSL | 443 | HTTPS | 生产 | SSL加密 |
| 前端服务 | 8200 | HTTP | 开发/生产 | Vue.js应用 |
| 后端服务 | 8081 | HTTP | 开发/生产 | Spring Boot API |
| AI预测服务 | 5000 | HTTP | 开发/生产 | Flask AI服务 |
| 新闻爬取服务 | 5001 | HTTP | 开发/生产 | Flask爬虫服务 |
| 价格爬取服务 | 8002 | HTTP | 预留 | 未来API扩展 |
| MySQL数据库 | 3306 | TCP | 内网 | 数据存储 |
| Redis缓存 | 6379 | TCP | 内网 | 缓存服务 |

## 🔒 安全配置

### 网络安全
- 防火墙端口控制
- 内网服务隔离
- HTTPS加密传输
- API访问限制

### 数据安全
- 数据库用户权限控制
- 敏感信息加密存储
- 定期数据备份
- 访问日志记录

### 应用安全
- JWT令牌认证
- CORS跨域控制
- SQL注入防护
- XSS攻击防护

## 📈 性能优化

### 前端优化
- 代码分割和懒加载
- 静态资源CDN
- 图片压缩和优化
- 浏览器缓存策略

### 后端优化
- 数据库连接池
- Redis缓存策略
- API响应压缩
- 异步处理机制

### 数据库优化
- 索引优化
- 查询语句优化
- 分库分表策略
- 读写分离

## 🔍 监控和运维

### 服务监控
- 进程状态监控
- 端口连通性检查
- 资源使用率监控
- 错误日志监控

### 性能监控
- 响应时间监控
- 吞吐量统计
- 数据库性能监控
- 缓存命中率监控

### 告警机制
- 服务异常告警
- 资源不足告警
- 数据异常告警
- 安全事件告警

## 🚀 扩展规划

### 水平扩展
- 负载均衡配置
- 服务集群部署
- 数据库主从复制
- 缓存集群配置

### 功能扩展
- 微服务架构改造
- 容器化部署
- 自动化CI/CD
- 云原生架构

## 📝 维护指南

### 日常维护
- 日志文件清理
- 数据库备份
- 系统更新
- 安全补丁

### 故障处理
- 服务重启流程
- 数据恢复流程
- 紧急回滚流程
- 故障排查手册

---

**文档版本**: v1.0  
**创建日期**: 2025-01-25  
**适用版本**: SFAP农品汇平台 v4.0  
**维护人员**: 开发团队
