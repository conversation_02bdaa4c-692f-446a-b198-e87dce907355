-- 修复18号用户的user_type字段
-- 确保admin_new用户的角色信息完整

UPDATE user 
SET user_type = 'admin', 
    updated_at = NOW()
WHERE id = 18 AND username = 'admin_new';

-- 验证更新结果
SELECT id, username, role, user_type, status, created_at, updated_at 
FROM user 
WHERE id = 18;

-- 同时检查其他可能存在问题的管理员用户
SELECT id, username, role, user_type, status 
FROM user 
WHERE (role = 'admin' OR role = 'ADMIN') 
  AND (user_type IS NULL OR user_type != 'admin');
