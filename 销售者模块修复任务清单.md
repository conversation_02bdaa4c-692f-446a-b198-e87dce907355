# 销售者模块修复任务清单

## 问题分析

### 1. 前端问题
- ✅ 表单验证错误：async-validator报错（idNumber、phone、description字段required验证）
- ✅ API接口404错误：/api/seller/certificate/upload 接口不存在
- ✅ 缺少admin.js API文件
- ⚠️ SellerApplicationManagement组件API导入混乱

### 2. 后端问题
- ⚠️ 证书上传接口路径不统一
- ⚠️ API响应格式不一致
- ⚠️ 权限验证可能存在问题

## 修复进度

### 已完成 ✅
1. **创建admin.js API文件** - 包含管理员相关的API接口
2. **修复SellerApplication组件表单验证** - 将验证函数移到methods中，修复this指向问题
3. **修复证书上传API路径** - 统一使用/api/seller/certificate路径
4. **更新SellerApplicationManagement组件API导入** - 引入admin.js中的API函数

### 待完成 ⚠️

#### 前端任务
1. **统一API调用方式**
   - 检查所有组件中的API调用
   - 确保使用正确的API函数
   - 统一错误处理方式

2. **完善SellerApplicationForm组件**
   - 检查申请状态显示逻辑
   - 优化用户体验
   - 添加加载状态

3. **优化SellerApplicationButton组件**
   - 检查状态判断逻辑
   - 优化按钮显示

4. **完善管理员管理页面**
   - 检查分页逻辑
   - 优化审核流程
   - 添加批量操作

#### 后端任务
1. **统一API响应格式**
   - 检查所有Controller的响应格式
   - 确保前后端数据格式一致

2. **完善证书上传功能**
   - 检查文件上传逻辑
   - 添加文件类型和大小验证
   - 优化文件存储路径

3. **优化权限验证**
   - 检查所有接口的权限注解
   - 确保安全性

4. **数据库优化**
   - 检查表结构
   - 优化查询性能

## 文件清单

### 前端文件
- ✅ `/src/api/admin.js` - 管理员API接口
- ✅ `/src/api/seller.js` - 销售者API接口
- ✅ `/src/components/shop/SellerApplication.vue` - 销售者申请表单
- ⚠️ `/src/components/shop/SellerApplicationForm.vue` - 销售者申请表单（另一个版本）
- ⚠️ `/src/components/shop/SellerApplicationButton.vue` - 申请按钮组件
- ✅ `/src/views/admin/SellerApplicationManagement.vue` - 管理员管理页面

### 后端文件
- ⚠️ `/backend/main/src/main/java/com/agriculture/controller/SellerController.java`
- ⚠️ `/backend/main/src/main/java/com/agriculture/controller/SellerApplicationController.java`
- ⚠️ `/backend/main/src/main/java/com/agriculture/controller/AdminSellerController.java`
- ⚠️ `/backend/main/src/main/java/com/agriculture/service/SellerService.java`
- ⚠️ `/backend/main/src/main/java/com/agriculture/service/SellerApplicationService.java`
- ⚠️ `/backend/main/src/main/java/com/agriculture/service/impl/SellerServiceImpl.java`
- ⚠️ `/backend/main/src/main/java/com/agriculture/service/impl/SellerApplicationServiceImpl.java`
- ⚠️ `/backend/main/src/main/java/com/agriculture/entity/SellerApplication.java`
- ⚠️ `/backend/main/src/main/java/com/agriculture/dto/SellerApplicationDTO.java`

## 下一步计划

1. **检查和修复SellerApplicationForm组件**
2. **统一前端API调用方式**
3. **检查后端API响应格式**
4. **测试完整的申请流程**
5. **测试管理员审核流程**
6. **优化用户体验**

## 测试计划

### 功能测试
1. 用户申请销售者流程
2. 证书上传功能
3. 申请状态查看
4. 管理员审核流程
5. 申请列表分页和筛选

### 集成测试
1. 前后端API对接
2. 权限验证
3. 文件上传
4. 数据库操作

---

**状态说明：**
- ✅ 已完成
- ⚠️ 待处理
- ❌ 存在问题