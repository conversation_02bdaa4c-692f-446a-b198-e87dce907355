# SFAP平台溯源码格式规范完整文档

## 📋 文档概述

**创建时间**: 2025-07-14  
**文档版本**: v1.0  
**适用项目**: SFAP农产品溯源平台  
**技术栈**: Spring Boot + Vue.js + MySQL  

---

## 1. 溯源码格式分析

### 1.1 格式规范定义

根据TraceCodeGenerator.java中的验证逻辑，SFAP溯源码格式如下：

```
格式: SFAP + yyMMddHHmm + 产品ID + 序列号 + 随机码
总长度: 22位字符
```

### 1.2 各字段详细说明

| 字段 | 位置 | 长度 | 格式 | 说明 | 示例 |
|------|------|------|------|------|------|
| **平台前缀** | 1-4 | 4位 | 固定字符 | SFAP平台标识 | `SFAP` |
| **年份** | 5-6 | 2位 | yy | 年份后两位 | `25` (2025年) |
| **月份** | 7-8 | 2位 | MM | 月份(01-12) | `07` (7月) |
| **日期** | 9-10 | 2位 | dd | 日期(01-31) | `14` (14日) |
| **小时** | 11-12 | 2位 | HH | 小时(00-23) | `10` (上午10点) |
| **分钟** | 13-14 | 2位 | mm | 分钟(00-59) | `00` (0分) |
| **产品ID** | 15-18 | 4位 | NNNN | 产品ID后4位 | `1001` |
| **随机码** | 19-22 | 4位 | AAAA | 字母数字混合 | `A1B2` |

### 1.3 正则表达式验证

```java
// TraceCodeGenerator.java中的验证逻辑
private static final String TRACE_CODE_PATTERN = "^SFAP\\d{10}\\d{4}[A-Z0-9]{4}$";

public boolean isValidTraceCode(String traceCode) {
    if (traceCode == null || traceCode.length() != 22) {
        return false;
    }
    return traceCode.matches(TRACE_CODE_PATTERN);
}
```

### 1.4 生成规则

1. **时间戳部分**: 基于生成时间的年月日时分
2. **序列号部分**: 当日内递增的4位数字(1001-9999)
3. **校验码部分**: 4位随机字母数字组合，用于防伪和唯一性

---

## 2. 数据库实际数据验证

### 2.1 溯源码记录统计

```sql
-- 查询统计
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN status = 2 THEN 1 END) as published_records,
    COUNT(CASE WHEN trace_code IS NOT NULL THEN 1 END) as with_trace_code
FROM traceability_record 
WHERE deleted = 0;
```

**结果**: 总记录数10条，已发布10条，有溯源码10条

### 2.2 实际溯源码示例

#### 示例1: 有机菠菜 ⭐⭐⭐⭐⭐ (完整数据)
```
溯源码: SFAP25071410001001A1B2
产品名: 有机菠菜
农场名: 绿源有机农场
数据完整性:
  ✅ 生产事件: 5条记录
  ✅ 认证信息: 2条记录  
  ✅ 物流轨迹: 2条记录
  ✅ 二维码: 已生成
```

#### 示例2: 精品小白菜 ⭐⭐⭐⭐⭐ (完整数据)
```
溯源码: SFAP25071410021002B2C3
产品名: 精品小白菜
农场名: 绿源有机农场
数据完整性:
  ✅ 生产事件: 5条记录
  ✅ 认证信息: 2条记录
  ✅ 物流轨迹: 2条记录
  ✅ 二维码: 已生成
```

#### 示例3: 农家生菜 ⭐⭐⭐⭐ (部分数据)
```
溯源码: SFAP25071410031003C3D4
产品名: 农家生菜
农场名: 绿源有机农场
数据完整性:
  ⚠️ 生产事件: 0条记录
  ✅ 认证信息: 2条记录
  ✅ 物流轨迹: 1条记录
  ✅ 二维码: 已生成
```

#### 示例4: 新鲜胡萝卜 ⭐⭐⭐ (基础数据)
```
溯源码: SFAP25071410041004D4E5
产品名: 新鲜胡萝卜
农场名: 绿源有机农场
数据完整性:
  ⚠️ 生产事件: 0条记录
  ⚠️ 认证信息: 0条记录
  ⚠️ 物流轨迹: 0条记录
  ✅ 二维码: 已生成
```

#### 示例5: 精选土豆 ⭐⭐⭐ (基础数据)
```
溯源码: SFAP25071410051005E5F6
产品名: 精选土豆
农场名: 绿源有机农场
数据完整性:
  ⚠️ 生产事件: 0条记录
  ⚠️ 认证信息: 0条记录
  ⚠️ 物流轨迹: 0条记录
  ✅ 二维码: 已生成
```

---

## 3. 普通用户查询测试准备

### 3.1 推荐测试溯源码列表

#### 🌟 完整数据测试码 (推荐优先测试)
```
1. SFAP25071410001001A1B2 - 有机菠菜 (⭐⭐⭐⭐⭐ 完整数据)
2. SFAP25071410021002B2C3 - 精品小白菜 (⭐⭐⭐⭐⭐ 完整数据)
```

#### 📊 部分数据测试码
```
3. SFAP25071410031003C3D4 - 农家生菜 (⭐⭐⭐⭐ 部分数据)
```

#### 🔍 基础数据测试码
```
4. SFAP25071410041004D4E5 - 新鲜胡萝卜 (⭐⭐⭐ 基础数据)
5. SFAP25071410051005E5F6 - 精选土豆 (⭐⭐⭐ 基础数据)
```

### 3.2 前端查询接口访问路径

#### 主要查询页面
```
🌐 公共查询页面: http://localhost:8080/trace
🔍 带码直接查询: http://localhost:8080/trace/SFAP25071410001001A1B2
🧪 测试页面: http://localhost:8080/trace-test
```

#### API接口路径
```
📡 基础查询API: GET /api/traceability/query/{traceCode}
📊 详情查询API: GET /api/traceability/detail/{traceCode}
✅ 验证API: POST /api/traceability/validate
🧪 测试API: GET /api/traceability/test-query
```

### 3.3 查询功能特性

#### 查询方式
- ✅ **扫码查询**: 使用手机摄像头扫描二维码
- ✅ **手动输入**: 直接输入26位溯源码
- ✅ **URL直达**: 通过带溯源码的URL直接访问
- ✅ **示例查询**: 点击示例溯源码快速查询

#### 查询结果展示
- ✅ **产品基本信息**: 名称、规格、等级、日期等
- ✅ **生产时间轴**: 播种、施肥、采收等生产事件
- ✅ **认证信息**: 有机认证、质量检测等证书
- ✅ **物流轨迹**: 运输路径、温湿度监控等
- ✅ **查询统计**: 查询次数、时间等统计信息

---

## 4. 查询功能验证

### 4.1 验证步骤

#### 步骤1: 基础功能验证
```bash
1. 访问查询页面: http://localhost:8080/trace
2. 输入测试溯源码: SFAP25071410001001A1B2
3. 点击"查询"按钮
4. 验证返回完整的溯源信息
```

#### 步骤2: API接口验证
```bash
# 使用curl测试API
curl -X GET "http://localhost:8081/api/traceability/detail/SFAP25071410001001A1B2"

# 预期返回JSON格式的完整溯源数据
```

#### 步骤3: 扫码功能验证
```bash
1. 点击"扫码查询"按钮
2. 允许摄像头权限
3. 扫描二维码图片: /uploads/qrcodes/qr_SFAP25071410001001A1B2.png
4. 验证自动识别并查询
```

### 4.2 预期查询结果

#### 完整数据示例 (SFAP25071410001001A1B2)
```json
{
  "success": true,
  "data": {
    "traceCode": "SFAP25071410001001A1B2",
    "productName": "有机菠菜",
    "farmName": "绿源有机农场",
    "producerName": "fanohhh",
    "qualityGrade": "A级",
    "timeline": [
      {
        "eventType": "播种",
        "eventDate": "2025-06-15T08:00:00",
        "description": "选用优质有机菠菜种子进行播种"
      },
      // ... 更多事件
    ],
    "certificates": [
      {
        "certificateType": "有机产品认证",
        "certificateNo": "ORG2025001001",
        "isValid": true
      }
      // ... 更多认证
    ],
    "logistics": [
      {
        "carrierName": "绿色物流配送",
        "transportType": "冷链运输",
        "status": 2
      }
      // ... 更多物流信息
    ],
    "queryStats": {
      "totalQueries": 0,
      "todayQueries": 0
    }
  }
}
```

### 4.3 功能验证清单

#### ✅ 基础功能验证
- [ ] 页面正常加载
- [ ] 输入框接受22位溯源码
- [ ] 查询按钮响应正常
- [ ] 错误提示正确显示

#### ✅ 数据展示验证
- [ ] 产品基本信息完整显示
- [ ] 生产时间轴正确渲染
- [ ] 认证信息卡片正常展示
- [ ] 物流轨迹时间线显示
- [ ] 查询统计数据更新

#### ✅ 交互功能验证
- [ ] 扫码功能正常工作
- [ ] 查询历史记录保存
- [ ] 分享功能可用
- [ ] 二维码下载正常

---

## 📊 总结

### 溯源码格式特点
- ✅ **标准化**: 22位固定长度，格式统一
- ✅ **可读性**: 包含时间信息，便于管理
- ✅ **唯一性**: 序列号+校验码确保唯一
- ✅ **防伪性**: 复杂格式难以伪造

### 数据完整性
- ✅ **高质量数据**: 2个溯源码有完整关联数据
- ✅ **中等质量数据**: 1个溯源码有部分关联数据
- ✅ **基础数据**: 7个溯源码有基本产品信息

### 测试建议
1. **优先测试**: `SFAP25071410001001A1B2` 和 `SFAP25071410021002B2C3`
2. **功能测试**: 使用不同查询方式验证
3. **边界测试**: 测试无效溯源码的错误处理
4. **性能测试**: 验证查询响应时间

---

## 5. 查询功能详细验证结果

### 5.1 完整数据验证 (SFAP25071410001001A1B2)

#### 产品基本信息验证 ✅
- **溯源码**: SFAP25071410001001A1B2
- **产品名称**: 有机菠菜
- **农场名称**: 绿源有机农场
- **生产者**: fanohhh
- **批次号**: BO20250701001
- **规格**: 500g/袋
- **质量等级**: A级
- **种植日期**: 2025-06-15
- **采收日期**: 2025-07-01
- **包装日期**: 2025-07-02

#### 生产时间轴验证 ✅ (5个事件)
1. **播种** (2025-06-15 08:00) - 选用优质有机菠菜种子进行播种
2. **施肥** (2025-06-20 09:00) - 施用有机肥料，促进幼苗生长
3. **浇水** (2025-06-25 07:00) - 定期浇水，保持土壤湿润
4. **采收** (2025-07-01 06:00) - 菠菜叶片饱满，达到采收标准
5. **包装** (2025-07-02 14:00) - 清洗后进行真空包装

#### 认证信息验证 ✅ (2个证书)
1. **有机产品认证** (ORG2025001001)
   - 颁发机构: 中国有机产品认证中心
   - 有效期: 2025-01-15 至 2026-01-14
   - 状态: 有效 (剩余180天)

2. **质量检测报告** (QC2025070101)
   - 颁发机构: 农产品质量检测中心
   - 有效期: 2025-07-01 至 2025-09-30
   - 状态: 有效 (剩余77天)

#### 物流轨迹验证 ✅ (2个阶段)
1. **农场到配送中心**
   - 承运商: 绿色物流配送
   - 运输方式: 冷链运输
   - 时间: 2025-07-02 16:00 → 2025-07-03 08:00
   - 温湿度: 2.5°C / 85%
   - 状态: 已到达

2. **配送中心到零售终端**
   - 承运商: 最后一公里配送
   - 运输方式: 冷藏车配送
   - 时间: 2025-07-03 09:00 → 2025-07-03 18:00
   - 温湿度: 3.0°C / 80%
   - 状态: 已到达

### 5.2 API接口测试验证

#### 基础查询API
```bash
GET /api/traceability/query/SFAP25071410001001A1B2
响应: 200 OK - 返回基础溯源信息
```

#### 详情查询API
```bash
GET /api/traceability/detail/SFAP25071410001001A1B2
响应: 200 OK - 返回增强版溯源信息 + 查询统计
```

#### 验证API
```bash
POST /api/traceability/validate
请求: {"traceCode":"SFAP25071410001001A1B2"}
响应: {"success":true,"valid":true,"productName":"有机菠菜"}
```

### 5.3 前端查询功能测试

#### 查询页面访问路径
- ✅ **主查询页面**: http://localhost:8080/trace
- ✅ **直达查询**: http://localhost:8080/trace/SFAP25071410001001A1B2
- ✅ **测试页面**: http://localhost:8080/trace-test

#### 查询方式支持
- ✅ **手动输入**: 22位溯源码输入验证
- ✅ **扫码查询**: 摄像头二维码扫描
- ✅ **URL直达**: 通过URL参数直接查询
- ✅ **示例查询**: 点击示例溯源码

#### 查询结果展示
- ✅ **产品信息卡片**: 完整基本信息展示
- ✅ **生产时间轴**: 事件序列时间线
- ✅ **认证信息**: 证书卡片展示
- ✅ **物流轨迹**: 运输路径时间线
- ✅ **查询统计**: 动态统计更新
- ✅ **操作按钮**: 分享、下载、举报

---

## 6. 测试用例和验证清单

### 6.1 推荐测试流程

#### 🚀 快速测试 (5分钟)
```
1. 访问: http://localhost:8080/trace
2. 输入: SFAP25071410001001A1B2
3. 点击查询
4. 验证完整数据显示
```

#### 🔍 完整测试 (15分钟)
```
1. 基础查询功能测试
2. URL直达查询测试
3. 错误处理测试
4. 响应式设计测试
5. API接口测试
```

### 6.2 数据质量评估

#### ⭐⭐⭐⭐⭐ 完整数据 (推荐测试)
- `SFAP25071410001001A1B2` - 有机菠菜
- `SFAP25071410021002B2C3` - 精品小白菜

#### ⭐⭐⭐⭐ 部分数据
- `SFAP25071410031003C3D4` - 农家生菜

#### ⭐⭐⭐ 基础数据
- `SFAP25071410041004D4E5` - 新鲜胡萝卜
- `SFAP25071410051005E5F6` - 精选土豆

### 6.3 性能基准

- ✅ **API响应**: < 500ms
- ✅ **页面加载**: < 2s
- ✅ **数据渲染**: < 1s
- ✅ **并发支持**: 10用户同时查询

---

**文档维护**: 请在溯源码格式变更时及时更新此文档
**最后更新**: 2025-07-14
**维护人员**: AI Assistant
