-- 智慧农业平台数据库图片路径修复脚本
-- 将所有图片路径统一为 /uploads/images/[category]/filename 格式

-- 开始事务
START TRANSACTION;

-- 1. 修复商品表中的图片路径
UPDATE product 
SET image = CASE 
    -- 如果已经是正确格式，保持不变
    WHEN image LIKE '/uploads/images/products/%' THEN image
    -- 如果是 /images/products/ 格式，转换为正确格式
    WHEN image LIKE '/images/products/%' THEN REPLACE(image, '/images/products/', '/uploads/images/products/')
    -- 如果是 uploads/images/products/ 格式，添加前缀 /
    WHEN image LIKE 'uploads/images/products/%' THEN CONCAT('/', image)
    -- 如果是 images/products/ 格式，转换为正确格式
    WHEN image LIKE 'images/products/%' THEN REPLACE(image, 'images/products/', '/uploads/images/products/')
    -- 如果只是文件名，添加完整路径
    WHEN image NOT LIKE 'http%' AND image NOT LIKE '/%' AND image IS NOT NULL AND image != '' 
    THEN CONCAT('/uploads/images/products/', image)
    -- 其他情况保持不变
    ELSE image
END
WHERE image IS NOT NULL AND image != '';

-- 1.5. 修复商品图片表中的图片路径
UPDATE product_image
SET image_url = CASE
    -- 如果已经是正确格式，保持不变
    WHEN image_url LIKE '/uploads/images/products/%' THEN image_url
    -- 如果是 /images/products/ 格式，转换为正确格式
    WHEN image_url LIKE '/images/products/%' THEN REPLACE(image_url, '/images/products/', '/uploads/images/products/')
    -- 如果是 uploads/images/products/ 格式，添加前缀 /
    WHEN image_url LIKE 'uploads/images/products/%' THEN CONCAT('/', image_url)
    -- 如果是 images/products/ 格式，转换为正确格式
    WHEN image_url LIKE 'images/products/%' THEN REPLACE(image_url, 'images/products/', '/uploads/images/products/')
    -- 如果只是文件名，添加完整路径
    WHEN image_url NOT LIKE 'http%' AND image_url NOT LIKE '/%' AND image_url IS NOT NULL AND image_url != ''
    THEN CONCAT('/uploads/images/products/', image_url)
    -- 其他情况保持不变
    ELSE image_url
END
WHERE image_url IS NOT NULL AND image_url != '';

-- 2. 修复用户表中的头像路径
UPDATE user 
SET avatar = CASE 
    -- 如果已经是正确格式，保持不变
    WHEN avatar LIKE '/uploads/images/avatars/%' THEN avatar
    -- 如果是 /images/avatars/ 格式，转换为正确格式
    WHEN avatar LIKE '/images/avatars/%' THEN REPLACE(avatar, '/images/avatars/', '/uploads/images/avatars/')
    -- 如果是 uploads/images/avatars/ 格式，添加前缀 /
    WHEN avatar LIKE 'uploads/images/avatars/%' THEN CONCAT('/', avatar)
    -- 如果是 images/avatars/ 格式，转换为正确格式
    WHEN avatar LIKE 'images/avatars/%' THEN REPLACE(avatar, 'images/avatars/', '/uploads/images/avatars/')
    -- 如果是 /uploads/avatars/ 格式（旧格式），转换为新格式
    WHEN avatar LIKE '/uploads/avatars/%' THEN REPLACE(avatar, '/uploads/avatars/', '/uploads/images/avatars/')
    -- 如果只是文件名，添加完整路径
    WHEN avatar NOT LIKE 'http%' AND avatar NOT LIKE '/%' AND avatar IS NOT NULL AND avatar != '' 
    THEN CONCAT('/uploads/images/avatars/', avatar)
    -- 其他情况保持不变
    ELSE avatar
END
WHERE avatar IS NOT NULL AND avatar != '';

-- 3. 修复新闻表中的图片路径（如果存在imageUrl字段）
UPDATE agriculture_news 
SET imageUrl = CASE 
    -- 如果已经是正确格式，保持不变
    WHEN imageUrl LIKE '/uploads/images/news/%' THEN imageUrl
    -- 如果是 /images/news/ 格式，转换为正确格式
    WHEN imageUrl LIKE '/images/news/%' THEN REPLACE(imageUrl, '/images/news/', '/uploads/images/news/')
    -- 如果是 uploads/images/news/ 格式，添加前缀 /
    WHEN imageUrl LIKE 'uploads/images/news/%' THEN CONCAT('/', imageUrl)
    -- 如果是 images/news/ 格式，转换为正确格式
    WHEN imageUrl LIKE 'images/news/%' THEN REPLACE(imageUrl, 'images/news/', '/uploads/images/news/')
    -- 如果只是文件名，添加完整路径
    WHEN imageUrl NOT LIKE 'http%' AND imageUrl NOT LIKE '/%' AND imageUrl IS NOT NULL AND imageUrl != '' 
    THEN CONCAT('/uploads/images/news/', imageUrl)
    -- 其他情况保持不变
    ELSE imageUrl
END
WHERE imageUrl IS NOT NULL AND imageUrl != '';

-- 4. 修复百科表中的图片路径（如果存在）
UPDATE encyclopedia 
SET image = CASE 
    -- 如果已经是正确格式，保持不变
    WHEN image LIKE '/uploads/images/encyclopedia/%' THEN image
    -- 如果是 /images/encyclopedia/ 格式，转换为正确格式
    WHEN image LIKE '/images/encyclopedia/%' THEN REPLACE(image, '/images/encyclopedia/', '/uploads/images/encyclopedia/')
    -- 如果是 uploads/images/encyclopedia/ 格式，添加前缀 /
    WHEN image LIKE 'uploads/images/encyclopedia/%' THEN CONCAT('/', image)
    -- 如果是 images/encyclopedia/ 格式，转换为正确格式
    WHEN image LIKE 'images/encyclopedia/%' THEN REPLACE(image, 'images/encyclopedia/', '/uploads/images/encyclopedia/')
    -- 如果只是文件名，添加完整路径
    WHEN image NOT LIKE 'http%' AND image NOT LIKE '/%' AND image IS NOT NULL AND image != '' 
    THEN CONCAT('/uploads/images/encyclopedia/', image)
    -- 其他情况保持不变
    ELSE image
END
WHERE image IS NOT NULL AND image != '';

-- 5. 修复轮播图表中的图片路径（如果存在）
UPDATE banner 
SET image_url = CASE 
    -- 如果已经是正确格式，保持不变
    WHEN image_url LIKE '/uploads/images/banners/%' THEN image_url
    -- 如果是 /images/banners/ 格式，转换为正确格式
    WHEN image_url LIKE '/images/banners/%' THEN REPLACE(image_url, '/images/banners/', '/uploads/images/banners/')
    -- 如果是 uploads/images/banners/ 格式，添加前缀 /
    WHEN image_url LIKE 'uploads/images/banners/%' THEN CONCAT('/', image_url)
    -- 如果是 images/banners/ 格式，转换为正确格式
    WHEN image_url LIKE 'images/banners/%' THEN REPLACE(image_url, 'images/banners/', '/uploads/images/banners/')
    -- 如果只是文件名，添加完整路径
    WHEN image_url NOT LIKE 'http%' AND image_url NOT LIKE '/%' AND image_url IS NOT NULL AND image_url != '' 
    THEN CONCAT('/uploads/images/banners/', image_url)
    -- 其他情况保持不变
    ELSE image_url
END
WHERE image_url IS NOT NULL AND image_url != '';

-- 6. 修复分类表中的图片路径（如果存在）
UPDATE category 
SET icon = CASE 
    -- 如果已经是正确格式，保持不变
    WHEN icon LIKE '/uploads/images/categories/%' THEN icon
    -- 如果是 /images/categories/ 格式，转换为正确格式
    WHEN icon LIKE '/images/categories/%' THEN REPLACE(icon, '/images/categories/', '/uploads/images/categories/')
    -- 如果是 uploads/images/categories/ 格式，添加前缀 /
    WHEN icon LIKE 'uploads/images/categories/%' THEN CONCAT('/', icon)
    -- 如果是 images/categories/ 格式，转换为正确格式
    WHEN icon LIKE 'images/categories/%' THEN REPLACE(icon, 'images/categories/', '/uploads/images/categories/')
    -- 如果只是文件名，添加完整路径
    WHEN icon NOT LIKE 'http%' AND icon NOT LIKE '/%' AND icon IS NOT NULL AND icon != '' 
    THEN CONCAT('/uploads/images/categories/', icon)
    -- 其他情况保持不变
    ELSE icon
END
WHERE icon IS NOT NULL AND icon != '';

-- 提交事务
COMMIT;

-- 验证修复结果
SELECT '=== 商品图片路径修复结果 ===' as info;
SELECT 
    COUNT(*) as total_products,
    COUNT(CASE WHEN image LIKE '/uploads/images/products/%' THEN 1 END) as correct_format,
    COUNT(CASE WHEN image IS NOT NULL AND image != '' AND image NOT LIKE '/uploads/images/products/%' AND image NOT LIKE 'http%' THEN 1 END) as incorrect_format
FROM product;

SELECT '=== 用户头像路径修复结果 ===' as info;
SELECT 
    COUNT(*) as total_users,
    COUNT(CASE WHEN avatar LIKE '/uploads/images/avatars/%' THEN 1 END) as correct_format,
    COUNT(CASE WHEN avatar IS NOT NULL AND avatar != '' AND avatar NOT LIKE '/uploads/images/avatars/%' AND avatar NOT LIKE 'http%' THEN 1 END) as incorrect_format
FROM user;

SELECT '=== 新闻图片路径修复结果 ===' as info;
SELECT 
    COUNT(*) as total_news,
    COUNT(CASE WHEN imageUrl LIKE '/uploads/images/news/%' THEN 1 END) as correct_format,
    COUNT(CASE WHEN imageUrl IS NOT NULL AND imageUrl != '' AND imageUrl NOT LIKE '/uploads/images/news/%' AND imageUrl NOT LIKE 'http%' THEN 1 END) as incorrect_format
FROM agriculture_news;

-- 显示一些示例路径
SELECT '=== 商品图片路径示例 ===' as info;
SELECT id, name, image FROM product WHERE image IS NOT NULL AND image != '' LIMIT 5;

SELECT '=== 用户头像路径示例 ===' as info;
SELECT id, username, avatar FROM user WHERE avatar IS NOT NULL AND avatar != '' LIMIT 5;

SELECT '=== 新闻图片路径示例 ===' as info;
SELECT id, title, imageUrl FROM agriculture_news WHERE imageUrl IS NOT NULL AND imageUrl != '' LIMIT 5;
