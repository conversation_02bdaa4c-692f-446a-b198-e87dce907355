# SFAP农品汇平台数据库备份说明

## 📋 备份概述

**备份时间**: 2025-01-25  
**数据库名称**: agriculture_mall  
**MySQL版本**: 8.0+  
**字符集**: utf8mb4  
**排序规则**: utf8mb4_unicode_ci  

---

## 📁 备份文件说明

### 1. 主要备份文件

#### `SFAP_current_database_backup.sql`
- **类型**: 简化版备份文件
- **内容**: 核心表结构 + 部分关键数据
- **大小**: 约300行
- **用途**: 快速部署和测试

#### `SFAP_complete_database_backup.sql`
- **类型**: 完整版备份文件
- **内容**: 完整表结构 + 核心用户数据
- **大小**: 约300行（基础版本）
- **用途**: 生产环境部署

### 2. 备份内容统计

| 数据类型 | 数量 | 说明 |
|----------|------|------|
| 用户总数 | 21个 | 包含管理员、销售者、普通用户 |
| 商品总数 | 80个 | 各类农产品信息 |
| 分类总数 | 47个 | 多级分类体系 |
| 溯源记录 | ~80条 | 商品溯源信息 |
| 表结构 | 20+张表 | 完整业务表结构 |

---

## 🔧 数据库结构概览

### 核心表结构

#### 1. 用户管理模块
- **user**: 用户基础信息表
- **user_address**: 用户地址表
- **user_behavior**: 用户行为记录表

#### 2. 商品管理模块
- **product**: 商品信息表
- **category**: 商品分类表
- **product_review**: 商品评论表
- **product_image**: 商品图片表
- **product_favorite**: 商品收藏表

#### 3. 订单管理模块
- **order**: 订单主表
- **order_item**: 订单项表
- **cart_item**: 购物车表

#### 4. 溯源系统模块
- **traceability_record**: 溯源记录表
- **traceability_query**: 溯源查询记录表
- **traceability_event**: 溯源事件表

#### 5. 销售者管理模块
- **seller_application**: 销售者申请表
- **seller_shop**: 销售者店铺表

#### 6. 价格数据模块
- **price_data**: 价格数据表
- **price_history**: 价格历史表

#### 7. AI预测模块
- **prediction_models**: 预测模型表
- **prediction_results**: 预测结果表
- **model_evaluations**: 模型评估表

#### 8. 新闻百科模块
- **agriculture_news**: 农业新闻表
- **news_images**: 新闻图片表
- **encyclopedia**: 农业百科表
- **encyclopedia_category**: 百科分类表

#### 9. 系统管理模块
- **system_configs**: 系统配置表
- **api_access_log**: API访问日志表
- **error_log**: 错误日志表

---

## 🚀 恢复部署步骤

### 1. 环境准备

#### 1.1 MySQL服务器要求
```bash
MySQL版本: 8.0+
内存: 4GB+
磁盘空间: 20GB+
字符集支持: utf8mb4
```

#### 1.2 连接到MySQL
```bash
mysql -u root -p
```

### 2. 执行备份恢复

#### 2.1 方法一：命令行执行
```bash
# 恢复简化版备份
mysql -u root -p < SFAP_current_database_backup.sql

# 恢复完整版备份
mysql -u root -p < SFAP_complete_database_backup.sql
```

#### 2.2 方法二：MySQL客户端执行
```sql
-- 连接MySQL后执行
source /path/to/SFAP_current_database_backup.sql;
```

#### 2.3 方法三：图形化工具
- 使用Navicat、phpMyAdmin等工具
- 导入SQL文件并执行

### 3. 验证恢复结果

#### 3.1 检查数据库
```sql
SHOW DATABASES LIKE 'agriculture_mall';
USE agriculture_mall;
SHOW TABLES;
```

#### 3.2 检查数据完整性
```sql
-- 检查用户数据
SELECT COUNT(*) AS total_users, 
       COUNT(CASE WHEN role = 'admin' THEN 1 END) AS admin_users,
       COUNT(CASE WHEN role = 'seller' THEN 1 END) AS seller_users,
       COUNT(CASE WHEN role = 'user' THEN 1 END) AS normal_users
FROM user WHERE deleted = 0;

-- 检查商品数据
SELECT COUNT(*) AS total_products,
       COUNT(CASE WHEN status = 1 THEN 1 END) AS active_products
FROM product WHERE deleted = 0;

-- 检查分类数据
SELECT COUNT(*) AS total_categories,
       COUNT(CASE WHEN level = 1 THEN 1 END) AS level1_categories
FROM category WHERE deleted = 0;
```

#### 3.3 检查用户权限
```sql
-- 检查应用用户
SELECT User, Host FROM mysql.user WHERE User = 'sfap_user';
SHOW GRANTS FOR 'sfap_user'@'localhost';
```

---

## 🔐 重要账号信息

### 默认管理员账号
- **用户名**: `system_admin`
- **密码**: 已加密存储
- **角色**: admin
- **邮箱**: <EMAIL>

### 数据库用户
- **用户名**: `sfap_user`
- **密码**: `sfap_secure_password_2025`
- **权限**: 数据库完整操作权限

### 测试账号
- **销售者**: `fanohhh` (seller角色)
- **普通用户**: `buyer1` (user角色)
- **管理员**: `admin` (admin角色)

---

## ⚠️ 安全注意事项

### 1. 密码安全
```sql
-- 部署后立即修改数据库用户密码
ALTER USER 'sfap_user'@'localhost' IDENTIFIED BY '新的安全密码';
ALTER USER 'sfap_user'@'%' IDENTIFIED BY '新的安全密码';
FLUSH PRIVILEGES;
```

### 2. 权限控制
```sql
-- 生产环境建议移除远程访问权限
DROP USER 'sfap_user'@'%';
-- 只保留本地访问
```

### 3. 数据保护
- 定期备份数据库
- 设置访问控制
- 监控异常访问

---

## 🔍 故障排查

### 1. 常见问题

#### 字符集问题
```sql
-- 检查字符集
SHOW VARIABLES LIKE 'character_set%';
-- 如果不正确，修改my.cnf配置
```

#### 权限问题
```sql
-- 重新授权
GRANT ALL PRIVILEGES ON agriculture_mall.* TO 'sfap_user'@'localhost';
FLUSH PRIVILEGES;
```

#### 外键约束问题
```sql
-- 临时禁用外键检查
SET FOREIGN_KEY_CHECKS = 0;
-- 执行SQL后重新启用
SET FOREIGN_KEY_CHECKS = 1;
```

### 2. 数据验证

#### 检查表结构
```sql
DESCRIBE user;
DESCRIBE product;
DESCRIBE category;
```

#### 检查索引
```sql
SHOW INDEX FROM product;
SHOW INDEX FROM user;
```

---

## 📊 备份文件对比

| 特性 | 简化版备份 | 完整版备份 |
|------|------------|------------|
| 文件大小 | 较小 | 较大 |
| 数据完整性 | 核心数据 | 完整数据 |
| 恢复速度 | 快速 | 较慢 |
| 适用场景 | 开发测试 | 生产部署 |
| 用户数据 | 核心用户 | 全部用户 |
| 商品数据 | 示例商品 | 全部商品 |

---

## 📞 技术支持

### 联系信息
- **项目**: SFAP农品汇平台
- **版本**: v4.0
- **备份日期**: 2025-01-25

### 相关文档
- 项目部署指南
- API接口文档
- 系统配置说明

---

**备份完成，请妥善保存备份文件！**
