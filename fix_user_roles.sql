-- 用户角色修复SQL脚本
-- 用于修复智慧农业平台用户角色数据不一致问题

-- 1. 修复admin_new用户为管理员
UPDATE user 
SET role = 'admin', 
    user_type = 'admin', 
    updated_at = NOW() 
WHERE username = 'admin_new';

-- 2. 修复所有管理员用户
UPDATE user 
SET role = 'admin', 
    user_type = 'admin', 
    updated_at = NOW() 
WHERE username IN ('admin', 'admin1', 'admin2', 'admin3', 'admin_new') 
   OR id IN (10, 11, 12, 18)
   OR role = 'ADMIN';

-- 3. 修复销售者用户角色格式
UPDATE user 
SET role = 'seller', 
    user_type = 'seller', 
    updated_at = NOW() 
WHERE role IN ('SELLER', 'FARMER') 
   OR user_type = 'seller';

-- 4. 修复普通用户角色格式
UPDATE user 
SET role = 'user', 
    user_type = 'normal', 
    updated_at = NOW() 
WHERE role IN ('USER', 'BUYER', 'ROLE_USER') 
   OR (role IS NULL AND user_type IN ('normal', 'USER'))
   OR (role = 'user' AND user_type IS NULL);

-- 5. 处理空值情况
UPDATE user 
SET role = 'user', 
    user_type = 'normal', 
    updated_at = NOW() 
WHERE (role IS NULL OR role = '') 
  AND (user_type IS NULL OR user_type = '' OR user_type = 'normal');

-- 6. 确保状态正常
UPDATE user 
SET status = 1 
WHERE status IS NULL OR status = 0;

-- 7. 验证修复结果
SELECT 
    id,
    username,
    role,
    user_type,
    status,
    CASE 
        WHEN role = 'admin' AND user_type = 'admin' THEN '管理员'
        WHEN role = 'seller' AND user_type = 'seller' THEN '销售者'
        WHEN role = 'user' AND user_type = 'normal' THEN '普通用户'
        ELSE '角色不一致'
    END as role_status
FROM user 
ORDER BY id;

-- 8. 统计各角色用户数量
SELECT 
    role,
    user_type,
    COUNT(*) as count
FROM user 
WHERE status = 1
GROUP BY role, user_type
ORDER BY count DESC;

-- 9. 查找仍然不一致的用户
SELECT 
    id,
    username,
    role,
    user_type,
    '角色不一致' as issue
FROM user 
WHERE status = 1
  AND NOT (
    (role = 'admin' AND user_type = 'admin') OR
    (role = 'seller' AND user_type = 'seller') OR
    (role = 'user' AND user_type = 'normal')
  );

-- 10. 特别确认admin_new用户
SELECT 
    id,
    username,
    role,
    user_type,
    status,
    created_at,
    updated_at,
    CASE 
        WHEN role = 'admin' AND user_type = 'admin' THEN '✅ 管理员权限正确'
        ELSE '❌ 管理员权限异常'
    END as admin_status
FROM user 
WHERE username = 'admin_new';
