中心界面# 前后端API对接成功文档

## 项目概述
本文档记录了农产品溯源系统前后端API对接的完整实现过程和成功对接的API接口清单。

## 数据库表结构调整

### 1. 实体类与数据库表映射修正

#### 1.1 TraceabilityRecord (溯源记录表)
- **表名修正**: `traceability_records` → `traceability_record`
- **字段调整**: 根据数据库表结构删除了不存在的字段，新增了 `qrCodeUrl` 字段
- **状态**: ✅ 已完成对接

#### 1.2 TraceabilityEvent (溯源事件表)
- **表名修正**: `traceability_events` → `traceability_event`
- **字段调整**: 删除了数据库中不存在的扩展字段
- **状态**: ✅ 已完成对接

#### 1.3 TraceabilityCertificate (溯源认证表)
- **表名修正**: `trace_certificates` → `traceability_certificate`
- **状态**: ✅ 已完成对接

#### 1.4 TraceabilityLogistics (溯源物流表)
- **表名修正**: `trace_logistics` → `traceability_logistics`
- **状态**: ✅ 已完成对接

#### 1.5 TraceabilityQuery (溯源查询记录表)
- **新增实体**: 根据数据库表 `traceability_query` 创建对应实体类
- **状态**: ✅ 已完成对接

## API接口对接清单

### 1. 溯源记录管理 API

| 接口名称 | HTTP方法 | 接口路径 | 功能描述 | 对接状态 |
|---------|---------|----------|----------|----------|
| 创建溯源记录 | POST | `/api/traceability/records` | 创建新的溯源记录 | ✅ 已对接 |
| 更新溯源记录 | PUT | `/api/traceability/records/{id}` | 更新指定溯源记录 | ✅ 已对接 |
| 删除溯源记录 | DELETE | `/api/traceability/records/{id}` | 删除指定溯源记录 | ✅ 已对接 |
| 获取溯源记录详情 | GET | `/api/traceability/records/{id}` | 获取指定溯源记录详情 | ✅ 已对接 |
| 分页查询溯源记录 | GET | `/api/traceability/records` | 分页查询溯源记录列表 | ✅ 已对接 |
| 发布溯源记录 | PUT | `/api/traceability/records/{id}/publish` | 发布溯源记录 | ✅ 已对接 |
| 撤回溯源记录 | PUT | `/api/traceability/records/{id}/withdraw` | 撤回溯源记录 | ✅ 已对接 |

### 2. 溯源事件管理 API

| 接口名称 | HTTP方法 | 接口路径 | 功能描述 | 对接状态 |
|---------|---------|----------|----------|----------|
| 添加溯源事件 | POST | `/api/traceability/events` | 添加新的溯源事件 | ✅ 已对接 |
| 更新溯源事件 | PUT | `/api/traceability/events/{id}` | 更新指定溯源事件 | ✅ 已对接 |
| 删除溯源事件 | DELETE | `/api/traceability/events/{id}` | 删除指定溯源事件 | ✅ 已对接 |
| 获取事件列表 | GET | `/api/traceability/records/{recordId}/events` | 获取溯源记录的事件列表 | ✅ 已对接 |
| 获取事件时间线 | GET | `/api/traceability/records/{recordId}/timeline` | 获取事件时间线 | ✅ 已对接 |

### 3. 溯源认证管理 API

| 接口名称 | HTTP方法 | 接口路径 | 功能描述 | 对接状态 |
|---------|---------|----------|----------|----------|
| 添加溯源认证 | POST | `/api/traceability/certificates` | 添加新的溯源认证 | ✅ 已对接 |
| 更新溯源认证 | PUT | `/api/traceability/certificates/{id}` | 更新指定溯源认证 | ✅ 已对接 |
| 删除溯源认证 | DELETE | `/api/traceability/certificates/{id}` | 删除指定溯源认证 | ✅ 已对接 |
| 获取认证列表 | GET | `/api/traceability/records/{recordId}/certificates` | 获取溯源记录的认证列表 | ✅ 已对接 |

### 4. 溯源物流管理 API

| 接口名称 | HTTP方法 | 接口路径 | 功能描述 | 对接状态 |
|---------|---------|----------|----------|----------|
| 添加物流信息 | POST | `/api/traceability/logistics` | 添加新的物流信息 | ✅ 已对接 |
| 更新物流信息 | PUT | `/api/traceability/logistics/{id}` | 更新指定物流信息 | ✅ 已对接 |
| 删除物流信息 | DELETE | `/api/traceability/logistics/{id}` | 删除指定物流信息 | ✅ 已对接 |
| 获取物流信息列表 | GET | `/api/traceability/records/{recordId}/logistics` | 获取溯源记录的物流信息 | ✅ 已对接 |
| 获取物流轨迹 | GET | `/api/traceability/records/{recordId}/logistics/track` | 获取物流轨迹 | ✅ 已对接 |

### 5. 公共查询 API

| 接口名称 | HTTP方法 | 接口路径 | 功能描述 | 对接状态 |
|---------|---------|----------|----------|----------|
| 验证溯源码 | GET | `/api/traceability/verify/{traceCode}` | 验证溯源码有效性 | ✅ 已对接 |
| 查询完整溯源信息 | GET | `/api/traceability/query/{traceCode}` | 根据溯源码查询完整信息 | ✅ 已对接 |
| 获取溯源统计信息 | GET | `/api/traceability/stats` | 获取系统溯源统计 | ✅ 已对接 |
| 获取生产者统计 | GET | `/api/traceability/producer/{producerId}/stats` | 获取生产者溯源统计 | ✅ 已对接 |

### 6. 溯源查询记录管理 API (新增)

| 接口名称 | HTTP方法 | 接口路径 | 功能描述 | 对接状态 |
|---------|---------|----------|----------|----------|
| 记录溯源查询 | POST | `/api/traceability/query/record` | 记录用户查询行为 | ✅ 已对接 |
| 分页查询查询记录 | GET | `/api/traceability/query/records` | 分页查询溯源查询记录 | ✅ 已对接 |
| 根据溯源码获取查询记录 | GET | `/api/traceability/query/records/{traceCode}` | 获取指定溯源码的查询记录 | ✅ 已对接 |
| 获取查询统计信息 | GET | `/api/traceability/query/statistics` | 获取查询行为统计 | ✅ 已对接 |
| 获取热门查询溯源码 | GET | `/api/traceability/query/hot-codes` | 获取热门查询的溯源码 | ✅ 已对接 |

#### 6.1 记录溯源查询
- **接口**: `POST /api/traceability/query/record`
- **参数**: 
  - `traceCode`: 溯源码（必填）
  - `traceRecordId`: 溯源记录ID（可选）
  - `deviceInfo`: 设备信息（可选）
  - `userId`: 用户ID（可选）
  - 自动获取: IP地址通过HttpServletRequest自动获取
- **功能**: 记录用户的溯源查询行为
- **状态**: ✅ 已实现

#### 6.2 分页查询溯源查询记录
- **接口**: `GET /api/traceability/query/records`
- **参数**: 
  - `page`: 页码（默认1）
  - `size`: 每页大小（默认10）
  - `traceCode`: 溯源码（可选）
  - `userId`: 用户ID（可选）
- **功能**: 分页获取溯源查询记录
- **状态**: ✅ 已实现

#### 6.3 根据溯源码获取查询记录
- **接口**: `GET /api/traceability/query/records/{traceCode}`
- **功能**: 获取指定溯源码的所有查询记录
- **状态**: ✅ 已实现

#### 6.4 获取查询统计信息
- **接口**: `GET /api/traceability/query/statistics`
- **功能**: 获取查询统计数据（地理位置分布、日期趋势、活跃IP等）
- **状态**: ✅ 已实现

#### 6.5 获取热门查询溯源码
- **接口**: `GET /api/traceability/query/hot-codes`
- **参数**: `limit`: 返回数量限制（默认10）
- **功能**: 获取查询次数最多的溯源码列表
- **状态**: ✅ 已实现

## 技术实现细节

### 1. 数据访问层 (Mapper)

#### 已实现的Mapper接口:
- `TraceabilityRecordMapper`: 溯源记录数据访问
- `TraceabilityEventMapper`: 溯源事件数据访问
- `TraceabilityCertificateMapper`: 溯源认证数据访问
- `TraceabilityLogisticsMapper`: 溯源物流数据访问
- `TraceabilityQueryMapper`: 溯源查询记录数据访问 (新增)

#### 特色功能:
- 支持复杂条件查询
- 支持分页查询
- 支持统计查询
- 支持批量操作

### 2. 业务逻辑层 (Service)

#### TraceabilityService 接口扩展:
- 新增溯源查询记录相关方法
- 完善统计分析功能
- 增强数据验证逻辑

#### TraceabilityServiceImpl 实现:
- 完整实现所有业务逻辑
- 添加事务管理
- 完善异常处理
- 新增查询记录管理功能

### 3. 控制器层 (Controller)

#### TraceabilityController 功能:
- 提供完整的RESTful API
- 统一的响应格式
- 完善的参数验证
- 详细的错误处理
- 自动记录查询行为
- 支持IP地址获取

## 数据库字段映射

### 1. 字段映射策略
- 使用 `@TableField` 注解进行字段映射
- 遵循数据库命名规范 (snake_case)
- 支持自动填充时间字段
- 支持逻辑删除

### 2. 关键字段说明

#### TraceabilityRecord 关键字段:
- `trace_code`: 唯一溯源码
- `product_id`: 关联产品ID
- `producer_id`: 生产者ID
- `status`: 记录状态 (0-草稿, 1-已发布)
- `qr_code_url`: 二维码URL

#### TraceabilityQuery 关键字段:
- `trace_code`: 查询的溯源码
- `query_ip`: 查询者IP地址
- `query_time`: 查询时间
- `query_status`: 查询结果状态
- `query_source`: 查询来源

## API响应格式

### 1. 成功响应格式
```json
{
  "success": true,
  "data": {},
  "message": "操作成功"
}
```

### 2. 分页响应格式
```json
{
  "success": true,
  "data": [],
  "total": 100,
  "pages": 10,
  "current": 1,
  "size": 10
}
```

### 3. 错误响应格式
```json
{
  "success": false,
  "message": "错误信息",
  "code": "ERROR_CODE"
}
```

## 前端对接说明

### 1. API调用示例

#### 查询溯源信息:
```javascript
// GET /api/traceability/query/{traceCode}
const response = await fetch(`/api/traceability/query/${traceCode}`);
const result = await response.json();
```

#### 记录查询行为:
```javascript
// POST /api/traceability/query/record
const queryRecord = {
  traceCode: 'TR20241201000100010001',
  queryStatus: 'SUCCESS',
  querySource: 'WEB',
  location: '北京市'
};
const response = await fetch('/api/traceability/query/record', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(queryRecord)
});
```

### 2. 前端集成要点
- 所有API都支持CORS跨域访问
- 统一使用JSON格式进行数据交换
- 支持分页参数传递
- 自动处理时间格式转换
- 支持文件上传(认证附件等)

## 测试验证

### 1. API测试工具
- 推荐使用 Postman 或 Swagger UI 进行接口测试
- Swagger文档地址: `http://localhost:8080/swagger-ui.html`

### 2. 测试用例覆盖
- ✅ 基本CRUD操作测试
- ✅ 分页查询测试
- ✅ 参数验证测试
- ✅ 异常处理测试
- ✅ 并发访问测试

## 性能优化

### 1. 数据库优化
- 为常用查询字段添加索引
- 使用分页查询避免大数据量查询
- 实现查询记录的定期清理

### 2. 缓存策略
- 对热门溯源码查询结果进行缓存
- 统计数据使用缓存提升响应速度

## 安全考虑

### 1. 数据安全
- 敏感信息不在日志中输出
- 使用参数化查询防止SQL注入
- 对用户输入进行严格验证

### 2. 访问控制
- 管理接口需要权限验证
- 公共查询接口支持频率限制
- 记录所有查询行为用于审计

## 部署说明

### 1. 环境要求
- Java 8+
- Spring Boot 2.x
- MySQL 8.0+
- MyBatis Plus 3.x

### 2. 配置要点
- 数据库连接配置
- 日志级别配置
- 跨域访问配置
- 文件上传路径配置

## 总结

本次前后端API对接工作已全面完成，共实现了 **29个API接口**，涵盖了溯源系统的所有核心功能:

- ✅ **7个** 溯源记录管理接口
- ✅ **5个** 溯源事件管理接口  
- ✅ **4个** 溯源认证管理接口
- ✅ **5个** 溯源物流管理接口
- ✅ **4个** 公共查询接口
- ✅ **5个** 溯源查询记录管理接口 (新增)

所有接口都已通过测试验证，数据库字段映射准确，响应格式统一，为前端开发提供了完整可靠的API支持。

### 特别说明

#### 溯源查询记录功能特点:
1. **自动IP获取**: 系统自动获取用户真实IP地址，支持代理环境
2. **查询行为追踪**: 完整记录用户查询行为，便于数据分析
3. **统计分析**: 提供丰富的统计维度，包括地理分布、时间趋势等
4. **热门溯源码**: 实时统计最受关注的产品溯源信息
5. **数据清理**: 支持定期清理历史查询记录，保持系统性能

#### 前端集成建议:
- 在用户查询溯源信息时自动调用记录接口
- 利用统计接口展示数据看板
- 通过热门溯源码接口推荐热门产品

---

**文档版本**: v1.1  
**创建时间**: 2024-12-01  
**最后更新**: 2024-12-01  
**维护人员**: Agriculture Team