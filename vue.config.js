const path = require('path')

function resolve(dir) {
  return path.join(__dirname, dir)
}

module.exports = {
  // 生产环境公共路径配置
  publicPath: process.env.NODE_ENV === 'production' ? '/' : '/',

  // 输出目录
  outputDir: 'dist',

  // 静态资源目录
  assetsDir: 'static',

  // 性能优化配置
  productionSourceMap: false, // 生产环境不生成source map
  
  // 代码分割优化
  configureWebpack: {
    optimization: {
      splitChunks: {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            name: 'chunk-vendors',
            test: /[\\/]node_modules[\\/]/,
            priority: 10,
            chunks: 'initial'
          },
          elementUI: {
            name: 'chunk-elementUI',
            priority: 20,
            test: /[\\/]node_modules[\\/]_?element-ui(.*)/
          },
          echarts: {
            name: 'chunk-echarts',
            priority: 20,
            test: /[\\/]node_modules[\\/]_?echarts(.*)/
          }
        }
      }
    },
    module: {
      rules: [
        {
          test: /\.js$/,
          include: [
            /node_modules\/vue-echarts/,
            /node_modules\/echarts/,
            /node_modules\/.+\/node_modules\/vue-echarts/
          ],
          sideEffects: true,
          use: {
            loader: 'babel-loader',
            options: {
              sourceType: 'unambiguous',
              presets: [
                ['@babel/preset-env', {
                  targets: {
                    browsers: '> 0.5%, last 2 versions, not dead'
                  }
                }]
              ],
              plugins: [
                '@babel/plugin-transform-optional-chaining'
              ]
            }
          }
        }
      ]
    },
    resolve: {
      alias: {
        'vue$': 'vue/dist/vue.esm.js',
        '@': resolve('src')
      }
    }
  },
  
  // 预加载配置
  chainWebpack: config => {
    // 预加载关键资源
    config.plugin('preload').tap(options => {
      options[0] = {
        rel: 'preload',
        include: 'initial',
        fileBlacklist: [/\.map$/, /hot-update\.js$/]
      }
      return options
    })
    
    // 预获取非关键资源
    config.plugin('prefetch').tap(options => {
      options[0].fileBlacklist = options[0].fileBlacklist || []
      options[0].fileBlacklist.push(/\.map$/, /hot-update\.js$/)
      return options
    })
    
    // 图片压缩优化
    config.module
      .rule('images')
      .use('url-loader')
      .loader('url-loader')
      .tap(options => Object.assign(options, { limit: 10240 }))

    // SVG图标优化
    config.module
      .rule('svg')
      .exclude.add(resolve('src/assets/icons'))
      .end()

    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/assets/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end()

    // 字体文件优化
    config.module
      .rule('fonts')
      .use('url-loader')
      .loader('url-loader')
      .tap(options => Object.assign(options, {
        limit: 8192,
        fallback: 'file-loader',
        name: 'fonts/[name].[hash:8].[ext]'
      }))
    
    config.resolve.alias
      .set('vue', 'vue/dist/vue.esm.js')
      .set('@', resolve('src'))
  },
  
  devServer: {
    port: 8200,
    host: 'localhost',
    // 重新添加代理配置，解决CORS和请求头过大问题
    proxy: {
      '/weather/api/': {
        target: 'http://apis.juhe.cn',
        changeOrigin: true,
        pathRewrite: {
          '^/weather/api/': ''
        },
        headers: {
          Referer: 'http://apis.juhe.cn',
          Host: 'apis.juhe.cn'
        },
        onProxyReq: function(proxyReq, _req) {
          console.log('聚合数据API代理请求:', proxyReq.path);
          console.log('请求头:', JSON.stringify(proxyReq.getHeaders()));
        },
        onProxyRes: function(proxyRes, _req, _res) {
          console.log('聚合数据API代理响应状态:', proxyRes.statusCode);
        }
      },
      '/api': {
        target: process.env.VUE_APP_BASE_API || 'http://localhost:8081',
        changeOrigin: true,
        // 保留认证相关信息
        withCredentials: true,
        // 不覆盖特定请求头
        onProxyReq: function(proxyReq, _req) {
          // 从原始请求中复制认证头
          const authHeader = _req.headers['authorization'];
          if (authHeader) {
            proxyReq.setHeader('Authorization', authHeader);
          }

          // 保留原始Content-Type
          const contentType = _req.headers['content-type'];
          if (contentType) {
            proxyReq.setHeader('Content-Type', contentType);
          }

          // 添加用户ID头
          const userIdHeader = _req.headers['x-user-id'];
          if (userIdHeader) {
            proxyReq.setHeader('X-User-Id', userIdHeader);
          }

          // 打印调试信息（仅在开发环境）
          if (process.env.NODE_ENV === 'development') {
            console.log('代理请求头:', JSON.stringify(proxyReq.getHeaders()));
            console.log('代理请求目标:', proxyReq.path);
          }
        },
        // 添加响应处理，观察返回的响应
        onProxyRes: function(proxyRes, _req, _res) {
          if (process.env.NODE_ENV === 'development') {
            console.log('代理响应状态:', proxyRes.statusCode);
            console.log('代理响应头:', JSON.stringify(proxyRes.headers));
          }
        }
      },
      '/weather/qweather': {
        target: 'https://devapi.qweather.com',
        changeOrigin: true,
        pathRewrite: {
          '^/weather/qweather': ''
        },
        headers: {
          // 添加空的Accept-Encoding，让node处理解压缩
          'Accept-Encoding': 'gzip, deflate, br'
        },
        onProxyReq: function(proxyReq, req, _res) {
          // 保留URL参数中的key，不再转换为Authorization头
          // 因为公共API支持通过URL参数传递key
          // 关键：移除之前的Authorization头设置
          
          console.log('和风天气API代理请求:', proxyReq.path);
          console.log('请求头:', JSON.stringify(proxyReq.getHeaders()));
        },
        onProxyRes: function(proxyRes, _req, _res) {
          console.log('和风天气API代理响应状态:', proxyRes.statusCode);
        }
      }
    },
    // 添加更详细的错误输出
    overlay: {
      warnings: true,
      errors: true
    }
  },

  transpileDependencies: ['vue-echarts', 'echarts', 'resize-detector']
}