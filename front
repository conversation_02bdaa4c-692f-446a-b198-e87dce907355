"use client";

import React, { useState, useEffect, useRef } from 'react';
import { motion, useScroll, useTransform, useInView, useSpring } from 'framer-motion';
import {
  LayoutDashboard,
  Package,
  ShoppingCart,
  FileText,
  Store,
  BarChart3,
  TrendingUp,
  Users,
  Calendar,
  Award,
  Leaf,
  Sprout,
  TreePine,
  Recycle,
  Menu,
  X,
  ChevronLeft,
  ArrowRight,
  CheckCircle2,
  Clock,
  Circle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';

interface SidebarProps {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  isCollapsed: boolean;
  setIsCollapsed: (collapsed: boolean) => void;
  activeItem: string;
  setActiveItem: (item: string) => void;
}

interface StatCardProps {
  icon: React.ReactNode;
  title: string;
  value: string;
  change: string;
  trend: 'up' | 'down';
}

interface QuickActionProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  color: string;
}

interface RoadmapItem {
  title: string;
  description: string;
  status: "completed" | "in-progress" | "upcoming";
  quarter: string;
  progress: number;
  tasks: string[];
}

const SidebarComponent: React.FC<SidebarProps> = ({
  isOpen,
  setIsOpen,
  isCollapsed,
  setIsCollapsed,
  activeItem,
  setActiveItem
}) => {
  const menuItems = [
    { name: '销售仪表板', icon: LayoutDashboard, href: '/seller/dashboard' },
    { name: '产品管理', icon: Package, href: '/seller/products' },
    { name: '订单管理', icon: ShoppingCart, href: '/seller/orders' },
    { name: '溯源管理', icon: FileText, href: '/seller/traceability' },
    { name: '店铺管理', icon: Store, href: '/seller/shop' },
    { name: '数据分析', icon: BarChart3, href: '/seller/analytics' },
  ];

  return (
    <>
      {/* Mobile Hamburger Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="lg:hidden fixed top-4 left-4 z-50 p-2 rounded-lg bg-green-800 text-white hover:bg-green-700 transition-colors duration-200 shadow-lg"
      >
        {isOpen ? <X size={24} /> : <Menu size={24} />}
      </button>

      {/* Mobile Overlay */}
      {isOpen && (
        <div
          className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-30 transition-opacity duration-300"
          onClick={() => setIsOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div
        className={`fixed top-0 left-0 h-full bg-gradient-to-b from-green-900 via-green-800 to-green-900 border-r border-green-700 transition-all duration-300 ease-in-out z-40 ${
          isOpen ? 'translate-x-0' : '-translate-x-full'
        } lg:translate-x-0 ${
          isCollapsed ? 'lg:w-20' : 'lg:w-64'
        } w-64 shadow-2xl`}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-green-700">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg flex items-center justify-center">
              <Leaf className="text-white w-5 h-5" />
            </div>
            {!isCollapsed && (
              <h1 className="text-xl font-bold text-white tracking-wide">
                绿色销售中心
              </h1>
            )}
          </div>

          {/* Desktop Collapse Button */}
          <button
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="hidden lg:block p-1.5 rounded-lg hover:bg-green-700 text-green-400 hover:text-white transition-colors duration-200"
          >
            <ChevronLeft
              size={20}
              className={`transition-transform duration-300 ${
                isCollapsed ? 'rotate-180' : ''
              }`}
            />
          </button>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4 space-y-2">
          {menuItems.map((item) => {
            const Icon = item.icon;
            const isActive = activeItem === item.name;

            return (
              <button
                key={item.name}
                onClick={() => setActiveItem(item.name)}
                className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 group ${
                  isActive
                    ? 'bg-gradient-to-r from-green-600 to-emerald-600 text-white shadow-lg'
                    : 'text-green-300 hover:bg-green-700 hover:text-white'
                }`}
              >
                <Icon
                  size={20}
                  className={`transition-colors duration-200 ${
                    isActive ? 'text-white' : 'text-green-400 group-hover:text-white'
                  }`}
                />
                {!isCollapsed && (
                  <span className="font-medium transition-colors duration-200">
                    {item.name}
                  </span>
                )}

                {/* Active indicator */}
                {isActive && !isCollapsed && (
                  <div className="ml-auto w-2 h-2 bg-white rounded-full opacity-80" />
                )}
              </button>
            );
          })}
        </nav>

        {/* Footer */}
        {!isCollapsed && (
          <div className="p-4 border-t border-green-700">
            <div className="flex items-center space-x-3 px-4 py-3">
              <div className="w-8 h-8 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full flex items-center justify-center">
                <span className="text-white font-semibold text-sm">销</span>
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-white truncate">
                  销售管理员
                </p>
                <p className="text-xs text-green-400 truncate">
                  <EMAIL>
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
};

const StatCard: React.FC<StatCardProps> = ({ icon, title, value, change, trend }) => {
  return (
    <Card className="p-6 bg-white/80 backdrop-blur-sm border-green-200 hover:shadow-lg transition-all duration-300">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-3 bg-green-100 rounded-lg text-green-600">
            {icon}
          </div>
          <div>
            <p className="text-sm text-gray-600">{title}</p>
            <p className="text-2xl font-bold text-gray-900">{value}</p>
          </div>
        </div>
        <div className={`flex items-center space-x-1 ${trend === 'up' ? 'text-green-600' : 'text-red-600'}`}>
          <TrendingUp className={`w-4 h-4 ${trend === 'down' ? 'rotate-180' : ''}`} />
          <span className="text-sm font-medium">{change}</span>
        </div>
      </div>
    </Card>
  );
};

const QuickAction: React.FC<QuickActionProps> = ({ icon, title, description, color }) => {
  return (
    <motion.div
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      className="cursor-pointer"
    >
      <Card className="p-6 bg-white/80 backdrop-blur-sm border-green-200 hover:shadow-lg transition-all duration-300">
        <div className="flex items-start space-x-4">
          <div className={`p-3 rounded-lg ${color}`}>
            {icon}
          </div>
          <div className="flex-1">
            <h3 className="font-semibold text-gray-900 mb-1">{title}</h3>
            <p className="text-sm text-gray-600">{description}</p>
          </div>
          <ArrowRight className="w-5 h-5 text-gray-400" />
        </div>
      </Card>
    </motion.div>
  );
};

const StatCounter: React.FC<{ icon: React.ReactNode; value: number; label: string; suffix: string; delay: number }> = ({
  icon,
  value,
  label,
  suffix,
  delay
}) => {
  const countRef = useRef(null);
  const isInView = useInView(countRef, { once: false });
  const [hasAnimated, setHasAnimated] = useState(false);

  const springValue = useSpring(0, {
    stiffness: 50,
    damping: 10,
  });

  useEffect(() => {
    if (isInView && !hasAnimated) {
      springValue.set(value);
      setHasAnimated(true);
    } else if (!isInView && hasAnimated) {
      springValue.set(0);
      setHasAnimated(false);
    }
  }, [isInView, value, springValue, hasAnimated]);

  const displayValue = useTransform(springValue, (latest) => Math.floor(latest));

  return (
    <motion.div
      className="bg-white/50 backdrop-blur-sm p-6 rounded-xl flex flex-col items-center text-center group hover:bg-white transition-colors duration-300"
      variants={{
        hidden: { opacity: 0, y: 20 },
        visible: {
          opacity: 1,
          y: 0,
          transition: { duration: 0.6, delay },
        },
      }}
      whileHover={{ y: -5, transition: { duration: 0.2 } }}
    >
      <motion.div
        className="w-14 h-14 rounded-full bg-green-100 flex items-center justify-center mb-4 text-green-600 group-hover:bg-green-200 transition-colors duration-300"
        whileHover={{ rotate: 360, transition: { duration: 0.8 } }}
      >
        {icon}
      </motion.div>
      <motion.div ref={countRef} className="text-3xl font-bold text-gray-900 flex items-center">
        <motion.span>{displayValue}</motion.span>
        <span>{suffix}</span>
      </motion.div>
      <p className="text-gray-600 text-sm mt-1">{label}</p>
      <motion.div className="w-10 h-0.5 bg-green-600 mt-3 group-hover:w-16 transition-all duration-300" />
    </motion.div>
  );
};

const getStatusColor = (status: RoadmapItem["status"]) => {
  switch (status) {
    case "completed":
      return "bg-green-500/10 text-green-600";
    case "in-progress":
      return "bg-blue-500/10 text-blue-600";
    case "upcoming":
      return "bg-gray-500/10 text-gray-600";
    default:
      return "bg-gray-500/10 text-gray-600";
  }
};

const getStatusIcon = (status: RoadmapItem["status"]) => {
  switch (status) {
    case "completed":
      return <CheckCircle2 className="w-4 h-4 text-green-600" />;
    case "in-progress":
      return <Clock className="w-4 h-4 text-blue-600" />;
    case "upcoming":
      return <Circle className="w-4 h-4 text-gray-600" />;
    default:
      return <Circle className="w-4 h-4 text-gray-600" />;
  }
};

const SellerDashboard = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [activeItem, setActiveItem] = useState('销售仪表板');
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef<HTMLDivElement>(null);
  const statsRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(sectionRef, { once: false, amount: 0.1 });
  const isStatsInView = useInView(statsRef, { once: false, amount: 0.3 });

  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ["start end", "end start"],
  });

  const y1 = useTransform(scrollYProgress, [0, 1], [0, -50]);
  const y2 = useTransform(scrollYProgress, [0, 1], [0, 50]);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const stats = [
    { icon: <Award />, value: 1250, label: "总销售额 (万元)", suffix: "" },
    { icon: <Users />, value: 856, label: "活跃客户", suffix: "+" },
    { icon: <Package />, value: 124, label: "在售产品", suffix: "" },
    { icon: <Calendar />, value: 98, label: "溯源覆盖率", suffix: "%" },
  ];

  const roadmapData: RoadmapItem[] = [
    {
      title: "产品溯源系统",
      description: "建立完整的有机产品溯源体系，确保产品质量可追踪",
      status: "completed",
      quarter: "第一阶段",
      progress: 100,
      tasks: [
        "溯源码生成系统",
        "产品信息录入",
        "质检报告管理",
        "批次追踪功能",
      ],
    },
    {
      title: "智能销售分析",
      description: "基于数据的销售分析和预测系统，优化销售策略",
      status: "in-progress",
      quarter: "第二阶段",
      progress: 75,
      tasks: [
        "销售数据分析",
        "客户行为分析",
        "库存预警系统",
        "销售预测模型",
      ],
    },
    {
      title: "生态认证集成",
      description: "集成多种有机认证标准，提升产品可信度",
      status: "upcoming",
      quarter: "第三阶段",
      progress: 0,
      tasks: [
        "有机认证对接",
        "绿色标准验证",
        "环保评级系统",
        "可持续发展指标",
      ],
    },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.6, ease: "easeOut" },
    },
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-emerald-50 to-green-100">
      <SidebarComponent
        isOpen={isOpen}
        setIsOpen={setIsOpen}
        isCollapsed={isCollapsed}
        setIsCollapsed={setIsCollapsed}
        activeItem={activeItem}
        setActiveItem={setActiveItem}
      />

      {/* Main Content */}
      <div
        className={`transition-all duration-300 ease-in-out ${
          isCollapsed ? 'lg:ml-20' : 'lg:ml-64'
        }`}
      >
        <div className="p-8 ml-16 lg:ml-0">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="mb-8"
          >
            <div className="flex items-center space-x-3 mb-2">
              <Leaf className="w-8 h-8 text-green-600" />
              <h1 className="text-3xl font-bold text-gray-900">有机农产品销售中心</h1>
            </div>
            <p className="text-gray-600">
              欢迎来到绿色销售管理平台，这里是您管理有机农产品销售的智能中心
            </p>
          </motion.div>

          {/* Stats Cards */}
          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
            initial="hidden"
            animate={isVisible ? "visible" : "hidden"}
            variants={containerVariants}
          >
            <StatCard
              icon={<TrendingUp className="w-6 h-6" />}
              title="本月销售额"
              value="¥125,000"
              change="+12.5%"
              trend="up"
            />
            <StatCard
              icon={<ShoppingCart className="w-6 h-6" />}
              title="待处理订单"
              value="23"
              change="-5.2%"
              trend="down"
            />
            <StatCard
              icon={<Package className="w-6 h-6" />}
              title="库存预警"
              value="8"
              change="+2"
              trend="up"
            />
            <StatCard
              icon={<Users className="w-6 h-6" />}
              title="新增客户"
              value="156"
              change="+18.3%"
              trend="up"
            />
          </motion.div>

          {/* Quick Actions */}
          <motion.div
            className="mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
              <Sprout className="w-5 h-5 text-green-600 mr-2" />
              快速操作
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <QuickAction
                icon={<Package className="w-6 h-6 text-white" />}
                title="添加新产品"
                description="快速添加有机农产品到您的商店"
                color="bg-green-600"
              />
              <QuickAction
                icon={<FileText className="w-6 h-6 text-white" />}
                title="生成溯源码"
                description="为产品批次生成唯一溯源标识"
                color="bg-emerald-600"
              />
              <QuickAction
                icon={<BarChart3 className="w-6 h-6 text-white" />}
                title="查看销售报表"
                description="分析销售数据和趋势"
                color="bg-teal-600"
              />
            </div>
          </motion.div>

          {/* About Section with Stats */}
          <section
            ref={sectionRef}
            className="w-full py-16 px-4 bg-gradient-to-b from-green-50 to-emerald-50 text-gray-800 overflow-hidden relative rounded-2xl mb-8"
          >
            {/* Decorative background elements */}
            <motion.div
              className="absolute top-10 left-10 w-32 h-32 rounded-full bg-green-200/30 blur-3xl"
              style={{ y: y1 }}
            />
            <motion.div
              className="absolute bottom-10 right-10 w-40 h-40 rounded-full bg-emerald-200/30 blur-3xl"
              style={{ y: y2 }}
            />

            <motion.div
              className="container mx-auto max-w-4xl relative z-10"
              initial="hidden"
              animate={isInView ? "visible" : "hidden"}
              variants={containerVariants}
            >
              <motion.div className="flex flex-col items-center mb-8" variants={itemVariants}>
                <motion.span
                  className="text-green-600 font-medium mb-2 flex items-center gap-2"
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                >
                  <TreePine className="w-4 h-4" />
                  关于我们的平台
                </motion.span>
                <h2 className="text-3xl md:text-4xl font-light mb-4 text-center">绿色农业销售平台</h2>
                <motion.div
                  className="w-24 h-1 bg-green-600"
                  initial={{ width: 0 }}
                  animate={{ width: 96 }}
                  transition={{ duration: 1, delay: 0.5 }}
                />
              </motion.div>

              <motion.p className="text-center max-w-2xl mx-auto mb-12 text-gray-700" variants={itemVariants}>
                我们致力于为有机农产品销售者提供全面的数字化管理解决方案，
                通过先进的溯源技术和智能分析，帮助您建立可信赖的绿色品牌。
              </motion.p>

              {/* Stats Section */}
              <motion.div
                ref={statsRef}
                className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6"
                initial="hidden"
                animate={isStatsInView ? "visible" : "hidden"}
                variants={containerVariants}
              >
                {stats.map((stat, index) => (
                  <StatCounter
                    key={index}
                    icon={stat.icon}
                    value={stat.value}
                    label={stat.label}
                    suffix={stat.suffix}
                    delay={index * 0.1}
                  />
                ))}
              </motion.div>
            </motion.div>
          </section>

          {/* Roadmap Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="mb-8"
          >
            <div className="flex items-center mb-6">
              <Recycle className="w-6 h-6 text-green-600 mr-3" />
              <h2 className="text-2xl font-bold text-gray-900">平台发展路线图</h2>
            </div>
            <p className="text-gray-600 mb-8">
              跟随我们的发展历程，了解平台功能的持续改进和创新。
            </p>

            <div className="max-w-4xl">
              {roadmapData.map((item, index) => (
                <motion.div
                  key={item.title}
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="mb-6 relative"
                >
                  <div className="absolute left-0 top-0 bottom-0 w-px bg-green-200 -ml-4 hidden md:block">
                    <div
                      className={`w-3 h-3 rounded-full -ml-[5px] mt-1 ${
                        item.status === "completed"
                          ? "bg-green-500"
                          : item.status === "in-progress"
                            ? "bg-blue-500"
                            : "bg-gray-300"
                      }`}
                    />
                  </div>

                  <Card className="p-6 ml-0 md:ml-4 hover:shadow-lg transition-shadow bg-white/80 backdrop-blur-sm border-green-200">
                    <div className="flex flex-col md:flex-row gap-4 mb-4">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="text-xl font-semibold text-gray-900">{item.title}</h3>
                          <Badge
                            variant="secondary"
                            className={getStatusColor(item.status)}
                          >
                            {item.status === 'completed' ? '已完成' :
                             item.status === 'in-progress' ? '进行中' : '计划中'}
                          </Badge>
                        </div>
                        <p className="text-gray-600 mb-4">
                          {item.description}
                        </p>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-gray-500">
                        {getStatusIcon(item.status)}
                        <span>{item.quarter}</span>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div className="flex items-center gap-2">
                        <Progress value={item.progress} className="h-2 flex-1" />
                        <span className="text-sm text-gray-500 w-12 text-right">
                          {item.progress}%
                        </span>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-2">
                        {item.tasks.map((task, taskIndex) => (
                          <motion.div
                            key={task}
                            initial={{ opacity: 0 }}
                            whileInView={{ opacity: 1 }}
                            viewport={{ once: true }}
                            transition={{ delay: taskIndex * 0.05 + index * 0.1 + 0.2 }}
                            className="flex items-center gap-2 text-sm text-gray-600"
                          >
                            <ArrowRight className="w-4 h-4 flex-shrink-0 text-green-500" />
                            <span>{task}</span>
                          </motion.div>
                        ))}
                      </div>
                    </div>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* CTA Section */}
          <motion.div
            className="bg-gradient-to-r from-green-600 to-emerald-600 text-white p-8 rounded-xl flex flex-col md:flex-row items-center justify-between gap-6"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.5 }}
          >
            <div className="flex-1">
              <h3 className="text-2xl font-medium mb-2">准备开始您的绿色销售之旅？</h3>
              <p className="text-green-100">让我们一起打造可持续的农业未来。</p>
            </div>
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button
                variant="secondary"
                size="lg"
                className="bg-white text-green-600 hover:bg-green-50"
              >
                开始使用 <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default SellerDashboard;
