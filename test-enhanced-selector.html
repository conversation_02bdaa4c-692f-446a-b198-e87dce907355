<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试增强选择器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e4e7ed;
            border-radius: 6px;
        }
        .test-title {
            color: #409eff;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
        }
        .api-test {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        .success {
            color: #67c23a;
        }
        .error {
            color: #f56c6c;
        }
        .loading {
            color: #409eff;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f0f9ff;
            border-left: 4px solid #409eff;
            font-family: monospace;
            font-size: 12px;
        }
        button {
            background: #409eff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #66b1ff;
        }
        button:disabled {
            background: #c0c4cc;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>SFAP农品汇平台 - 增强选择器功能测试</h1>
        
        <div class="test-section">
            <div class="test-title">1. AI服务连接测试</div>
            <div class="api-test">
                <button onclick="testAIService()">测试AI服务连接</button>
                <div id="ai-service-result"></div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">2. 爬取数据API测试</div>
            
            <div class="api-test">
                <button onclick="testCategories()">获取分类列表</button>
                <div id="categories-result"></div>
            </div>
            
            <div class="api-test">
                <button onclick="testRegions()">获取地区列表</button>
                <div id="regions-result"></div>
            </div>
            
            <div class="api-test">
                <button onclick="testProducts()">搜索产品</button>
                <div id="products-result"></div>
            </div>
            
            <div class="api-test">
                <button onclick="testPriceHistory()">获取历史价格</button>
                <div id="price-history-result"></div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">3. 数据验证测试</div>
            
            <div class="api-test">
                <button onclick="testDataValidation()">验证数据可用性</button>
                <div id="data-validation-result"></div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">4. 集成预测测试</div>
            
            <div class="api-test">
                <button onclick="testRNNPrediction()">RNN预测（使用爬取数据）</button>
                <div id="rnn-prediction-result"></div>
            </div>
            
            <div class="api-test">
                <button onclick="testARIMAPrediction()">ARIMA预测（使用爬取数据）</button>
                <div id="arima-prediction-result"></div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">5. 性能测试</div>
            
            <div class="api-test">
                <button onclick="testPerformance()">并发请求测试</button>
                <div id="performance-result"></div>
            </div>
        </div>
    </div>

    <script>
        const AI_SERVICE_URL = 'http://localhost:5000';
        
        function showResult(elementId, status, message, data = null) {
            const element = document.getElementById(elementId);
            const statusClass = status === 'success' ? 'success' : status === 'error' ? 'error' : 'loading';
            
            let html = `<span class="${statusClass}">${message}</span>`;
            if (data) {
                html += `<div class="result">${JSON.stringify(data, null, 2)}</div>`;
            }
            
            element.innerHTML = html;
        }

        async function testAIService() {
            showResult('ai-service-result', 'loading', '正在测试AI服务连接...');
            
            try {
                const response = await fetch(`${AI_SERVICE_URL}/api/v1/health`);
                const data = await response.json();
                
                if (response.ok) {
                    showResult('ai-service-result', 'success', 'AI服务连接成功', data);
                } else {
                    showResult('ai-service-result', 'error', 'AI服务响应错误', data);
                }
            } catch (error) {
                showResult('ai-service-result', 'error', `连接失败: ${error.message}`);
            }
        }

        async function testCategories() {
            showResult('categories-result', 'loading', '正在获取分类列表...');
            
            try {
                const response = await fetch(`${AI_SERVICE_URL}/api/v1/crawl_data/categories`);
                const data = await response.json();
                
                if (response.ok) {
                    const categories = data.data.categories;
                    showResult('categories-result', 'success', 
                        `获取到 ${categories.length} 个分类`, 
                        categories.slice(0, 5) // 只显示前5个
                    );
                } else {
                    showResult('categories-result', 'error', '获取分类失败', data);
                }
            } catch (error) {
                showResult('categories-result', 'error', `请求失败: ${error.message}`);
            }
        }

        async function testRegions() {
            showResult('regions-result', 'loading', '正在获取地区列表...');
            
            try {
                const response = await fetch(`${AI_SERVICE_URL}/api/v1/crawl_data/regions`);
                const data = await response.json();
                
                if (response.ok) {
                    const regions = data.data.regions;
                    showResult('regions-result', 'success', 
                        `获取到 ${regions.length} 个地区`, 
                        regions.slice(0, 5) // 只显示前5个
                    );
                } else {
                    showResult('regions-result', 'error', '获取地区失败', data);
                }
            } catch (error) {
                showResult('regions-result', 'error', `请求失败: ${error.message}`);
            }
        }

        async function testProducts() {
            showResult('products-result', 'loading', '正在搜索产品...');
            
            try {
                const response = await fetch(`${AI_SERVICE_URL}/api/v1/crawl_data/products?keyword=苹果&limit=5`);
                const data = await response.json();
                
                if (response.ok) {
                    const products = data.data.products;
                    showResult('products-result', 'success', 
                        `找到 ${products.length} 个相关产品`, 
                        products
                    );
                } else {
                    showResult('products-result', 'error', '搜索产品失败', data);
                }
            } catch (error) {
                showResult('products-result', 'error', `请求失败: ${error.message}`);
            }
        }

        async function testPriceHistory() {
            showResult('price-history-result', 'loading', '正在获取历史价格...');
            
            try {
                const response = await fetch(`${AI_SERVICE_URL}/api/v1/crawl_data/prices/history?product_name=苹果&limit=5`);
                const data = await response.json();
                
                if (response.ok) {
                    const historyData = data.data.history_data;
                    const qualityReport = data.data.quality_report;
                    showResult('price-history-result', 'success', 
                        `获取到 ${historyData.length} 条历史数据，质量评分: ${qualityReport.quality_score}%`, 
                        { historyData: historyData.slice(0, 3), qualityReport }
                    );
                } else {
                    showResult('price-history-result', 'error', '获取历史价格失败', data);
                }
            } catch (error) {
                showResult('price-history-result', 'error', `请求失败: ${error.message}`);
            }
        }

        async function testDataValidation() {
            showResult('data-validation-result', 'loading', '正在验证数据可用性...');
            
            try {
                // 先获取历史数据来验证
                const response = await fetch(`${AI_SERVICE_URL}/api/v1/crawl_data/prices/history?product_name=苹果&region_name=山东&limit=1`);
                const data = await response.json();
                
                if (response.ok) {
                    const historyData = data.data.history_data;
                    const qualityReport = data.data.quality_report;
                    
                    const validation = {
                        available: historyData.length > 0,
                        dataCount: historyData.length,
                        qualityScore: qualityReport.quality_score || 0,
                        latestDate: historyData.length > 0 ? historyData[0].date : null
                    };
                    
                    showResult('data-validation-result', 'success', 
                        `数据验证完成: ${validation.available ? '有数据' : '无数据'}`, 
                        validation
                    );
                } else {
                    showResult('data-validation-result', 'error', '数据验证失败', data);
                }
            } catch (error) {
                showResult('data-validation-result', 'error', `请求失败: ${error.message}`);
            }
        }

        async function testRNNPrediction() {
            showResult('rnn-prediction-result', 'loading', '正在进行RNN预测...');
            
            try {
                const predictionData = {
                    category: '苹果',
                    region: '山东省',
                    product_name: '苹果',
                    forecast_days: 3
                };
                
                const response = await fetch(`${AI_SERVICE_URL}/api/v1/predict_rnn`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(predictionData)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    const predictions = data.data.predictions;
                    showResult('rnn-prediction-result', 'success', 
                        `RNN预测成功，生成 ${predictions.length} 个预测点`, 
                        predictions
                    );
                } else {
                    showResult('rnn-prediction-result', 'error', 'RNN预测失败', data);
                }
            } catch (error) {
                showResult('rnn-prediction-result', 'error', `请求失败: ${error.message}`);
            }
        }

        async function testARIMAPrediction() {
            showResult('arima-prediction-result', 'loading', '正在进行ARIMA预测...');
            
            try {
                const predictionData = {
                    category: '苹果',
                    region: '山东省',
                    product_name: '苹果',
                    forecast_days: 3
                };
                
                const response = await fetch(`${AI_SERVICE_URL}/api/v1/predict_arima`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(predictionData)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    const predictions = data.data.predictions;
                    showResult('arima-prediction-result', 'success', 
                        `ARIMA预测成功，生成 ${predictions.length} 个预测点`, 
                        predictions
                    );
                } else {
                    showResult('arima-prediction-result', 'error', 'ARIMA预测失败', data);
                }
            } catch (error) {
                showResult('arima-prediction-result', 'error', `请求失败: ${error.message}`);
            }
        }

        async function testPerformance() {
            showResult('performance-result', 'loading', '正在进行性能测试...');
            
            const startTime = Date.now();
            const promises = [];
            
            // 并发发送5个请求
            for (let i = 0; i < 5; i++) {
                promises.push(fetch(`${AI_SERVICE_URL}/api/v1/crawl_data/categories`));
            }
            
            try {
                const responses = await Promise.all(promises);
                const endTime = Date.now();
                const duration = endTime - startTime;
                
                const successCount = responses.filter(r => r.ok).length;
                
                showResult('performance-result', 'success', 
                    `性能测试完成: ${successCount}/5 个请求成功，耗时 ${duration}ms`, 
                    { duration, successCount, totalRequests: 5 }
                );
            } catch (error) {
                showResult('performance-result', 'error', `性能测试失败: ${error.message}`);
            }
        }

        // 页面加载时自动测试AI服务连接
        window.onload = function() {
            testAIService();
        };
    </script>
</body>
</html>
