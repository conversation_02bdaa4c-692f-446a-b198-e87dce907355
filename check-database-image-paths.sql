-- 智慧农业平台数据库图片路径检查脚本
-- 检查所有表中的图片路径格式是否统一

-- 1. 检查商品表中的图片路径
SELECT '=== 商品图片路径检查 ===' as info;
SELECT 
    id,
    name,
    image,
    CASE 
        WHEN image IS NULL OR image = '' THEN '空路径'
        WHEN image LIKE 'http%' THEN '完整URL'
        WHEN image LIKE '/uploads/images/products/%' THEN '✅ 正确格式'
        WHEN image LIKE '/images/products/%' THEN '❌ 缺少uploads前缀'
        WHEN image LIKE 'uploads/images/products/%' THEN '❌ 缺少/前缀'
        WHEN image LIKE 'images/products/%' THEN '❌ 错误格式'
        WHEN image NOT LIKE '/%' AND image NOT LIKE 'http%' THEN '❌ 只有文件名'
        ELSE '❌ 其他格式'
    END as path_status
FROM product 
WHERE image IS NOT NULL AND image != ''
ORDER BY path_status, id
LIMIT 20;

-- 统计商品图片路径格式
SELECT 
    CASE 
        WHEN image IS NULL OR image = '' THEN '空路径'
        WHEN image LIKE 'http%' THEN '完整URL'
        WHEN image LIKE '/uploads/images/products/%' THEN '✅ 正确格式'
        WHEN image LIKE '/images/products/%' THEN '❌ 缺少uploads前缀'
        WHEN image LIKE 'uploads/images/products/%' THEN '❌ 缺少/前缀'
        WHEN image LIKE 'images/products/%' THEN '❌ 错误格式'
        WHEN image NOT LIKE '/%' AND image NOT LIKE 'http%' THEN '❌ 只有文件名'
        ELSE '❌ 其他格式'
    END as path_format,
    COUNT(*) as count
FROM product 
GROUP BY path_format
ORDER BY count DESC;

-- 2. 检查用户头像路径
SELECT '=== 用户头像路径检查 ===' as info;
SELECT 
    id,
    username,
    avatar,
    CASE 
        WHEN avatar IS NULL OR avatar = '' THEN '空路径'
        WHEN avatar LIKE 'http%' THEN '完整URL'
        WHEN avatar LIKE '/uploads/images/avatars/%' THEN '✅ 正确格式'
        WHEN avatar LIKE '/uploads/avatars/%' THEN '❌ 旧格式'
        WHEN avatar LIKE '/images/avatars/%' THEN '❌ 缺少uploads前缀'
        WHEN avatar LIKE 'uploads/images/avatars/%' THEN '❌ 缺少/前缀'
        WHEN avatar LIKE 'uploads/avatars/%' THEN '❌ 旧格式缺少/前缀'
        WHEN avatar NOT LIKE '/%' AND avatar NOT LIKE 'http%' THEN '❌ 只有文件名'
        ELSE '❌ 其他格式'
    END as path_status
FROM user 
WHERE avatar IS NOT NULL AND avatar != ''
ORDER BY path_status, id
LIMIT 20;

-- 统计用户头像路径格式
SELECT 
    CASE 
        WHEN avatar IS NULL OR avatar = '' THEN '空路径'
        WHEN avatar LIKE 'http%' THEN '完整URL'
        WHEN avatar LIKE '/uploads/images/avatars/%' THEN '✅ 正确格式'
        WHEN avatar LIKE '/uploads/avatars/%' THEN '❌ 旧格式'
        WHEN avatar LIKE '/images/avatars/%' THEN '❌ 缺少uploads前缀'
        WHEN avatar LIKE 'uploads/images/avatars/%' THEN '❌ 缺少/前缀'
        WHEN avatar LIKE 'uploads/avatars/%' THEN '❌ 旧格式缺少/前缀'
        WHEN avatar NOT LIKE '/%' AND avatar NOT LIKE 'http%' THEN '❌ 只有文件名'
        ELSE '❌ 其他格式'
    END as path_format,
    COUNT(*) as count
FROM user 
GROUP BY path_format
ORDER BY count DESC;

-- 3. 检查新闻图片路径
SELECT '=== 新闻图片路径检查 ===' as info;
SELECT 
    id,
    title,
    imageUrl,
    CASE 
        WHEN imageUrl IS NULL OR imageUrl = '' THEN '空路径'
        WHEN imageUrl LIKE 'http%' THEN '完整URL'
        WHEN imageUrl LIKE '/uploads/images/news/%' THEN '✅ 正确格式'
        WHEN imageUrl LIKE '/images/news/%' THEN '❌ 缺少uploads前缀'
        WHEN imageUrl LIKE 'uploads/images/news/%' THEN '❌ 缺少/前缀'
        WHEN imageUrl NOT LIKE '/%' AND imageUrl NOT LIKE 'http%' THEN '❌ 只有文件名'
        ELSE '❌ 其他格式'
    END as path_status
FROM agriculture_news 
WHERE imageUrl IS NOT NULL AND imageUrl != ''
ORDER BY path_status, id
LIMIT 20;

-- 4. 检查百科图片路径（如果表存在）
SELECT '=== 百科图片路径检查 ===' as info;
SELECT 
    id,
    title,
    coverImage,
    CASE 
        WHEN coverImage IS NULL OR coverImage = '' THEN '空路径'
        WHEN coverImage LIKE 'http%' THEN '完整URL'
        WHEN coverImage LIKE '/uploads/images/encyclopedia/%' THEN '✅ 正确格式'
        WHEN coverImage LIKE '/images/encyclopedia/%' THEN '❌ 缺少uploads前缀'
        WHEN coverImage LIKE 'uploads/images/encyclopedia/%' THEN '❌ 缺少/前缀'
        WHEN coverImage NOT LIKE '/%' AND coverImage NOT LIKE 'http%' THEN '❌ 只有文件名'
        ELSE '❌ 其他格式'
    END as path_status
FROM encyclopedia 
WHERE coverImage IS NOT NULL AND coverImage != ''
ORDER BY path_status, id
LIMIT 20;

-- 5. 检查轮播图路径（如果表存在）
SELECT '=== 轮播图路径检查 ===' as info;
SELECT 
    id,
    title,
    imageUrl,
    CASE 
        WHEN imageUrl IS NULL OR imageUrl = '' THEN '空路径'
        WHEN imageUrl LIKE 'http%' THEN '完整URL'
        WHEN imageUrl LIKE '/uploads/images/banners/%' THEN '✅ 正确格式'
        WHEN imageUrl LIKE '/images/banners/%' THEN '❌ 缺少uploads前缀'
        WHEN imageUrl LIKE 'uploads/images/banners/%' THEN '❌ 缺少/前缀'
        WHEN imageUrl NOT LIKE '/%' AND imageUrl NOT LIKE 'http%' THEN '❌ 只有文件名'
        ELSE '❌ 其他格式'
    END as path_status
FROM banner 
WHERE imageUrl IS NOT NULL AND imageUrl != ''
ORDER BY path_status, id
LIMIT 20;

-- 6. 检查商品图片表路径（如果表存在）
SELECT '=== 商品图片表路径检查 ===' as info;
SELECT 
    id,
    productId,
    imageUrl,
    imageType,
    CASE 
        WHEN imageUrl IS NULL OR imageUrl = '' THEN '空路径'
        WHEN imageUrl LIKE 'http%' THEN '完整URL'
        WHEN imageUrl LIKE '/uploads/images/products/%' THEN '✅ 正确格式'
        WHEN imageUrl LIKE '/images/products/%' THEN '❌ 缺少uploads前缀'
        WHEN imageUrl LIKE 'uploads/images/products/%' THEN '❌ 缺少/前缀'
        WHEN imageUrl NOT LIKE '/%' AND imageUrl NOT LIKE 'http%' THEN '❌ 只有文件名'
        ELSE '❌ 其他格式'
    END as path_status
FROM product_image 
WHERE imageUrl IS NOT NULL AND imageUrl != ''
ORDER BY path_status, id
LIMIT 20;

-- 7. 总体统计
SELECT '=== 总体路径格式统计 ===' as info;

-- 需要修复的路径统计
SELECT 
    '商品图片' as table_name,
    COUNT(CASE WHEN image NOT LIKE '/uploads/images/products/%' AND image NOT LIKE 'http%' AND image IS NOT NULL AND image != '' THEN 1 END) as need_fix,
    COUNT(CASE WHEN image LIKE '/uploads/images/products/%' THEN 1 END) as correct_format,
    COUNT(CASE WHEN image IS NOT NULL AND image != '' THEN 1 END) as total_with_image
FROM product

UNION ALL

SELECT 
    '用户头像' as table_name,
    COUNT(CASE WHEN avatar NOT LIKE '/uploads/images/avatars/%' AND avatar NOT LIKE 'http%' AND avatar IS NOT NULL AND avatar != '' THEN 1 END) as need_fix,
    COUNT(CASE WHEN avatar LIKE '/uploads/images/avatars/%' THEN 1 END) as correct_format,
    COUNT(CASE WHEN avatar IS NOT NULL AND avatar != '' THEN 1 END) as total_with_image
FROM user

UNION ALL

SELECT 
    '新闻图片' as table_name,
    COUNT(CASE WHEN imageUrl NOT LIKE '/uploads/images/news/%' AND imageUrl NOT LIKE 'http%' AND imageUrl IS NOT NULL AND imageUrl != '' THEN 1 END) as need_fix,
    COUNT(CASE WHEN imageUrl LIKE '/uploads/images/news/%' THEN 1 END) as correct_format,
    COUNT(CASE WHEN imageUrl IS NOT NULL AND imageUrl != '' THEN 1 END) as total_with_image
FROM agriculture_news;

-- 8. 修复建议
SELECT '=== 修复建议 ===' as info;
SELECT 
    '1. 运行 fix-database-image-paths.sql 脚本修复路径格式' as suggestion
UNION ALL
SELECT 
    '2. 确保所有新上传的图片使用 /uploads/images/[category]/ 格式' as suggestion
UNION ALL
SELECT 
    '3. 前端确保使用环境变量配置的API基础URL' as suggestion
UNION ALL
SELECT 
    '4. 后端确保返回统一的相对路径格式' as suggestion;
