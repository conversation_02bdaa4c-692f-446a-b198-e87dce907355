# SFAP农品汇平台 - 销售者销售中心界面架构规格文档

## 1. 项目概述

### 1.1 项目背景
SFAP农品汇平台是一个专注于农产品溯源的电商平台，本文档设计全新的销售者销售中心界面，为销售者提供完整的店铺管理、产品管理、订单处理、溯源管理等功能。

### 1.2 设计目标
- 提供现代化、直观的销售者管理界面
- 支持完整的农产品电商业务流程
- 强化农产品溯源功能的用户体验
- 实现响应式设计，支持多端访问
- 确保数据安全和权限隔离

### 1.3 技术栈
- **前端**: Vue 2 + Element UI + SCSS
- **后端**: Spring Boot + MyBatis Plus
- **数据库**: MySQL 8.0
- **构建工具**: Webpack
- **包管理**: npm/yarn

## 2. 数据库结构分析

### 2.1 核心业务表

#### 2.1.1 用户相关表
```sql
-- 用户主表
user: id, username, role, user_type, phone, email, status, created_at, updated_at

-- 销售者申请表  
seller_application: id, user_id, name, phone, email, farm_name, status, audit_comment
```

#### 2.1.2 商店管理表
```sql
-- 销售者店铺表
seller_shop: id, seller_id, shop_name, shop_logo, shop_banner, shop_description, 
            business_hours, contact_phone, service_rating, delivery_rating, 
            product_rating, total_sales, total_orders, status

-- 销售者统计表
seller_statistics: id, seller_id, stat_date, daily_sales, daily_orders, 
                   monthly_sales, yearly_sales
```

#### 2.1.3 产品管理表
```sql
-- 产品主表
product: id, name, description, image, price, original_price, stock, sales_count,
         rating, category_id, seller_id, brand, origin, unit, status, 
         has_traceability, trace_code, qr_code_url

-- 产品图片表
product_image: id, product_id, image_url, sort_order

-- 产品属性表
product_attribute: id, product_id, attribute_name, attribute_value
```

#### 2.1.4 订单管理表
```sql
-- 订单主表
order: id, order_no, user_id, seller_id, total_amount, actual_amount, 
       payment_status, order_status, shipping_address, payment_time, 
       shipping_time, delivery_company, tracking_number

-- 订单商品表
order_item: id, order_id, product_id, product_name, product_price, quantity, total_price
```

#### 2.1.5 溯源管理表
```sql
-- 溯源记录主表
traceability_record: id, product_id, trace_code, product_name, farm_name, 
                    producer_id, batch_number, creation_date, harvest_date, 
                    packaging_date, qr_code_url, status

-- 溯源事件表
traceability_event: id, trace_record_id, event_type, event_time, event_description

-- 溯源码表
trace_codes: code, trace_record_id, qr_code_url, generated_time, scan_count

-- 物流信息表
trace_logistics: id, trace_record_id, carrier_name, departure_time, arrival_time, 
                origin, destination, status
```

#### 2.1.6 客户互动表
```sql
-- 产品评价表
product_review: id, product_id, user_id, order_id, rating, content, images, 
               reply_content, reply_time, like_count, status

-- 产品收藏表
product_favorite: id, user_id, product_id, folder_name, created_at

-- 产品点赞表
product_likes: id, user_id, product_id, created_at
```

### 2.2 权限控制设计
- **角色验证**: 基于user.role字段验证seller权限
- **数据隔离**: 所有查询自动添加seller_id过滤条件
- **API权限**: 使用/api/seller/*路径前缀

## 3. 功能模块设计

### 3.1 仪表板概览模块 (Dashboard)

#### 3.1.1 核心功能
- **销售数据概览**: 今日/本月/本年销售额、订单数、访客数
- **店铺评分展示**: 服务评分、物流评分、商品评分
- **待处理事项**: 待发货订单、待回复评价、库存预警
- **快速操作**: 添加产品、处理订单、查看统计

#### 3.1.2 数据来源
```sql
-- 销售统计
SELECT daily_sales, monthly_sales, yearly_sales 
FROM seller_statistics WHERE seller_id = ?

-- 店铺评分
SELECT service_rating, delivery_rating, product_rating, total_sales, total_orders
FROM seller_shop WHERE seller_id = ?

-- 待处理订单
SELECT COUNT(*) FROM order WHERE seller_id = ? AND order_status = 1
```

#### 3.1.3 UI组件设计
- **指标卡片**: 使用Element UI的Card组件展示关键数据
- **图表展示**: 集成ECharts显示销售趋势
- **待办列表**: 使用List组件展示待处理事项

### 3.2 商店管理模块 (Shop Management)

#### 3.2.1 核心功能
- **基本信息设置**: 店铺名称、Logo、横幅、描述
- **营业信息管理**: 营业时间、联系方式、地址
- **店铺装修**: 自定义店铺页面布局（可选功能）
- **评分管理**: 查看各项评分详情和改进建议

#### 3.2.2 表单设计
```javascript
// 店铺信息表单结构
shopForm: {
  shop_name: '',        // 店铺名称
  shop_logo: '',        // 店铺Logo URL
  shop_banner: '',      // 店铺横幅 URL  
  shop_description: '', // 店铺描述
  business_hours: '',   // 营业时间
  contact_phone: '',    // 联系电话
  contact_address: ''   // 联系地址
}
```

#### 3.2.3 API接口设计
```javascript
// 获取店铺信息
GET /api/seller/shop

// 更新店铺信息  
PUT /api/seller/shop

// 上传店铺图片
POST /api/seller/shop/upload
```

### 3.3 产品管理模块 (Product Management)

#### 3.3.1 核心功能
- **产品列表管理**: 支持筛选、搜索、排序、批量操作
- **产品CRUD操作**: 添加、编辑、删除产品
- **库存管理**: 库存查看、更新、预警设置
- **产品上下架**: 批量上下架操作
- **图片管理**: 产品主图和详情图片上传管理

#### 3.3.2 产品列表设计
```javascript
// 产品列表数据结构
productList: [
  {
    id: 1,
    name: '有机苹果',
    image: '/uploads/products/apple.jpg',
    price: 15.80,
    stock: 100,
    sales_count: 50,
    rating: 4.8,
    status: 1, // 1-上架 0-下架
    has_traceability: 1,
    created_at: '2024-01-01 10:00:00'
  }
]
```

#### 3.3.3 产品表单设计
```javascript
// 产品表单结构
productForm: {
  name: '',              // 产品名称
  description: '',       // 产品描述  
  category_id: null,     // 分类ID
  price: 0,             // 售价
  original_price: 0,    // 原价
  stock: 0,             // 库存
  brand: '',            // 品牌
  origin: '',           // 产地
  unit: '',             // 单位
  shelf_life: '',       // 保质期
  storage_method: '',   // 储存方式
  tags: '',             // 标签
  specifications: '',   // 规格说明
  images: []            // 产品图片数组
}
```

### 3.4 订单管理模块 (Order Management)

#### 3.4.1 核心功能
- **订单列表**: 按状态分类显示（待支付、待发货、已发货、已完成、已取消）
- **订单详情**: 查看订单完整信息
- **发货处理**: 填写物流信息、打印发货单
- **订单统计**: 订单数量、金额统计分析

#### 3.4.2 订单状态定义
```javascript
// 订单状态枚举
ORDER_STATUS = {
  0: '待支付',
  1: '待发货', 
  2: '待收货',
  3: '已完成',
  4: '已取消'
}

// 支付状态枚举
PAYMENT_STATUS = {
  0: '未支付',
  1: '已支付', 
  2: '已退款'
}
```

#### 3.4.3 发货处理功能
```javascript
// 发货表单
shippingForm: {
  order_id: null,
  delivery_company: '',    // 物流公司
  tracking_number: '',     // 快递单号
  shipping_time: null,     // 发货时间
  seller_note: ''          // 卖家备注
}
```

### 3.5 溯源管理模块 (Traceability Management)

#### 3.5.1 核心功能（基于实际代码实现）
- **溯源记录创建**: 为产品创建完整溯源信息
- **三环节信息录入**: 生产、加工、流通环节详细记录
- **状态管理**: 草稿、待审核、已发布、已驳回状态流转
- **二维码自动生成**: 发布时自动生成溯源二维码
- **批量操作**: 支持批量状态更新和二维码生成
- **查询统计**: 扫码次数统计和查询记录

#### 3.5.2 实际溯源表单结构（基于TraceabilityRecord实体）
```javascript
// 基于实际数据库表结构的溯源表单
traceabilityForm: {
  // 基本信息
  productId: null,              // 关联产品ID
  productName: '',              // 产品名称
  farmName: '',                 // 农场名称
  producerId: null,             // 生产者ID
  producerName: '',             // 生产者名称

  // 批次和规格信息
  batchNumber: '',              // 批次号
  specification: '',            // 产品规格
  qualityGrade: '',             // 质量等级

  // 日期信息
  creationDate: null,           // 生产开始日期
  harvestDate: null,            // 采收日期
  packagingDate: null,          // 包装日期
  productionDate: null,         // 生产日期
  processingDate: null,         // 加工日期

  // 生产环节详细信息
  soilCondition: '',            // 土壤条件
  weatherConditions: '',        // 天气条件
  irrigationMethod: '',         // 灌溉方式
  fertilizersUsed: '',          // 使用肥料
  pesticidesUsed: '',           // 使用农药
  harvestMethod: '',            // 采收方式

  // 加工环节信息
  processingMethod: '',         // 加工方式
  packagingMaterial: '',        // 包装材料
  qualityTestResults: '',       // 质检结果
  certifications: '',           // 认证信息

  // 流通环节信息
  storageConditions: '',        // 储存条件
  transportationMethod: '',     // 运输方式
  additionalNotes: '',          // 备注信息

  // JSON格式的环节信息（实际存储格式）
  productionInfo: '',           // 生产环节信息(JSON)
  processingInfo: '',           // 加工环节信息(JSON)
  circulationInfo: '',          // 流通环节信息(JSON)

  // 状态和溯源码
  status: 0,                    // 状态：0-草稿，1-待审核，2-已发布
  traceCode: '',                // 溯源码（自动生成）
  qrCodeUrl: '',                // 二维码URL
  sourceType: 'seller_upload'   // 来源类型
}
```

#### 3.5.3 溯源码生成规则（基于实际代码）
```javascript
// 溯源码格式规范（实际22位格式）
// SFAP + 时间戳(10位) + 产品ID(4位) + 随机码(4位) = 22位
// 示例: SFAP25071410001001A1B2

// 溯源码生成逻辑
generateTraceCode(productId) {
  const timestamp = moment().format('YYMMDDHHmm');  // 10位时间戳
  const productSuffix = String(productId % 10000).padStart(4, '0');  // 4位产品ID
  const randomStr = generateRandomString(4);  // 4位随机字符

  return `SFAP${timestamp}${productSuffix}${randomStr}`;
}
```

#### 3.5.4 二维码生成功能（基于实际实现）
```javascript
// 二维码生成API（实际路径）
POST /api/qrcode/generate/{traceCode}

// 批量生成API
POST /api/qrcode/batch/generate-all

// 为特定记录生成
POST /api/qrcode/generate-for-record/{recordId}

// 二维码生成配置
const QR_CONFIG = {
  size: 300,                    // 300x300像素
  format: 'PNG',                // PNG格式
  queryUrl: appDomain + '/trace/' + traceCode,  // 查询URL
  logoPath: '/static/logo.png', // Logo路径
  savePath: '/uploads/qrcodes/' // 保存路径
}

// 生成响应格式
{
  success: true,
  qrCodeUrl: '/uploads/qrcodes/qr_SFAP25071410001001A1B2.png',
  traceCode: 'SFAP25071410001001A1B2'
}
```

### 3.6 客户管理模块 (Customer Management)

#### 3.6.1 核心功能
- **客户列表**: 查看购买过商品的客户信息
- **客户详情**: 客户购买历史、偏好分析
- **评价管理**: 查看和回复客户评价
- **客户反馈**: 处理客户投诉和建议
- **客户分析**: 客户行为数据分析

#### 3.6.2 客户数据结构
```javascript
// 客户信息
customerInfo: {
  user_id: 1,
  nickname: '张三',
  avatar: '/uploads/avatars/user1.jpg',
  phone: '138****8888',
  total_orders: 5,           // 总订单数
  total_amount: 299.50,      // 总消费金额
  last_order_time: '2024-01-15',
  favorite_categories: ['水果', '蔬菜'],
  avg_rating: 4.8            // 平均评分
}
```

#### 3.6.3 评价管理功能
```javascript
// 评价列表
reviewList: [
  {
    id: 1,
    product_name: '有机苹果',
    user_nickname: '张三',
    rating: 5,
    content: '苹果很新鲜，包装也很好',
    images: ['/uploads/reviews/1.jpg'],
    created_at: '2024-01-10 14:30:00',
    reply_content: '',         // 商家回复
    reply_time: null
  }
]

// 回复评价表单
replyForm: {
  review_id: null,
  reply_content: ''
}
```

### 3.7 数据统计模块 (Statistics & Analytics)

#### 3.7.1 核心功能
- **销售报表**: 日/周/月/年销售数据统计
- **产品分析**: 产品销售排行、库存分析
- **客户分析**: 客户来源、购买行为分析
- **收入统计**: 收入趋势、利润分析
- **溯源统计**: 二维码扫描数据统计

#### 3.7.2 统计数据结构
```javascript
// 销售统计
salesStats: {
  today: {
    sales: 1580.50,          // 今日销售额
    orders: 12,              // 今日订单数
    visitors: 156            // 今日访客数
  },
  month: {
    sales: 45600.80,
    orders: 298,
    visitors: 4520
  },
  year: {
    sales: 186500.00,
    orders: 1250,
    visitors: 18600
  }
}

// 产品销售排行
productRanking: [
  {
    product_id: 1,
    product_name: '有机苹果',
    sales_count: 150,
    sales_amount: 2370.00,
    stock: 50
  }
]
```

#### 3.7.3 图表组件设计
```javascript
// 使用ECharts展示数据
chartOptions: {
  // 销售趋势图
  salesTrend: {
    type: 'line',
    data: [], // 时间序列数据
    xAxis: 'date',
    yAxis: 'sales_amount'
  },

  // 产品销售饼图
  productSales: {
    type: 'pie',
    data: [], // 产品销售占比
    radius: ['40%', '70%']
  },

  // 订单状态柱状图
  orderStatus: {
    type: 'bar',
    data: [], // 各状态订单数量
    categories: ['待支付', '待发货', '已发货', '已完成']
  }
}
```

## 4. 技术架构设计

### 4.1 前端架构

#### 4.1.1 项目结构（基于实际代码结构）
```
src/
├── views/
│   ├── seller/                    # 销售者中心页面
│   │   ├── SellerCenter.vue       # 销售者中心主页
│   │   ├── TraceabilityRecords.vue # 溯源记录管理
│   │   └── components/
│   │       └── TraceabilityRecordForm.vue # 溯源表单组件
│   ├── traceability/              # 溯源相关页面
│   │   ├── TraceabilityQuery.vue  # 溯源查询页面
│   │   └── TraceabilityDetail.vue # 溯源详情页面
│   └── mall/                      # 商城页面
│       ├── ProductList.vue        # 产品列表
│       └── ProductDetail.vue      # 产品详情
├── components/
│   ├── traceability/              # 溯源组件
│   │   ├── CreateTraceabilityForm.vue # 创建溯源表单
│   │   └── TraceabilityCard.vue   # 溯源卡片组件
│   ├── common/                    # 通用组件
│   │   ├── ProductCard.vue        # 产品卡片
│   │   └── StatusTag.vue          # 状态标签
│   └── layout/                    # 布局组件
│       ├── Header.vue             # 顶部导航
│       └── Sidebar.vue            # 侧边栏
├── api/                           # API接口
│   ├── traceability.js            # 溯源API
│   ├── auth.js                    # 认证API
│   ├── product.js                 # 产品API
│   └── user.js                    # 用户API
├── utils/                         # 工具类
│   ├── auth.js                    # 认证工具
│   ├── request.js                 # 请求封装
│   └── constants.js               # 常量定义
└── styles/                        # 样式文件
    ├── common.scss                # 通用样式
    ├── variables.scss             # 变量定义
    └── components/                # 组件样式
        ├── traceability.scss      # 溯源样式
        └── product.scss           # 产品样式
```

#### 4.1.2 路由配置（基于实际项目路由）
```javascript
// router/index.js - 实际路由配置
export default [
  {
    path: '/seller-center',
    name: 'SellerCenter',
    component: () => import('@/views/seller/SellerCenter.vue'),
    meta: {
      requiresAuth: true,
      title: '销售者中心',
      roles: ['seller', 'SELLER', 'ROLE_SELLER']
    }
  },
  {
    path: '/seller/traceability',
    name: 'SellerTraceability',
    component: () => import('@/views/seller/TraceabilityRecords.vue'),
    meta: {
      requiresAuth: true,
      title: '溯源管理',
      roles: ['seller', 'SELLER', 'ROLE_SELLER']
    }
  },
  {
    path: '/trace',
    name: 'TraceabilityQuery',
    component: () => import('@/views/traceability/TraceabilityQuery.vue'),
    meta: { title: '溯源查询' }
  },
  {
    path: '/trace/:traceCode',
    name: 'TraceabilityDetail',
    component: () => import('@/views/traceability/TraceabilityDetail.vue'),
    meta: { title: '溯源详情' },
    props: true
  },
  {
    path: '/mall/products',
    name: 'ProductList',
    component: () => import('@/views/mall/ProductList.vue'),
    meta: { title: '产品列表' }
  },
  {
    path: '/mall/product/:id',
    name: 'ProductDetail',
    component: () => import('@/views/mall/ProductDetail.vue'),
    meta: { title: '产品详情' },
    props: true
  }
]
```

#### 4.1.3 权限控制（基于实际认证机制）
```javascript
// router/guards.js - 实际权限控制实现
import { isLoggedIn, getUserInfo } from '@/utils/auth'

router.beforeEach((to, from, next) => {
  // 检查是否需要认证
  if (to.matched.some(record => record.meta.requiresAuth)) {
    if (!isLoggedIn()) {
      next('/login')
      return
    }

    // 检查角色权限
    if (to.meta.roles) {
      const userInfo = getUserInfo()
      const userRole = userInfo?.role || userInfo?.user_type

      if (!to.meta.roles.includes(userRole)) {
        next('/403') // 权限不足
        return
      }
    }
  }

  next()
})

// 销售者权限检查工具
export function checkSellerPermission() {
  const userInfo = getUserInfo()
  if (!userInfo) return false

  const sellerRoles = ['seller', 'SELLER', 'ROLE_SELLER']
  return sellerRoles.includes(userInfo.role) ||
         userInfo.user_type === 'seller'
}
```

#### 4.1.4 实际API接口设计
```javascript
// api/traceability.js - 溯源相关API
export const traceabilityApi = {
  // 销售者溯源记录管理
  getSellerRecords: (params) => request.get('/api/traceability/seller/records', { params }),
  createSellerRecord: (data) => request.post('/api/traceability/seller/records', data),
  updateSellerRecord: (id, data) => request.put(`/api/traceability/seller/records/${id}`, data),
  deleteSellerRecord: (id) => request.delete(`/api/traceability/seller/records/${id}`),

  // 批量操作
  batchUpdateStatus: (data) => request.post('/api/traceability/seller/records/batch-status', data),

  // 溯源查询
  queryByTraceCode: (traceCode) => request.get(`/api/traceability/query/${traceCode}`),
  getTraceabilityDetail: (traceCode) => request.get(`/api/traceability/detail/${traceCode}`),

  // 销售者产品列表
  getSellerProducts: () => request.get('/api/traceability/seller/products')
}

// api/qrcode.js - 二维码相关API
export const qrcodeApi = {
  // 生成二维码
  generateQRCode: (traceCode) => request.post(`/api/qrcode/generate/${traceCode}`),

  // 批量生成
  batchGenerateAll: () => request.post('/api/qrcode/batch/generate-all'),

  // 为记录生成二维码
  generateForRecord: (recordId) => request.post(`/api/qrcode/generate-for-record/${recordId}`)
}

// api/auth.js - 认证相关API
export const authApi = {
  // 用户登录
  login: (data) => request.post('/api/users/login-user', data),

  // 用户注册
  register: (data) => request.post('/api/users/register', data),

  // 获取用户信息
  getUserInfo: (userId) => request.get(`/api/users/info/${userId}`)
}
```

### 4.2 后端API架构

#### 4.2.1 实际认证机制分析
基于代码分析，SFAP项目使用的是**Session + Header认证**机制，而非JWT Token：

```java
// UserAuthInterceptor.java - 实际认证实现
@Override
public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
    // 从请求头中获取用户ID
    String userIdHeader = request.getHeader("X-User-Id");

    if (userIdHeader != null && !userIdHeader.isEmpty()) {
        try {
            Long userId = Long.parseLong(userIdHeader);

            // 验证用户是否存在且状态正常
            User user = userService.getById(userId);
            if (user != null && user.getStatus() == 1) {
                // 设置用户ID到request属性中
                request.setAttribute("userId", userId);
                request.setAttribute("user", user);
            }
        } catch (NumberFormatException e) {
            log.warn("无效的用户ID格式: {}", userIdHeader);
        }
    }

    return true;
}
```

#### 4.2.2 API路径设计（基于实际项目结构）
```
/api/
├── users/                         # 用户认证
│   ├── login-user                 # 用户登录
│   ├── register                   # 用户注册
│   └── info/{userId}              # 用户信息
├── traceability/                  # 溯源管理
│   ├── seller/
│   │   ├── records               # 销售者溯源记录
│   │   ├── products              # 销售者产品列表
│   │   └── records/batch-status  # 批量状态更新
│   ├── query/{traceCode}         # 溯源查询
│   └── records/{id}              # 溯源记录详情
├── mall/                         # 商城相关
│   ├── products/                 # 产品管理
│   │   ├── list                  # 产品列表
│   │   ├── create                # 创建产品
│   │   └── update/{id}           # 更新产品
│   └── orders/                   # 订单管理
│       ├── list                  # 订单列表
│       └── detail/{id}           # 订单详情
├── qrcode/                       # 二维码管理
│   ├── generate/{traceCode}      # 生成二维码
│   ├── batch/generate-all        # 批量生成
│   └── generate-for-record/{id}  # 为记录生成
└── seller/                       # 销售者专用
    ├── application/              # 销售者申请
    └── shop/                     # 店铺管理
```

#### 4.2.3 权限控制实现（基于实际代码）
```java
// AuthUtils.java - 实际权限验证工具
public static boolean hasSellerPermission(HttpServletRequest request) {
    User user = getCurrentUser(request);
    if (user == null) {
        return false;
    }

    // 检查用户角色
    String role = user.getRole();
    String userType = user.getUserType();

    return "seller".equals(role) ||
           "SELLER".equals(role) ||
           "seller".equals(userType);
}

// 数据权限控制
public static void validateSellerDataAccess(HttpServletRequest request, Long sellerId) {
    User currentUser = getCurrentUser(request);
    if (currentUser == null || !currentUser.getId().equals(sellerId)) {
        throw new AccessDeniedException("无权访问其他销售者的数据");
    }
}
```

#### 4.2.4 前端认证状态管理
```javascript
// auth.js - 前端认证实现
export function isLoggedIn() {
  const userInfo = getUserInfo();
  // 系统不使用token验证，只检查用户信息
  const loggedIn = !!(userInfo && userInfo.id && !userInfo.isGuest);
  return loggedIn;
}

// 角色验证
export function isSeller() {
  const userInfo = getUserInfo();
  if (!userInfo) return false;

  const sellerRoles = ['seller', 'SELLER', 'ROLE_SELLER'];
  return sellerRoles.includes(userInfo.role) ||
         userInfo.user_type === 'seller';
}

// 请求拦截器设置用户ID
request.interceptors.request.use(config => {
  const userInfo = getUserInfo();
  if (userInfo && userInfo.id) {
    config.headers['X-User-Id'] = userInfo.id;
  }
  return config;
});
```

### 4.3 响应式设计架构

#### 4.3.1 断点设计
```scss
// styles/variables.scss
$breakpoints: (
  mobile: 768px,
  tablet: 1200px,
  desktop: 1200px
);

// 响应式混入
@mixin mobile {
  @media (max-width: #{map-get($breakpoints, mobile) - 1px}) {
    @content;
  }
}

@mixin tablet {
  @media (min-width: #{map-get($breakpoints, mobile)}) and
         (max-width: #{map-get($breakpoints, tablet) - 1px}) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: #{map-get($breakpoints, desktop)}) {
    @content;
  }
}
```

#### 4.3.2 布局适配策略
```vue
<!-- SellerLayout.vue -->
<template>
  <div class="seller-layout">
    <!-- 桌面端侧边栏 -->
    <el-aside
      :width="sidebarWidth"
      class="seller-sidebar"
      v-show="!isMobile">
      <seller-sidebar />
    </el-aside>

    <!-- 移动端抽屉 -->
    <el-drawer
      v-model="drawerVisible"
      direction="ltr"
      :size="280"
      v-if="isMobile">
      <seller-sidebar @close="drawerVisible = false" />
    </el-drawer>

    <el-container class="seller-main">
      <!-- 顶部导航 -->
      <el-header class="seller-header">
        <seller-header
          :is-mobile="isMobile"
          @toggle-drawer="drawerVisible = !drawerVisible" />
      </el-header>

      <!-- 主内容区 -->
      <el-main class="seller-content">
        <router-view />
      </el-main>
    </el-container>
  </div>
</template>

<script>
export default {
  name: 'SellerLayout',
  data() {
    return {
      drawerVisible: false,
      screenWidth: window.innerWidth
    }
  },
  computed: {
    isMobile() {
      return this.screenWidth < 768
    },
    isTablet() {
      return this.screenWidth >= 768 && this.screenWidth < 1200
    },
    sidebarWidth() {
      if (this.isMobile) return '0px'
      if (this.isTablet) return '200px'
      return '240px'
    }
  },
  mounted() {
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    handleResize() {
      this.screenWidth = window.innerWidth
    }
  }
}
</script>
```

#### 4.3.3 组件响应式设计
```scss
// styles/seller/dashboard.scss
.dashboard-stats {
  display: grid;
  gap: 20px;

  // 桌面端：4列布局
  @include desktop {
    grid-template-columns: repeat(4, 1fr);
  }

  // 平板端：2列布局
  @include tablet {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  // 移动端：1列布局
  @include mobile {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

.dashboard-charts {
  display: grid;
  gap: 20px;
  margin-top: 20px;

  @include desktop {
    grid-template-columns: 2fr 1fr;
  }

  @include tablet {
    grid-template-columns: 1fr;
  }

  @include mobile {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

// 产品管理响应式
.product-management {
  .product-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    @include mobile {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;
    }
  }

  .product-table {
    @include mobile {
      // 移动端使用卡片布局替代表格
      .el-table {
        display: none;
      }

      .product-cards {
        display: block;
      }
    }

    @include tablet {
      .product-cards {
        display: none;
      }
    }

    @include desktop {
      .product-cards {
        display: none;
      }
    }
  }
}
```

## 5. 溯源表单标准化设计（基于实际代码分析）

### 5.1 溯源表单组件架构

#### 5.1.1 主表单组件（TraceabilityRecordForm.vue）
基于实际代码分析，溯源表单采用分区域设计：

```vue
<template>
  <div class="traceability-record-form">
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">

      <!-- 基本信息区域 -->
      <div class="form-section">
        <h3 class="section-title">基本信息</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="关联产品" prop="productId" required>
              <el-select v-model="form.productId" placeholder="选择要关联的产品"
                         filterable style="width: 100%" @change="handleProductChange">
                <el-option v-for="product in productList" :key="product.id"
                          :label="product.name" :value="product.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="溯源码" prop="traceCode">
              <el-input v-model="form.traceCode" placeholder="系统自动生成" :disabled="true" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="农场名称" prop="farmName" required>
              <el-input v-model="form.farmName" placeholder="输入农场名称"
                       :maxlength="255" show-word-limit />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="批次号" prop="batchNumber" required>
              <el-input v-model="form.batchNumber" placeholder="输入批次号"
                       :maxlength="100" show-word-limit />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 日期信息区域 -->
      <div class="form-section">
        <h3 class="section-title">日期信息</h3>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="生产日期" prop="creationDate" required>
              <el-date-picker v-model="form.creationDate" type="date"
                             placeholder="选择生产日期" format="yyyy-MM-dd"
                             value-format="yyyy-MM-dd" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="采收日期" prop="harvestDate">
              <el-date-picker v-model="form.harvestDate" type="date"
                             placeholder="选择采收日期" format="yyyy-MM-dd"
                             value-format="yyyy-MM-dd" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="包装日期" prop="packagingDate">
              <el-date-picker v-model="form.packagingDate" type="date"
                             placeholder="选择包装日期" format="yyyy-MM-dd"
                             value-format="yyyy-MM-dd" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 生产环节信息 -->
      <div class="form-section">
        <h3 class="section-title">生产环节信息</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="土壤条件" prop="soilCondition">
              <el-input v-model="form.soilCondition" type="textarea" :rows="3"
                       placeholder="描述土壤条件" :maxlength="500" show-word-limit />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="灌溉方式" prop="irrigationMethod">
              <el-input v-model="form.irrigationMethod" placeholder="输入灌溉方式"
                       :maxlength="100" show-word-limit />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="使用肥料" prop="fertilizersUsed">
              <el-input v-model="form.fertilizersUsed" type="textarea" :rows="3"
                       placeholder="描述使用的肥料" :maxlength="500" show-word-limit />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="农药使用" prop="pesticidesUsed">
              <el-input v-model="form.pesticidesUsed" type="textarea" :rows="3"
                       placeholder="描述农药使用情况" :maxlength="500" show-word-limit />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 加工环节信息 -->
      <div class="form-section">
        <h3 class="section-title">加工环节信息</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="加工方式" prop="processingMethod">
              <el-input v-model="form.processingMethod" type="textarea" :rows="3"
                       placeholder="描述加工方式" :maxlength="500" show-word-limit />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="包装材料" prop="packagingMaterial">
              <el-input v-model="form.packagingMaterial" placeholder="输入包装材料"
                       :maxlength="200" show-word-limit />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="质检结果" prop="qualityTestResults">
              <el-input v-model="form.qualityTestResults" type="textarea" :rows="3"
                       placeholder="输入质检结果" :maxlength="1000" show-word-limit />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 流通环节信息 -->
      <div class="form-section">
        <h3 class="section-title">流通环节信息</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="储存条件" prop="storageConditions">
              <el-input v-model="form.storageConditions" type="textarea" :rows="3"
                       placeholder="描述储存条件" :maxlength="500" show-word-limit />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="运输方式" prop="transportationMethod">
              <el-input v-model="form.transportationMethod" placeholder="输入运输方式"
                       :maxlength="100" show-word-limit />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 附件上传 -->
      <div class="form-section">
        <h3 class="section-title">附件信息</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="图片附件">
              <el-upload class="upload-demo" drag action="/api/upload/images"
                        :file-list="imageList" :on-success="handleImageSuccess"
                        :on-remove="handleImageRemove" multiple>
                <i class="el-icon-upload"></i>
                <div class="el-upload__text">将图片拖到此处，或<em>点击上传</em></div>
                <div class="el-upload__tip" slot="tip">只能上传jpg/png文件，且不超过2MB</div>
              </el-upload>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="文档附件">
              <el-upload class="upload-demo" action="/api/upload/documents"
                        :file-list="documentList" :on-success="handleDocumentSuccess"
                        :on-remove="handleDocumentRemove" multiple>
                <el-button size="small" type="primary">点击上传</el-button>
                <div class="el-upload__tip" slot="tip">支持pdf/doc/docx格式，不超过5MB</div>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 备注信息 -->
      <div class="form-section">
        <h3 class="section-title">备注信息</h3>
        <el-form-item label="备注说明" prop="additionalNotes">
          <el-input v-model="form.additionalNotes" type="textarea" :rows="4"
                   placeholder="输入其他需要说明的信息" :maxlength="1000" show-word-limit />
        </el-form-item>
      </div>
    </el-form>

    <!-- 操作按钮 -->
    <div class="form-actions">
      <el-button @click="handleCancel">取消</el-button>
      <el-button @click="handleSaveDraft">保存草稿</el-button>
      <el-button type="primary" @click="handleSubmit">{{ isEdit ? '更新' : '创建' }}</el-button>
    </div>
  </div>
</template>
```

### 5.2 表单验证规则标准化

#### 5.2.1 必填字段验证
```javascript
// 基于实际业务需求的验证规则
const rules = {
  // 基本信息必填
  productId: [
    { required: true, message: '请选择关联产品', trigger: 'change' }
  ],
  farmName: [
    { required: true, message: '请输入农场名称', trigger: 'blur' },
    { min: 2, max: 255, message: '农场名称长度在2到255个字符', trigger: 'blur' }
  ],
  batchNumber: [
    { required: true, message: '请输入批次号', trigger: 'blur' },
    { min: 1, max: 100, message: '批次号长度在1到100个字符', trigger: 'blur' }
  ],

  // 日期信息验证
  creationDate: [
    { required: true, message: '请选择生产日期', trigger: 'change' },
    { validator: validateDateLogic, trigger: 'change' }
  ],

  // 可选字段长度限制
  soilCondition: [
    { max: 500, message: '土壤条件描述不能超过500字符', trigger: 'blur' }
  ],
  irrigationMethod: [
    { max: 100, message: '灌溉方式不能超过100字符', trigger: 'blur' }
  ],
  fertilizersUsed: [
    { max: 500, message: '肥料使用描述不能超过500字符', trigger: 'blur' }
  ],
  pesticidesUsed: [
    { max: 500, message: '农药使用描述不能超过500字符', trigger: 'blur' }
  ],
  processingMethod: [
    { max: 500, message: '加工方式描述不能超过500字符', trigger: 'blur' }
  ],
  packagingMaterial: [
    { max: 200, message: '包装材料不能超过200字符', trigger: 'blur' }
  ],
  qualityTestResults: [
    { max: 1000, message: '质检结果不能超过1000字符', trigger: 'blur' }
  ],
  storageConditions: [
    { max: 500, message: '储存条件描述不能超过500字符', trigger: 'blur' }
  ],
  transportationMethod: [
    { max: 100, message: '运输方式不能超过100字符', trigger: 'blur' }
  ],
  additionalNotes: [
    { max: 1000, message: '备注信息不能超过1000字符', trigger: 'blur' }
  ]
}

// 日期逻辑验证
function validateDateLogic(rule, value, callback) {
  if (!value) {
    callback()
    return
  }

  const creationDate = new Date(value)
  const today = new Date()

  if (creationDate > today) {
    callback(new Error('生产日期不能晚于今天'))
    return
  }

  // 验证采收日期不能早于生产日期
  if (this.form.harvestDate) {
    const harvestDate = new Date(this.form.harvestDate)
    if (harvestDate < creationDate) {
      callback(new Error('采收日期不能早于生产日期'))
      return
    }
  }

  callback()
}
```

### 5.3 数据提交格式标准化

#### 5.3.1 表单数据结构转换
```javascript
// 提交前数据格式化
function formatFormData(form) {
  // 将环节信息转换为JSON格式存储
  const productionInfo = {
    soilCondition: form.soilCondition,
    irrigationMethod: form.irrigationMethod,
    fertilizersUsed: form.fertilizersUsed,
    pesticidesUsed: form.pesticidesUsed,
    harvestMethod: form.harvestMethod,
    weatherConditions: form.weatherConditions
  }

  const processingInfo = {
    processingMethod: form.processingMethod,
    packagingMaterial: form.packagingMaterial,
    qualityTestResults: form.qualityTestResults,
    certifications: form.certifications,
    processingDate: form.processingDate
  }

  const circulationInfo = {
    storageConditions: form.storageConditions,
    transportationMethod: form.transportationMethod,
    logisticsInfo: form.logisticsInfo || []
  }

  return {
    // 基本信息
    productId: form.productId,
    farmName: form.farmName,
    batchNumber: form.batchNumber,
    specification: form.specification,
    qualityGrade: form.qualityGrade,

    // 日期信息
    creationDate: form.creationDate,
    harvestDate: form.harvestDate,
    packagingDate: form.packagingDate,
    productionDate: form.productionDate,
    processingDate: form.processingDate,

    // JSON格式的环节信息
    productionInfo: JSON.stringify(productionInfo),
    processingInfo: JSON.stringify(processingInfo),
    circulationInfo: JSON.stringify(circulationInfo),

    // 其他信息
    additionalNotes: form.additionalNotes,
    status: form.status || 0, // 默认草稿状态
    sourceType: 'seller_upload'
  }
}
```

## 6. 性能优化策略

### 5.1 前端性能优化

#### 5.1.1 代码分割和懒加载
```javascript
// 路由懒加载
const Dashboard = () => import(
  /* webpackChunkName: "seller-dashboard" */
  '@/views/seller/Dashboard.vue'
)

// 组件懒加载
const ProductForm = () => import(
  /* webpackChunkName: "seller-product" */
  '@/components/seller/ProductForm.vue'
)

// 动态导入
async loadChartLibrary() {
  const { default: ECharts } = await import('echarts')
  return ECharts
}
```

#### 5.1.2 数据缓存策略
```javascript
// api/seller.js
import { cache } from '@/utils/cache'

export const sellerApi = {
  // 获取店铺信息（缓存5分钟）
  async getShopInfo() {
    const cacheKey = 'seller:shop:info'
    const cached = cache.get(cacheKey)
    if (cached) return cached

    const data = await request.get('/api/seller/shop/info')
    cache.set(cacheKey, data, 5 * 60 * 1000) // 5分钟缓存
    return data
  },

  // 获取产品列表（分页缓存）
  async getProducts(params) {
    const cacheKey = `seller:products:${JSON.stringify(params)}`
    const cached = cache.get(cacheKey)
    if (cached) return cached

    const data = await request.get('/api/seller/products/list', { params })
    cache.set(cacheKey, data, 2 * 60 * 1000) // 2分钟缓存
    return data
  }
}
```

#### 5.1.3 图片优化
```javascript
// 图片懒加载指令
Vue.directive('lazy', {
  bind(el, binding) {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          el.src = binding.value
          observer.unobserve(el)
        }
      })
    })
    observer.observe(el)
  }
})

// 图片压缩上传
async compressImage(file, quality = 0.8) {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()

    img.onload = () => {
      canvas.width = img.width
      canvas.height = img.height
      ctx.drawImage(img, 0, 0)

      canvas.toBlob(resolve, 'image/jpeg', quality)
    }

    img.src = URL.createObjectURL(file)
  })
}
```

#### 5.1.4 虚拟滚动
```vue
<!-- 大数据列表虚拟滚动 -->
<template>
  <div class="virtual-list" ref="container" @scroll="handleScroll">
    <div class="virtual-list-phantom" :style="{ height: totalHeight + 'px' }"></div>
    <div class="virtual-list-content" :style="{ transform: `translateY(${offset}px)` }">
      <div
        v-for="item in visibleItems"
        :key="item.id"
        class="virtual-list-item"
        :style="{ height: itemHeight + 'px' }">
        <slot :item="item"></slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    items: Array,
    itemHeight: { type: Number, default: 60 },
    containerHeight: { type: Number, default: 400 }
  },
  data() {
    return {
      scrollTop: 0
    }
  },
  computed: {
    totalHeight() {
      return this.items.length * this.itemHeight
    },
    visibleCount() {
      return Math.ceil(this.containerHeight / this.itemHeight) + 2
    },
    startIndex() {
      return Math.floor(this.scrollTop / this.itemHeight)
    },
    endIndex() {
      return Math.min(this.startIndex + this.visibleCount, this.items.length)
    },
    visibleItems() {
      return this.items.slice(this.startIndex, this.endIndex)
    },
    offset() {
      return this.startIndex * this.itemHeight
    }
  },
  methods: {
    handleScroll(e) {
      this.scrollTop = e.target.scrollTop
    }
  }
}
</script>
```

### 5.2 后端性能优化

#### 5.2.1 数据库查询优化
```java
// 分页查询优化
@Service
public class ProductService {

    public PageResult<Product> getSellerProducts(Long sellerId, ProductQuery query) {
        // 使用索引优化查询
        QueryWrapper<Product> wrapper = new QueryWrapper<>();
        wrapper.eq("seller_id", sellerId)  // 利用seller_id索引
               .eq("deleted", 0);          // 利用deleted索引

        // 条件查询
        if (StringUtils.isNotEmpty(query.getName())) {
            wrapper.like("name", query.getName());
        }
        if (query.getStatus() != null) {
            wrapper.eq("status", query.getStatus());
        }

        // 排序优化
        wrapper.orderByDesc("created_at");  // 利用created_at索引

        // 分页查询
        Page<Product> page = new Page<>(query.getPageNum(), query.getPageSize());
        IPage<Product> result = productMapper.selectPage(page, wrapper);

        return new PageResult<>(result.getRecords(), result.getTotal());
    }
}
```

#### 5.2.2 缓存策略
```java
// Redis缓存配置
@Configuration
@EnableCaching
public class CacheConfig {

    @Bean
    public CacheManager cacheManager(RedisConnectionFactory factory) {
        RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofMinutes(10))  // 默认10分钟过期
            .serializeKeysWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new StringRedisSerializer()))
            .serializeValuesWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new GenericJackson2JsonRedisSerializer()));

        return RedisCacheManager.builder(factory)
            .cacheDefaults(config)
            .build();
    }
}

// 服务层缓存
@Service
public class SellerStatisticsService {

    @Cacheable(value = "seller:stats", key = "#sellerId + ':' + #date")
    public SellerStats getDailyStats(Long sellerId, LocalDate date) {
        // 查询数据库
        return statisticsMapper.getDailyStats(sellerId, date);
    }

    @CacheEvict(value = "seller:stats", key = "#sellerId + ':*'")
    public void clearSellerStatsCache(Long sellerId) {
        // 清除缓存
    }
}
```

## 8. 安全性设计（基于实际认证机制）

### 8.1 认证和授权

#### 8.1.1 Session + Header认证机制
基于实际代码分析，SFAP使用Session + Header的认证方式：

```java
// UserAuthInterceptor.java - 实际认证拦截器
@Component
public class UserAuthInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        // 从请求头获取用户ID
        String userIdHeader = request.getHeader("X-User-Id");

        if (userIdHeader != null && !userIdHeader.isEmpty()) {
            try {
                Long userId = Long.parseLong(userIdHeader);

                // 验证用户存在性和状态
                User user = userService.getById(userId);
                if (user != null && user.getStatus() == 1) {
                    request.setAttribute("userId", userId);
                    request.setAttribute("user", user);
                    log.debug("用户认证成功，用户ID: {}, 用户名: {}", userId, user.getUsername());
                } else {
                    log.warn("用户不存在或已被禁用，用户ID: {}", userId);
                }
            } catch (NumberFormatException e) {
                log.warn("无效的用户ID格式: {}", userIdHeader);
            }
        }

        return true;
    }
}
```

#### 8.1.2 前端认证状态管理
```javascript
// utils/auth.js - 前端认证工具
export function getUserInfo() {
  try {
    const userStr = localStorage.getItem('sfap_user');
    return userStr ? JSON.parse(userStr) : null;
  } catch (e) {
    console.error('解析用户信息失败:', e);
    return null;
  }
}

export function isLoggedIn() {
  const userInfo = getUserInfo();
  // 不使用token验证，只检查用户信息
  return !!(userInfo && userInfo.id && !userInfo.isGuest);
}

export function isSeller() {
  const userInfo = getUserInfo();
  if (!userInfo) return false;

  const sellerRoles = ['seller', 'SELLER', 'ROLE_SELLER'];
  return sellerRoles.includes(userInfo.role) ||
         userInfo.user_type === 'seller';
}

// 请求拦截器 - 自动添加用户ID头
import axios from 'axios';

axios.interceptors.request.use(config => {
  const userInfo = getUserInfo();
  if (userInfo && userInfo.id) {
    config.headers['X-User-Id'] = userInfo.id;
  }
  return config;
});
```

#### 8.1.3 数据权限控制（基于实际实现）
```java
// AuthUtils.java - 权限验证工具类
public class AuthUtils {

    /**
     * 获取当前用户
     */
    public static User getCurrentUser(HttpServletRequest request) {
        return (User) request.getAttribute("user");
    }

    /**
     * 获取当前用户ID
     */
    public static Long getCurrentUserId(HttpServletRequest request) {
        return (Long) request.getAttribute("userId");
    }

    /**
     * 验证销售者权限
     */
    public static boolean hasSellerPermission(HttpServletRequest request) {
        User user = getCurrentUser(request);
        if (user == null) return false;

        String role = user.getRole();
        String userType = user.getUserType();

        return "seller".equals(role) ||
               "SELLER".equals(role) ||
               "seller".equals(userType);
    }

    /**
     * 验证销售者数据访问权限
     */
    public static void validateSellerDataAccess(HttpServletRequest request, Long sellerId) {
        User currentUser = getCurrentUser(request);
        if (currentUser == null) {
            throw new UnauthorizedException("用户未登录");
        }

        if (!currentUser.getId().equals(sellerId)) {
            throw new AccessDeniedException("无权访问其他销售者的数据");
        }
    }

    /**
     * 验证管理员权限
     */
    public static boolean hasAdminPermission(HttpServletRequest request) {
        User user = getCurrentUser(request);
        if (user == null) return false;

        String role = user.getRole();
        return "ADMIN".equals(role) || "admin".equals(role);
    }
}
```

#### 8.1.4 Controller层权限验证
```java
// TraceabilityController.java - 实际权限验证示例
@RestController
@RequestMapping("/api/traceability/seller")
public class SellerTraceabilityController {

    @GetMapping("/records")
    public ResponseEntity<Map<String, Object>> getSellerRecords(
            HttpServletRequest request,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {

        // 验证销售者权限
        if (!AuthUtils.hasSellerPermission(request)) {
            return ResponseEntity.status(403).body(Map.of(
                "success", false,
                "message", "无销售者权限"
            ));
        }

        // 获取当前用户ID作为销售者ID
        Long sellerId = AuthUtils.getCurrentUserId(request);

        // 查询该销售者的溯源记录
        IPage<TraceabilityRecord> records = traceabilityService.getSellerRecords(sellerId, page, size);

        return ResponseEntity.ok(Map.of(
            "success", true,
            "data", records
        ));
    }

    @PostMapping("/records")
    public ResponseEntity<Map<String, Object>> createSellerRecord(
            HttpServletRequest request,
            @RequestBody TraceabilityRecord record) {

        // 验证销售者权限
        if (!AuthUtils.hasSellerPermission(request)) {
            return ResponseEntity.status(403).body(Map.of(
                "success", false,
                "message", "无销售者权限"
            ));
        }

        // 设置生产者ID为当前用户ID
        Long sellerId = AuthUtils.getCurrentUserId(request);
        record.setProducerId(sellerId);

        // 创建溯源记录
        TraceabilityRecord created = traceabilityService.createRecord(record);

        return ResponseEntity.ok(Map.of(
            "success", true,
            "data", created
        ));
    }
}
```

### 6.2 输入验证和XSS防护

#### 6.2.1 参数验证
```java
// DTO验证
public class ProductCreateRequest {

    @NotBlank(message = "产品名称不能为空")
    @Length(max = 255, message = "产品名称长度不能超过255字符")
    private String name;

    @NotNull(message = "价格不能为空")
    @DecimalMin(value = "0.01", message = "价格必须大于0")
    @DecimalMax(value = "999999.99", message = "价格不能超过999999.99")
    private BigDecimal price;

    @Min(value = 0, message = "库存不能为负数")
    @Max(value = 999999, message = "库存不能超过999999")
    private Integer stock;

    @Pattern(regexp = "^[\\u4e00-\\u9fa5a-zA-Z0-9\\s,，。.]+$", message = "描述包含非法字符")
    private String description;
}

// 控制器验证
@RestController
@RequestMapping("/api/seller/products")
@Validated
public class SellerProductController {

    @PostMapping("/create")
    public Result<Product> createProduct(@Valid @RequestBody ProductCreateRequest request) {
        // 业务逻辑
        return Result.success(productService.createProduct(request));
    }
}
```

#### 6.2.2 XSS防护
```java
// XSS过滤器
@Component
public class XssFilter implements Filter {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {

        XssHttpServletRequestWrapper wrappedRequest = new XssHttpServletRequestWrapper(
            (HttpServletRequest) request);

        chain.doFilter(wrappedRequest, response);
    }
}

// XSS请求包装器
public class XssHttpServletRequestWrapper extends HttpServletRequestWrapper {

    public XssHttpServletRequestWrapper(HttpServletRequest request) {
        super(request);
    }

    @Override
    public String getParameter(String name) {
        String value = super.getParameter(name);
        return cleanXSS(value);
    }

    @Override
    public String[] getParameterValues(String name) {
        String[] values = super.getParameterValues(name);
        if (values != null) {
            for (int i = 0; i < values.length; i++) {
                values[i] = cleanXSS(values[i]);
            }
        }
        return values;
    }

    private String cleanXSS(String value) {
        if (value == null) return null;

        // 移除脚本标签
        value = value.replaceAll("<script[^>]*>.*?</script>", "");
        value = value.replaceAll("javascript:", "");
        value = value.replaceAll("onload", "");
        value = value.replaceAll("onerror", "");

        return value;
    }
}
```

## 7. 部署和运维

### 7.1 构建配置

#### 7.1.1 Webpack优化配置
```javascript
// vue.config.js
const path = require('path')

module.exports = {
  publicPath: process.env.NODE_ENV === 'production' ? '/seller/' : '/',
  outputDir: 'dist/seller',

  configureWebpack: {
    optimization: {
      splitChunks: {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            name: 'chunk-vendors',
            test: /[\\/]node_modules[\\/]/,
            priority: 10,
            chunks: 'initial'
          },
          elementUI: {
            name: 'chunk-elementUI',
            test: /[\\/]node_modules[\\/]element-ui[\\/]/,
            priority: 20
          },
          echarts: {
            name: 'chunk-echarts',
            test: /[\\/]node_modules[\\/]echarts[\\/]/,
            priority: 20
          }
        }
      }
    }
  },

  chainWebpack: config => {
    // 预加载优化
    config.plugin('preload').tap(options => {
      options[0] = {
        rel: 'preload',
        include: 'initial',
        fileBlacklist: [/\.map$/, /hot-update\.js$/]
      }
      return options
    })

    // 图片压缩
    config.module
      .rule('images')
      .use('image-webpack-loader')
      .loader('image-webpack-loader')
      .options({
        mozjpeg: { progressive: true, quality: 80 },
        optipng: { enabled: false },
        pngquant: { quality: [0.65, 0.8], speed: 4 },
        gifsicle: { interlaced: false }
      })
  }
}
```

#### 7.1.2 环境配置
```javascript
// .env.development
NODE_ENV=development
VUE_APP_API_BASE_URL=http://localhost:8081/api
VUE_APP_UPLOAD_URL=http://localhost:8081/uploads
VUE_APP_ENABLE_MOCK=false

// .env.production
NODE_ENV=production
VUE_APP_API_BASE_URL=https://api.sfap.com/api
VUE_APP_UPLOAD_URL=https://cdn.sfap.com/uploads
VUE_APP_ENABLE_MOCK=false
```

### 7.2 Docker部署

#### 7.2.1 前端Dockerfile
```dockerfile
# Dockerfile.frontend
FROM node:16-alpine as builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build:seller

FROM nginx:alpine
COPY --from=builder /app/dist/seller /usr/share/nginx/html/seller
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

#### 7.2.2 Nginx配置
```nginx
# nginx.conf
server {
    listen 80;
    server_name localhost;

    # 前端静态资源
    location /seller {
        alias /usr/share/nginx/html/seller;
        try_files $uri $uri/ /seller/index.html;

        # 缓存策略
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # API代理
    location /api {
        proxy_pass http://backend:8081;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 文件上传
    location /uploads {
        proxy_pass http://backend:8081;
        client_max_body_size 10M;
    }
}
```

#### 7.2.3 Docker Compose
```yaml
# docker-compose.yml
version: '3.8'

services:
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    ports:
      - "80:80"
    depends_on:
      - backend
    networks:
      - sfap-network

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8081:8081"
    environment:
      - SPRING_PROFILES_ACTIVE=production
      - MYSQL_HOST=mysql
      - REDIS_HOST=redis
    depends_on:
      - mysql
      - redis
    networks:
      - sfap-network

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: fan13965711955
      MYSQL_DATABASE: agriculture_mall
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - sfap-network

  redis:
    image: redis:alpine
    volumes:
      - redis_data:/data
    networks:
      - sfap-network

volumes:
  mysql_data:
  redis_data:

networks:
  sfap-network:
    driver: bridge
```

### 7.3 监控和日志

#### 7.3.1 前端监控
```javascript
// utils/monitor.js
class Monitor {
  constructor() {
    this.init()
  }

  init() {
    // 性能监控
    this.monitorPerformance()

    // 错误监控
    this.monitorErrors()

    // 用户行为监控
    this.monitorUserBehavior()
  }

  monitorPerformance() {
    window.addEventListener('load', () => {
      const timing = performance.timing
      const loadTime = timing.loadEventEnd - timing.navigationStart

      this.sendMetric('page_load_time', loadTime)
    })
  }

  monitorErrors() {
    window.addEventListener('error', (event) => {
      this.sendError({
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error?.stack
      })
    })

    window.addEventListener('unhandledrejection', (event) => {
      this.sendError({
        message: 'Unhandled Promise Rejection',
        reason: event.reason
      })
    })
  }

  monitorUserBehavior() {
    // 页面访问统计
    this.trackPageView()

    // 按钮点击统计
    this.trackClicks()
  }

  sendMetric(name, value) {
    fetch('/api/metrics', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ name, value, timestamp: Date.now() })
    })
  }

  sendError(error) {
    fetch('/api/errors', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ ...error, timestamp: Date.now() })
    })
  }
}

export default new Monitor()
```

#### 7.3.2 后端日志配置
```yaml
# logback-spring.xml
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <springProfile name="!production">
        <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
            <encoder>
                <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
            </encoder>
        </appender>
        <root level="INFO">
            <appender-ref ref="CONSOLE"/>
        </root>
    </springProfile>

    <springProfile name="production">
        <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>logs/seller-center.log</file>
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <fileNamePattern>logs/seller-center.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
                <maxFileSize>100MB</maxFileSize>
                <maxHistory>30</maxHistory>
            </rollingPolicy>
            <encoder>
                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
            </encoder>
        </appender>

        <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>logs/seller-center-error.log</file>
            <filter class="ch.qos.logback.classic.filter.LevelFilter">
                <level>ERROR</level>
                <onMatch>ACCEPT</onMatch>
                <onMismatch>DENY</onMismatch>
            </filter>
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <fileNamePattern>logs/seller-center-error.%d{yyyy-MM-dd}.log</fileNamePattern>
                <maxHistory>30</maxHistory>
            </rollingPolicy>
            <encoder>
                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
            </encoder>
        </appender>

        <root level="INFO">
            <appender-ref ref="FILE"/>
            <appender-ref ref="ERROR_FILE"/>
        </root>
    </springProfile>
</configuration>
```

## 8. 开发计划和里程碑

### 8.1 开发阶段规划

#### 第一阶段：基础架构搭建（1-2周）
- [ ] 项目结构搭建和环境配置
- [ ] 基础布局组件开发（SellerLayout、Sidebar、Header）
- [ ] 路由配置和权限控制
- [ ] API接口基础架构
- [ ] 数据库表结构确认和优化

#### 第二阶段：核心功能开发（3-4周）
- [ ] 仪表板页面开发
- [ ] 商店管理功能
- [ ] 产品管理模块（CRUD、图片上传）
- [ ] 订单管理基础功能
- [ ] 用户认证和权限验证

#### 第三阶段：高级功能开发（3-4周）
- [ ] 溯源管理完整功能
- [ ] 客户管理和评价系统
- [ ] 数据统计和图表展示
- [ ] 文件上传和图片处理
- [ ] 响应式设计优化

#### 第四阶段：优化和测试（2-3周）
- [ ] 性能优化和代码分割
- [ ] 安全性测试和加固
- [ ] 兼容性测试
- [ ] 用户体验优化
- [ ] 文档完善

#### 第五阶段：部署和上线（1周）
- [ ] 生产环境部署
- [ ] 监控系统配置
- [ ] 性能监控和日志收集
- [ ] 用户培训和支持

### 8.2 技术风险评估

#### 高风险项
- **数据权限控制**：确保销售者只能访问自己的数据
- **文件上传安全**：防止恶意文件上传
- **性能优化**：大数据量下的页面响应速度

#### 中风险项
- **响应式适配**：多端兼容性问题
- **图表渲染**：复杂数据可视化性能
- **缓存策略**：数据一致性问题

#### 低风险项
- **UI组件开发**：基于成熟的Element UI
- **基础CRUD操作**：标准的增删改查功能
- **路由配置**：Vue Router标准用法

### 8.3 成功标准

#### 功能完整性
- [ ] 所有规划功能模块100%实现
- [ ] 核心业务流程端到端测试通过
- [ ] 权限控制和数据安全验证通过

#### 性能指标
- [ ] 页面首次加载时间 < 3秒
- [ ] 页面切换响应时间 < 1秒
- [ ] 大数据列表渲染流畅（>30fps）

#### 用户体验
- [ ] 移动端适配完美支持
- [ ] 操作流程直观易用
- [ ] 错误提示友好明确

#### 技术质量
- [ ] 代码覆盖率 > 80%
- [ ] 安全漏洞扫描通过
- [ ] 性能监控指标正常

---

## 总结

### 基于实际代码分析的关键发现

本文档基于对SFAP项目实际代码的深入分析，更新了销售者销售中心界面架构设计，主要发现和修正包括：

#### 1. 认证机制修正
- **实际机制**: Session + Header认证（X-User-Id）
- **原设计**: JWT Token认证
- **影响**: 前端需要在请求头中设置用户ID，后端通过拦截器验证用户状态

#### 2. 溯源码格式标准化
- **实际格式**: 22位字符（SFAP + 10位时间戳 + 4位产品ID + 4位随机码）
- **示例**: SFAP25071410001001A1B2
- **生成规则**: 基于TraceCodeGenerator.java的实际实现

#### 3. 溯源表单结构优化
- **数据库映射**: 完全基于TraceabilityRecord实体类字段
- **三环节设计**: 生产、加工、流通信息分别存储为JSON格式
- **验证规则**: 基于实际业务需求的字段长度和必填验证

#### 4. API接口路径修正
- **实际路径**: /api/traceability/seller/*、/api/qrcode/*
- **权限控制**: 基于UserAuthInterceptor和AuthUtils的实际实现
- **数据隔离**: 通过producer_id/seller_id字段确保数据安全

#### 5. 二维码生成机制
- **存储路径**: /uploads/qrcodes/
- **文件格式**: PNG，300x300像素
- **生成触发**: 手动API调用，支持批量生成
- **URL格式**: /uploads/qrcodes/qr_{traceCode}.png

### 技术架构特点

1. **前后端分离**: Vue 2 + Element UI前端，Spring Boot后端
2. **数据库设计**: MySQL，完整的溯源数据表结构
3. **权限控制**: 基于用户角色的数据访问控制
4. **文件管理**: 统一的文件上传和存储机制
5. **响应式设计**: 支持桌面端、平板端、移动端适配

### 开发建议

1. **认证机制**: 继续使用现有的Session + Header认证，确保兼容性
2. **溯源表单**: 采用本文档标准化的表单设计，提高用户体验
3. **API设计**: 遵循现有的RESTful API规范和路径结构
4. **权限验证**: 使用AuthUtils工具类进行统一的权限验证
5. **数据验证**: 前后端双重验证，确保数据完整性和安全性

### 实施优先级

1. **高优先级**: 溯源表单标准化、权限控制完善
2. **中优先级**: 二维码自动生成、批量操作优化
3. **低优先级**: 响应式设计优化、性能监控完善

通过基于实际代码的架构设计，确保了销售者销售中心与现有SFAP平台的完美集成，为销售者提供了一个功能完整、技术先进、用户体验优良的管理平台。
```
```
```
```
