<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端集成测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e4e7ed;
            border-radius: 6px;
        }
        .test-title {
            color: #409eff;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
        }
        .success {
            color: #67c23a;
        }
        .error {
            color: #f56c6c;
        }
        .loading {
            color: #409eff;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f0f9ff;
            border-left: 4px solid #409eff;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        button {
            background: #409eff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #66b1ff;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-online {
            background-color: #67c23a;
        }
        .status-offline {
            background-color: #f56c6c;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>SFAP农品汇平台 - 前端集成测试</h1>
        
        <div class="test-section">
            <div class="test-title">服务状态检查</div>
            <div id="service-status">
                <div>
                    <span class="status-indicator" id="ai-status"></span>
                    <span>AI服务 (端口5000): </span>
                    <span id="ai-status-text">检查中...</span>
                </div>
                <div>
                    <span class="status-indicator" id="backend-status"></span>
                    <span>后端服务 (端口8081): </span>
                    <span id="backend-status-text">检查中...</span>
                </div>
                <div>
                    <span class="status-indicator" id="frontend-status"></span>
                    <span>前端服务 (端口8080): </span>
                    <span id="frontend-status-text">检查中...</span>
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">AI服务API测试</div>
            <button onclick="testAIHealth()">健康检查</button>
            <button onclick="testCrawlCategories()">分类数据</button>
            <button onclick="testCrawlRegions()">地区数据</button>
            <button onclick="testCrawlProducts()">产品搜索</button>
            <button onclick="testPriceHistory()">历史价格</button>
            <div id="ai-api-results"></div>
        </div>

        <div class="test-section">
            <div class="test-title">预测功能测试</div>
            <button onclick="testRNNPrediction()">RNN预测</button>
            <button onclick="testARIMAPrediction()">ARIMA预测</button>
            <div id="prediction-results"></div>
        </div>

        <div class="test-section">
            <div class="test-title">数据格式验证</div>
            <button onclick="testDataFormats()">验证数据格式</button>
            <div id="format-results"></div>
        </div>

        <div class="test-section">
            <div class="test-title">前端组件测试指南</div>
            <div>
                <h4>测试步骤：</h4>
                <ol>
                    <li>确保所有服务状态为在线</li>
                    <li>运行AI服务API测试，确保所有接口正常</li>
                    <li>访问前端页面: <a href="http://localhost:8080/#/ai/prediction" target="_blank">http://localhost:8080/#/ai/prediction</a></li>
                    <li>测试增强选择器功能：
                        <ul>
                            <li>分类选择器应显示18个真实分类</li>
                            <li>地区选择器应支持三级联动</li>
                            <li>数据可用性指示器应显示质量评分</li>
                            <li>推荐功能应提供最佳组合建议</li>
                        </ul>
                    </li>
                    <li>测试预测功能：
                        <ul>
                            <li>选择有数据的分类和地区组合</li>
                            <li>运行RNN或ARIMA预测</li>
                            <li>验证预测结果显示</li>
                        </ul>
                    </li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        // 检查服务状态
        async function checkServiceStatus() {
            // 检查AI服务
            try {
                const response = await fetch('http://localhost:5000/api/v1/health', { timeout: 5000 });
                if (response.ok) {
                    document.getElementById('ai-status').className = 'status-indicator status-online';
                    document.getElementById('ai-status-text').textContent = '在线';
                    document.getElementById('ai-status-text').className = 'success';
                } else {
                    throw new Error('响应异常');
                }
            } catch (error) {
                document.getElementById('ai-status').className = 'status-indicator status-offline';
                document.getElementById('ai-status-text').textContent = '离线';
                document.getElementById('ai-status-text').className = 'error';
            }

            // 检查后端服务
            try {
                const response = await fetch('http://localhost:8081/api/health', { timeout: 5000 });
                if (response.ok) {
                    document.getElementById('backend-status').className = 'status-indicator status-online';
                    document.getElementById('backend-status-text').textContent = '在线';
                    document.getElementById('backend-status-text').className = 'success';
                } else {
                    throw new Error('响应异常');
                }
            } catch (error) {
                document.getElementById('backend-status').className = 'status-indicator status-offline';
                document.getElementById('backend-status-text').textContent = '离线';
                document.getElementById('backend-status-text').className = 'error';
            }

            // 检查前端服务
            try {
                const response = await fetch('http://localhost:8080/', { timeout: 5000 });
                if (response.ok) {
                    document.getElementById('frontend-status').className = 'status-indicator status-online';
                    document.getElementById('frontend-status-text').textContent = '在线';
                    document.getElementById('frontend-status-text').className = 'success';
                } else {
                    throw new Error('响应异常');
                }
            } catch (error) {
                document.getElementById('frontend-status').className = 'status-indicator status-offline';
                document.getElementById('frontend-status-text').textContent = '离线';
                document.getElementById('frontend-status-text').className = 'error';
            }
        }

        function showResult(containerId, title, status, data) {
            const container = document.getElementById(containerId);
            const statusClass = status === 'success' ? 'success' : status === 'error' ? 'error' : 'loading';
            
            let html = `<h4 class="${statusClass}">${title}</h4>`;
            if (data) {
                html += `<div class="result">${JSON.stringify(data, null, 2)}</div>`;
            }
            
            container.innerHTML = html;
        }

        async function testAIHealth() {
            showResult('ai-api-results', '正在测试AI服务健康检查...', 'loading');
            
            try {
                const response = await fetch('http://localhost:5000/api/v1/health');
                const data = await response.json();
                
                if (response.ok) {
                    showResult('ai-api-results', '✅ AI服务健康检查成功', 'success', data);
                } else {
                    showResult('ai-api-results', '❌ AI服务健康检查失败', 'error', data);
                }
            } catch (error) {
                showResult('ai-api-results', `❌ AI服务连接失败: ${error.message}`, 'error');
            }
        }

        async function testCrawlCategories() {
            showResult('ai-api-results', '正在测试分类数据获取...', 'loading');
            
            try {
                const response = await fetch('http://localhost:5000/api/v1/crawl_data/categories');
                const data = await response.json();
                
                if (response.ok) {
                    const categories = data.data.categories;
                    showResult('ai-api-results', `✅ 分类数据获取成功: ${categories.length}个分类`, 'success', categories.slice(0, 3));
                } else {
                    showResult('ai-api-results', '❌ 分类数据获取失败', 'error', data);
                }
            } catch (error) {
                showResult('ai-api-results', `❌ 分类数据请求失败: ${error.message}`, 'error');
            }
        }

        async function testCrawlRegions() {
            showResult('ai-api-results', '正在测试地区数据获取...', 'loading');
            
            try {
                const response = await fetch('http://localhost:5000/api/v1/crawl_data/regions');
                const data = await response.json();
                
                if (response.ok) {
                    const regions = data.data.regions;
                    showResult('ai-api-results', `✅ 地区数据获取成功: ${regions.length}个地区`, 'success', regions.slice(0, 3));
                } else {
                    showResult('ai-api-results', '❌ 地区数据获取失败', 'error', data);
                }
            } catch (error) {
                showResult('ai-api-results', `❌ 地区数据请求失败: ${error.message}`, 'error');
            }
        }

        async function testCrawlProducts() {
            showResult('ai-api-results', '正在测试产品搜索...', 'loading');
            
            try {
                const response = await fetch('http://localhost:5000/api/v1/crawl_data/products?keyword=苹果&limit=3');
                const data = await response.json();
                
                if (response.ok) {
                    const products = data.data.products;
                    showResult('ai-api-results', `✅ 产品搜索成功: ${products.length}个产品`, 'success', products);
                } else {
                    showResult('ai-api-results', '❌ 产品搜索失败', 'error', data);
                }
            } catch (error) {
                showResult('ai-api-results', `❌ 产品搜索请求失败: ${error.message}`, 'error');
            }
        }

        async function testPriceHistory() {
            showResult('ai-api-results', '正在测试历史价格数据...', 'loading');
            
            try {
                const response = await fetch('http://localhost:5000/api/v1/crawl_data/prices/history?product_name=苹果&limit=3');
                const data = await response.json();
                
                if (response.ok) {
                    const historyData = data.data.history_data;
                    const qualityReport = data.data.quality_report;
                    showResult('ai-api-results', `✅ 历史价格数据获取成功: ${historyData.length}条记录, 质量评分: ${qualityReport.quality_score}%`, 'success', { historyData: historyData.slice(0, 2), qualityReport });
                } else {
                    showResult('ai-api-results', '❌ 历史价格数据获取失败', 'error', data);
                }
            } catch (error) {
                showResult('ai-api-results', `❌ 历史价格数据请求失败: ${error.message}`, 'error');
            }
        }

        async function testRNNPrediction() {
            showResult('prediction-results', '正在测试RNN预测...', 'loading');
            
            try {
                const predictionData = {
                    category: '苹果',
                    region: '山东省',
                    product_name: '苹果',
                    forecast_days: 3
                };
                
                const response = await fetch('http://localhost:5000/api/v1/predict_rnn', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(predictionData)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    const predictions = data.data.predictions;
                    showResult('prediction-results', `✅ RNN预测成功: ${predictions.length}个预测点`, 'success', predictions);
                } else {
                    showResult('prediction-results', '❌ RNN预测失败', 'error', data);
                }
            } catch (error) {
                showResult('prediction-results', `❌ RNN预测请求失败: ${error.message}`, 'error');
            }
        }

        async function testARIMAPrediction() {
            showResult('prediction-results', '正在测试ARIMA预测...', 'loading');
            
            try {
                const predictionData = {
                    category: '苹果',
                    region: '山东省',
                    product_name: '苹果',
                    forecast_days: 3
                };
                
                const response = await fetch('http://localhost:5000/api/v1/predict_arima', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(predictionData)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    const predictions = data.data.predictions;
                    showResult('prediction-results', `✅ ARIMA预测成功: ${predictions.length}个预测点`, 'success', predictions);
                } else {
                    showResult('prediction-results', '❌ ARIMA预测失败', 'error', data);
                }
            } catch (error) {
                showResult('prediction-results', `❌ ARIMA预测请求失败: ${error.message}`, 'error');
            }
        }

        async function testDataFormats() {
            showResult('format-results', '正在验证数据格式...', 'loading');
            
            try {
                // 测试分类数据格式
                const categoriesResponse = await fetch('http://localhost:5000/api/v1/crawl_data/categories');
                const categoriesData = await categoriesResponse.json();
                
                const formatValidation = {
                    categories: {
                        hasData: categoriesData.data && categoriesData.data.categories,
                        count: categoriesData.data ? categoriesData.data.categories.length : 0,
                        sampleFields: categoriesData.data && categoriesData.data.categories.length > 0 ? 
                            Object.keys(categoriesData.data.categories[0]) : []
                    }
                };
                
                // 测试地区数据格式
                const regionsResponse = await fetch('http://localhost:5000/api/v1/crawl_data/regions');
                const regionsData = await regionsResponse.json();
                
                formatValidation.regions = {
                    hasData: regionsData.data && regionsData.data.regions,
                    count: regionsData.data ? regionsData.data.regions.length : 0,
                    sampleFields: regionsData.data && regionsData.data.regions.length > 0 ? 
                        Object.keys(regionsData.data.regions[0]) : []
                };
                
                showResult('format-results', '✅ 数据格式验证完成', 'success', formatValidation);
                
            } catch (error) {
                showResult('format-results', `❌ 数据格式验证失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动检查服务状态
        window.onload = function() {
            checkServiceStatus();
        };
    </script>
</body>
</html>
