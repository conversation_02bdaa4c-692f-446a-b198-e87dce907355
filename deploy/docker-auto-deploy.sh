#!/bin/bash

# SFAP Docker自动部署脚本
# 用于生产环境的容器化自动部署

set -e  # 遇到错误立即退出

# 配置变量
PROJECT_DIR="/opt/sfap"
BACKUP_DIR="/opt/backup/sfap"
LOG_FILE="/var/log/sfap-deploy.log"
DOCKER_COMPOSE_FILE="docker-compose.yml"
HEALTH_CHECK_URL="http://localhost/api/health"
MAX_LOG_SIZE=10485760  # 10MB
BACKUP_RETENTION_DAYS=7
DOCKER_REGISTRY="${DOCKER_REGISTRY:-docker.io}"
IMAGE_TAG="${IMAGE_TAG:-latest}"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志轮转函数
rotate_log() {
    if [ -f "$LOG_FILE" ] && [ $(stat -c%s "$LOG_FILE") -gt $MAX_LOG_SIZE ]; then
        mv "$LOG_FILE" "${LOG_FILE}.old"
        touch "$LOG_FILE"
        chmod 644 "$LOG_FILE"
    fi
}

# 增强的日志函数
log() {
    local level=${2:-INFO}
    local message="[$(date '+%Y-%m-%d %H:%M:%S')] [$level] $1"
    
    rotate_log
    echo -e "$message" | tee -a "$LOG_FILE"
    
    # 根据级别设置颜色
    case $level in
        ERROR) echo -e "${RED}$message${NC}" ;;
        WARN)  echo -e "${YELLOW}$message${NC}" ;;
        SUCCESS) echo -e "${GREEN}$message${NC}" ;;
        INFO) echo -e "${BLUE}$message${NC}" ;;
        *) echo "$message" ;;
    esac
}

# 错误处理
error_exit() {
    log "$1" ERROR
    
    # 发送通知（如果配置了）
    send_notification "🚨 SFAP部署失败: $1" "error"
    
    # 尝试回滚
    if [ "$ENABLE_ROLLBACK" = "true" ]; then
        log "尝试自动回滚..." WARN
        rollback_deployment
    fi
    
    exit 1
}

# 成功通知
success_notify() {
    log "$1" SUCCESS
    send_notification "✅ SFAP部署成功: $1" "success"
}

# 发送通知
send_notification() {
    local message="$1"
    local type="${2:-info}"
    
    # Slack通知
    if [ -n "$SLACK_WEBHOOK" ]; then
        local color="good"
        [ "$type" = "error" ] && color="danger"
        [ "$type" = "warning" ] && color="warning"
        
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"attachments\":[{\"color\":\"$color\",\"text\":\"$message\"}]}" \
            "$SLACK_WEBHOOK" &>/dev/null || true
    fi
    
    # 企业微信通知
    if [ -n "$WECHAT_WEBHOOK" ]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"msgtype\":\"text\",\"text\":{\"content\":\"$message\"}}" \
            "$WECHAT_WEBHOOK" &>/dev/null || true
    fi
}

# 检查必要的命令
check_commands() {
    log "检查必要的命令..."
    local commands=("git" "docker" "docker-compose" "curl" "jq")
    local missing_commands=()
    
    for cmd in "${commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            missing_commands+=("$cmd")
        fi
    done
    
    if [ ${#missing_commands[@]} -ne 0 ]; then
        error_exit "以下命令未找到: ${missing_commands[*]}"
    fi
    
    # 检查Docker服务状态
    if ! docker info &>/dev/null; then
        error_exit "Docker服务未运行"
    fi
    
    log "所有必要命令检查通过" SUCCESS
}

# 检查系统资源
check_system_resources() {
    log "检查系统资源..."
    
    # 检查磁盘空间
    local available_space=$(df "$PROJECT_DIR" | awk 'NR==2 {print $4}')
    local required_space=2097152  # 2GB in KB
    
    if [ "$available_space" -lt "$required_space" ]; then
        error_exit "磁盘空间不足，需要至少2GB可用空间"
    fi
    
    # 检查内存
    local available_memory=$(free -m | awk 'NR==2{print $7}')
    local required_memory=1024  # 1GB
    
    if [ "$available_memory" -lt "$required_memory" ]; then
        log "可用内存不足1GB，部署可能会很慢" WARN
    fi
    
    log "系统资源检查通过" SUCCESS
}

# 创建备份
create_backup() {
    log "创建备份..."
    local backup_name="sfap_backup_$(date +%Y%m%d_%H%M%S)"
    local backup_path="$BACKUP_DIR/$backup_name"
    
    mkdir -p "$BACKUP_DIR"
    
    # 备份当前代码
    if [ -d "$PROJECT_DIR" ]; then
        cp -r "$PROJECT_DIR" "$backup_path" || error_exit "代码备份失败"
        log "代码备份完成: $backup_path" SUCCESS
    fi
    
    # 备份Docker镜像
    if docker images | grep -q "sfap"; then
        log "备份Docker镜像..."
        docker save $(docker images --format "{{.Repository}}:{{.Tag}}" | grep sfap) | gzip > "$backup_path/docker_images.tar.gz" || log "Docker镜像备份失败" WARN
    fi
    
    # 备份数据库
    backup_database "$backup_path"
    
    # 清理旧备份
    find "$BACKUP_DIR" -type d -name "sfap_backup_*" -mtime +$BACKUP_RETENTION_DAYS -exec rm -rf {} + 2>/dev/null || true
    
    # 保存当前部署信息用于回滚
    echo "$backup_name" > "$BACKUP_DIR/.last_backup"
}

# 备份数据库
backup_database() {
    local backup_path="$1"
    
    # 检查数据库容器
    local db_containers=$(docker ps --format "{{.Names}}" | grep -E "mysql|mariadb|postgres" || true)
    
    if [ -n "$db_containers" ]; then
        log "备份数据库..."
        
        for container in $db_containers; do
            if [[ "$container" == *"mysql"* ]] || [[ "$container" == *"mariadb"* ]]; then
                docker exec "$container" mysqldump -u root -p"${MYSQL_ROOT_PASSWORD:-root}" --all-databases > "$backup_path/mysql_backup.sql" 2>/dev/null || log "MySQL备份失败" WARN
            elif [[ "$container" == *"postgres"* ]]; then
                docker exec "$container" pg_dumpall -U postgres > "$backup_path/postgres_backup.sql" 2>/dev/null || log "PostgreSQL备份失败" WARN
            fi
        done
        
        log "数据库备份完成" SUCCESS
    fi
}

# 更新代码
update_code() {
    log "更新代码..."
    cd "$PROJECT_DIR" || error_exit "无法进入项目目录: $PROJECT_DIR"
    
    # 检查Git状态
    if ! git status &>/dev/null; then
        error_exit "当前目录不是Git仓库"
    fi
    
    # 保存当前提交ID
    local current_commit=$(git rev-parse HEAD)
    echo "$current_commit" > "$BACKUP_DIR/.last_commit"
    
    # 拉取最新代码
    git fetch origin || error_exit "Git fetch失败"
    git reset --hard origin/main || error_exit "Git reset失败"
    
    # 更新子模块（如果有）
    if [ -f ".gitmodules" ]; then
        git submodule update --init --recursive || log "子模块更新失败" WARN
    fi
    
    local new_commit=$(git rev-parse HEAD)
    log "代码更新完成: $current_commit -> $new_commit" SUCCESS
}

# 构建Docker镜像
build_docker_images() {
    log "构建Docker镜像..."
    cd "$PROJECT_DIR"
    
    # 构建前端镜像
    log "构建前端镜像..."
    docker build -f deploy/frontend-Dockerfile -t "${DOCKER_REGISTRY}/sfap-frontend:${IMAGE_TAG}" . || error_exit "前端镜像构建失败"
    
    # 构建后端镜像
    log "构建后端镜像..."
    docker build -f deploy/backend-Dockerfile -t "${DOCKER_REGISTRY}/sfap-backend:${IMAGE_TAG}" ./backend || error_exit "后端镜像构建失败"
    
    # 构建AI服务镜像
    log "构建AI服务镜像..."
    docker build -f deploy/ai-service-Dockerfile -t "${DOCKER_REGISTRY}/sfap-ai-service:${IMAGE_TAG}" ./ai-service || error_exit "AI服务镜像构建失败"
    
    log "Docker镜像构建完成" SUCCESS
}

# 部署服务
deploy_services() {
    log "部署服务..."
    cd "$PROJECT_DIR"
    
    # 停止现有服务
    log "停止现有服务..."
    docker-compose down --remove-orphans || log "停止服务时出现警告" WARN
    
    # 清理未使用的网络和卷
    docker network prune -f || true
    docker volume prune -f || true
    
    # 启动服务
    log "启动新服务..."
    docker-compose up -d || error_exit "服务启动失败"
    
    # 等待服务启动
    log "等待服务启动..."
    sleep 30
    
    log "服务部署完成" SUCCESS
}

# 健康检查
health_check() {
    log "执行健康检查..."
    
    local max_attempts=60
    local attempt=1
    local check_interval=10
    
    while [ $attempt -le $max_attempts ]; do
        # 检查容器状态
        local unhealthy_containers=$(docker-compose ps | grep -v "Up" | grep -v "Name" | wc -l)
        
        if [ "$unhealthy_containers" -eq 0 ]; then
            # 检查HTTP健康端点
            if curl -f "$HEALTH_CHECK_URL" &>/dev/null; then
                log "健康检查通过" SUCCESS
                return 0
            fi
        fi
        
        log "健康检查失败，重试 $attempt/$max_attempts" WARN
        sleep $check_interval
        ((attempt++))
    done
    
    error_exit "健康检查失败，部署可能有问题"
}

# 回滚部署
rollback_deployment() {
    log "开始回滚部署..." WARN
    
    if [ -f "$BACKUP_DIR/.last_backup" ]; then
        local last_backup=$(cat "$BACKUP_DIR/.last_backup")
        local backup_path="$BACKUP_DIR/$last_backup"
        
        if [ -d "$backup_path" ]; then
            log "回滚到备份: $last_backup"
            
            # 停止当前服务
            docker-compose down --remove-orphans || true
            
            # 恢复代码
            rm -rf "$PROJECT_DIR"
            cp -r "$backup_path" "$PROJECT_DIR"
            
            # 恢复Docker镜像
            if [ -f "$backup_path/docker_images.tar.gz" ]; then
                docker load < "$backup_path/docker_images.tar.gz" || log "Docker镜像恢复失败" WARN
            fi
            
            # 启动服务
            cd "$PROJECT_DIR"
            docker-compose up -d || log "回滚后服务启动失败" ERROR
            
            log "回滚完成" SUCCESS
        else
            log "备份目录不存在，无法回滚" ERROR
        fi
    else
        log "没有找到备份信息，无法回滚" ERROR
    fi
}

# 清理旧资源
cleanup() {
    log "清理旧资源..."
    
    # 清理未使用的Docker镜像
    docker image prune -f || true
    
    # 清理未使用的容器
    docker container prune -f || true
    
    # 清理未使用的网络
    docker network prune -f || true
    
    # 清理未使用的卷（谨慎操作）
    if [ "$CLEANUP_VOLUMES" = "true" ]; then
        docker volume prune -f || true
    fi
    
    log "清理完成" SUCCESS
}

# 主函数
main() {
    log "开始Docker自动部署..." INFO
    
    # 设置环境变量
    export COMPOSE_PROJECT_NAME="sfap"
    export DOCKER_BUILDKIT=1
    
    check_commands
    check_system_resources
    create_backup
    update_code
    build_docker_images
    deploy_services
    health_check
    cleanup
    
    success_notify "SFAP Docker部署完成!"
}

# 信号处理
trap 'error_exit "部署被中断"' INT TERM

# 执行主函数
main "$@"
