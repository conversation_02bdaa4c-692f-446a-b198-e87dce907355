#!/bin/bash

# SFAP GitHub + 云服务器一键部署脚本
# 专门针对GitHub代码托管 + 云服务器Docker部署的完整解决方案

set -e

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
GITHUB_REPO="https://github.com/Arrbel/SFAP.git"
PROJECT_NAME="SFAP智慧农业平台"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
    exit 1
}

warn() {
    echo -e "${YELLOW}[WARN] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

# 显示横幅
show_banner() {
    echo -e "${GREEN}"
    cat << 'EOF'
   _____ ______      _____  
  / ____|  ____/\   |  __ \ 
 | (___ | |__ /  \  | |__) |
  \___ \|  __/ /\ \ |  ___/ 
  ____) | | / ____ \| |     
 |_____/|_|/_/    \_\_|     
                            
GitHub + 云服务器一键部署方案
EOF
    echo -e "${NC}"
}

# 检查运行环境
check_environment() {
    log "检查运行环境..."
    
    # 检查操作系统
    if [[ "$OSTYPE" != "linux-gnu"* ]]; then
        error "此脚本仅支持Linux系统"
    fi
    
    # 检查网络连接
    if ! ping -c 1 github.com &>/dev/null; then
        error "无法连接到GitHub，请检查网络"
    fi
    
    # 检查基础命令
    local required_commands=("curl" "wget" "git")
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" &>/dev/null; then
            error "缺少必要命令: $cmd"
        fi
    done
    
    log "环境检查通过"
}

# 收集用户配置
collect_user_config() {
    log "收集部署配置信息..."
    
    # 服务器信息
    read -p "请输入服务器IP地址: " SERVER_IP
    read -p "请输入服务器用户名 [sfap]: " SERVER_USER
    SERVER_USER=${SERVER_USER:-sfap}
    
    # 域名信息
    read -p "请输入域名 (可选): " DOMAIN
    DOMAIN=${DOMAIN:-$SERVER_IP}
    
    read -p "请输入邮箱地址: " EMAIL
    
    # GitHub信息
    read -p "请输入GitHub用户名 [Arrbel]: " GITHUB_USER
    GITHUB_USER=${GITHUB_USER:-Arrbel}
    
    # 数据库密码
    read -s -p "请输入MySQL root密码: " MYSQL_ROOT_PASSWORD
    echo
    read -s -p "请输入MySQL用户密码: " MYSQL_PASSWORD
    echo
    read -s -p "请输入Redis密码: " REDIS_PASSWORD
    echo
    
    info "配置信息收集完成"
}

# 安装Docker环境
install_docker() {
    log "安装Docker环境..."
    
    # 检查Docker是否已安装
    if command -v docker &>/dev/null; then
        info "Docker已安装，跳过安装步骤"
        return 0
    fi
    
    # 更新系统
    sudo apt update
    
    # 安装Docker
    curl -fsSL https://get.docker.com -o get-docker.sh
    sudo sh get-docker.sh
    
    # 安装Docker Compose
    sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    sudo chmod +x /usr/local/bin/docker-compose
    
    # 添加用户到docker组
    sudo usermod -aG docker "$USER"
    
    # 启动Docker服务
    sudo systemctl start docker
    sudo systemctl enable docker
    
    log "Docker环境安装完成"
}

# 配置SSH密钥
setup_ssh_keys() {
    log "配置SSH密钥..."
    
    # 检查SSH密钥是否存在
    if [ ! -f ~/.ssh/id_rsa ]; then
        info "生成SSH密钥..."
        ssh-keygen -t rsa -b 4096 -C "$EMAIL" -f ~/.ssh/id_rsa -N ""
    fi
    
    # 显示公钥
    echo ""
    echo "请将以下SSH公钥添加到GitHub账户:"
    echo "访问 GitHub → Settings → SSH and GPG keys → New SSH key"
    echo ""
    cat ~/.ssh/id_rsa.pub
    echo ""
    
    read -p "SSH密钥已添加到GitHub? (y/N): " ssh_confirmed
    if [[ ! "$ssh_confirmed" =~ ^[Yy]$ ]]; then
        error "请先添加SSH密钥到GitHub"
    fi
    
    # 测试SSH连接
    if ssh -T ************** 2>&1 | grep -q "successfully authenticated"; then
        log "GitHub SSH连接验证成功"
    else
        error "GitHub SSH连接验证失败"
    fi
}

# 克隆项目代码
clone_project() {
    log "克隆项目代码..."
    
    local target_dir="/opt/sfap"
    
    # 创建项目目录
    sudo mkdir -p "$target_dir"
    sudo chown -R "$USER:$USER" "$target_dir"
    
    # 克隆代码
    if [ -d "$target_dir/.git" ]; then
        info "项目已存在，拉取最新代码..."
        cd "$target_dir"
        git pull origin main
    else
        git clone "$GITHUB_REPO" "$target_dir"
        cd "$target_dir"
    fi
    
    log "项目代码克隆完成"
}

# 配置环境变量
setup_environment() {
    log "配置环境变量..."
    
    cat > /opt/sfap/.env.production << EOF
# SFAP生产环境配置
MYSQL_ROOT_PASSWORD=$MYSQL_ROOT_PASSWORD
MYSQL_PASSWORD=$MYSQL_PASSWORD
MYSQL_DATABASE=agriculture_mall
MYSQL_USER=sfap

REDIS_PASSWORD=$REDIS_PASSWORD

DOMAIN=$DOMAIN
EMAIL=$EMAIL
ENVIRONMENT=production

COMPOSE_PROJECT_NAME=sfap
DOCKER_BUILDKIT=1
EOF
    
    # 设置文件权限
    chmod 600 /opt/sfap/.env.production
    
    log "环境变量配置完成"
}

# 配置防火墙
setup_firewall() {
    log "配置防火墙..."
    
    # 安装UFW
    sudo apt install -y ufw
    
    # 重置防火墙规则
    sudo ufw --force reset
    sudo ufw default deny incoming
    sudo ufw default allow outgoing
    
    # 允许必要端口
    sudo ufw allow ssh
    sudo ufw allow 80/tcp
    sudo ufw allow 443/tcp
    
    # 启用防火墙
    sudo ufw --force enable
    
    log "防火墙配置完成"
}

# 创建项目目录结构
create_directories() {
    log "创建项目目录结构..."
    
    local directories=(
        "/opt/sfap/data/mysql"
        "/opt/sfap/data/redis"
        "/opt/sfap/logs/nginx"
        "/opt/sfap/logs/backend"
        "/opt/sfap/logs/ai"
        "/opt/sfap/logs/system"
        "/opt/sfap/uploads"
        "/opt/sfap/backup"
        "/opt/sfap/secrets"
    )
    
    for dir in "${directories[@]}"; do
        sudo mkdir -p "$dir"
        sudo chown -R "$USER:$USER" "$dir"
    done
    
    # 设置secrets目录权限
    chmod 700 /opt/sfap/secrets
    
    log "目录结构创建完成"
}

# 构建Docker镜像
build_images() {
    log "构建Docker镜像..."
    
    cd /opt/sfap
    
    # 构建前端镜像
    info "构建前端镜像..."
    docker build -f deploy/frontend-Dockerfile -t sfap/frontend:latest .
    
    # 构建后端镜像
    info "构建后端镜像..."
    docker build -f deploy/backend-Dockerfile -t sfap/backend:latest ./backend
    
    # 构建AI服务镜像
    info "构建AI服务镜像..."
    docker build -f deploy/ai-service-Dockerfile -t sfap/ai-service:latest ./ai-service
    
    log "Docker镜像构建完成"
}

# 部署应用
deploy_application() {
    log "部署应用..."
    
    cd /opt/sfap
    
    # 启动服务
    docker-compose -f deploy/docker-compose.prod.yml --env-file .env.production up -d
    
    log "应用部署完成"
}

# 健康检查
health_check() {
    log "执行健康检查..."
    
    local max_attempts=60
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        info "健康检查尝试 $attempt/$max_attempts"
        
        # 检查容器状态
        local running_containers=$(docker-compose -f /opt/sfap/deploy/docker-compose.prod.yml ps | grep "Up" | wc -l)
        
        if [ "$running_containers" -ge 5 ]; then
            # 检查HTTP端点
            if curl -f "http://localhost/health" &>/dev/null; then
                log "健康检查通过"
                return 0
            fi
        fi
        
        warn "健康检查失败，等待10秒后重试..."
        sleep 10
        ((attempt++))
    done
    
    error "健康检查失败，部署可能有问题"
}

# 配置自动化脚本
setup_automation() {
    log "配置自动化脚本..."
    
    # 设置脚本执行权限
    chmod +x /opt/sfap/deploy/*.sh
    
    # 配置定时任务
    (crontab -l 2>/dev/null; echo "*/5 * * * * /opt/sfap/system-monitor.sh") | crontab -
    (crontab -l 2>/dev/null; echo "0 2 * * * /opt/sfap/auto-backup.sh") | crontab -
    (crontab -l 2>/dev/null; echo "0 6 * * * /opt/sfap/analyze-logs.sh") | crontab -
    
    log "自动化脚本配置完成"
}

# 生成部署报告
generate_report() {
    log "生成部署报告..."
    
    cat > /opt/sfap/deployment-report.txt << EOF
SFAP GitHub + 云服务器部署报告
==============================

部署时间: $(date)
服务器IP: $SERVER_IP
域名: $DOMAIN
邮箱: $EMAIL

服务访问地址:
- 前端应用: http://$DOMAIN
- 后端API: http://$DOMAIN/api
- AI服务: http://$DOMAIN/ai

容器状态:
$(docker-compose -f /opt/sfap/deploy/docker-compose.prod.yml ps)

系统信息:
- 操作系统: $(lsb_release -d | cut -f2)
- Docker版本: $(docker --version)
- Docker Compose版本: $(docker-compose --version)

重要文件位置:
- 项目目录: /opt/sfap
- 配置文件: /opt/sfap/.env.production
- 日志目录: /opt/sfap/logs
- 备份目录: /opt/sfap/backup

常用命令:
- 查看状态: cd /opt/sfap && ./daily-ops.sh status
- 查看日志: cd /opt/sfap && ./daily-ops.sh logs
- 健康检查: cd /opt/sfap && ./daily-ops.sh health
- 备份数据: cd /opt/sfap && ./daily-ops.sh backup

部署完成！
EOF
    
    log "部署报告已生成: /opt/sfap/deployment-report.txt"
}

# 显示完成信息
show_completion() {
    echo -e "${GREEN}"
    cat << EOF

🎉 SFAP GitHub + 云服务器部署完成！

📱 访问地址:
   前端应用: http://$DOMAIN
   后端API: http://$DOMAIN/api
   AI服务: http://$DOMAIN/ai

📋 重要信息:
   - 部署报告: /opt/sfap/deployment-report.txt
   - 配置文件: /opt/sfap/.env.production
   - 项目目录: /opt/sfap

🔧 常用命令:
   - 查看状态: cd /opt/sfap && ./daily-ops.sh status
   - 更新应用: cd /opt/sfap && ./daily-ops.sh update
   - 查看日志: cd /opt/sfap && ./daily-ops.sh logs
   - 健康检查: cd /opt/sfap && ./daily-ops.sh health

📞 技术支持:
   如有问题，请查看部署文档或联系技术支持。

EOF
    echo -e "${NC}"
}

# 主函数
main() {
    show_banner
    
    log "开始SFAP GitHub + 云服务器一键部署..."
    
    check_environment
    collect_user_config
    install_docker
    setup_ssh_keys
    clone_project
    setup_environment
    setup_firewall
    create_directories
    build_images
    deploy_application
    health_check
    setup_automation
    generate_report
    show_completion
    
    log "SFAP GitHub + 云服务器部署完成!"
}

# 显示帮助信息
show_help() {
    echo "SFAP GitHub + 云服务器一键部署脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help    显示帮助信息"
    echo ""
    echo "此脚本将自动完成以下步骤:"
    echo "1. 检查运行环境"
    echo "2. 收集部署配置"
    echo "3. 安装Docker环境"
    echo "4. 配置SSH密钥"
    echo "5. 克隆项目代码"
    echo "6. 配置环境变量"
    echo "7. 配置防火墙"
    echo "8. 创建目录结构"
    echo "9. 构建Docker镜像"
    echo "10. 部署应用"
    echo "11. 执行健康检查"
    echo "12. 配置自动化脚本"
    echo "13. 生成部署报告"
}

# 处理命令行参数
case "${1:-}" in
    -h|--help|help)
        show_help
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
