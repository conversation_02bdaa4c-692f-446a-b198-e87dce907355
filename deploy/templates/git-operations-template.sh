#!/bin/bash

# SFAP项目Git操作标准模板
# 提供标准化的Git操作流程和最佳实践

set -e

# ================================
# 配置变量
# ================================
PROJECT_NAME="SFAP智慧农业平台"
PROJECT_PATH="E:\计算机设计大赛2\V4.0\新建文件夹\新建文件夹\SFAP"
GITHUB_REPO="**************:Arrbel/SFAP.git"
GITEE_REPO="*************:arrbel/sfap.git"
DEFAULT_BRANCH="main"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# ================================
# 工具函数
# ================================
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
    exit 1
}

warn() {
    echo -e "${YELLOW}[WARN] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

# 检查Git仓库状态
check_git_status() {
    if ! git status &>/dev/null; then
        error "当前目录不是Git仓库"
    fi
    
    local current_branch=$(git branch --show-current)
    info "当前分支: $current_branch"
    
    local status=$(git status --porcelain)
    if [ -n "$status" ]; then
        info "发现未提交的更改:"
        git status --short
    else
        info "工作目录干净"
    fi
}

# 验证远程仓库连接
verify_remote_connection() {
    info "验证远程仓库连接..."
    
    # 检查GitHub连接
    if ssh -T ************** 2>&1 | grep -q "successfully authenticated"; then
        echo -e "${GREEN}✓ GitHub SSH连接正常${NC}"
    else
        warn "GitHub SSH连接失败"
    fi
    
    # 检查Gitee连接
    if ssh -T ************* 2>&1 | grep -q "successfully authenticated"; then
        echo -e "${GREEN}✓ Gitee SSH连接正常${NC}"
    else
        warn "Gitee SSH连接失败"
    fi
}

# ================================
# Git初始化模板
# ================================
init_repository() {
    log "初始化Git仓库..."
    
    cd "$PROJECT_PATH" || error "无法进入项目目录"
    
    # 初始化Git仓库
    if [ ! -d ".git" ]; then
        git init
        git branch -M main
        info "Git仓库初始化完成"
    else
        info "Git仓库已存在"
    fi
    
    # 创建.gitignore文件
    create_gitignore
    
    # 配置远程仓库
    configure_remote_repositories
    
    # 首次提交
    initial_commit
}

# 创建.gitignore文件
create_gitignore() {
    if [ ! -f ".gitignore" ]; then
        cat > .gitignore << 'EOF'
# ================================
# SFAP项目.gitignore配置
# ================================

# 依赖文件
node_modules/
target/
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
pip-log.txt
pip-delete-this-directory.txt

# 构建产物
dist/
build/
*.jar
*.war
*.ear
*.class

# 日志文件
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 运行时文件
*.pid
*.seed
*.pid.lock

# 环境配置
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local

# IDE配置
.vscode/
.idea/
*.swp
*.swo
*~

# 系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 临时文件
tmp/
temp/
*.tmp
*.temp
*.cache

# 测试覆盖率
coverage/
.nyc_output/
.coverage
htmlcov/
.pytest_cache/

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 备份文件
*.bak
*.backup
*.old

# 压缩文件
*.zip
*.tar.gz
*.rar

# 密钥和证书
*.key
*.pem
*.crt
*.p12
secrets/

# Docker相关
.dockerignore
docker-compose.override.yml

# 自定义忽略
uploads/
data/
storage/
EOF
        info ".gitignore文件创建完成"
    else
        info ".gitignore文件已存在"
    fi
}

# 配置远程仓库
configure_remote_repositories() {
    info "配置远程仓库..."
    
    # 添加GitHub远程仓库
    if ! git remote get-url origin &>/dev/null; then
        git remote add origin "$GITHUB_REPO"
        info "GitHub远程仓库添加完成"
    else
        git remote set-url origin "$GITHUB_REPO"
        info "GitHub远程仓库URL更新完成"
    fi
    
    # 添加Gitee远程仓库
    if ! git remote get-url gitee &>/dev/null; then
        git remote add gitee "$GITEE_REPO"
        info "Gitee远程仓库添加完成"
    else
        git remote set-url gitee "$GITEE_REPO"
        info "Gitee远程仓库URL更新完成"
    fi
    
    # 验证远程仓库配置
    git remote -v
}

# 首次提交
initial_commit() {
    info "执行首次提交..."
    
    git add .
    git commit -m "初始提交: $PROJECT_NAME项目"
    
    # 推送到远程仓库
    git push -u origin main
    git push -u gitee main
    
    log "首次提交完成"
}

# ================================
# 日常Git操作模板
# ================================

# 标准提交流程
standard_commit() {
    local commit_message="$1"
    
    if [ -z "$commit_message" ]; then
        read -p "请输入提交信息: " commit_message
    fi
    
    if [ -z "$commit_message" ]; then
        error "提交信息不能为空"
    fi
    
    log "执行标准提交流程..."
    
    cd "$PROJECT_PATH" || error "无法进入项目目录"
    
    # 检查状态
    check_git_status
    
    # 添加文件
    info "添加文件更改..."
    git add .
    
    # 检查是否有更改
    if git diff --cached --quiet; then
        warn "没有文件更改，跳过提交"
        return 0
    fi
    
    # 提交更改
    local full_message="$commit_message - $(date '+%Y-%m-%d %H:%M:%S')"
    info "提交更改: $full_message"
    git commit -m "$full_message"
    
    log "提交完成"
}

# 推送到单个平台
push_to_platform() {
    local platform="$1"
    
    if [ -z "$platform" ]; then
        error "请指定平台 (origin|gitee)"
    fi
    
    log "推送到 $platform..."
    
    cd "$PROJECT_PATH" || error "无法进入项目目录"
    
    # 推送到指定平台
    git push "$platform" "$DEFAULT_BRANCH" || error "推送到 $platform 失败"
    
    log "推送到 $platform 完成"
}

# 同步双平台
sync_dual_platforms() {
    local commit_message="$1"
    
    log "同步双平台..."
    
    cd "$PROJECT_PATH" || error "无法进入项目目录"
    
    # 执行标准提交
    if [ -n "$commit_message" ]; then
        standard_commit "$commit_message"
    else
        standard_commit "同步更新"
    fi
    
    # 推送到GitHub
    info "推送到GitHub..."
    git push origin "$DEFAULT_BRANCH" || warn "推送到GitHub失败"
    
    # 推送到Gitee
    info "推送到Gitee..."
    git push gitee "$DEFAULT_BRANCH" || warn "推送到Gitee失败"
    
    log "双平台同步完成"
}

# 拉取远程更新
pull_updates() {
    local platform="${1:-origin}"
    
    log "拉取 $platform 更新..."
    
    cd "$PROJECT_PATH" || error "无法进入项目目录"
    
    # 检查本地是否有未提交的更改
    if ! git diff --quiet || ! git diff --cached --quiet; then
        warn "发现未提交的更改，请先提交或暂存"
        git status
        return 1
    fi
    
    # 拉取更新
    git pull "$platform" "$DEFAULT_BRANCH" || error "拉取更新失败"
    
    log "更新拉取完成"
}

# ================================
# 分支管理模板
# ================================

# 创建功能分支
create_feature_branch() {
    local feature_name="$1"
    
    if [ -z "$feature_name" ]; then
        read -p "请输入功能分支名称: " feature_name
    fi
    
    if [ -z "$feature_name" ]; then
        error "分支名称不能为空"
    fi
    
    local branch_name="feature/$feature_name"
    
    log "创建功能分支: $branch_name"
    
    cd "$PROJECT_PATH" || error "无法进入项目目录"
    
    # 确保在主分支
    git checkout "$DEFAULT_BRANCH"
    git pull origin "$DEFAULT_BRANCH"
    
    # 创建并切换到功能分支
    git checkout -b "$branch_name"
    
    info "功能分支 $branch_name 创建完成"
}

# 合并功能分支
merge_feature_branch() {
    local feature_name="$1"
    
    if [ -z "$feature_name" ]; then
        read -p "请输入要合并的功能分支名称: " feature_name
    fi
    
    if [ -z "$feature_name" ]; then
        error "分支名称不能为空"
    fi
    
    local branch_name="feature/$feature_name"
    
    log "合并功能分支: $branch_name"
    
    cd "$PROJECT_PATH" || error "无法进入项目目录"
    
    # 切换到主分支
    git checkout "$DEFAULT_BRANCH"
    git pull origin "$DEFAULT_BRANCH"
    
    # 合并功能分支
    git merge "$branch_name" --no-ff -m "合并功能分支: $feature_name"
    
    # 删除功能分支
    git branch -d "$branch_name"
    
    # 推送更新
    sync_dual_platforms "合并功能: $feature_name"
    
    log "功能分支合并完成"
}

# 创建发布分支
create_release_branch() {
    local version="$1"
    
    if [ -z "$version" ]; then
        read -p "请输入版本号 (例如: v1.0.0): " version
    fi
    
    if [ -z "$version" ]; then
        error "版本号不能为空"
    fi
    
    local branch_name="release/$version"
    
    log "创建发布分支: $branch_name"
    
    cd "$PROJECT_PATH" || error "无法进入项目目录"
    
    # 确保在主分支
    git checkout "$DEFAULT_BRANCH"
    git pull origin "$DEFAULT_BRANCH"
    
    # 创建发布分支
    git checkout -b "$branch_name"
    
    # 推送发布分支
    git push origin "$branch_name"
    git push gitee "$branch_name"
    
    info "发布分支 $branch_name 创建完成"
}

# ================================
# 紧急修复模板
# ================================

# 创建热修复分支
create_hotfix_branch() {
    local issue_description="$1"
    
    if [ -z "$issue_description" ]; then
        read -p "请输入问题描述: " issue_description
    fi
    
    if [ -z "$issue_description" ]; then
        error "问题描述不能为空"
    fi
    
    local timestamp=$(date +%Y%m%d-%H%M%S)
    local branch_name="hotfix/$timestamp-$issue_description"
    
    log "创建热修复分支: $branch_name"
    
    cd "$PROJECT_PATH" || error "无法进入项目目录"
    
    # 从主分支创建热修复分支
    git checkout "$DEFAULT_BRANCH"
    git pull origin "$DEFAULT_BRANCH"
    git checkout -b "$branch_name"
    
    info "热修复分支 $branch_name 创建完成"
    info "请在此分支上进行修复，完成后运行 merge_hotfix_branch"
}

# 合并热修复分支
merge_hotfix_branch() {
    local current_branch=$(git branch --show-current)
    
    if [[ ! "$current_branch" =~ ^hotfix/ ]]; then
        error "当前不在热修复分支上"
    fi
    
    log "合并热修复分支: $current_branch"
    
    cd "$PROJECT_PATH" || error "无法进入项目目录"
    
    # 提交当前更改
    if ! git diff --quiet || ! git diff --cached --quiet; then
        read -p "请输入修复描述: " fix_description
        git add .
        git commit -m "紧急修复: $fix_description"
    fi
    
    # 切换到主分支并合并
    git checkout "$DEFAULT_BRANCH"
    git pull origin "$DEFAULT_BRANCH"
    git merge "$current_branch" --no-ff -m "合并热修复: $current_branch"
    
    # 删除热修复分支
    git branch -d "$current_branch"
    
    # 推送更新
    sync_dual_platforms "紧急修复合并"
    
    log "热修复合并完成"
}

# ================================
# 主函数
# ================================

show_help() {
    echo "$PROJECT_NAME Git操作模板"
    echo ""
    echo "用法: $0 [操作]"
    echo ""
    echo "初始化操作:"
    echo "  init                初始化Git仓库"
    echo ""
    echo "日常操作:"
    echo "  commit [message]    标准提交流程"
    echo "  push-github         推送到GitHub"
    echo "  push-gitee          推送到Gitee"
    echo "  sync [message]      同步双平台"
    echo "  pull [platform]     拉取更新"
    echo ""
    echo "分支管理:"
    echo "  feature [name]      创建功能分支"
    echo "  merge-feature [name] 合并功能分支"
    echo "  release [version]   创建发布分支"
    echo ""
    echo "紧急修复:"
    echo "  hotfix [description] 创建热修复分支"
    echo "  merge-hotfix        合并热修复分支"
    echo ""
    echo "工具操作:"
    echo "  status              检查Git状态"
    echo "  verify              验证远程连接"
    echo ""
}

main() {
    case "${1:-}" in
        init)
            init_repository
            ;;
        commit)
            standard_commit "$2"
            ;;
        push-github)
            push_to_platform "origin"
            ;;
        push-gitee)
            push_to_platform "gitee"
            ;;
        sync)
            sync_dual_platforms "$2"
            ;;
        pull)
            pull_updates "$2"
            ;;
        feature)
            create_feature_branch "$2"
            ;;
        merge-feature)
            merge_feature_branch "$2"
            ;;
        release)
            create_release_branch "$2"
            ;;
        hotfix)
            create_hotfix_branch "$2"
            ;;
        merge-hotfix)
            merge_hotfix_branch
            ;;
        status)
            check_git_status
            ;;
        verify)
            verify_remote_connection
            ;;
        -h|--help|help)
            show_help
            ;;
        "")
            show_help
            ;;
        *)
            error "未知操作: $1"
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
