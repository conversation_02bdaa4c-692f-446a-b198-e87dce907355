# SFAP智慧农业平台环境变量配置模板
# 复制此文件为 .env 并根据实际情况修改配置

# ================================
# 基本配置
# ================================
# 部署环境 (development|testing|production)
ENVIRONMENT=production

# 域名配置
DOMAIN=your-domain.com
EMAIL=<EMAIL>

# 应用版本
APP_VERSION=1.0.0
BUILD_DATE=2024-08-02

# ================================
# 数据库配置
# ================================
# MySQL配置
MYSQL_ROOT_PASSWORD=your_secure_mysql_root_password_here
MYSQL_PASSWORD=your_secure_mysql_password_here
MYSQL_DATABASE=agriculture_mall
MYSQL_USER=sfap
DB_HOST=mysql
DB_PORT=3306
DB_NAME=agriculture_mall

# 数据库连接池配置
DB_POOL_MIN=5
DB_POOL_MAX=20
DB_POOL_ACQUIRE=30000
DB_POOL_IDLE=10000

# ================================
# Redis配置
# ================================
REDIS_PASSWORD=your_secure_redis_password_here
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0
REDIS_TIMEOUT=2000

# Redis连接池配置
REDIS_POOL_SIZE=10
REDIS_POOL_MIN=2

# ================================
# 应用服务配置
# ================================
# 前端配置
FRONTEND_PORT=80
API_BASE_URL=https://your-domain.com/api
AI_SERVICE_URL=https://your-domain.com/ai

# 后端配置
BACKEND_PORT=8080
SPRING_PROFILES_ACTIVE=prod
SERVER_PORT=8080

# AI服务配置
AI_SERVICE_PORT=5000
FLASK_ENV=production
PYTHONPATH=/app
WORKERS=4
TIMEOUT=300

# ================================
# Docker配置
# ================================
# Docker镜像配置
DOCKER_REGISTRY=docker.io
DOCKER_NAMESPACE=sfap
IMAGE_TAG=latest

# Docker Compose项目名称
COMPOSE_PROJECT_NAME=sfap

# ================================
# 安全配置
# ================================
# JWT配置
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRATION=86400

# 加密配置
ENCRYPTION_KEY=your_encryption_key_here
SALT_ROUNDS=12

# CORS配置
CORS_ORIGINS=https://your-domain.com,https://www.your-domain.com
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_HEADERS=Content-Type,Authorization,X-Requested-With

# ================================
# SSL/TLS配置
# ================================
# SSL证书配置
SSL_EMAIL=<EMAIL>
CERTBOT_STAGING=false
SSL_CERT_PATH=/etc/ssl/certs/sfap
SSL_KEY_PATH=/etc/ssl/private/sfap

# ================================
# 文件存储配置
# ================================
# 上传文件配置
UPLOAD_MAX_SIZE=100MB
UPLOAD_PATH=/opt/sfap/uploads
UPLOAD_ALLOWED_TYPES=jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx

# 静态文件配置
STATIC_PATH=/opt/sfap/static
STATIC_URL=/static

# ================================
# 缓存配置
# ================================
# 应用缓存配置
CACHE_TTL=3600
CACHE_PREFIX=sfap:
CACHE_DRIVER=redis

# 静态资源缓存
STATIC_CACHE_TTL=31536000
API_CACHE_TTL=300

# ================================
# 日志配置
# ================================
# 日志级别 (DEBUG|INFO|WARN|ERROR)
LOG_LEVEL=INFO
LOG_PATH=/opt/sfap/logs
LOG_MAX_SIZE=10MB
LOG_MAX_FILES=10

# 日志格式
LOG_FORMAT=json
LOG_TIMESTAMP=true

# ================================
# 监控配置
# ================================
# Prometheus配置
PROMETHEUS_PORT=9090
PROMETHEUS_RETENTION=200h
PROMETHEUS_SCRAPE_INTERVAL=15s

# Grafana配置
GRAFANA_PORT=3000
GRAFANA_PASSWORD=your_secure_grafana_password_here
GRAFANA_ADMIN_USER=admin

# 健康检查配置
HEALTH_CHECK_INTERVAL=30s
HEALTH_CHECK_TIMEOUT=10s
HEALTH_CHECK_RETRIES=3

# ================================
# 通知配置
# ================================
# 邮件通知配置
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_smtp_password_here
SMTP_FROM=SFAP系统 <<EMAIL>>

# Slack通知配置
SLACK_WEBHOOK=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
SLACK_CHANNEL=#deployments

# 企业微信通知配置
WECHAT_WEBHOOK=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_KEY

# ================================
# 备份配置
# ================================
# 备份策略配置
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=7
BACKUP_PATH=/opt/backup/sfap

# 远程备份配置
BACKUP_REMOTE_ENABLED=false
BACKUP_REMOTE_TYPE=s3
BACKUP_REMOTE_BUCKET=your-backup-bucket
BACKUP_REMOTE_REGION=us-east-1
BACKUP_REMOTE_ACCESS_KEY=your_access_key
BACKUP_REMOTE_SECRET_KEY=your_secret_key

# ================================
# 性能配置
# ================================
# JVM配置
JAVA_OPTS=-Xms1g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=200

# Node.js配置
NODE_ENV=production
NODE_OPTIONS=--max-old-space-size=4096

# Python配置
PYTHONUNBUFFERED=1
PYTHONDONTWRITEBYTECODE=1

# ================================
# 网络配置
# ================================
# 网络配置
NETWORK_SUBNET=**********/16
NETWORK_GATEWAY=**********

# 代理配置
HTTP_PROXY=
HTTPS_PROXY=
NO_PROXY=localhost,127.0.0.1

# ================================
# 开发配置
# ================================
# 调试配置
DEBUG=false
VERBOSE=false
PROFILING=false

# 热重载配置
HOT_RELOAD=false
WATCH_FILES=false

# ================================
# 第三方服务配置
# ================================
# 对象存储配置
OSS_ENABLED=false
OSS_ENDPOINT=https://oss-cn-hangzhou.aliyuncs.com
OSS_BUCKET=your-oss-bucket
OSS_ACCESS_KEY=your_oss_access_key
OSS_SECRET_KEY=your_oss_secret_key

# CDN配置
CDN_ENABLED=false
CDN_DOMAIN=cdn.your-domain.com
CDN_PATH=/static

# 短信服务配置
SMS_ENABLED=false
SMS_PROVIDER=aliyun
SMS_ACCESS_KEY=your_sms_access_key
SMS_SECRET_KEY=your_sms_secret_key

# ================================
# 特性开关
# ================================
# 功能开关
FEATURE_AI_ENABLED=true
FEATURE_ANALYTICS_ENABLED=true
FEATURE_NOTIFICATIONS_ENABLED=true
FEATURE_BACKUP_ENABLED=true

# 实验性功能
EXPERIMENTAL_FEATURES=false
BETA_FEATURES=false

# ================================
# 自定义配置
# ================================
# 业务相关配置
COMPANY_NAME=SFAP智慧农业平台
COMPANY_LOGO=/static/images/logo.png
COMPANY_URL=https://your-domain.com

# 联系信息
SUPPORT_EMAIL=<EMAIL>
SUPPORT_PHONE=+86-xxx-xxxx-xxxx
SUPPORT_QQ=xxxxxxxxx

# ================================
# 注意事项
# ================================
# 1. 所有密码和密钥都应该使用强密码
# 2. 生产环境中不要使用默认值
# 3. 定期更换密码和密钥
# 4. 不要将此文件提交到版本控制系统
# 5. 使用环境变量管理敏感信息
