#!/bin/bash

# 宝塔面板专用自动化部署脚本
# 针对宝塔面板环境优化的部署方案

set -e

# 配置变量
BT_PANEL_URL="http://**************:8888"
PROJECT_NAME="SFAP"
REPO_URL="https://github.com/your-username/SFAP.git"
BRANCH="main"

# 宝塔面板路径
BT_WWW_ROOT="/www/wwwroot"
BT_BACKUP_ROOT="/www/backup"
BT_LOG_ROOT="/www/logs"

# 项目路径
PROJECT_DIR="$BT_WWW_ROOT/sfap"
FRONTEND_DIR="$BT_WWW_ROOT/sfap-frontend"
BACKEND_DIR="$BT_WWW_ROOT/sfap-backend"
AI_SERVICE_DIR="$BT_WWW_ROOT/sfap-ai"

# 日志文件
DEPLOY_LOG="$BT_LOG_ROOT/sfap-deploy.log"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}" | tee -a $DEPLOY_LOG
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}" | tee -a $DEPLOY_LOG
}

warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}" | tee -a $DEPLOY_LOG
}

info() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')] INFO: $1${NC}" | tee -a $DEPLOY_LOG
}

# 检查宝塔面板环境
check_bt_environment() {
    log "检查宝塔面板环境..."
    
    # 检查必要的目录
    for dir in "$BT_WWW_ROOT" "$BT_BACKUP_ROOT" "$BT_LOG_ROOT"; do
        if [ ! -d "$dir" ]; then
            error "宝塔面板目录不存在: $dir"
            exit 1
        fi
    done
    
    # 检查必要的软件
    local missing_software=()
    
    if ! command -v node &> /dev/null; then
        missing_software+=("Node.js")
    fi
    
    if ! command -v java &> /dev/null; then
        missing_software+=("Java")
    fi
    
    if ! command -v python3 &> /dev/null; then
        missing_software+=("Python3")
    fi
    
    if ! command -v git &> /dev/null; then
        missing_software+=("Git")
    fi
    
    if [ ${#missing_software[@]} -gt 0 ]; then
        error "缺少必要软件: ${missing_software[*]}"
        error "请在宝塔面板软件商店中安装这些软件"
        exit 1
    fi
    
    log "宝塔面板环境检查通过"
}

# 创建项目目录结构
setup_directories() {
    log "创建项目目录结构..."
    
    mkdir -p "$PROJECT_DIR"
    mkdir -p "$FRONTEND_DIR"
    mkdir -p "$BACKEND_DIR"
    mkdir -p "$AI_SERVICE_DIR"
    mkdir -p "$BT_BACKUP_ROOT/sfap"
    mkdir -p "$(dirname $DEPLOY_LOG)"
    
    log "目录结构创建完成"
}

# 备份当前版本
backup_current_version() {
    log "备份当前版本..."
    
    local backup_timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_dir="$BT_BACKUP_ROOT/sfap/backup_$backup_timestamp"
    
    mkdir -p "$backup_dir"
    
    # 备份前端
    if [ -d "$FRONTEND_DIR" ]; then
        cp -r "$FRONTEND_DIR" "$backup_dir/frontend"
    fi
    
    # 备份后端
    if [ -d "$BACKEND_DIR" ]; then
        cp -r "$BACKEND_DIR" "$backup_dir/backend"
    fi
    
    # 备份AI服务
    if [ -d "$AI_SERVICE_DIR" ]; then
        cp -r "$AI_SERVICE_DIR" "$backup_dir/ai-service"
    fi
    
    log "备份完成: $backup_dir"
    echo "$backup_dir" > "$BT_BACKUP_ROOT/sfap/latest_backup.txt"
}

# 拉取最新代码
pull_latest_code() {
    log "拉取最新代码..."
    
    if [ -d "$PROJECT_DIR/.git" ]; then
        cd "$PROJECT_DIR"
        git fetch origin
        git reset --hard origin/$BRANCH
        git clean -fd
    else
        rm -rf "$PROJECT_DIR"
        git clone -b "$BRANCH" "$REPO_URL" "$PROJECT_DIR"
    fi
    
    cd "$PROJECT_DIR"
    local commit_hash=$(git rev-parse HEAD)
    local commit_message=$(git log -1 --pretty=format:"%s")
    
    log "代码拉取完成"
    info "当前提交: $commit_hash"
    info "提交信息: $commit_message"
}

# 部署前端
deploy_frontend() {
    log "开始部署前端..."
    
    cd "$PROJECT_DIR"
    
    # 检查package.json
    if [ ! -f "package.json" ]; then
        error "package.json文件不存在"
        exit 1
    fi
    
    # 安装依赖
    log "安装前端依赖..."
    npm install --production
    
    # 构建项目
    log "构建前端项目..."
    npm run build
    
    # 检查构建结果
    if [ ! -d "dist" ]; then
        error "前端构建失败，dist目录不存在"
        exit 1
    fi
    
    # 部署到前端目录
    rm -rf "$FRONTEND_DIR"/*
    cp -r dist/* "$FRONTEND_DIR/"
    
    # 设置正确的权限
    chown -R www:www "$FRONTEND_DIR"
    chmod -R 755 "$FRONTEND_DIR"
    
    log "前端部署完成"
}

# 部署后端
deploy_backend() {
    log "开始部署后端..."
    
    cd "$PROJECT_DIR/backend/main"
    
    # 检查pom.xml
    if [ ! -f "pom.xml" ]; then
        error "pom.xml文件不存在"
        exit 1
    fi
    
    # Maven构建
    log "Maven构建后端项目..."
    mvn clean package -DskipTests
    
    # 检查构建结果
    local jar_file="target/agriculture-mall-1.0.0.jar"
    if [ ! -f "$jar_file" ]; then
        error "后端构建失败，jar文件不存在"
        exit 1
    fi
    
    # 部署jar文件
    mkdir -p "$BACKEND_DIR"
    cp "$jar_file" "$BACKEND_DIR/app.jar"
    cp -r src/main/resources "$BACKEND_DIR/"
    
    # 设置正确的权限
    chown -R www:www "$BACKEND_DIR"
    chmod +x "$BACKEND_DIR/app.jar"
    
    log "后端部署完成"
}

# 部署AI服务
deploy_ai_service() {
    log "开始部署AI服务..."
    
    cd "$PROJECT_DIR/ai-service"
    
    # 检查requirements.txt
    if [ ! -f "requirements.txt" ]; then
        error "requirements.txt文件不存在"
        exit 1
    fi
    
    # 复制AI服务文件
    rm -rf "$AI_SERVICE_DIR"/*
    cp -r . "$AI_SERVICE_DIR/"
    
    cd "$AI_SERVICE_DIR"
    
    # 创建虚拟环境
    if [ ! -d "venv" ]; then
        python3 -m venv venv
    fi
    
    # 激活虚拟环境并安装依赖
    source venv/bin/activate
    pip install -r requirements.txt
    
    # 设置正确的权限
    chown -R www:www "$AI_SERVICE_DIR"
    
    log "AI服务部署完成"
}

# 更新宝塔面板网站配置
update_bt_website_config() {
    log "更新宝塔面板网站配置..."
    
    # 创建Nginx配置文件
    local nginx_config="$BT_WWW_ROOT/sfap/nginx.conf"
    
    cat > "$nginx_config" << 'EOF'
server {
    listen 8200;
    server_name **************;
    root /www/wwwroot/sfap-frontend;
    index index.html;
    
    # 前端路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # 后端API代理
    location /api/ {
        proxy_pass http://127.0.0.1:8081/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # AI服务代理
    location /ai/ {
        proxy_pass http://127.0.0.1:5000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 7d;
        add_header Cache-Control "public, immutable";
    }
}
EOF
    
    log "Nginx配置文件已更新"
}

# 重启服务
restart_services() {
    log "重启服务..."
    
    # 停止旧的Java进程
    pkill -f "agriculture-mall" || true
    sleep 2
    
    # 启动后端服务
    cd "$BACKEND_DIR"
    nohup java -jar app.jar --spring.profiles.active=prod > backend.log 2>&1 &
    
    # 停止旧的AI服务
    pkill -f "ai-service.*app.py" || true
    sleep 2
    
    # 启动AI服务
    cd "$AI_SERVICE_DIR"
    source venv/bin/activate
    nohup python app.py > ai-service.log 2>&1 &
    
    # 重载Nginx配置
    nginx -t && nginx -s reload
    
    log "服务重启完成"
}

# 健康检查
health_check() {
    log "执行健康检查..."
    
    local max_attempts=10
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        log "健康检查尝试 $attempt/$max_attempts"
        
        # 检查前端
        if curl -f -s http://localhost:8200 > /dev/null; then
            log "✓ 前端服务正常"
        else
            warning "✗ 前端服务异常"
        fi
        
        # 检查后端
        if curl -f -s http://localhost:8081/api/health > /dev/null; then
            log "✓ 后端服务正常"
            break
        else
            warning "✗ 后端服务异常，等待启动..."
            sleep 5
            ((attempt++))
        fi
    done
    
    if [ $attempt -gt $max_attempts ]; then
        error "健康检查失败，服务可能启动异常"
        return 1
    fi
    
    log "健康检查通过"
    return 0
}

# 清理旧备份
cleanup_old_backups() {
    log "清理旧备份..."
    
    cd "$BT_BACKUP_ROOT/sfap"
    
    # 保留最近5个备份
    ls -t | grep "backup_" | tail -n +6 | xargs -r rm -rf
    
    log "旧备份清理完成"
}

# 发送通知
send_notification() {
    local status=$1
    local message=$2
    
    # 这里可以集成钉钉、企业微信等通知
    log "部署通知: $status - $message"
}

# 回滚函数
rollback() {
    error "部署失败，开始回滚..."
    
    if [ -f "$BT_BACKUP_ROOT/sfap/latest_backup.txt" ]; then
        local backup_dir=$(cat "$BT_BACKUP_ROOT/sfap/latest_backup.txt")
        
        if [ -d "$backup_dir" ]; then
            log "回滚到备份: $backup_dir"
            
            # 回滚前端
            if [ -d "$backup_dir/frontend" ]; then
                rm -rf "$FRONTEND_DIR"/*
                cp -r "$backup_dir/frontend"/* "$FRONTEND_DIR/"
            fi
            
            # 回滚后端
            if [ -d "$backup_dir/backend" ]; then
                rm -rf "$BACKEND_DIR"/*
                cp -r "$backup_dir/backend"/* "$BACKEND_DIR/"
            fi
            
            # 回滚AI服务
            if [ -d "$backup_dir/ai-service" ]; then
                rm -rf "$AI_SERVICE_DIR"/*
                cp -r "$backup_dir/ai-service"/* "$AI_SERVICE_DIR/"
            fi
            
            restart_services
            log "回滚完成"
            send_notification "ROLLBACK" "部署失败，已回滚到上一个版本"
        else
            error "备份目录不存在: $backup_dir"
        fi
    else
        error "没有可用的备份"
    fi
}

# 主部署流程
main() {
    log "🚀 开始宝塔面板自动化部署 $PROJECT_NAME..."
    
    # 设置错误处理
    trap rollback ERR
    
    check_bt_environment
    setup_directories
    backup_current_version
    pull_latest_code
    deploy_frontend
    deploy_backend
    deploy_ai_service
    update_bt_website_config
    restart_services
    
    # 等待服务启动
    sleep 15
    
    if health_check; then
        cleanup_old_backups
        log "🎉 部署成功完成！"
        send_notification "SUCCESS" "SFAP项目部署成功"
        
        info "访问地址:"
        info "前端: http://**************:8200"
        info "后端API: http://**************:8081"
        info "部署日志: $DEPLOY_LOG"
    else
        error "健康检查失败"
        exit 1
    fi
}

# 执行主流程
main "$@"
