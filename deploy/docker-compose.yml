# SFAP生产环境Docker Compose配置
# 完整的微服务架构，包含前端、后端、AI服务、数据库、缓存和监控

version: '3.8'

services:
  # Nginx反向代理
  nginx:
    image: nginx:1.24-alpine
    container_name: sfap-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./ssl:/etc/ssl/certs:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - frontend
      - backend
      - ai-service
    networks:
      - sfap-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 3s
      retries: 3

  # 前端服务
  frontend:
    build:
      context: .
      dockerfile: deploy/frontend-Dockerfile
      args:
        - NODE_ENV=production
    container_name: sfap-frontend
    expose:
      - "80"
    environment:
      - API_BASE_URL=http://backend:8080
      - AI_SERVICE_URL=http://ai-service:5000
    depends_on:
      - backend
    networks:
      - sfap-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 3s
      retries: 3
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.frontend.rule=Host(`sfap.local`)"

  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: ../deploy/backend-Dockerfile
      args:
        - SPRING_PROFILES_ACTIVE=prod
    container_name: sfap-backend
    expose:
      - "8080"
    environment:
      # Spring配置
      - SPRING_PROFILES_ACTIVE=prod
      - SERVER_PORT=8080
      - MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE=health,info,metrics,prometheus
      - MANAGEMENT_ENDPOINT_HEALTH_SHOW_DETAILS=always

      # 数据库配置
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_NAME=agriculture_mall
      - DB_USER=sfap
      - DB_PASSWORD=${MYSQL_PASSWORD:-sfap123456}
      - SPRING_DATASOURCE_URL=******************************************************************************************************************************************************
      - SPRING_DATASOURCE_USERNAME=sfap
      - SPRING_DATASOURCE_PASSWORD=${MYSQL_PASSWORD:-sfap123456}
      - SPRING_DATASOURCE_DRIVER_CLASS_NAME=com.mysql.cj.jdbc.Driver

      # Redis配置
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
      - SPRING_REDIS_HOST=redis
      - SPRING_REDIS_PORT=6379
      - SPRING_REDIS_PASSWORD=${REDIS_PASSWORD:-}
      - SPRING_REDIS_DATABASE=0
      - SPRING_REDIS_TIMEOUT=2000ms

      # JPA配置
      - SPRING_JPA_HIBERNATE_DDL_AUTO=update
      - SPRING_JPA_SHOW_SQL=false
      - SPRING_JPA_DATABASE_PLATFORM=org.hibernate.dialect.MySQL8Dialect
      - SPRING_JPA_PROPERTIES_HIBERNATE_FORMAT_SQL=true

      # 连接池配置
      - SPRING_DATASOURCE_HIKARI_MAXIMUM_POOL_SIZE=20
      - SPRING_DATASOURCE_HIKARI_MINIMUM_IDLE=5
      - SPRING_DATASOURCE_HIKARI_CONNECTION_TIMEOUT=30000
      - SPRING_DATASOURCE_HIKARI_IDLE_TIMEOUT=600000
      - SPRING_DATASOURCE_HIKARI_MAX_LIFETIME=1800000

      # 文件上传配置
      - SPRING_SERVLET_MULTIPART_MAX_FILE_SIZE=100MB
      - SPRING_SERVLET_MULTIPART_MAX_REQUEST_SIZE=100MB
      - SPRING_SERVLET_MULTIPART_LOCATION=/app/temp

      # 日志配置
      - LOGGING_LEVEL_ROOT=INFO
      - LOGGING_LEVEL_COM_AGRICULTURE=DEBUG
      - LOGGING_FILE_PATH=/app/logs
      - LOGGING_PATTERN_CONSOLE=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n

      # JVM配置
      - JAVA_OPTS=-Xms512m -Xmx2g -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/app/logs/

    volumes:
      - backend_logs:/app/logs
      - backend_uploads:/app/uploads
      - backend_temp:/app/temp
      - backend_config:/app/config
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - sfap-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 120s
    deploy:
      resources:
        limits:
          memory: 2.5G
          cpus: '1.5'
        reservations:
          memory: 512M
          cpus: '0.5'

  # AI服务
  ai-service:
    build:
      context: ./ai-service
      dockerfile: ../deploy/ai-service-Dockerfile
      args:
        - ENVIRONMENT=production
    container_name: sfap-ai-service
    expose:
      - "5000"
    environment:
      - FLASK_ENV=production
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_NAME=agriculture_mall
      - DB_USER=sfap
      - DB_PASSWORD=${MYSQL_PASSWORD:-sfap123456}
      - MODEL_PATH=/app/saved_models
      - CACHE_DIR=/app/cache
      - LOG_LEVEL=INFO
    volumes:
      - ai_logs:/app/logs
      - ai_data:/app/data
      - ai_models:/app/saved_models
      - ai_cache:/app/cache
      - ai_temp:/app/temp
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - sfap-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 15s
      retries: 5
      start_period: 120s
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '0.5'

  # MySQL数据库
  mysql:
    image: mysql:8.0.34
    container_name: sfap-mysql
    expose:
      - "3306"
    ports:
      - "3306:3306"  # 仅开发环境暴露
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD:-sfap123456}
      - MYSQL_DATABASE=agriculture_mall
      - MYSQL_USER=sfap
      - MYSQL_PASSWORD=${MYSQL_PASSWORD:-sfap123456}
      - MYSQL_CHARACTER_SET_SERVER=utf8mb4
      - MYSQL_COLLATION_SERVER=utf8mb4_unicode_ci
      - TZ=Asia/Shanghai
    volumes:
      - mysql_data:/var/lib/mysql
      - mysql_config:/etc/mysql/conf.d
      - mysql_logs:/var/log/mysql
      - ./database:/docker-entrypoint-initdb.d:ro
      - ./mysql/my.cnf:/etc/mysql/conf.d/custom.cnf:ro
    command: >
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --explicit_defaults_for_timestamp=true
      --lower_case_table_names=1
      --max_allowed_packet=128M
      --max_connections=200
      --innodb_buffer_pool_size=512M
      --innodb_log_file_size=128M
      --innodb_flush_log_at_trx_commit=2
      --slow_query_log=1
      --slow_query_log_file=/var/log/mysql/slow.log
      --long_query_time=2
      --log_bin=/var/log/mysql/mysql-bin.log
      --binlog_format=ROW
      --expire_logs_days=7
      --sql-mode="STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO"
    networks:
      - sfap-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "sfap", "-p${MYSQL_PASSWORD:-sfap123456}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.25'

  # Redis缓存
  redis:
    image: redis:7.0-alpine
    container_name: sfap-redis
    expose:
      - "6379"
    ports:
      - "6379:6379"  # 仅开发环境暴露
    environment:
      - TZ=Asia/Shanghai
    command: >
      redis-server
      --requirepass ${REDIS_PASSWORD:-}
      --appendonly yes
      --appendfsync everysec
      --maxmemory 512mb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
      --tcp-keepalive 60
      --timeout 300
      --databases 16
      --maxclients 10000
    volumes:
      - redis_data:/data
      - redis_logs:/var/log/redis
    networks:
      - sfap-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 3s
      retries: 5
      start_period: 10s
    deploy:
      resources:
        limits:
          memory: 768M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.1'

  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: sfap-prometheus
    expose:
      - "9090"
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus:/etc/prometheus:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - sfap-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9090/-/healthy"]
      interval: 30s
      timeout: 3s
      retries: 3

  # Grafana监控面板
  grafana:
    image: grafana/grafana:latest
    container_name: sfap-grafana
    expose:
      - "3000"
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin123}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning:ro
    depends_on:
      - prometheus
    networks:
      - sfap-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f localhost:3000/api/health || exit 1"]
      interval: 30s
      timeout: 3s
      retries: 3

# 网络配置
networks:
  sfap-network:
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: **********/16
          gateway: **********
    driver_opts:
      com.docker.network.bridge.name: sfap-bridge
      com.docker.network.bridge.enable_icc: "true"
      com.docker.network.bridge.enable_ip_masquerade: "true"
      com.docker.network.driver.mtu: 1500

# 数据卷配置
volumes:
  # 数据库相关
  mysql_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/sfap/data/mysql
  mysql_config:
    driver: local
  mysql_logs:
    driver: local

  # Redis相关
  redis_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/sfap/data/redis
  redis_logs:
    driver: local

  # 后端相关
  backend_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/sfap/logs/backend
  backend_uploads:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/sfap/uploads
  backend_temp:
    driver: local
  backend_config:
    driver: local

  # AI服务相关
  ai_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/sfap/logs/ai
  ai_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/sfap/data/ai
  ai_models:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/sfap/models
  ai_cache:
    driver: local
  ai_temp:
    driver: local

  # Nginx相关
  nginx_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/sfap/logs/nginx

  # 监控相关
  prometheus_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/sfap/data/prometheus
  grafana_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/sfap/data/grafana
