#!/bin/bash

# SFAP环境检查脚本
# 用于验证服务器环境是否满足自动化部署要求

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 检查结果统计
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# 输出函数
log() {
    echo -e "${GREEN}[✓] $1${NC}"
    ((PASSED_CHECKS++))
}

error() {
    echo -e "${RED}[✗] $1${NC}"
    ((FAILED_CHECKS++))
}

warning() {
    echo -e "${YELLOW}[!] $1${NC}"
}

info() {
    echo -e "${BLUE}[i] $1${NC}"
}

check() {
    ((TOTAL_CHECKS++))
}

# 显示标题
show_header() {
    clear
    echo -e "${BLUE}"
    echo "=================================================="
    echo "    SFAP自动化部署环境检查"
    echo "=================================================="
    echo -e "${NC}"
    echo "正在检查服务器环境是否满足自动化部署要求..."
    echo ""
}

# 检查操作系统
check_os() {
    info "检查操作系统..."
    check
    
    if [ -f /etc/redhat-release ]; then
        OS_VERSION=$(cat /etc/redhat-release)
        log "操作系统: $OS_VERSION"
    elif [ -f /etc/lsb-release ]; then
        OS_VERSION=$(cat /etc/lsb-release | grep DESCRIPTION | cut -d'"' -f2)
        log "操作系统: $OS_VERSION"
    else
        error "无法识别操作系统版本"
        return 1
    fi
}

# 检查宝塔面板
check_bt_panel() {
    info "检查宝塔面板..."
    check
    
    if [ -d "/www" ] && [ -d "/www/server" ]; then
        log "宝塔面板已安装"
        
        # 检查宝塔版本
        if [ -f "/www/server/panel/class/common.py" ]; then
            BT_VERSION=$(grep -o "version = '[^']*'" /www/server/panel/class/common.py | cut -d"'" -f2)
            info "宝塔面板版本: $BT_VERSION"
        fi
    else
        error "宝塔面板未安装或安装不完整"
        echo "请先安装宝塔面板: wget -O install.sh http://download.bt.cn/install/install_6.0.sh && sh install.sh"
        return 1
    fi
}

# 检查必要软件
check_software() {
    info "检查必要软件..."
    
    # 检查Git
    check
    if command -v git &> /dev/null; then
        GIT_VERSION=$(git --version)
        log "Git已安装: $GIT_VERSION"
    else
        error "Git未安装"
        echo "安装方法: yum install -y git 或在宝塔面板软件商店安装"
    fi
    
    # 检查Node.js
    check
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        if [[ "$NODE_VERSION" =~ ^v1[6-9]\. ]] || [[ "$NODE_VERSION" =~ ^v[2-9][0-9]\. ]]; then
            log "Node.js已安装: $NODE_VERSION"
        else
            warning "Node.js版本过低: $NODE_VERSION (推荐16+)"
        fi
    else
        error "Node.js未安装"
        echo "安装方法: 在宝塔面板软件商店安装Node.js 16+"
    fi
    
    # 检查Java
    check
    if command -v java &> /dev/null; then
        JAVA_VERSION=$(java -version 2>&1 | head -n1)
        if [[ "$JAVA_VERSION" =~ "17." ]]; then
            log "Java已安装: $JAVA_VERSION"
        else
            warning "Java版本不是17: $JAVA_VERSION"
        fi
    else
        error "Java未安装"
        echo "安装方法: 在宝塔面板软件商店安装Java 17"
    fi
    
    # 检查Python3
    check
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version)
        log "Python3已安装: $PYTHON_VERSION"
    else
        error "Python3未安装"
        echo "安装方法: yum install -y python3 或在宝塔面板软件商店安装"
    fi
    
    # 检查MySQL
    check
    if command -v mysql &> /dev/null; then
        MYSQL_VERSION=$(mysql --version)
        log "MySQL已安装: $MYSQL_VERSION"
        
        # 检查MySQL服务状态
        if systemctl is-active --quiet mysql || systemctl is-active --quiet mysqld; then
            log "MySQL服务正在运行"
        else
            warning "MySQL服务未运行"
        fi
    else
        error "MySQL未安装"
        echo "安装方法: 在宝塔面板软件商店安装MySQL 8.0"
    fi
    
    # 检查Redis
    check
    if command -v redis-server &> /dev/null; then
        REDIS_VERSION=$(redis-server --version)
        log "Redis已安装: $REDIS_VERSION"
        
        # 检查Redis服务状态
        if systemctl is-active --quiet redis; then
            log "Redis服务正在运行"
        else
            warning "Redis服务未运行"
        fi
    else
        error "Redis未安装"
        echo "安装方法: 在宝塔面板软件商店安装Redis"
    fi
    
    # 检查Nginx
    check
    if command -v nginx &> /dev/null; then
        NGINX_VERSION=$(nginx -v 2>&1)
        log "Nginx已安装: $NGINX_VERSION"
        
        # 检查Nginx服务状态
        if systemctl is-active --quiet nginx; then
            log "Nginx服务正在运行"
        else
            warning "Nginx服务未运行"
        fi
    else
        error "Nginx未安装"
        echo "安装方法: 在宝塔面板软件商店安装Nginx"
    fi
}

# 检查端口占用
check_ports() {
    info "检查端口占用情况..."
    
    PORTS=(8200 8081 5000 3306 6379)
    
    for port in "${PORTS[@]}"; do
        check
        if netstat -tlnp | grep ":$port " > /dev/null; then
            PORT_INFO=$(netstat -tlnp | grep ":$port " | awk '{print $7}')
            warning "端口 $port 已被占用: $PORT_INFO"
        else
            log "端口 $port 可用"
        fi
    done
}

# 检查磁盘空间
check_disk_space() {
    info "检查磁盘空间..."
    check
    
    ROOT_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    WWW_USAGE=$(df /www 2>/dev/null | awk 'NR==2 {print $5}' | sed 's/%//' || echo "0")
    
    if [ "$ROOT_USAGE" -lt 80 ]; then
        log "根分区磁盘空间充足: ${ROOT_USAGE}% 已使用"
    else
        error "根分区磁盘空间不足: ${ROOT_USAGE}% 已使用"
    fi
    
    if [ "$WWW_USAGE" -lt 80 ] && [ "$WWW_USAGE" -gt 0 ]; then
        log "www分区磁盘空间充足: ${WWW_USAGE}% 已使用"
    elif [ "$WWW_USAGE" -eq 0 ]; then
        info "www目录在根分区上"
    else
        error "www分区磁盘空间不足: ${WWW_USAGE}% 已使用"
    fi
}

# 检查内存
check_memory() {
    info "检查内存..."
    check
    
    TOTAL_MEM=$(free -m | awk 'NR==2{print $2}')
    USED_MEM=$(free -m | awk 'NR==2{print $3}')
    MEM_USAGE=$((USED_MEM * 100 / TOTAL_MEM))
    
    if [ "$TOTAL_MEM" -gt 1024 ]; then
        log "内存充足: ${TOTAL_MEM}MB 总内存"
    else
        warning "内存可能不足: ${TOTAL_MEM}MB 总内存 (推荐2GB+)"
    fi
    
    if [ "$MEM_USAGE" -lt 80 ]; then
        log "内存使用率正常: ${MEM_USAGE}%"
    else
        warning "内存使用率较高: ${MEM_USAGE}%"
    fi
}

# 检查网络连接
check_network() {
    info "检查网络连接..."
    
    # 检查外网连接
    check
    if ping -c 1 8.8.8.8 &> /dev/null; then
        log "外网连接正常"
    else
        error "外网连接异常"
    fi
    
    # 检查Git仓库连接
    check
    if curl -I https://gitee.com &> /dev/null; then
        log "Gitee连接正常"
    else
        warning "Gitee连接异常，可能影响代码拉取"
    fi
}

# 检查权限
check_permissions() {
    info "检查目录权限..."
    
    # 检查www目录权限
    check
    if [ -w "/www/wwwroot" ]; then
        log "/www/wwwroot 目录可写"
    else
        error "/www/wwwroot 目录不可写"
    fi
    
    # 检查是否为root用户
    check
    if [ "$EUID" -eq 0 ]; then
        log "当前用户为root，权限充足"
    else
        warning "当前用户非root，可能需要sudo权限"
    fi
}

# 生成检查报告
generate_report() {
    echo ""
    echo -e "${BLUE}=================================================="
    echo "              环境检查报告"
    echo -e "==================================================${NC}"
    echo ""
    echo "总检查项: $TOTAL_CHECKS"
    echo -e "通过检查: ${GREEN}$PASSED_CHECKS${NC}"
    echo -e "失败检查: ${RED}$FAILED_CHECKS${NC}"
    echo ""
    
    if [ $FAILED_CHECKS -eq 0 ]; then
        echo -e "${GREEN}🎉 环境检查全部通过！可以开始部署自动化系统。${NC}"
        echo ""
        echo "下一步操作："
        echo "1. 准备Git仓库"
        echo "2. 上传部署脚本"
        echo "3. 运行快速配置脚本"
        return 0
    elif [ $FAILED_CHECKS -le 3 ]; then
        echo -e "${YELLOW}⚠️  环境检查发现少量问题，建议修复后再继续。${NC}"
        echo ""
        echo "建议操作："
        echo "1. 根据上述提示修复问题"
        echo "2. 重新运行环境检查"
        echo "3. 问题修复后继续部署"
        return 1
    else
        echo -e "${RED}❌ 环境检查发现较多问题，请先修复环境。${NC}"
        echo ""
        echo "必须操作："
        echo "1. 安装缺失的软件"
        echo "2. 修复配置问题"
        echo "3. 重新运行环境检查"
        return 2
    fi
}

# 主函数
main() {
    show_header
    
    check_os
    check_bt_panel
    check_software
    check_ports
    check_disk_space
    check_memory
    check_network
    check_permissions
    
    generate_report
}

# 执行检查
main "$@"
