#!/bin/bash

# SFAP自动化部署快速配置脚本
# 一键配置宝塔面板自动化部署环境

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="/www/wwwroot/sfap"
BACKUP_ROOT="/www/backup/sfap"
LOG_ROOT="/www/logs"

# 输出函数
log() {
    echo -e "${GREEN}[INFO] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
}

warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

# 显示欢迎信息
show_welcome() {
    clear
    echo -e "${GREEN}"
    echo "=================================================="
    echo "    SFAP智慧农业平台自动化部署配置向导"
    echo "=================================================="
    echo -e "${NC}"
    echo "本脚本将帮助您快速配置自动化部署环境"
    echo "包括：目录结构、权限设置、Nginx配置等"
    echo ""
    read -p "按回车键继续..." -r
}

# 检查系统环境
check_environment() {
    log "检查系统环境..."
    
    # 检查是否为root用户
    if [ "$EUID" -ne 0 ]; then
        error "请使用root用户运行此脚本"
        exit 1
    fi
    
    # 检查宝塔面板
    if [ ! -d "/www" ]; then
        error "未检测到宝塔面板，请先安装宝塔面板"
        exit 1
    fi
    
    # 检查必要软件
    local missing_software=()
    
    if ! command -v git &> /dev/null; then
        missing_software+=("Git")
    fi
    
    if ! command -v node &> /dev/null; then
        missing_software+=("Node.js")
    fi
    
    if ! command -v java &> /dev/null; then
        missing_software+=("Java")
    fi
    
    if ! command -v python3 &> /dev/null; then
        missing_software+=("Python3")
    fi
    
    if ! command -v nginx &> /dev/null; then
        missing_software+=("Nginx")
    fi
    
    if [ ${#missing_software[@]} -gt 0 ]; then
        error "缺少必要软件，请在宝塔面板软件商店中安装："
        for software in "${missing_software[@]}"; do
            echo "  - $software"
        done
        exit 1
    fi
    
    log "系统环境检查通过"
}

# 收集用户配置
collect_config() {
    log "收集配置信息..."
    
    echo ""
    echo "请输入以下配置信息："
    
    # Git仓库地址
    read -p "Git仓库地址 (例: https://gitee.com/username/SFAP.git): " REPO_URL
    if [ -z "$REPO_URL" ]; then
        error "Git仓库地址不能为空"
        exit 1
    fi
    
    # 分支名称
    read -p "部署分支 [main]: " BRANCH
    BRANCH=${BRANCH:-main}
    
    # 服务器IP
    read -p "服务器IP地址 [**************]: " SERVER_IP
    SERVER_IP=${SERVER_IP:-**************}
    
    # Webhook密钥
    read -p "Webhook密钥 (可选，增强安全性): " WEBHOOK_SECRET
    
    # 通知配置
    read -p "钉钉通知Webhook URL (可选): " DINGTALK_WEBHOOK
    
    echo ""
    log "配置信息收集完成"
}

# 创建目录结构
create_directories() {
    log "创建目录结构..."
    
    # 创建主要目录
    mkdir -p "$PROJECT_ROOT"
    mkdir -p "$PROJECT_ROOT/deploy"
    mkdir -p "$BACKUP_ROOT"
    mkdir -p "$LOG_ROOT"
    mkdir -p "/www/wwwroot/sfap-frontend"
    mkdir -p "/www/wwwroot/sfap-backend"
    mkdir -p "/www/wwwroot/sfap-ai"
    
    # 设置权限
    chown -R www:www "/www/wwwroot/sfap"*
    chown -R www:www "$BACKUP_ROOT"
    chmod -R 755 "/www/wwwroot/sfap"*
    
    log "目录结构创建完成"
}

# 复制部署脚本
copy_deploy_scripts() {
    log "复制部署脚本..."
    
    # 复制脚本文件
    cp "$SCRIPT_DIR"/*.sh "$PROJECT_ROOT/deploy/"
    cp "$SCRIPT_DIR"/*.php "$PROJECT_ROOT/deploy/"
    cp "$SCRIPT_DIR"/*.yml "$PROJECT_ROOT/deploy/"
    
    # 设置执行权限
    chmod +x "$PROJECT_ROOT/deploy"/*.sh
    
    # 更新配置文件中的变量
    sed -i "s|REPO_URL=\".*\"|REPO_URL=\"$REPO_URL\"|g" "$PROJECT_ROOT/deploy/auto-deploy.sh"
    sed -i "s|REPO_URL=\".*\"|REPO_URL=\"$REPO_URL\"|g" "$PROJECT_ROOT/deploy/bt-panel-deploy.sh"
    sed -i "s|BRANCH=\".*\"|BRANCH=\"$BRANCH\"|g" "$PROJECT_ROOT/deploy/auto-deploy.sh"
    sed -i "s|BRANCH=\".*\"|BRANCH=\"$BRANCH\"|g" "$PROJECT_ROOT/deploy/bt-panel-deploy.sh"
    
    if [ -n "$WEBHOOK_SECRET" ]; then
        sed -i "s|'secret' => '.*'|'secret' => '$WEBHOOK_SECRET'|g" "$PROJECT_ROOT/deploy/git-webhook.php"
    fi
    
    log "部署脚本复制完成"
}

# 配置Nginx
configure_nginx() {
    log "配置Nginx..."
    
    # 创建网站配置文件
    local nginx_config="/www/server/panel/vhost/nginx/sfap.conf"
    
    cat > "$nginx_config" << EOF
server {
    listen 8200;
    server_name $SERVER_IP;
    root /www/wwwroot/sfap-frontend;
    index index.html;
    
    # 访问日志
    access_log /www/logs/sfap_access.log;
    error_log /www/logs/sfap_error.log;
    
    # 前端路由支持
    location / {
        try_files \$uri \$uri/ /index.html;
    }
    
    # Webhook处理
    location /webhook.php {
        root /www/wwwroot/sfap/deploy;
        fastcgi_pass unix:/tmp/php-cgi-74.sock;
        fastcgi_index index.php;
        include fastcgi.conf;
    }
    
    # 后端API代理
    location /api/ {
        proxy_pass http://127.0.0.1:8081/api/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # AI服务代理
    location /ai/ {
        proxy_pass http://127.0.0.1:5000/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 7d;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "*";
    }
    
    # 安全配置
    location ~ /\. {
        deny all;
    }
    
    location ~ \.(sql|log|conf)$ {
        deny all;
    }
}
EOF
    
    # 测试Nginx配置
    if nginx -t; then
        nginx -s reload
        log "Nginx配置完成并重载成功"
    else
        error "Nginx配置有误，请检查"
        exit 1
    fi
}

# 创建系统服务
create_systemd_services() {
    log "创建系统服务..."
    
    # 后端服务
    cat > "/etc/systemd/system/sfap-backend.service" << EOF
[Unit]
Description=SFAP Backend Service
After=network.target mysql.service

[Service]
Type=simple
User=www
Group=www
WorkingDirectory=/www/wwwroot/sfap-backend
ExecStart=/usr/bin/java -jar app.jar --spring.profiles.active=prod
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF
    
    # AI服务
    cat > "/etc/systemd/system/sfap-ai.service" << EOF
[Unit]
Description=SFAP AI Service
After=network.target

[Service]
Type=simple
User=www
Group=www
WorkingDirectory=/www/wwwroot/sfap-ai
ExecStart=/www/wwwroot/sfap-ai/venv/bin/python app.py
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF
    
    # 重载systemd
    systemctl daemon-reload
    
    log "系统服务创建完成"
}

# 创建监控脚本
create_monitoring() {
    log "创建监控脚本..."
    
    cat > "$PROJECT_ROOT/deploy/monitor.sh" << 'EOF'
#!/bin/bash

# SFAP服务监控脚本

check_service() {
    local service_name=$1
    local port=$2
    local endpoint=$3
    
    if curl -f -s "http://localhost:$port$endpoint" > /dev/null; then
        echo "✓ $service_name 服务正常"
        return 0
    else
        echo "✗ $service_name 服务异常"
        return 1
    fi
}

echo "SFAP服务状态检查 - $(date)"
echo "================================"

# 检查前端
check_service "前端" "8200" "/"

# 检查后端
check_service "后端" "8081" "/api/health"

# 检查AI服务
check_service "AI服务" "5000" "/health"

echo "================================"
EOF
    
    chmod +x "$PROJECT_ROOT/deploy/monitor.sh"
    
    # 添加到crontab（每5分钟检查一次）
    (crontab -l 2>/dev/null; echo "*/5 * * * * $PROJECT_ROOT/deploy/monitor.sh >> $LOG_ROOT/monitor.log 2>&1") | crontab -
    
    log "监控脚本创建完成"
}

# 执行首次部署
initial_deploy() {
    log "执行首次部署..."
    
    if [ -n "$REPO_URL" ]; then
        cd "$PROJECT_ROOT/deploy"
        
        # 克隆代码
        if [ ! -d "$PROJECT_ROOT/.git" ]; then
            git clone -b "$BRANCH" "$REPO_URL" "$PROJECT_ROOT/source"
            
            # 复制源码到部署目录
            cp -r "$PROJECT_ROOT/source"/* "$PROJECT_ROOT/"
            rm -rf "$PROJECT_ROOT/source"
        fi
        
        # 执行部署
        ./bt-panel-deploy.sh
        
        log "首次部署完成"
    else
        warning "跳过首次部署，请手动执行部署脚本"
    fi
}

# 显示完成信息
show_completion() {
    clear
    echo -e "${GREEN}"
    echo "=================================================="
    echo "    🎉 SFAP自动化部署配置完成！"
    echo "=================================================="
    echo -e "${NC}"
    echo ""
    echo "配置信息："
    echo "  项目目录: $PROJECT_ROOT"
    echo "  备份目录: $BACKUP_ROOT"
    echo "  日志目录: $LOG_ROOT"
    echo "  Git仓库: $REPO_URL"
    echo "  部署分支: $BRANCH"
    echo ""
    echo "访问地址："
    echo "  前端: http://$SERVER_IP:8200"
    echo "  后端API: http://$SERVER_IP:8081"
    echo "  Webhook: http://$SERVER_IP:8200/webhook.php"
    echo ""
    echo "常用命令："
    echo "  手动部署: cd $PROJECT_ROOT/deploy && ./bt-panel-deploy.sh"
    echo "  查看日志: tail -f $LOG_ROOT/sfap-deploy.log"
    echo "  服务监控: $PROJECT_ROOT/deploy/monitor.sh"
    echo ""
    echo "下一步："
    echo "1. 在Git仓库中配置Webhook: http://$SERVER_IP:8200/webhook.php"
    echo "2. 提交代码测试自动部署功能"
    echo "3. 查看部署日志确认部署状态"
    echo ""
    log "配置完成！现在您可以享受一键部署的便利了！"
}

# 主函数
main() {
    show_welcome
    check_environment
    collect_config
    create_directories
    copy_deploy_scripts
    configure_nginx
    create_systemd_services
    create_monitoring
    initial_deploy
    show_completion
}

# 执行主函数
main "$@"
