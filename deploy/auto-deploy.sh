#!/bin/bash

# SFAP自动化部署脚本
# 适用于宝塔面板环境

set -e

# 配置变量
PROJECT_NAME="SFAP"
REPO_URL="https://gitee.com/your-username/SFAP.git"  # 替换为你的仓库地址（推荐使用Gitee，国内访问更快）
BRANCH="main"
DEPLOY_DIR="/www/wwwroot/sfap"
BACKUP_DIR="/www/backup/sfap"
LOG_FILE="/www/logs/deploy.log"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}" | tee -a $LOG_FILE
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}" | tee -a $LOG_FILE
}

warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}" | tee -a $LOG_FILE
}

# 创建必要目录
create_directories() {
    log "创建部署目录..."
    mkdir -p $DEPLOY_DIR
    mkdir -p $BACKUP_DIR
    mkdir -p $(dirname $LOG_FILE)
}

# 备份当前版本
backup_current() {
    if [ -d "$DEPLOY_DIR" ]; then
        log "备份当前版本..."
        BACKUP_NAME="backup_$(date +%Y%m%d_%H%M%S)"
        cp -r $DEPLOY_DIR $BACKUP_DIR/$BACKUP_NAME
        log "备份完成: $BACKUP_DIR/$BACKUP_NAME"
    fi
}

# 拉取最新代码
pull_code() {
    log "拉取最新代码..."
    
    if [ -d "$DEPLOY_DIR/.git" ]; then
        cd $DEPLOY_DIR
        git fetch origin
        git reset --hard origin/$BRANCH
        git clean -fd
    else
        rm -rf $DEPLOY_DIR
        git clone -b $BRANCH $REPO_URL $DEPLOY_DIR
        cd $DEPLOY_DIR
    fi
    
    log "代码拉取完成"
}

# 构建前端
build_frontend() {
    log "开始构建前端..."
    cd $DEPLOY_DIR
    
    # 检查Node.js版本
    if ! command -v node &> /dev/null; then
        error "Node.js未安装，请先安装Node.js"
        exit 1
    fi
    
    # 安装依赖
    log "安装前端依赖..."
    npm install --production
    
    # 构建项目
    log "构建前端项目..."
    npm run build
    
    # 检查构建结果
    if [ ! -d "dist" ]; then
        error "前端构建失败，dist目录不存在"
        exit 1
    fi
    
    log "前端构建完成"
}

# 构建后端
build_backend() {
    log "开始构建后端..."
    cd $DEPLOY_DIR/backend/main
    
    # 检查Java版本
    if ! command -v java &> /dev/null; then
        error "Java未安装，请先安装Java 17"
        exit 1
    fi
    
    # Maven构建
    log "Maven构建后端项目..."
    mvn clean package -DskipTests
    
    # 检查构建结果
    if [ ! -f "target/agriculture-mall-1.0.0.jar" ]; then
        error "后端构建失败，jar文件不存在"
        exit 1
    fi
    
    log "后端构建完成"
}

# 部署AI服务
deploy_ai_service() {
    log "部署AI服务..."
    cd $DEPLOY_DIR/ai-service
    
    # 创建虚拟环境
    if [ ! -d "venv" ]; then
        python3 -m venv venv
    fi
    
    # 激活虚拟环境并安装依赖
    source venv/bin/activate
    pip install -r requirements.txt
    
    log "AI服务部署完成"
}

# 更新Nginx配置
update_nginx() {
    log "更新Nginx配置..."
    
    # 复制前端文件到Nginx目录
    cp -r $DEPLOY_DIR/dist/* /www/wwwroot/sfap/
    
    # 重载Nginx配置
    nginx -t && nginx -s reload
    
    log "Nginx配置更新完成"
}

# 重启服务
restart_services() {
    log "重启服务..."
    
    # 重启后端服务（通过宝塔面板API或直接操作）
    # 这里需要根据你的宝塔面板配置调整
    
    # 重启AI服务
    cd $DEPLOY_DIR/ai-service
    pkill -f "python.*app.py" || true
    nohup python app.py > /dev/null 2>&1 &
    
    log "服务重启完成"
}

# 健康检查
health_check() {
    log "执行健康检查..."
    
    # 检查前端
    if curl -f http://localhost:8200 > /dev/null 2>&1; then
        log "前端服务正常"
    else
        error "前端服务异常"
        return 1
    fi
    
    # 检查后端
    if curl -f http://localhost:8081/api/health > /dev/null 2>&1; then
        log "后端服务正常"
    else
        warning "后端服务可能异常，请检查"
    fi
    
    # 检查AI服务
    if curl -f http://localhost:5000/health > /dev/null 2>&1; then
        log "AI服务正常"
    else
        warning "AI服务可能异常，请检查"
    fi
}

# 回滚函数
rollback() {
    error "部署失败，开始回滚..."
    
    # 获取最新备份
    LATEST_BACKUP=$(ls -t $BACKUP_DIR | head -n1)
    
    if [ -n "$LATEST_BACKUP" ]; then
        log "回滚到备份: $LATEST_BACKUP"
        rm -rf $DEPLOY_DIR
        cp -r $BACKUP_DIR/$LATEST_BACKUP $DEPLOY_DIR
        update_nginx
        restart_services
        log "回滚完成"
    else
        error "没有可用的备份，请手动处理"
    fi
}

# 主部署流程
main() {
    log "开始自动化部署 $PROJECT_NAME..."
    
    # 设置错误处理
    trap rollback ERR
    
    create_directories
    backup_current
    pull_code
    build_frontend
    build_backend
    deploy_ai_service
    update_nginx
    restart_services
    
    # 等待服务启动
    sleep 10
    
    if health_check; then
        log "🎉 部署成功完成！"
        
        # 清理旧备份（保留最近5个）
        cd $BACKUP_DIR
        ls -t | tail -n +6 | xargs -r rm -rf
        
        log "部署日志已保存到: $LOG_FILE"
    else
        error "健康检查失败，请检查服务状态"
        exit 1
    fi
}

# 执行主流程
main "$@"
