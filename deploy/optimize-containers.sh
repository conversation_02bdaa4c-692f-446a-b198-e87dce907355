#!/bin/bash

# SFAP容器优化脚本
# 优化镜像大小、网络通信和数据持久化

set -e

# 配置变量
PROJECT_DIR="/opt/sfap"
REGISTRY="${DOCKER_REGISTRY:-docker.io}"
NAMESPACE="${DOCKER_NAMESPACE:-sfap}"
VERSION="${VERSION:-latest}"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log() {
    local level=${2:-INFO}
    local message="[$(date '+%Y-%m-%d %H:%M:%S')] [$level] $1"
    
    case $level in
        ERROR) echo -e "${RED}$message${NC}" ;;
        WARN)  echo -e "${YELLOW}$message${NC}" ;;
        SUCCESS) echo -e "${GREEN}$message${NC}" ;;
        INFO) echo -e "${BLUE}$message${NC}" ;;
        *) echo "$message" ;;
    esac
}

# 错误处理
error_exit() {
    log "$1" ERROR
    exit 1
}

# 检查Docker环境
check_docker() {
    log "检查Docker环境..." INFO
    
    if ! command -v docker &> /dev/null; then
        error_exit "Docker未安装"
    fi
    
    if ! docker info &>/dev/null; then
        error_exit "Docker服务未运行"
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        error_exit "Docker Compose未安装"
    fi
    
    log "Docker环境检查通过" SUCCESS
}

# 创建优化的Dockerfile
create_optimized_dockerfiles() {
    log "创建优化的Dockerfile..." INFO
    
    # 创建.dockerignore文件
    cat > .dockerignore << 'EOF'
# Git
.git
.gitignore
.gitattributes

# Documentation
README.md
CHANGELOG.md
LICENSE
docs/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Dependencies
node_modules/
target/
__pycache__/
*.pyc
.pytest_cache/

# Build artifacts
dist/
build/
*.tar.gz
*.zip

# Environment
.env
.env.local
.env.*.local

# Testing
coverage/
.nyc_output/
test-results/

# Temporary files
tmp/
temp/
*.tmp
EOF
    
    log "优化的Dockerfile配置创建完成" SUCCESS
}

# 构建优化镜像
build_optimized_images() {
    log "构建优化镜像..." INFO
    
    cd "$PROJECT_DIR"
    
    # 构建前端镜像
    log "构建前端镜像..."
    docker build \
        --file deploy/frontend-Dockerfile \
        --tag "${REGISTRY}/${NAMESPACE}/frontend:${VERSION}" \
        --tag "${REGISTRY}/${NAMESPACE}/frontend:latest" \
        --build-arg NODE_ENV=production \
        --build-arg BUILD_DATE="$(date -u +'%Y-%m-%dT%H:%M:%SZ')" \
        --build-arg VCS_REF="$(git rev-parse --short HEAD)" \
        --compress \
        --squash \
        . || error_exit "前端镜像构建失败"
    
    # 构建后端镜像
    log "构建后端镜像..."
    docker build \
        --file deploy/backend-Dockerfile \
        --tag "${REGISTRY}/${NAMESPACE}/backend:${VERSION}" \
        --tag "${REGISTRY}/${NAMESPACE}/backend:latest" \
        --build-arg SPRING_PROFILES_ACTIVE=prod \
        --build-arg BUILD_DATE="$(date -u +'%Y-%m-%dT%H:%M:%SZ')" \
        --build-arg VCS_REF="$(git rev-parse --short HEAD)" \
        --compress \
        --squash \
        ./backend || error_exit "后端镜像构建失败"
    
    # 构建AI服务镜像
    log "构建AI服务镜像..."
    docker build \
        --file deploy/ai-service-Dockerfile \
        --tag "${REGISTRY}/${NAMESPACE}/ai-service:${VERSION}" \
        --tag "${REGISTRY}/${NAMESPACE}/ai-service:latest" \
        --build-arg ENVIRONMENT=production \
        --build-arg BUILD_DATE="$(date -u +'%Y-%m-%dT%H:%M:%SZ')" \
        --build-arg VCS_REF="$(git rev-parse --short HEAD)" \
        --compress \
        --squash \
        ./ai-service || error_exit "AI服务镜像构建失败"
    
    log "镜像构建完成" SUCCESS
}

# 分析镜像大小
analyze_image_sizes() {
    log "分析镜像大小..." INFO
    
    echo "镜像大小统计:"
    echo "=============="
    
    local images=(
        "${REGISTRY}/${NAMESPACE}/frontend:${VERSION}"
        "${REGISTRY}/${NAMESPACE}/backend:${VERSION}"
        "${REGISTRY}/${NAMESPACE}/ai-service:${VERSION}"
    )
    
    for image in "${images[@]}"; do
        local size=$(docker images --format "table {{.Size}}" "$image" | tail -n 1)
        echo "$image: $size"
    done
    
    echo ""
    log "镜像大小分析完成" SUCCESS
}

# 优化镜像
optimize_images() {
    log "优化镜像..." INFO
    
    # 使用dive分析镜像层（如果安装了）
    if command -v dive &> /dev/null; then
        log "使用dive分析镜像层..."
        
        local images=(
            "${REGISTRY}/${NAMESPACE}/frontend:${VERSION}"
            "${REGISTRY}/${NAMESPACE}/backend:${VERSION}"
            "${REGISTRY}/${NAMESPACE}/ai-service:${VERSION}"
        )
        
        for image in "${images[@]}"; do
            log "分析镜像: $image"
            dive "$image" --ci || log "镜像 $image 分析失败" WARN
        done
    fi
    
    # 清理悬空镜像
    log "清理悬空镜像..."
    docker image prune -f || true
    
    log "镜像优化完成" SUCCESS
}

# 创建数据持久化目录
create_persistent_directories() {
    log "创建数据持久化目录..." INFO
    
    local directories=(
        "/opt/sfap/data/mysql"
        "/opt/sfap/data/redis"
        "/opt/sfap/data/prometheus"
        "/opt/sfap/data/grafana"
        "/opt/sfap/data/ai"
        "/opt/sfap/logs/nginx"
        "/opt/sfap/logs/backend"
        "/opt/sfap/logs/ai"
        "/opt/sfap/uploads"
        "/opt/sfap/models"
        "/opt/sfap/secrets"
    )
    
    for dir in "${directories[@]}"; do
        mkdir -p "$dir"
        chown -R 1001:1001 "$dir" 2>/dev/null || true
        log "创建目录: $dir"
    done
    
    log "数据持久化目录创建完成" SUCCESS
}

# 生成密钥文件
generate_secrets() {
    log "生成密钥文件..." INFO
    
    local secrets_dir="/opt/sfap/secrets"
    mkdir -p "$secrets_dir"
    
    # 生成MySQL密码
    if [ ! -f "$secrets_dir/mysql_root_password.txt" ]; then
        openssl rand -base64 32 > "$secrets_dir/mysql_root_password.txt"
        log "生成MySQL root密码"
    fi
    
    if [ ! -f "$secrets_dir/mysql_password.txt" ]; then
        openssl rand -base64 32 > "$secrets_dir/mysql_password.txt"
        log "生成MySQL用户密码"
    fi
    
    # 生成Redis密码
    if [ ! -f "$secrets_dir/redis_password.txt" ]; then
        openssl rand -base64 32 > "$secrets_dir/redis_password.txt"
        log "生成Redis密码"
    fi
    
    # 设置权限
    chmod 600 "$secrets_dir"/*.txt
    chown root:root "$secrets_dir"/*.txt
    
    log "密钥文件生成完成" SUCCESS
}

# 创建网络优化配置
create_network_optimization() {
    log "创建网络优化配置..." INFO
    
    # 创建自定义网络配置
    cat > /opt/sfap/network-optimization.conf << 'EOF'
# Docker网络优化配置

# 启用IP转发
net.ipv4.ip_forward = 1

# 优化TCP参数
net.core.rmem_max = 16777216
net.core.wmem_max = 16777216
net.ipv4.tcp_rmem = 4096 87380 16777216
net.ipv4.tcp_wmem = 4096 65536 16777216

# 优化连接跟踪
net.netfilter.nf_conntrack_max = 1048576
net.netfilter.nf_conntrack_tcp_timeout_established = 7200

# 优化文件描述符
fs.file-max = 1048576
EOF
    
    # 应用网络优化（需要root权限）
    if [ "$EUID" -eq 0 ]; then
        sysctl -p /opt/sfap/network-optimization.conf || log "网络优化应用失败" WARN
    fi
    
    log "网络优化配置创建完成" SUCCESS
}

# 创建监控配置
create_monitoring_config() {
    log "创建监控配置..." INFO
    
    mkdir -p /opt/sfap/monitoring/{prometheus,grafana}
    
    # 创建Prometheus配置
    cat > /opt/sfap/monitoring/prometheus/prometheus.yml << 'EOF'
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'sfap-backend'
    static_configs:
      - targets: ['backend:8080']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 30s

  - job_name: 'sfap-ai-service'
    static_configs:
      - targets: ['ai-service:5000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:80']
    metrics_path: '/nginx_status'
    scrape_interval: 30s
EOF
    
    log "监控配置创建完成" SUCCESS
}

# 创建备份脚本
create_backup_script() {
    log "创建备份脚本..." INFO
    
    cat > /opt/sfap/backup-containers.sh << 'EOF'
#!/bin/bash

# 容器数据备份脚本
BACKUP_DIR="/opt/backup/containers"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p "$BACKUP_DIR"

# 备份MySQL数据
docker exec sfap-mysql-prod mysqldump -u root -p"$(cat /opt/sfap/secrets/mysql_root_password.txt)" --all-databases | gzip > "$BACKUP_DIR/mysql_$DATE.sql.gz"

# 备份Redis数据
docker exec sfap-redis-prod redis-cli --rdb /data/dump_$DATE.rdb
docker cp sfap-redis-prod:/data/dump_$DATE.rdb "$BACKUP_DIR/"

# 备份上传文件
tar -czf "$BACKUP_DIR/uploads_$DATE.tar.gz" -C /opt/sfap uploads/

# 备份AI模型
tar -czf "$BACKUP_DIR/models_$DATE.tar.gz" -C /opt/sfap models/

# 清理旧备份（保留7天）
find "$BACKUP_DIR" -type f -mtime +7 -delete

echo "备份完成: $DATE"
EOF
    
    chmod +x /opt/sfap/backup-containers.sh
    
    log "备份脚本创建完成" SUCCESS
}

# 性能测试
performance_test() {
    log "执行性能测试..." INFO
    
    # 等待服务启动
    sleep 30
    
    # 测试前端响应
    if curl -f http://localhost/health &>/dev/null; then
        log "前端健康检查通过" SUCCESS
    else
        log "前端健康检查失败" WARN
    fi
    
    # 测试后端API
    if curl -f http://localhost/api/health &>/dev/null; then
        log "后端API健康检查通过" SUCCESS
    else
        log "后端API健康检查失败" WARN
    fi
    
    # 测试AI服务
    if curl -f http://localhost/ai/health &>/dev/null; then
        log "AI服务健康检查通过" SUCCESS
    else
        log "AI服务健康检查失败" WARN
    fi
    
    log "性能测试完成" SUCCESS
}

# 生成部署报告
generate_deployment_report() {
    log "生成部署报告..." INFO
    
    cat > /opt/sfap/deployment-report.txt << EOF
SFAP容器部署报告
================

部署时间: $(date)
版本: $VERSION
镜像仓库: $REGISTRY/$NAMESPACE

镜像信息:
$(docker images --format "table {{.Repository}}:{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}" | grep "$NAMESPACE")

容器状态:
$(docker-compose -f deploy/docker-compose.yml ps)

资源使用:
$(docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}")

网络信息:
$(docker network ls | grep sfap)

数据卷信息:
$(docker volume ls | grep sfap)

健康检查:
- 前端: $(curl -s -o /dev/null -w "%{http_code}" http://localhost/health || echo "失败")
- 后端: $(curl -s -o /dev/null -w "%{http_code}" http://localhost/api/health || echo "失败")
- AI服务: $(curl -s -o /dev/null -w "%{http_code}" http://localhost/ai/health || echo "失败")

部署完成!
EOF
    
    log "部署报告已生成: /opt/sfap/deployment-report.txt" SUCCESS
}

# 主函数
main() {
    log "开始容器优化..." INFO
    
    check_docker
    create_optimized_dockerfiles
    build_optimized_images
    analyze_image_sizes
    optimize_images
    create_persistent_directories
    generate_secrets
    create_network_optimization
    create_monitoring_config
    create_backup_script
    performance_test
    generate_deployment_report
    
    log "容器优化完成!" SUCCESS
    log "查看部署报告: /opt/sfap/deployment-report.txt" INFO
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -r, --registry REGISTRY    设置Docker镜像仓库"
    echo "  -n, --namespace NAMESPACE  设置命名空间"
    echo "  -v, --version VERSION      设置版本标签"
    echo "  -h, --help                 显示帮助信息"
    echo ""
    echo "环境变量:"
    echo "  DOCKER_REGISTRY   Docker镜像仓库"
    echo "  DOCKER_NAMESPACE  Docker命名空间"
    echo "  VERSION           版本标签"
    echo ""
    echo "示例:"
    echo "  $0 -r docker.io -n sfap -v v1.0.0"
    echo "  DOCKER_REGISTRY=registry.cn-hangzhou.aliyuncs.com $0"
}

# 处理命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -r|--registry)
            REGISTRY="$2"
            shift 2
            ;;
        -n|--namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        -v|--version)
            VERSION="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 执行主函数
main "$@"
