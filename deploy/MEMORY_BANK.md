# SFAP智慧农业平台记忆库和操作手册

## 📚 Agent Memory记忆库

### 项目基本信息
- **项目名称**: SFAP智慧农业平台
- **项目路径**: `E:\计算机设计大赛2\V4.0\新建文件夹\新建文件夹\SFAP`
- **GitHub仓库**: https://github.com/Arrbel/SFAP.git (用户名：Arrbel)
- **Gitee仓库**: https://gitee.com/arrbel/sfap.git (用户名：arrbel)
- **部署状态**: 已配置完整的CI/CD部署流水线和Docker容器化方案

### Git操作标准流程
```bash
# 标准推送流程
git add .
git commit -m "更新：$(date '+%Y-%m-%d %H:%M:%S')"
git push origin main    # 推送到GitHub
git push gitee main     # 推送到Gitee
```

### 部署配置核心组件
- **GitHub Actions CI/CD**: `.github/workflows/ci-cd.yml`
- **Gitee Go流水线**: `.gitee/workflows/ci-cd.yml`
- **Docker多阶段构建**: `deploy/frontend-Dockerfile`, `deploy/backend-Dockerfile`, `deploy/ai-service-Dockerfile`
- **生产环境编排**: `deploy/docker-compose.prod.yml`
- **一键部署脚本**: `deploy/deploy-all.sh`
- **服务器环境配置**: `deploy/server-setup.sh`
- **SSL和Nginx配置**: `deploy/ssl-setup.sh`, `deploy/nginx-config.conf`
- **监控系统**: `deploy/monitoring-setup.sh` (Prometheus + Grafana)

## 🚀 快速操作触发词

### 推送到Gitee
```bash
./deploy/quick-operations.sh push-gitee
```

### 同步双平台
```bash
./deploy/quick-operations.sh sync-dual
```

### 部署生产环境
```bash
./deploy/quick-operations.sh deploy-prod
```

### 检查部署状态
```bash
./deploy/quick-operations.sh check-status
```

## 📋 标准化模板清单

### 1. 配置模板
- **环境变量模板**: `deploy/templates/environment-template.env`
- **Git操作模板**: `deploy/templates/git-operations-template.sh`
- **快速操作脚本**: `deploy/quick-operations.sh`

### 2. 部署脚本
- **一键部署**: `deploy/deploy-all.sh`
- **服务器配置**: `deploy/server-setup.sh`
- **数据库配置**: `deploy/database-setup.sh`
- **SSL配置**: `deploy/ssl-setup.sh`
- **监控配置**: `deploy/monitoring-setup.sh`
- **容器优化**: `deploy/optimize-containers.sh`

### 3. Docker配置
- **前端Dockerfile**: `deploy/frontend-Dockerfile`
- **后端Dockerfile**: `deploy/backend-Dockerfile`
- **AI服务Dockerfile**: `deploy/ai-service-Dockerfile`
- **开发环境编排**: `deploy/docker-compose.yml`
- **生产环境编排**: `deploy/docker-compose.prod.yml`

## 🔧 常用操作命令

### Git操作
```bash
# 初始化仓库
./deploy/templates/git-operations-template.sh init

# 标准提交
./deploy/templates/git-operations-template.sh commit "提交信息"

# 同步双平台
./deploy/templates/git-operations-template.sh sync "同步信息"

# 创建功能分支
./deploy/templates/git-operations-template.sh feature "功能名称"

# 紧急修复
./deploy/templates/git-operations-template.sh hotfix "问题描述"
```

### 部署操作
```bash
# 完整部署
sudo ./deploy/deploy-all.sh -e production -d your-domain.com -m <EMAIL>

# 分步部署
sudo ./deploy/server-setup.sh
sudo ./deploy/database-setup.sh
sudo ./deploy/ssl-setup.sh letsencrypt
sudo ./deploy/monitoring-setup.sh
sudo ./deploy/optimize-containers.sh

# 启动服务
docker-compose -f deploy/docker-compose.prod.yml up -d
```

### 维护操作
```bash
# 查看服务状态
systemctl status sfap
docker-compose -f deploy/docker-compose.prod.yml ps

# 查看日志
docker-compose -f deploy/docker-compose.prod.yml logs -f

# 备份数据
sudo /opt/sfap/backup-containers.sh

# 更新应用
git pull origin main
docker-compose -f deploy/docker-compose.prod.yml up -d --build

# 健康检查
curl https://your-domain.com/health
curl https://your-domain.com/api/health
```

## 📊 监控和访问地址

### 应用访问
- **前端应用**: https://your-domain.com
- **后端API**: https://your-domain.com/api
- **AI服务**: https://your-domain.com/ai

### 监控面板
- **Grafana**: https://your-domain.com:3000 (admin/查看凭据文件)
- **Prometheus**: https://your-domain.com:9090

### 重要文件位置
- **项目目录**: `/opt/sfap`
- **数据目录**: `/opt/sfap/data`
- **日志目录**: `/opt/sfap/logs`
- **配置文件**: `/opt/sfap/config`
- **密钥文件**: `/opt/sfap/secrets`
- **备份目录**: `/opt/backup/sfap`

## 🔍 故障排查快速指南

### 常见问题解决
```bash
# Git推送失败
ssh -T **************
ssh -T *************
git remote -v

# 容器启动失败
docker-compose -f deploy/docker-compose.yml ps
docker-compose -f deploy/docker-compose.yml logs container_name

# 数据库连接失败
docker exec -it sfap-mysql mysql -u root -p
docker network inspect sfap_sfap-network

# SSL证书问题
sudo certbot certificates
sudo certbot renew
sudo systemctl reload nginx

# 性能问题
htop
docker stats
df -h
```

### 健康检查命令
```bash
# 系统健康检查
./deploy/quick-operations.sh health-check

# 服务状态检查
./deploy/quick-operations.sh check-status

# 日志查看
./deploy/quick-operations.sh view-logs

# 资源清理
./deploy/quick-operations.sh cleanup
```

## 📞 技术支持信息

### 联系方式
- **技术支持邮箱**: <EMAIL>
- **GitHub Issues**: https://github.com/Arrbel/SFAP/issues
- **在线文档**: https://docs.sfap.com

### 重要凭据文件
- **数据库凭据**: `/root/database_credentials.txt`
- **监控凭据**: `/root/monitoring_credentials.txt`
- **部署总结**: `/opt/sfap/deployment-summary.txt`

## 🔄 更新和维护计划

### 定期维护任务
- **每日**: 自动备份数据库和文件
- **每周**: 检查系统更新和安全补丁
- **每月**: 清理日志文件和临时数据
- **每季度**: 更新SSL证书和密钥

### 版本更新流程
1. 在开发分支进行更改
2. 测试验证功能
3. 合并到主分支
4. 触发CI/CD自动部署
5. 验证生产环境

## 📝 最佳实践

### Git操作最佳实践
- 使用描述性的提交信息
- 定期同步远程仓库
- 使用分支进行功能开发
- 及时合并和清理分支

### 部署最佳实践
- 使用环境变量管理配置
- 定期备份重要数据
- 监控系统性能和健康状态
- 保持系统和依赖更新

### 安全最佳实践
- 使用强密码和密钥
- 定期更换敏感信息
- 启用防火墙和SSL
- 限制不必要的端口访问

---

*最后更新时间：2024年8月*
*版本：v1.0.0*
