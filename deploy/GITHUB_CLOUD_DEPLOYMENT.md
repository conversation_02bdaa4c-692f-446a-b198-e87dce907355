# SFAP GitHub + 云服务器Docker部署专项方案

## 📋 方案概述

本方案专门针对"仅使用GitHub代码托管 + 云服务器Docker部署"的完整流程，适用于SFAP智慧农业平台项目的生产环境部署。

### 部署架构
```
开发环境 → GitHub仓库 → 云服务器 → Docker容器 → 生产应用
```

### 核心特性
- ✅ 纯GitHub代码托管
- ✅ 云服务器Docker容器化部署
- ✅ 自动化部署流程
- ✅ 完整监控和备份方案
- ✅ 故障自动恢复机制

## 🚀 第一阶段：GitHub仓库配置

### 1.1 创建GitHub仓库

```bash
# 1. 访问GitHub创建仓库
# 仓库名称：SFAP
# 描述：SFAP智慧农业平台
# 可见性：Public或Private
# 初始化：添加README.md和.gitignore

# 2. 克隆仓库到本地
git clone https://github.com/Arrbel/SFAP.git
cd SFAP

# 3. 配置Git用户信息
git config user.name "Your Name"
git config user.email "<EMAIL>"
```

### 1.2 配置SSH密钥认证

```bash
# 1. 生成SSH密钥
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"
# 按回车使用默认路径，设置密码（可选）

# 2. 查看公钥
cat ~/.ssh/id_rsa.pub

# 3. 添加SSH密钥到GitHub
# - 访问 GitHub → Settings → SSH and GPG keys
# - 点击 "New SSH key"
# - 粘贴公钥内容并保存

# 4. 测试SSH连接
ssh -T **************
# 应该看到：Hi username! You've successfully authenticated...
```

### 1.3 项目代码推送流程

```bash
# 1. 添加项目文件
cp -r /path/to/your/sfap/project/* ./

# 2. 创建.gitignore文件
cat > .gitignore << 'EOF'
# 依赖文件
node_modules/
target/
__pycache__/
*.pyc

# 构建产物
dist/
build/
*.jar

# 环境配置
.env
.env.local
.env.production

# 日志文件
*.log
logs/

# IDE配置
.vscode/
.idea/
*.swp

# 系统文件
.DS_Store
Thumbs.db

# Docker相关
docker-compose.override.yml

# 生产环境敏感文件
secrets/
*.key
*.pem
EOF

# 3. 首次提交和推送
git add .
git commit -m "初始提交：SFAP智慧农业平台项目"
git push -u origin main

# 4. 验证推送成功
git log --oneline -3
```

### 1.4 GitHub Actions自动化配置

```bash
# 创建GitHub Actions工作流目录
mkdir -p .github/workflows

# 创建自动化部署工作流
cat > .github/workflows/deploy.yml << 'EOF'
name: SFAP自动部署

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

env:
  DOCKER_REGISTRY: docker.io
  DOCKER_NAMESPACE: sfap

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: 设置Node.js环境
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: 安装前端依赖
      run: npm ci
    
    - name: 前端代码检查
      run: npm run lint
    
    - name: 前端单元测试
      run: npm run test:unit
    
    - name: 设置Java环境
      uses: actions/setup-java@v3
      with:
        java-version: '17'
        distribution: 'temurin'
    
    - name: 后端测试
      run: |
        cd backend
        mvn clean test

  build:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: 设置Docker Buildx
      uses: docker/setup-buildx-action@v2
    
    - name: 登录Docker Hub
      uses: docker/login-action@v2
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}
    
    - name: 构建并推送前端镜像
      uses: docker/build-push-action@v4
      with:
        context: .
        file: ./deploy/frontend-Dockerfile
        push: true
        tags: ${{ env.DOCKER_REGISTRY }}/${{ env.DOCKER_NAMESPACE }}/frontend:latest
    
    - name: 构建并推送后端镜像
      uses: docker/build-push-action@v4
      with:
        context: ./backend
        file: ./deploy/backend-Dockerfile
        push: true
        tags: ${{ env.DOCKER_REGISTRY }}/${{ env.DOCKER_NAMESPACE }}/backend:latest
    
    - name: 构建并推送AI服务镜像
      uses: docker/build-push-action@v4
      with:
        context: ./ai-service
        file: ./deploy/ai-service-Dockerfile
        push: true
        tags: ${{ env.DOCKER_REGISTRY }}/${{ env.DOCKER_NAMESPACE }}/ai-service:latest

  deploy:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: 部署到云服务器
      uses: appleboy/ssh-action@v0.1.5
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.PRIVATE_KEY }}
        script: |
          cd /opt/sfap
          git pull origin main
          docker-compose -f deploy/docker-compose.prod.yml pull
          docker-compose -f deploy/docker-compose.prod.yml up -d
          docker system prune -f
EOF

# 提交GitHub Actions配置
git add .github/workflows/deploy.yml
git commit -m "添加GitHub Actions自动部署配置"
git push origin main
```

## ☁️ 第二阶段：云服务器环境准备

### 2.1 云服务器基础配置

```bash
# 1. 连接到云服务器
ssh root@your-server-ip

# 2. 更新系统
apt update && apt upgrade -y

# 3. 安装基础工具
apt install -y curl wget git vim htop tree unzip \
    software-properties-common apt-transport-https \
    ca-certificates gnupg lsb-release

# 4. 配置时区
timedatectl set-timezone Asia/Shanghai

# 5. 创建应用用户
useradd -m -s /bin/bash sfap
usermod -aG sudo sfap
echo "sfap ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers

# 6. 配置SSH密钥登录
mkdir -p /home/<USER>/.ssh
cp ~/.ssh/authorized_keys /home/<USER>/.ssh/
chown -R sfap:sfap /home/<USER>/.ssh
chmod 700 /home/<USER>/.ssh
chmod 600 /home/<USER>/.ssh/authorized_keys
```

### 2.2 Docker环境安装

```bash
# 1. 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# 2. 安装Docker Compose
curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# 3. 将用户添加到docker组
usermod -aG docker sfap

# 4. 启动Docker服务
systemctl start docker
systemctl enable docker

# 5. 验证安装
docker --version
docker-compose --version
docker run hello-world

# 6. 配置Docker镜像加速（可选）
mkdir -p /etc/docker
cat > /etc/docker/daemon.json << 'EOF'
{
  "registry-mirrors": [
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com"
  ],
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  }
}
EOF

systemctl restart docker
```

### 2.3 防火墙和安全配置

```bash
# 1. 配置UFW防火墙
ufw --force reset
ufw default deny incoming
ufw default allow outgoing

# 2. 允许必要端口
ufw allow ssh
ufw allow 22/tcp
ufw allow 80/tcp
ufw allow 443/tcp

# 3. 启用防火墙
ufw --force enable

# 4. 配置fail2ban防暴力破解
apt install -y fail2ban

cat > /etc/fail2ban/jail.local << 'EOF'
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 3

[sshd]
enabled = true
port = ssh
filter = sshd
logpath = /var/log/auth.log
EOF

systemctl start fail2ban
systemctl enable fail2ban

# 5. 禁用root SSH登录（可选）
sed -i 's/#PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config
systemctl restart ssh
```

### 2.4 项目目录准备

```bash
# 切换到应用用户
su - sfap

# 1. 创建项目目录
sudo mkdir -p /opt/sfap
sudo chown -R sfap:sfap /opt/sfap

# 2. 创建数据目录
sudo mkdir -p /opt/sfap/{data,logs,uploads,secrets,backup}
sudo mkdir -p /opt/sfap/data/{mysql,redis,prometheus,grafana}
sudo mkdir -p /opt/sfap/logs/{nginx,backend,ai,system}

# 3. 设置目录权限
sudo chown -R sfap:sfap /opt/sfap
chmod -R 755 /opt/sfap
chmod -R 700 /opt/sfap/secrets

# 4. 克隆项目代码
cd /opt/sfap
git clone https://github.com/Arrbel/SFAP.git .

# 5. 验证项目结构
ls -la
```

## 🔄 第三阶段：自动化部署方案

### 3.1 创建部署脚本

```bash
# 创建自动化部署脚本
cat > /opt/sfap/auto-deploy.sh << 'EOF'
#!/bin/bash

# SFAP自动化部署脚本
set -e

# 配置变量
PROJECT_DIR="/opt/sfap"
DOCKER_COMPOSE_FILE="deploy/docker-compose.prod.yml"
BACKUP_DIR="/opt/sfap/backup"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
    exit 1
}

warn() {
    echo -e "${YELLOW}[WARN] $1${NC}"
}

# 备份当前部署
backup_current_deployment() {
    log "备份当前部署..."
    
    local backup_name="backup_$(date +%Y%m%d_%H%M%S)"
    local backup_path="$BACKUP_DIR/$backup_name"
    
    mkdir -p "$backup_path"
    
    # 备份数据库
    if docker ps | grep -q mysql; then
        docker exec sfap-mysql-prod mysqldump -u root -p"$MYSQL_ROOT_PASSWORD" --all-databases > "$backup_path/database.sql" 2>/dev/null || warn "数据库备份失败"
    fi
    
    # 备份上传文件
    if [ -d "/opt/sfap/uploads" ]; then
        tar -czf "$backup_path/uploads.tar.gz" -C /opt/sfap uploads/ || warn "文件备份失败"
    fi
    
    log "备份完成: $backup_path"
}

# 拉取最新代码
pull_latest_code() {
    log "拉取最新代码..."
    
    cd "$PROJECT_DIR"
    git fetch origin
    git reset --hard origin/main
    
    log "代码更新完成"
}

# 构建和部署
deploy_application() {
    log "部署应用..."
    
    cd "$PROJECT_DIR"
    
    # 停止现有服务
    docker-compose -f "$DOCKER_COMPOSE_FILE" down --remove-orphans || true
    
    # 拉取最新镜像
    docker-compose -f "$DOCKER_COMPOSE_FILE" pull || warn "镜像拉取失败，将使用本地构建"
    
    # 启动服务
    docker-compose -f "$DOCKER_COMPOSE_FILE" up -d --build
    
    log "应用部署完成"
}

# 健康检查
health_check() {
    log "执行健康检查..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost/health &>/dev/null; then
            log "健康检查通过"
            return 0
        fi
        
        warn "健康检查失败，重试 $attempt/$max_attempts"
        sleep 10
        ((attempt++))
    done
    
    error "健康检查失败，部署可能有问题"
}

# 清理资源
cleanup() {
    log "清理旧资源..."
    
    # 清理未使用的Docker资源
    docker system prune -f
    
    # 清理旧备份（保留最近10个）
    if [ -d "$BACKUP_DIR" ]; then
        cd "$BACKUP_DIR"
        ls -t | tail -n +11 | xargs -r rm -rf
    fi
    
    log "清理完成"
}

# 主函数
main() {
    log "开始自动部署..."
    
    backup_current_deployment
    pull_latest_code
    deploy_application
    health_check
    cleanup
    
    log "自动部署完成!"
}

# 执行主函数
main "$@"
EOF

# 设置执行权限
chmod +x /opt/sfap/auto-deploy.sh
```

### 3.2 配置GitHub Secrets

在GitHub仓库中配置以下Secrets：

```bash
# 访问 GitHub仓库 → Settings → Secrets and variables → Actions
# 添加以下Secrets：

# 服务器连接信息
HOST: your-server-ip
USERNAME: sfap
PRIVATE_KEY: (服务器SSH私钥内容)

# Docker Hub信息（如果使用）
DOCKER_USERNAME: your-docker-username
DOCKER_PASSWORD: your-docker-password

# 数据库密码
MYSQL_ROOT_PASSWORD: your-mysql-root-password
MYSQL_PASSWORD: your-mysql-password
REDIS_PASSWORD: your-redis-password
```

### 3.3 配置Webhook自动部署

```bash
# 创建Webhook处理脚本
cat > /opt/sfap/webhook-handler.php << 'EOF'
<?php
// GitHub Webhook自动部署处理器

$secret = 'your_webhook_secret_here';
$deploy_script = '/opt/sfap/auto-deploy.sh';
$log_file = '/opt/sfap/logs/webhook.log';

function writeLog($message) {
    global $log_file;
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($log_file, "[$timestamp] $message\n", FILE_APPEND | LOCK_EX);
}

// 验证GitHub签名
function verifySignature($payload, $signature, $secret) {
    $expectedSignature = 'sha256=' . hash_hmac('sha256', $payload, $secret);
    return hash_equals($expectedSignature, $signature);
}

// 获取请求数据
$headers = getallheaders();
$payload = file_get_contents('php://input');
$signature = $headers['X-Hub-Signature-256'] ?? '';

writeLog("收到Webhook请求");

// 验证签名
if (!verifySignature($payload, $signature, $secret)) {
    http_response_code(401);
    writeLog("签名验证失败");
    exit('Unauthorized');
}

// 解析payload
$data = json_decode($payload, true);
$event = $headers['X-GitHub-Event'] ?? '';

writeLog("GitHub事件: $event");

// 处理push事件
if ($event === 'push' && $data['ref'] === 'refs/heads/main') {
    writeLog("触发自动部署");
    
    // 异步执行部署脚本
    exec("nohup $deploy_script > /dev/null 2>&1 &");
    
    writeLog("部署脚本已启动");
    echo "Deployment triggered";
} else {
    writeLog("忽略事件: $event");
    echo "Event ignored";
}
?>
EOF

# 安装PHP（如果需要Webhook）
sudo apt install -y php php-cli

# 配置Nginx处理Webhook
sudo tee /etc/nginx/sites-available/webhook << 'EOF'
server {
    listen 8080;
    server_name _;
    
    root /opt/sfap;
    index webhook-handler.php;
    
    location / {
        try_files $uri $uri/ =404;
    }
    
    location ~ \.php$ {
        include snippets/fastcgi-php.conf;
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
    }
}
EOF

sudo ln -s /etc/nginx/sites-available/webhook /etc/nginx/sites-enabled/
sudo systemctl reload nginx

## 🐳 第四阶段：Docker镜像构建和容器部署

### 4.1 生产环境Docker Compose配置

```bash
# 创建生产环境配置文件
cat > /opt/sfap/deploy/docker-compose.prod.yml << 'EOF'
version: '3.8'

services:
  # Nginx反向代理
  nginx:
    image: nginx:1.24-alpine
    container_name: sfap-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - /etc/letsencrypt:/etc/letsencrypt:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - frontend
      - backend
      - ai-service
    networks:
      - sfap-network
    restart: unless-stopped

  # 前端服务
  frontend:
    image: sfap/frontend:latest
    container_name: sfap-frontend
    expose:
      - "80"
    environment:
      - API_BASE_URL=http://backend:8080
      - AI_SERVICE_URL=http://ai-service:5000
    depends_on:
      - backend
    networks:
      - sfap-network
    restart: unless-stopped

  # 后端服务
  backend:
    image: sfap/backend:latest
    container_name: sfap-backend
    expose:
      - "8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_NAME=agriculture_mall
      - DB_USER=sfap
      - DB_PASSWORD=${MYSQL_PASSWORD}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    volumes:
      - backend_logs:/app/logs
      - backend_uploads:/app/uploads
    depends_on:
      - mysql
      - redis
    networks:
      - sfap-network
    restart: unless-stopped

  # AI服务
  ai-service:
    image: sfap/ai-service:latest
    container_name: sfap-ai-service
    expose:
      - "5000"
    environment:
      - FLASK_ENV=production
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_NAME=agriculture_mall
      - DB_USER=sfap
      - DB_PASSWORD=${MYSQL_PASSWORD}
    volumes:
      - ai_logs:/app/logs
      - ai_models:/app/saved_models
    depends_on:
      - redis
    networks:
      - sfap-network
    restart: unless-stopped

  # MySQL数据库
  mysql:
    image: mysql:8.0.34
    container_name: sfap-mysql
    expose:
      - "3306"
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - MYSQL_DATABASE=agriculture_mall
      - MYSQL_USER=sfap
      - MYSQL_PASSWORD=${MYSQL_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
      - mysql_logs:/var/log/mysql
    command: >
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --max_connections=200
      --innodb_buffer_pool_size=512M
    networks:
      - sfap-network
    restart: unless-stopped

  # Redis缓存
  redis:
    image: redis:7.0-alpine
    container_name: sfap-redis
    expose:
      - "6379"
    command: >
      redis-server
      --requirepass ${REDIS_PASSWORD}
      --appendonly yes
      --maxmemory 256mb
      --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    networks:
      - sfap-network
    restart: unless-stopped

networks:
  sfap-network:
    driver: bridge

volumes:
  mysql_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/sfap/data/mysql
  redis_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/sfap/data/redis
  backend_logs:
    driver: local
  backend_uploads:
    driver: local
  ai_logs:
    driver: local
  ai_models:
    driver: local
  nginx_logs:
    driver: local
EOF
```

### 4.2 环境变量配置

```bash
# 创建生产环境变量文件
cat > /opt/sfap/.env.production << 'EOF'
# SFAP生产环境配置

# 数据库配置
MYSQL_ROOT_PASSWORD=your_secure_mysql_root_password
MYSQL_PASSWORD=your_secure_mysql_password
MYSQL_DATABASE=agriculture_mall
MYSQL_USER=sfap

# Redis配置
REDIS_PASSWORD=your_secure_redis_password

# 应用配置
DOMAIN=your-domain.com
EMAIL=<EMAIL>
ENVIRONMENT=production

# Docker配置
COMPOSE_PROJECT_NAME=sfap
DOCKER_BUILDKIT=1
EOF

# 设置文件权限
chmod 600 /opt/sfap/.env.production
```

### 4.3 镜像构建脚本

```bash
# 创建镜像构建脚本
cat > /opt/sfap/build-images.sh << 'EOF'
#!/bin/bash

# SFAP镜像构建脚本
set -e

PROJECT_DIR="/opt/sfap"
REGISTRY="docker.io"
NAMESPACE="sfap"
TAG="latest"

log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# 构建前端镜像
build_frontend() {
    log "构建前端镜像..."

    docker build \
        -f deploy/frontend-Dockerfile \
        -t "${REGISTRY}/${NAMESPACE}/frontend:${TAG}" \
        .

    log "前端镜像构建完成"
}

# 构建后端镜像
build_backend() {
    log "构建后端镜像..."

    docker build \
        -f deploy/backend-Dockerfile \
        -t "${REGISTRY}/${NAMESPACE}/backend:${TAG}" \
        ./backend

    log "后端镜像构建完成"
}

# 构建AI服务镜像
build_ai_service() {
    log "构建AI服务镜像..."

    docker build \
        -f deploy/ai-service-Dockerfile \
        -t "${REGISTRY}/${NAMESPACE}/ai-service:${TAG}" \
        ./ai-service

    log "AI服务镜像构建完成"
}

# 主函数
main() {
    log "开始构建所有镜像..."

    cd "$PROJECT_DIR"

    build_frontend
    build_backend
    build_ai_service

    log "所有镜像构建完成"

    # 显示镜像信息
    docker images | grep "$NAMESPACE"
}

main "$@"
EOF

chmod +x /opt/sfap/build-images.sh
```

### 4.4 容器部署和管理

```bash
# 创建容器管理脚本
cat > /opt/sfap/manage-containers.sh << 'EOF'
#!/bin/bash

# SFAP容器管理脚本
set -e

PROJECT_DIR="/opt/sfap"
COMPOSE_FILE="deploy/docker-compose.prod.yml"
ENV_FILE=".env.production"

log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# 启动所有服务
start_services() {
    log "启动所有服务..."

    cd "$PROJECT_DIR"
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d

    log "服务启动完成"
}

# 停止所有服务
stop_services() {
    log "停止所有服务..."

    cd "$PROJECT_DIR"
    docker-compose -f "$COMPOSE_FILE" down

    log "服务停止完成"
}

# 重启服务
restart_services() {
    log "重启服务..."

    stop_services
    start_services

    log "服务重启完成"
}

# 查看服务状态
show_status() {
    log "查看服务状态..."

    cd "$PROJECT_DIR"
    docker-compose -f "$COMPOSE_FILE" ps

    echo ""
    echo "容器资源使用情况:"
    docker stats --no-stream
}

# 查看日志
show_logs() {
    local service="$1"

    cd "$PROJECT_DIR"

    if [ -n "$service" ]; then
        docker-compose -f "$COMPOSE_FILE" logs -f --tail=100 "$service"
    else
        docker-compose -f "$COMPOSE_FILE" logs -f --tail=100
    fi
}

# 更新服务
update_services() {
    log "更新服务..."

    cd "$PROJECT_DIR"

    # 拉取最新镜像
    docker-compose -f "$COMPOSE_FILE" pull

    # 重新启动服务
    docker-compose -f "$COMPOSE_FILE" up -d

    # 清理旧镜像
    docker image prune -f

    log "服务更新完成"
}

# 备份数据
backup_data() {
    log "备份数据..."

    local backup_dir="/opt/sfap/backup/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"

    # 备份数据库
    docker exec sfap-mysql mysqldump -u root -p"$MYSQL_ROOT_PASSWORD" --all-databases > "$backup_dir/mysql_backup.sql"

    # 备份Redis
    docker exec sfap-redis redis-cli --rdb "$backup_dir/redis_backup.rdb"

    # 备份上传文件
    tar -czf "$backup_dir/uploads_backup.tar.gz" -C /opt/sfap/data uploads/

    log "数据备份完成: $backup_dir"
}

# 显示帮助
show_help() {
    echo "SFAP容器管理脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  start       启动所有服务"
    echo "  stop        停止所有服务"
    echo "  restart     重启所有服务"
    echo "  status      查看服务状态"
    echo "  logs [服务] 查看日志"
    echo "  update      更新服务"
    echo "  backup      备份数据"
    echo "  help        显示帮助"
}

# 主函数
case "${1:-}" in
    start)
        start_services
        ;;
    stop)
        stop_services
        ;;
    restart)
        restart_services
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs "$2"
        ;;
    update)
        update_services
        ;;
    backup)
        backup_data
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        show_help
        ;;
esac
EOF

chmod +x /opt/sfap/manage-containers.sh
```

## 📊 第五阶段：监控、备份和维护方案

### 5.1 系统监控配置

```bash
# 安装系统监控工具
sudo apt install -y htop iotop nethogs

# 创建系统监控脚本
cat > /opt/sfap/system-monitor.sh << 'EOF'
#!/bin/bash

# SFAP系统监控脚本
LOG_FILE="/opt/sfap/logs/system-monitor.log"
ALERT_EMAIL="<EMAIL>"

log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

send_alert() {
    local message="$1"
    log "ALERT: $message"

    # 发送邮件告警（需要配置邮件服务）
    if command -v mail &> /dev/null; then
        echo "$message" | mail -s "SFAP系统告警" "$ALERT_EMAIL"
    fi
}

# 检查系统负载
check_system_load() {
    local load=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    local cpu_cores=$(nproc)
    local threshold=$(echo "$cpu_cores * 2" | bc)

    if (( $(echo "$load > $threshold" | bc -l) )); then
        send_alert "系统负载过高: $load (CPU核心数: $cpu_cores)"
    fi

    log "系统负载: $load"
}

# 检查磁盘空间
check_disk_space() {
    local usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')

    if [ "$usage" -gt 85 ]; then
        send_alert "磁盘空间不足: ${usage}%"
    fi

    log "磁盘使用率: ${usage}%"
}

# 检查内存使用
check_memory_usage() {
    local mem_usage=$(free | awk 'NR==2{printf "%.1f", $3*100/$2}')

    if (( $(echo "$mem_usage > 85" | bc -l) )); then
        send_alert "内存使用率过高: ${mem_usage}%"
    fi

    log "内存使用率: ${mem_usage}%"
}

# 检查Docker容器状态
check_docker_containers() {
    local unhealthy_containers=$(docker ps --filter "health=unhealthy" --format "{{.Names}}" | wc -l)

    if [ "$unhealthy_containers" -gt 0 ]; then
        send_alert "发现 $unhealthy_containers 个不健康的容器"
    fi

    local stopped_containers=$(docker ps -a --filter "status=exited" --format "{{.Names}}" | grep sfap | wc -l)

    if [ "$stopped_containers" -gt 0 ]; then
        send_alert "发现 $stopped_containers 个已停止的SFAP容器"
    fi

    log "Docker容器检查完成"
}

# 检查应用健康状态
check_application_health() {
    local endpoints=(
        "http://localhost/health"
        "http://localhost/api/health"
    )

    for endpoint in "${endpoints[@]}"; do
        if ! curl -f "$endpoint" &>/dev/null; then
            send_alert "应用健康检查失败: $endpoint"
        fi
    done

    log "应用健康检查完成"
}

# 主函数
main() {
    log "开始系统监控检查..."

    check_system_load
    check_disk_space
    check_memory_usage
    check_docker_containers
    check_application_health

    log "系统监控检查完成"
}

main "$@"
EOF

chmod +x /opt/sfap/system-monitor.sh

# 添加到crontab（每5分钟执行一次）
(crontab -l 2>/dev/null; echo "*/5 * * * * /opt/sfap/system-monitor.sh") | crontab -
```

### 5.2 自动备份方案

```bash
# 创建自动备份脚本
cat > /opt/sfap/auto-backup.sh << 'EOF'
#!/bin/bash

# SFAP自动备份脚本
set -e

BACKUP_DIR="/opt/sfap/backup"
RETENTION_DAYS=7
LOG_FILE="/opt/sfap/logs/backup.log"

log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 创建备份目录
create_backup_dir() {
    local backup_name="backup_$(date +%Y%m%d_%H%M%S)"
    local backup_path="$BACKUP_DIR/$backup_name"

    mkdir -p "$backup_path"
    echo "$backup_path"
}

# 备份数据库
backup_database() {
    local backup_path="$1"

    log "备份MySQL数据库..."
    docker exec sfap-mysql mysqldump -u root -p"$MYSQL_ROOT_PASSWORD" --single-transaction --routines --triggers agriculture_mall | gzip > "$backup_path/mysql_backup.sql.gz"

    log "备份Redis数据..."
    docker exec sfap-redis redis-cli --rdb "$backup_path/redis_backup.rdb"

    log "数据库备份完成"
}

# 备份应用文件
backup_files() {
    local backup_path="$1"

    log "备份上传文件..."
    if [ -d "/opt/sfap/data/uploads" ]; then
        tar -czf "$backup_path/uploads_backup.tar.gz" -C /opt/sfap/data uploads/
    fi

    log "备份配置文件..."
    tar -czf "$backup_path/config_backup.tar.gz" -C /opt/sfap .env.production deploy/

    log "文件备份完成"
}

# 备份Docker镜像
backup_docker_images() {
    local backup_path="$1"

    log "备份Docker镜像..."
    docker save $(docker images --format "{{.Repository}}:{{.Tag}}" | grep sfap) | gzip > "$backup_path/docker_images.tar.gz"

    log "Docker镜像备份完成"
}

# 清理旧备份
cleanup_old_backups() {
    log "清理旧备份..."
    find "$BACKUP_DIR" -type d -name "backup_*" -mtime +$RETENTION_DAYS -exec rm -rf {} + 2>/dev/null || true
    log "旧备份清理完成"
}

# 主函数
main() {
    log "开始自动备份..."

    local backup_path=$(create_backup_dir)

    backup_database "$backup_path"
    backup_files "$backup_path"
    backup_docker_images "$backup_path"
    cleanup_old_backups

    log "自动备份完成: $backup_path"
}

main "$@"
EOF

chmod +x /opt/sfap/auto-backup.sh

# 添加到crontab（每天凌晨2点执行）
(crontab -l 2>/dev/null; echo "0 2 * * * /opt/sfap/auto-backup.sh") | crontab -
```

### 5.3 日志管理

```bash
# 配置日志轮转
sudo tee /etc/logrotate.d/sfap << 'EOF'
/opt/sfap/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 sfap sfap
    postrotate
        docker kill -s USR1 $(docker ps -q --filter name=sfap) 2>/dev/null || true
    endscript
}
EOF

# 创建日志分析脚本
cat > /opt/sfap/analyze-logs.sh << 'EOF'
#!/bin/bash

# SFAP日志分析脚本
LOG_DIR="/opt/sfap/logs"
REPORT_FILE="/tmp/sfap_log_analysis_$(date +%Y%m%d).txt"

# 分析错误日志
analyze_errors() {
    echo "=== SFAP日志分析报告 ===" > "$REPORT_FILE"
    echo "生成时间: $(date)" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"

    echo "错误统计:" >> "$REPORT_FILE"
    grep -r -i "error" "$LOG_DIR"/*.log 2>/dev/null | wc -l >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"

    echo "最近的错误 (最近10条):" >> "$REPORT_FILE"
    grep -r -i "error" "$LOG_DIR"/*.log 2>/dev/null | tail -10 >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"

    echo "警告统计:" >> "$REPORT_FILE"
    grep -r -i "warn" "$LOG_DIR"/*.log 2>/dev/null | wc -l >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"
}

# 发送报告
send_report() {
    if [ -n "$EMAIL" ] && command -v mail &> /dev/null; then
        mail -s "SFAP日志分析报告" "$EMAIL" < "$REPORT_FILE"
    fi

    echo "日志分析报告已生成: $REPORT_FILE"
}

analyze_errors
send_report
EOF

chmod +x /opt/sfap/analyze-logs.sh

# 添加到crontab（每天早上6点生成报告）
(crontab -l 2>/dev/null; echo "0 6 * * * /opt/sfap/analyze-logs.sh") | crontab -

## 🔧 第六阶段：故障排查和常见问题解决

### 6.1 GitHub相关问题

#### 问题1：Git推送失败
```bash
# 症状：git push失败，提示认证错误
# 解决方案：
# 1. 检查SSH密钥
ssh -T **************

# 2. 重新生成SSH密钥
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"
cat ~/.ssh/id_rsa.pub
# 将公钥添加到GitHub

# 3. 检查远程仓库URL
git remote -v
git remote set-<NAME_EMAIL>:Arrbel/SFAP.git

# 4. 强制推送（谨慎使用）
git push -f origin main
```

#### 问题2：GitHub Actions失败
```bash
# 症状：Actions工作流执行失败
# 解决方案：
# 1. 检查Secrets配置
# 访问 GitHub → Settings → Secrets and variables → Actions
# 确保以下Secrets已配置：
# - HOST (服务器IP)
# - USERNAME (服务器用户名)
# - PRIVATE_KEY (SSH私钥)
# - DOCKER_USERNAME (Docker Hub用户名)
# - DOCKER_PASSWORD (Docker Hub密码)

# 2. 检查工作流文件语法
# 验证 .github/workflows/deploy.yml 文件格式

# 3. 查看Actions日志
# 访问 GitHub → Actions → 选择失败的工作流 → 查看详细日志
```

### 6.2 云服务器相关问题

#### 问题1：SSH连接失败
```bash
# 症状：无法SSH连接到服务器
# 解决方案：
# 1. 检查服务器状态
ping your-server-ip

# 2. 检查SSH服务
sudo systemctl status ssh
sudo systemctl restart ssh

# 3. 检查防火墙设置
sudo ufw status
sudo ufw allow ssh

# 4. 检查SSH配置
sudo nano /etc/ssh/sshd_config
# 确保以下配置：
# Port 22
# PermitRootLogin yes (或no，根据需要)
# PasswordAuthentication yes (或no，根据需要)
```

#### 问题2：磁盘空间不足
```bash
# 症状：磁盘空间不足导致部署失败
# 解决方案：
# 1. 检查磁盘使用情况
df -h
du -sh /opt/sfap/*

# 2. 清理Docker资源
docker system prune -a -f
docker volume prune -f

# 3. 清理日志文件
sudo find /var/log -name "*.log" -type f -mtime +30 -delete
sudo find /opt/sfap/logs -name "*.log" -type f -mtime +7 -delete

# 4. 清理旧备份
find /opt/sfap/backup -type d -mtime +7 -exec rm -rf {} +

# 5. 清理系统缓存
sudo apt autoremove -y
sudo apt autoclean
```

### 6.3 Docker相关问题

#### 问题1：容器启动失败
```bash
# 症状：Docker容器无法启动
# 解决方案：
# 1. 查看容器状态
docker ps -a
docker-compose -f deploy/docker-compose.prod.yml ps

# 2. 查看容器日志
docker logs sfap-frontend
docker logs sfap-backend
docker logs sfap-mysql

# 3. 检查镜像是否存在
docker images | grep sfap

# 4. 重新构建镜像
./build-images.sh

# 5. 重启Docker服务
sudo systemctl restart docker
```

#### 问题2：数据库连接失败
```bash
# 症状：应用无法连接到数据库
# 解决方案：
# 1. 检查MySQL容器状态
docker exec -it sfap-mysql mysql -u root -p

# 2. 检查网络连接
docker network ls
docker network inspect sfap_sfap-network

# 3. 检查环境变量
cat /opt/sfap/.env.production

# 4. 重置数据库密码
docker exec -it sfap-mysql mysql -u root -p
ALTER USER 'root'@'%' IDENTIFIED BY 'new_password';
FLUSH PRIVILEGES;

# 5. 重新初始化数据库
docker-compose -f deploy/docker-compose.prod.yml down
docker volume rm sfap_mysql_data
docker-compose -f deploy/docker-compose.prod.yml up -d
```

### 6.4 应用相关问题

#### 问题1：前端页面无法访问
```bash
# 症状：浏览器无法访问前端页面
# 解决方案：
# 1. 检查Nginx状态
docker logs sfap-nginx

# 2. 检查端口映射
docker port sfap-nginx

# 3. 检查防火墙
sudo ufw status
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# 4. 检查域名解析
nslookup your-domain.com
ping your-domain.com

# 5. 重启Nginx容器
docker restart sfap-nginx
```

#### 问题2：API接口返回500错误
```bash
# 症状：后端API返回500内部服务器错误
# 解决方案：
# 1. 查看后端日志
docker logs sfap-backend -f

# 2. 检查数据库连接
docker exec -it sfap-backend curl http://localhost:8080/actuator/health

# 3. 检查Redis连接
docker exec -it sfap-redis redis-cli ping

# 4. 重启后端服务
docker restart sfap-backend

# 5. 检查JVM内存
docker stats sfap-backend
```

## 📋 第七阶段：完整操作指南

### 7.1 日常运维命令

```bash
# 创建日常运维脚本
cat > /opt/sfap/daily-ops.sh << 'EOF'
#!/bin/bash

# SFAP日常运维脚本
set -e

PROJECT_DIR="/opt/sfap"
cd "$PROJECT_DIR"

show_help() {
    echo "SFAP日常运维脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "部署相关:"
    echo "  deploy          执行完整部署"
    echo "  update          更新应用"
    echo "  rollback        回滚到上一版本"
    echo ""
    echo "服务管理:"
    echo "  start           启动所有服务"
    echo "  stop            停止所有服务"
    echo "  restart         重启所有服务"
    echo "  status          查看服务状态"
    echo ""
    echo "监控和日志:"
    echo "  logs [service]  查看日志"
    echo "  monitor         系统监控"
    echo "  health          健康检查"
    echo ""
    echo "数据管理:"
    echo "  backup          备份数据"
    echo "  restore [file]  恢复数据"
    echo "  cleanup         清理资源"
    echo ""
}

# 执行完整部署
deploy() {
    echo "执行完整部署..."
    ./auto-deploy.sh
}

# 更新应用
update() {
    echo "更新应用..."
    git pull origin main
    ./manage-containers.sh update
}

# 回滚到上一版本
rollback() {
    echo "回滚到上一版本..."
    git log --oneline -5
    read -p "请输入要回滚到的commit hash: " commit_hash
    git reset --hard "$commit_hash"
    ./manage-containers.sh restart
}

# 启动服务
start() {
    echo "启动所有服务..."
    ./manage-containers.sh start
}

# 停止服务
stop() {
    echo "停止所有服务..."
    ./manage-containers.sh stop
}

# 重启服务
restart() {
    echo "重启所有服务..."
    ./manage-containers.sh restart
}

# 查看状态
status() {
    echo "查看服务状态..."
    ./manage-containers.sh status
}

# 查看日志
logs() {
    local service="$1"
    echo "查看日志..."
    ./manage-containers.sh logs "$service"
}

# 系统监控
monitor() {
    echo "系统监控..."
    ./system-monitor.sh
}

# 健康检查
health() {
    echo "健康检查..."

    # 检查服务状态
    echo "=== 服务状态 ==="
    docker-compose -f deploy/docker-compose.prod.yml ps

    # 检查系统资源
    echo ""
    echo "=== 系统资源 ==="
    echo "CPU使用率: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}')"
    echo "内存使用率: $(free | awk 'NR==2{printf "%.1f%%", $3*100/$2}')"
    echo "磁盘使用率: $(df / | awk 'NR==2{print $5}')"

    # 检查应用健康
    echo ""
    echo "=== 应用健康 ==="
    curl -s http://localhost/health || echo "前端健康检查失败"
    curl -s http://localhost/api/health || echo "后端健康检查失败"
}

# 备份数据
backup() {
    echo "备份数据..."
    ./auto-backup.sh
}

# 恢复数据
restore() {
    local backup_file="$1"
    if [ -z "$backup_file" ]; then
        echo "请指定备份文件"
        return 1
    fi

    echo "恢复数据: $backup_file"
    # 实现数据恢复逻辑
}

# 清理资源
cleanup() {
    echo "清理资源..."
    docker system prune -f
    docker volume prune -f
    find /opt/sfap/logs -name "*.log" -mtime +7 -delete
    find /opt/sfap/backup -type d -mtime +7 -exec rm -rf {} +
}

# 主函数
case "${1:-}" in
    deploy)
        deploy
        ;;
    update)
        update
        ;;
    rollback)
        rollback
        ;;
    start)
        start
        ;;
    stop)
        stop
        ;;
    restart)
        restart
        ;;
    status)
        status
        ;;
    logs)
        logs "$2"
        ;;
    monitor)
        monitor
        ;;
    health)
        health
        ;;
    backup)
        backup
        ;;
    restore)
        restore "$2"
        ;;
    cleanup)
        cleanup
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        show_help
        ;;
esac
EOF

chmod +x /opt/sfap/daily-ops.sh
```

### 7.2 快速部署检查清单

```bash
# 创建部署检查清单
cat > /opt/sfap/deployment-checklist.md << 'EOF'
# SFAP部署检查清单

## 部署前检查
- [ ] 服务器资源充足（CPU、内存、磁盘）
- [ ] Docker和Docker Compose已安装
- [ ] GitHub SSH密钥已配置
- [ ] 环境变量文件已配置
- [ ] 防火墙端口已开放
- [ ] 域名DNS已解析

## 部署过程检查
- [ ] 代码成功拉取到服务器
- [ ] Docker镜像构建成功
- [ ] 所有容器启动成功
- [ ] 数据库连接正常
- [ ] Redis连接正常
- [ ] Nginx配置正确

## 部署后验证
- [ ] 前端页面可以访问
- [ ] 后端API响应正常
- [ ] AI服务功能正常
- [ ] 用户登录功能正常
- [ ] 文件上传功能正常
- [ ] 数据库读写正常

## 监控和维护
- [ ] 系统监控脚本运行正常
- [ ] 自动备份脚本配置完成
- [ ] 日志轮转配置正确
- [ ] 告警通知配置完成
- [ ] SSL证书有效期检查

## 性能优化
- [ ] 数据库查询优化
- [ ] 静态资源缓存配置
- [ ] CDN配置（如需要）
- [ ] 负载均衡配置（如需要）
- [ ] 容器资源限制配置
EOF
```

### 7.3 应急响应流程

```bash
# 创建应急响应脚本
cat > /opt/sfap/emergency-response.sh << 'EOF'
#!/bin/bash

# SFAP应急响应脚本
set -e

PROJECT_DIR="/opt/sfap"
EMERGENCY_LOG="/opt/sfap/logs/emergency.log"

log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$EMERGENCY_LOG"
}

# 快速诊断
quick_diagnosis() {
    log "开始快速诊断..."

    echo "=== 系统状态 ==="
    uptime
    df -h
    free -h

    echo ""
    echo "=== Docker状态 ==="
    docker ps

    echo ""
    echo "=== 网络状态 ==="
    netstat -tlnp | grep -E ':(80|443|3306|6379|8080|5000)'

    echo ""
    echo "=== 应用健康 ==="
    curl -s -o /dev/null -w "%{http_code}" http://localhost/health || echo "前端异常"
    curl -s -o /dev/null -w "%{http_code}" http://localhost/api/health || echo "后端异常"

    log "快速诊断完成"
}

# 紧急重启
emergency_restart() {
    log "执行紧急重启..."

    # 备份当前状态
    docker-compose -f deploy/docker-compose.prod.yml ps > /tmp/container_status_before_restart.txt

    # 重启所有服务
    docker-compose -f deploy/docker-compose.prod.yml restart

    # 等待服务启动
    sleep 30

    # 验证服务状态
    docker-compose -f deploy/docker-compose.prod.yml ps

    log "紧急重启完成"
}

# 回滚到上一个稳定版本
emergency_rollback() {
    log "执行紧急回滚..."

    # 显示最近的提交
    git log --oneline -10

    # 回滚到上一个提交
    git reset --hard HEAD~1

    # 重新部署
    ./auto-deploy.sh

    log "紧急回滚完成"
}

# 数据库紧急修复
database_emergency_fix() {
    log "执行数据库紧急修复..."

    # 检查数据库状态
    docker exec sfap-mysql mysqladmin ping -u root -p"$MYSQL_ROOT_PASSWORD"

    # 修复数据库表
    docker exec sfap-mysql mysqlcheck -u root -p"$MYSQL_ROOT_PASSWORD" --auto-repair --all-databases

    log "数据库紧急修复完成"
}

# 清理磁盘空间
emergency_cleanup() {
    log "执行紧急清理..."

    # 清理Docker资源
    docker system prune -a -f

    # 清理日志文件
    find /opt/sfap/logs -name "*.log" -mtime +1 -delete

    # 清理临时文件
    rm -rf /tmp/*

    # 清理系统缓存
    sync && echo 3 > /proc/sys/vm/drop_caches

    log "紧急清理完成"
}

# 显示帮助
show_help() {
    echo "SFAP应急响应脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  diagnosis       快速诊断"
    echo "  restart         紧急重启"
    echo "  rollback        紧急回滚"
    echo "  fix-db          数据库紧急修复"
    echo "  cleanup         紧急清理"
    echo "  help            显示帮助"
}

# 主函数
case "${1:-}" in
    diagnosis)
        quick_diagnosis
        ;;
    restart)
        emergency_restart
        ;;
    rollback)
        emergency_rollback
        ;;
    fix-db)
        database_emergency_fix
        ;;
    cleanup)
        emergency_cleanup
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        show_help
        ;;
esac
EOF

chmod +x /opt/sfap/emergency-response.sh
```

## 📞 技术支持和联系方式

### 紧急联系方式
- **技术支持**: <EMAIL>
- **紧急热线**: +86-xxx-xxxx-xxxx
- **GitHub Issues**: https://github.com/Arrbel/SFAP/issues

### 重要文档位置
- **部署手册**: `/opt/sfap/deploy/GITHUB_CLOUD_DEPLOYMENT.md`
- **操作日志**: `/opt/sfap/logs/`
- **备份文件**: `/opt/sfap/backup/`
- **配置文件**: `/opt/sfap/.env.production`

### 常用命令快速参考
```bash
# 部署相关
./daily-ops.sh deploy          # 完整部署
./daily-ops.sh update          # 更新应用
./daily-ops.sh status          # 查看状态

# 监控相关
./daily-ops.sh health          # 健康检查
./daily-ops.sh monitor         # 系统监控
./daily-ops.sh logs            # 查看日志

# 应急处理
./emergency-response.sh diagnosis    # 快速诊断
./emergency-response.sh restart      # 紧急重启
./emergency-response.sh rollback     # 紧急回滚
```

---

*最后更新时间：2024年8月*
*版本：v1.0.0*
```
```
