# SFAP后端多阶段构建Dockerfile
# 基于Maven 3.9和OpenJDK 17构建，优化镜像大小和安全性

# 构建阶段
FROM maven:3.9.4-eclipse-temurin-17-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装必要的工具
RUN apk add --no-cache git

# 创建Maven配置目录
RUN mkdir -p /root/.m2

# 创建Maven settings.xml（如果不存在）
RUN if [ ! -f settings.xml ]; then \
    cat > /root/.m2/settings.xml << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0
          http://maven.apache.org/xsd/settings-1.0.0.xsd">
    <mirrors>
        <mirror>
            <id>aliyun</id>
            <mirrorOf>central</mirrorOf>
            <name>Aliyun Central</name>
            <url>https://maven.aliyun.com/repository/central</url>
        </mirror>
    </mirrors>
</settings>
EOF
else \
    cp settings.xml /root/.m2/settings.xml; \
fi

# 复制pom.xml文件
COPY pom.xml .

# 下载依赖（利用Docker缓存层）
RUN mvn dependency:go-offline -B --no-transfer-progress

# 复制源代码
COPY src ./src

# 构建应用
RUN mvn clean package -DskipTests -B --no-transfer-progress

# 生产运行阶段
FROM eclipse-temurin:17-jre-alpine AS production

# 安装必要工具和字体
RUN apk add --no-cache \
    curl \
    tzdata \
    fontconfig \
    ttf-dejavu \
    && rm -rf /var/cache/apk/*

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 创建应用用户和组
RUN addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup -h /app

# 设置工作目录
WORKDIR /app

# 创建必要的目录
RUN mkdir -p /app/logs /app/config /app/data \
    && chown -R appuser:appgroup /app

# 复制jar文件（自动检测jar文件名）
COPY --from=builder --chown=appuser:appgroup /app/target/*.jar app.jar

# 创建启动脚本
RUN cat > /app/start.sh << 'EOF'
#!/bin/sh

# 等待数据库连接
echo "Waiting for database connection..."
while ! nc -z ${DB_HOST:-localhost} ${DB_PORT:-3306}; do
    echo "Waiting for database..."
    sleep 2
done
echo "Database is ready!"

# 等待Redis连接
echo "Waiting for Redis connection..."
while ! nc -z ${REDIS_HOST:-localhost} ${REDIS_PORT:-6379}; do
    echo "Waiting for Redis..."
    sleep 2
done
echo "Redis is ready!"

# 设置JVM参数
export JAVA_OPTS="${JAVA_OPTS:--Xms512m -Xmx2g -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/app/logs/}"

# 设置应用参数
export APP_OPTS="${APP_OPTS:---server.port=8080 --logging.file.path=/app/logs --spring.profiles.active=${SPRING_PROFILES_ACTIVE:-prod}}"

echo "Starting SFAP Backend with JAVA_OPTS: $JAVA_OPTS"
echo "Application options: $APP_OPTS"

# 启动应用
exec java $JAVA_OPTS -jar app.jar $APP_OPTS
EOF

RUN chmod +x /app/start.sh && chown appuser:appgroup /app/start.sh

# 安装netcat用于健康检查
RUN apk add --no-cache netcat-openbsd

# 切换到应用用户
USER appuser

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/actuator/health || exit 1

# 环境变量
ENV SPRING_PROFILES_ACTIVE=prod
ENV JAVA_OPTS="-Xms512m -Xmx2g -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/app/logs/"

# 设置标签
LABEL maintainer="SFAP Team" \
      version="1.0" \
      description="SFAP Backend Application"

# 启动命令
ENTRYPOINT ["/app/start.sh"]
