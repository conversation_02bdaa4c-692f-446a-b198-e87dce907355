#!/bin/bash

# SFAP系统监控配置脚本
# 配置Prometheus、Grafana和日志监控

set -e

# 配置变量
MONITORING_DIR="/opt/monitoring"
GRAFANA_PASSWORD="${GRAFANA_PASSWORD:-$(openssl rand -base64 32)}"
PROMETHEUS_PORT="9090"
GRAFANA_PORT="3000"
NODE_EXPORTER_PORT="9100"
LOG_DIR="/var/log/sfap"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log() {
    local level=${2:-INFO}
    local message="[$(date '+%Y-%m-%d %H:%M:%S')] [$level] $1"
    
    case $level in
        ERROR) echo -e "${RED}$message${NC}" ;;
        WARN)  echo -e "${YELLOW}$message${NC}" ;;
        SUCCESS) echo -e "${GREEN}$message${NC}" ;;
        INFO) echo -e "${BLUE}$message${NC}" ;;
        *) echo "$message" ;;
    esac
}

# 错误处理
error_exit() {
    log "$1" ERROR
    exit 1
}

# 检查是否为root用户
check_root() {
    if [ "$EUID" -ne 0 ]; then
        error_exit "请使用root用户运行此脚本"
    fi
}

# 创建监控目录
create_directories() {
    log "创建监控目录..." INFO
    
    mkdir -p "$MONITORING_DIR"/{prometheus,grafana,alertmanager,node_exporter}
    mkdir -p "$LOG_DIR"
    mkdir -p "/opt/scripts"
    
    log "监控目录创建完成" SUCCESS
}

# 安装Node Exporter
install_node_exporter() {
    log "安装Node Exporter..." INFO
    
    local version="1.6.1"
    local archive="node_exporter-${version}.linux-amd64.tar.gz"
    
    # 下载Node Exporter
    cd /tmp
    wget "https://github.com/prometheus/node_exporter/releases/download/v${version}/${archive}" || error_exit "Node Exporter下载失败"
    
    # 解压安装
    tar xzf "$archive"
    cp "node_exporter-${version}.linux-amd64/node_exporter" /usr/local/bin/
    chmod +x /usr/local/bin/node_exporter
    
    # 创建用户
    useradd --no-create-home --shell /bin/false node_exporter || true
    
    # 创建systemd服务
    cat > /etc/systemd/system/node_exporter.service << EOF
[Unit]
Description=Node Exporter
Wants=network-online.target
After=network-online.target

[Service]
User=node_exporter
Group=node_exporter
Type=simple
ExecStart=/usr/local/bin/node_exporter --web.listen-address=:$NODE_EXPORTER_PORT

[Install]
WantedBy=multi-user.target
EOF
    
    # 启动服务
    systemctl daemon-reload
    systemctl enable node_exporter
    systemctl start node_exporter
    
    # 清理
    rm -rf "/tmp/node_exporter-${version}.linux-amd64"*
    
    log "Node Exporter安装完成" SUCCESS
}

# 配置Prometheus
configure_prometheus() {
    log "配置Prometheus..." INFO
    
    # 创建Prometheus配置
    cat > "$MONITORING_DIR/prometheus/prometheus.yml" << EOF
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:$PROMETHEUS_PORT']

  - job_name: 'node_exporter'
    static_configs:
      - targets: ['localhost:$NODE_EXPORTER_PORT']

  - job_name: 'sfap_backend'
    static_configs:
      - targets: ['localhost:8080']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 30s

  - job_name: 'sfap_ai_service'
    static_configs:
      - targets: ['localhost:5000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'nginx'
    static_configs:
      - targets: ['localhost:8888']
    metrics_path: '/nginx_status'
    scrape_interval: 30s

  - job_name: 'mysql'
    static_configs:
      - targets: ['localhost:9104']

  - job_name: 'redis'
    static_configs:
      - targets: ['localhost:9121']
EOF
    
    # 创建告警规则
    cat > "$MONITORING_DIR/prometheus/alert_rules.yml" << EOF
groups:
  - name: sfap_alerts
    rules:
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is above 80% for more than 5 minutes"

      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100 > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is above 85% for more than 5 minutes"

      - alert: DiskSpaceLow
        expr: (node_filesystem_avail_bytes{mountpoint="/"} / node_filesystem_size_bytes{mountpoint="/"}) * 100 < 10
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Low disk space"
          description: "Disk space is below 10%"

      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Service is down"
          description: "{{ \$labels.job }} service is down"

      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High response time"
          description: "95th percentile response time is above 1 second"
EOF
    
    log "Prometheus配置完成" SUCCESS
}

# 配置Grafana
configure_grafana() {
    log "配置Grafana..." INFO
    
    # 创建Grafana配置目录
    mkdir -p "$MONITORING_DIR/grafana"/{data,logs,plugins,provisioning/{datasources,dashboards}}
    
    # 创建数据源配置
    cat > "$MONITORING_DIR/grafana/provisioning/datasources/prometheus.yml" << EOF
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:$PROMETHEUS_PORT
    isDefault: true
    editable: true
EOF
    
    # 创建仪表板配置
    cat > "$MONITORING_DIR/grafana/provisioning/dashboards/dashboard.yml" << EOF
apiVersion: 1

providers:
  - name: 'default'
    orgId: 1
    folder: ''
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards
EOF
    
    # 创建SFAP仪表板
    cat > "$MONITORING_DIR/grafana/dashboards/sfap-dashboard.json" << 'EOF'
{
  "dashboard": {
    "id": null,
    "title": "SFAP System Monitoring",
    "tags": ["sfap"],
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "CPU Usage",
        "type": "stat",
        "targets": [
          {
            "expr": "100 - (avg by(instance) (irate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100)",
            "legendFormat": "CPU Usage %"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "unit": "percent",
            "thresholds": {
              "steps": [
                {"color": "green", "value": null},
                {"color": "yellow", "value": 70},
                {"color": "red", "value": 85}
              ]
            }
          }
        },
        "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}
      },
      {
        "id": 2,
        "title": "Memory Usage",
        "type": "stat",
        "targets": [
          {
            "expr": "(node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100",
            "legendFormat": "Memory Usage %"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "unit": "percent",
            "thresholds": {
              "steps": [
                {"color": "green", "value": null},
                {"color": "yellow", "value": 70},
                {"color": "red", "value": 85}
              ]
            }
          }
        },
        "gridPos": {"h": 8, "w": 6, "x": 6, "y": 0}
      }
    ],
    "time": {"from": "now-1h", "to": "now"},
    "refresh": "30s"
  }
}
EOF
    
    log "Grafana配置完成" SUCCESS
}

# 创建Docker Compose配置
create_docker_compose() {
    log "创建监控Docker Compose配置..." INFO
    
    cat > "$MONITORING_DIR/docker-compose.yml" << EOF
version: '3.8'

services:
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    ports:
      - "$PROMETHEUS_PORT:9090"
    volumes:
      - ./prometheus:/etc/prometheus
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    networks:
      - monitoring

  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    ports:
      - "$GRAFANA_PORT:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning
      - ./grafana/dashboards:/var/lib/grafana/dashboards
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=$GRAFANA_PASSWORD
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    restart: unless-stopped
    networks:
      - monitoring

  alertmanager:
    image: prom/alertmanager:latest
    container_name: alertmanager
    ports:
      - "9093:9093"
    volumes:
      - ./alertmanager:/etc/alertmanager
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
      - '--web.external-url=http://localhost:9093'
    restart: unless-stopped
    networks:
      - monitoring

volumes:
  prometheus_data:
  grafana_data:

networks:
  monitoring:
    driver: bridge
EOF
    
    log "Docker Compose配置创建完成" SUCCESS
}

# 配置日志监控
configure_log_monitoring() {
    log "配置日志监控..." INFO
    
    # 创建日志轮转配置
    cat > /etc/logrotate.d/sfap-monitoring << EOF
$LOG_DIR/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 sfap sfap
    postrotate
        systemctl reload rsyslog > /dev/null 2>&1 || true
    endscript
}
EOF
    
    # 创建日志分析脚本
    cat > /opt/scripts/analyze-logs.sh << 'EOF'
#!/bin/bash

# 日志分析脚本
LOG_DIR="/var/log/sfap"
REPORT_FILE="/tmp/log_analysis_$(date +%Y%m%d).txt"

# 分析错误日志
analyze_errors() {
    echo "=== 错误日志分析 ===" > "$REPORT_FILE"
    echo "时间范围: $(date)" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"
    
    # 统计错误数量
    echo "错误统计:" >> "$REPORT_FILE"
    grep -i "error" "$LOG_DIR"/*.log | wc -l >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"
    
    # 最近的错误
    echo "最近的错误:" >> "$REPORT_FILE"
    grep -i "error" "$LOG_DIR"/*.log | tail -10 >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"
    
    # 404错误统计
    echo "404错误统计:" >> "$REPORT_FILE"
    grep "404" /var/log/nginx/access.log | wc -l >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"
    
    # 慢查询统计
    echo "慢查询统计:" >> "$REPORT_FILE"
    if [ -f /var/log/mysql/slow.log ]; then
        grep "Query_time" /var/log/mysql/slow.log | wc -l >> "$REPORT_FILE"
    fi
}

# 发送报告
send_report() {
    if [ -n "$EMAIL" ]; then
        mail -s "SFAP日志分析报告" "$EMAIL" < "$REPORT_FILE"
    fi
}

analyze_errors
send_report
EOF
    
    chmod +x /opt/scripts/analyze-logs.sh
    
    # 添加到crontab（每天生成报告）
    (crontab -l 2>/dev/null; echo "0 6 * * * /opt/scripts/analyze-logs.sh") | crontab -
    
    log "日志监控配置完成" SUCCESS
}

# 创建系统监控脚本
create_system_monitor() {
    log "创建系统监控脚本..." INFO
    
    cat > /opt/scripts/system-monitor.sh << 'EOF'
#!/bin/bash

# 系统监控脚本
WEBHOOK_URL="${WEBHOOK_URL:-}"
LOG_FILE="/var/log/sfap/system-monitor.log"

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 发送告警
send_alert() {
    local message="$1"
    log "ALERT: $message"
    
    if [ -n "$WEBHOOK_URL" ]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"🚨 SFAP系统告警: $message\"}" \
            "$WEBHOOK_URL" || true
    fi
}

# 检查系统负载
check_load() {
    local load=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    local cpu_cores=$(nproc)
    local load_threshold=$(echo "$cpu_cores * 2" | bc)
    
    if (( $(echo "$load > $load_threshold" | bc -l) )); then
        send_alert "系统负载过高: $load (CPU核心数: $cpu_cores)"
    fi
    
    log "系统负载: $load"
}

# 检查磁盘空间
check_disk() {
    local usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    
    if [ "$usage" -gt 85 ]; then
        send_alert "磁盘空间不足: ${usage}%"
    fi
    
    log "磁盘使用率: ${usage}%"
}

# 检查内存使用
check_memory() {
    local mem_usage=$(free | awk 'NR==2{printf "%.2f", $3*100/$2}')
    
    if (( $(echo "$mem_usage > 85" | bc -l) )); then
        send_alert "内存使用率过高: ${mem_usage}%"
    fi
    
    log "内存使用率: ${mem_usage}%"
}

# 检查服务状态
check_services() {
    local services=("nginx" "mysql" "redis-server" "docker")
    
    for service in "${services[@]}"; do
        if ! systemctl is-active --quiet "$service"; then
            send_alert "服务 $service 未运行"
        else
            log "服务 $service 运行正常"
        fi
    done
}

# 主函数
main() {
    log "开始系统监控检查..."
    
    check_load
    check_disk
    check_memory
    check_services
    
    log "系统监控检查完成"
}

main "$@"
EOF
    
    chmod +x /opt/scripts/system-monitor.sh
    
    # 添加到crontab（每5分钟检查一次）
    (crontab -l 2>/dev/null; echo "*/5 * * * * /opt/scripts/system-monitor.sh") | crontab -
    
    log "系统监控脚本创建完成" SUCCESS
}

# 启动监控服务
start_monitoring() {
    log "启动监控服务..." INFO
    
    cd "$MONITORING_DIR"
    docker-compose up -d || error_exit "监控服务启动失败"
    
    # 等待服务启动
    sleep 30
    
    # 检查服务状态
    if curl -s "http://localhost:$PROMETHEUS_PORT" &>/dev/null; then
        log "Prometheus启动成功" SUCCESS
    else
        log "Prometheus启动失败" ERROR
    fi
    
    if curl -s "http://localhost:$GRAFANA_PORT" &>/dev/null; then
        log "Grafana启动成功" SUCCESS
    else
        log "Grafana启动失败" ERROR
    fi
}

# 保存监控凭据
save_monitoring_credentials() {
    log "保存监控凭据..." INFO
    
    cat > /root/monitoring_credentials.txt << EOF
SFAP监控系统凭据
==================

Grafana:
- URL: http://localhost:$GRAFANA_PORT
- 用户名: admin
- 密码: $GRAFANA_PASSWORD

Prometheus:
- URL: http://localhost:$PROMETHEUS_PORT

Node Exporter:
- URL: http://localhost:$NODE_EXPORTER_PORT

监控目录: $MONITORING_DIR
日志目录: $LOG_DIR

生成时间: $(date)
EOF
    
    chmod 600 /root/monitoring_credentials.txt
    
    log "监控凭据已保存到 /root/monitoring_credentials.txt" SUCCESS
}

# 主函数
main() {
    log "开始配置系统监控..." INFO
    
    check_root
    create_directories
    install_node_exporter
    configure_prometheus
    configure_grafana
    create_docker_compose
    configure_log_monitoring
    create_system_monitor
    start_monitoring
    save_monitoring_credentials
    
    log "系统监控配置完成!" SUCCESS
    log "Grafana访问地址: http://localhost:$GRAFANA_PORT" INFO
    log "默认用户名: admin, 密码: $GRAFANA_PASSWORD" INFO
}

# 执行主函数
main "$@"
