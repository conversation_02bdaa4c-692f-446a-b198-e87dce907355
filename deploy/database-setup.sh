#!/bin/bash

# SFAP数据库配置脚本
# 配置MySQL、Redis和数据库连接

set -e

# 配置变量
DB_NAME="agriculture_mall"
DB_USER="sfap_user"
DB_PASSWORD="${DB_PASSWORD:-$(openssl rand -base64 32)}"
MYSQL_ROOT_PASSWORD="${MYSQL_ROOT_PASSWORD:-$(openssl rand -base64 32)}"
REDIS_PASSWORD="${REDIS_PASSWORD:-$(openssl rand -base64 32)}"
BACKUP_DIR="/opt/backup/database"
LOG_DIR="/var/log/sfap"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log() {
    local level=${2:-INFO}
    local message="[$(date '+%Y-%m-%d %H:%M:%S')] [$level] $1"
    
    case $level in
        ERROR) echo -e "${RED}$message${NC}" ;;
        WARN)  echo -e "${YELLOW}$message${NC}" ;;
        SUCCESS) echo -e "${GREEN}$message${NC}" ;;
        INFO) echo -e "${BLUE}$message${NC}" ;;
        *) echo "$message" ;;
    esac
}

# 错误处理
error_exit() {
    log "$1" ERROR
    exit 1
}

# 检查是否为root用户
check_root() {
    if [ "$EUID" -ne 0 ]; then
        error_exit "请使用root用户运行此脚本"
    fi
}

# 配置MySQL
configure_mysql() {
    log "配置MySQL..." INFO
    
    # 检查MySQL是否运行
    if ! systemctl is-active --quiet mysql; then
        systemctl start mysql
    fi
    
    # 设置root密码
    mysql -e "ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY '$MYSQL_ROOT_PASSWORD';" || true
    
    # 创建数据库
    mysql -u root -p"$MYSQL_ROOT_PASSWORD" << EOF
CREATE DATABASE IF NOT EXISTS $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER IF NOT EXISTS '$DB_USER'@'localhost' IDENTIFIED BY '$DB_PASSWORD';
GRANT ALL PRIVILEGES ON $DB_NAME.* TO '$DB_USER'@'localhost';
FLUSH PRIVILEGES;
EOF
    
    # 优化MySQL配置
    cat > /etc/mysql/mysql.conf.d/sfap.cnf << EOF
[mysqld]
# 基本配置
max_connections = 200
max_connect_errors = 10000
table_open_cache = 2048
max_allowed_packet = 64M
binlog_cache_size = 1M
max_heap_table_size = 64M
tmp_table_size = 64M

# InnoDB配置
innodb_buffer_pool_size = 512M
innodb_log_file_size = 128M
innodb_log_buffer_size = 16M
innodb_flush_log_at_trx_commit = 2
innodb_lock_wait_timeout = 120

# 查询缓存
query_cache_size = 64M
query_cache_type = 1
query_cache_limit = 2M

# 慢查询日志
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2

# 二进制日志
log_bin = /var/log/mysql/mysql-bin.log
binlog_format = ROW
expire_logs_days = 7
max_binlog_size = 100M

# 字符集
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
EOF
    
    # 重启MySQL
    systemctl restart mysql
    
    log "MySQL配置完成" SUCCESS
}

# 配置Redis
configure_redis() {
    log "配置Redis..." INFO
    
    # 备份原配置
    cp /etc/redis/redis.conf /etc/redis/redis.conf.backup
    
    # 配置Redis
    cat > /etc/redis/redis.conf << EOF
# 网络配置
bind 127.0.0.1
port 6379
timeout 300
tcp-keepalive 60

# 安全配置
requirepass $REDIS_PASSWORD
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command DEBUG ""
rename-command CONFIG "CONFIG_$(openssl rand -hex 8)"

# 内存配置
maxmemory 256mb
maxmemory-policy allkeys-lru
maxmemory-samples 5

# 持久化配置
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /var/lib/redis

# AOF配置
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# 日志配置
loglevel notice
logfile /var/log/redis/redis-server.log
syslog-enabled yes
syslog-ident redis

# 客户端配置
maxclients 10000

# 慢查询日志
slowlog-log-slower-than 10000
slowlog-max-len 128
EOF
    
    # 创建日志目录
    mkdir -p /var/log/redis
    chown redis:redis /var/log/redis
    
    # 重启Redis
    systemctl restart redis-server
    
    log "Redis配置完成" SUCCESS
}

# 导入数据库结构
import_database_schema() {
    log "导入数据库结构..." INFO
    
    local sql_file="/opt/sfap/database/agriculture_mall.sql"
    
    if [ -f "$sql_file" ]; then
        mysql -u root -p"$MYSQL_ROOT_PASSWORD" "$DB_NAME" < "$sql_file" || log "数据库导入失败" WARN
        log "数据库结构导入完成" SUCCESS
    else
        log "数据库文件不存在: $sql_file" WARN
    fi
}

# 创建数据库备份脚本
create_backup_script() {
    log "创建数据库备份脚本..." INFO
    
    mkdir -p "$BACKUP_DIR"
    mkdir -p "/opt/scripts"
    
    cat > /opt/scripts/backup-database.sh << EOF
#!/bin/bash

# 数据库备份脚本
BACKUP_DIR="$BACKUP_DIR"
LOG_FILE="$LOG_DIR/backup.log"
RETENTION_DAYS=30

# 日志函数
log() {
    echo "[\$(date '+%Y-%m-%d %H:%M:%S')] \$1" | tee -a "\$LOG_FILE"
}

# 创建备份目录
mkdir -p "\$BACKUP_DIR/\$(date +%Y%m%d)"

# MySQL备份
log "开始MySQL备份..."
mysqldump -u root -p"$MYSQL_ROOT_PASSWORD" --single-transaction --routines --triggers "$DB_NAME" | gzip > "\$BACKUP_DIR/\$(date +%Y%m%d)/${DB_NAME}_\$(date +%H%M%S).sql.gz"

if [ \$? -eq 0 ]; then
    log "MySQL备份完成"
else
    log "MySQL备份失败"
fi

# Redis备份
log "开始Redis备份..."
redis-cli -a "$REDIS_PASSWORD" --rdb "\$BACKUP_DIR/\$(date +%Y%m%d)/redis_\$(date +%H%M%S).rdb" 2>/dev/null

if [ \$? -eq 0 ]; then
    log "Redis备份完成"
else
    log "Redis备份失败"
fi

# 清理旧备份
log "清理旧备份..."
find "\$BACKUP_DIR" -type d -mtime +\$RETENTION_DAYS -exec rm -rf {} + 2>/dev/null || true

log "备份任务完成"
EOF
    
    chmod +x /opt/scripts/backup-database.sh
    
    # 添加到crontab
    (crontab -l 2>/dev/null; echo "0 2 * * * /opt/scripts/backup-database.sh") | crontab -
    
    log "数据库备份脚本创建完成" SUCCESS
}

# 创建数据库监控脚本
create_monitoring_script() {
    log "创建数据库监控脚本..." INFO
    
    cat > /opt/scripts/monitor-database.sh << EOF
#!/bin/bash

# 数据库监控脚本
LOG_FILE="$LOG_DIR/monitor.log"
ALERT_EMAIL="${ALERT_EMAIL:-<EMAIL>}"

# 日志函数
log() {
    echo "[\$(date '+%Y-%m-%d %H:%M:%S')] \$1" | tee -a "\$LOG_FILE"
}

# 发送告警
send_alert() {
    local message="\$1"
    log "ALERT: \$message"
    
    # 发送邮件告警（如果配置了邮件服务）
    if command -v mail &> /dev/null; then
        echo "\$message" | mail -s "SFAP Database Alert" "\$ALERT_EMAIL"
    fi
    
    # 发送Webhook告警
    if [ -n "\$WEBHOOK_URL" ]; then
        curl -X POST -H 'Content-type: application/json' \\
            --data "{\"text\":\"🚨 SFAP数据库告警: \$message\"}" \\
            "\$WEBHOOK_URL" || true
    fi
}

# 检查MySQL状态
check_mysql() {
    if ! systemctl is-active --quiet mysql; then
        send_alert "MySQL服务未运行"
        return 1
    fi
    
    # 检查连接数
    local connections=\$(mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "SHOW STATUS LIKE 'Threads_connected';" | awk 'NR==2 {print \$2}')
    if [ "\$connections" -gt 150 ]; then
        send_alert "MySQL连接数过高: \$connections"
    fi
    
    # 检查慢查询
    local slow_queries=\$(mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "SHOW STATUS LIKE 'Slow_queries';" | awk 'NR==2 {print \$2}')
    if [ "\$slow_queries" -gt 100 ]; then
        log "检测到慢查询: \$slow_queries"
    fi
    
    log "MySQL状态正常"
}

# 检查Redis状态
check_redis() {
    if ! systemctl is-active --quiet redis-server; then
        send_alert "Redis服务未运行"
        return 1
    fi
    
    # 检查内存使用
    local memory_usage=\$(redis-cli -a "$REDIS_PASSWORD" info memory | grep used_memory_human | cut -d: -f2 | tr -d '\\r')
    log "Redis内存使用: \$memory_usage"
    
    # 检查连接数
    local connections=\$(redis-cli -a "$REDIS_PASSWORD" info clients | grep connected_clients | cut -d: -f2 | tr -d '\\r')
    if [ "\$connections" -gt 100 ]; then
        send_alert "Redis连接数过高: \$connections"
    fi
    
    log "Redis状态正常"
}

# 检查磁盘空间
check_disk_space() {
    local usage=\$(df /var/lib/mysql | awk 'NR==2 {print \$5}' | sed 's/%//')
    if [ "\$usage" -gt 80 ]; then
        send_alert "数据库磁盘空间不足: \${usage}%"
    fi
    
    log "磁盘空间使用: \${usage}%"
}

# 主函数
main() {
    log "开始数据库监控检查..."
    
    check_mysql
    check_redis
    check_disk_space
    
    log "数据库监控检查完成"
}

main "\$@"
EOF
    
    chmod +x /opt/scripts/monitor-database.sh
    
    # 添加到crontab（每5分钟检查一次）
    (crontab -l 2>/dev/null; echo "*/5 * * * * /opt/scripts/monitor-database.sh") | crontab -
    
    log "数据库监控脚本创建完成" SUCCESS
}

# 创建数据库连接配置
create_connection_config() {
    log "创建数据库连接配置..." INFO
    
    # 创建环境配置文件
    cat > /opt/sfap/.env.production << EOF
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=$DB_NAME
DB_USER=$DB_USER
DB_PASSWORD=$DB_PASSWORD

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=$REDIS_PASSWORD
REDIS_DB=0

# 连接池配置
DB_POOL_MIN=5
DB_POOL_MAX=20
DB_POOL_ACQUIRE=30000
DB_POOL_IDLE=10000

# 缓存配置
CACHE_TTL=3600
CACHE_PREFIX=sfap:
EOF
    
    # 设置权限
    chmod 600 /opt/sfap/.env.production
    chown sfap:sfap /opt/sfap/.env.production
    
    log "数据库连接配置创建完成" SUCCESS
}

# 保存凭据
save_credentials() {
    log "保存数据库凭据..." INFO
    
    cat > /root/database_credentials.txt << EOF
SFAP数据库凭据
================

MySQL Root密码: $MYSQL_ROOT_PASSWORD
数据库名称: $DB_NAME
数据库用户: $DB_USER
数据库密码: $DB_PASSWORD

Redis密码: $REDIS_PASSWORD

连接信息:
- MySQL: mysql -u $DB_USER -p'$DB_PASSWORD' $DB_NAME
- Redis: redis-cli -a '$REDIS_PASSWORD'

备份目录: $BACKUP_DIR
日志目录: $LOG_DIR

生成时间: $(date)
EOF
    
    chmod 600 /root/database_credentials.txt
    
    log "数据库凭据已保存到 /root/database_credentials.txt" SUCCESS
}

# 测试数据库连接
test_connections() {
    log "测试数据库连接..." INFO
    
    # 测试MySQL连接
    if mysql -u "$DB_USER" -p"$DB_PASSWORD" -e "SELECT 1;" "$DB_NAME" &>/dev/null; then
        log "MySQL连接测试成功" SUCCESS
    else
        log "MySQL连接测试失败" ERROR
    fi
    
    # 测试Redis连接
    if redis-cli -a "$REDIS_PASSWORD" ping | grep -q PONG; then
        log "Redis连接测试成功" SUCCESS
    else
        log "Redis连接测试失败" ERROR
    fi
}

# 主函数
main() {
    log "开始数据库配置..." INFO
    
    check_root
    configure_mysql
    configure_redis
    import_database_schema
    create_backup_script
    create_monitoring_script
    create_connection_config
    save_credentials
    test_connections
    
    log "数据库配置完成!" SUCCESS
    log "凭据信息已保存到 /root/database_credentials.txt" INFO
}

# 执行主函数
main "$@"
