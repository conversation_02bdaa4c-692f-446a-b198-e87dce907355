# SFAP自动化部署维护指南

## 📋 维护概览

本文档提供SFAP自动化部署系统的日常维护指南，包括定期检查、性能优化、备份管理等。

## 🔄 日常维护任务

### 每日检查（5分钟）

```bash
# 1. 检查服务状态
/www/wwwroot/sfap/deploy/monitor.sh

# 2. 查看部署日志
tail -20 /www/logs/sfap-deploy.log

# 3. 检查磁盘空间
df -h | grep -E '(/$|/www)'

# 4. 检查系统负载
uptime
```

### 每周维护（30分钟）

```bash
# 1. 清理旧日志
find /www/logs -name "*.log" -mtime +7 -exec rm {} \;

# 2. 清理旧备份
find /www/backup/sfap -name "backup_*" -mtime +30 -exec rm -rf {} \;

# 3. 更新系统包
yum update -y

# 4. 检查SSL证书（如果使用）
openssl x509 -in /path/to/cert.pem -text -noout | grep "Not After"
```

### 每月维护（1小时）

```bash
# 1. 完整系统备份
tar -czf /www/backup/system_backup_$(date +%Y%m%d).tar.gz \
  /www/wwwroot/sfap \
  /www/backup/sfap \
  /www/logs

# 2. 数据库优化
mysql -u root -p -e "OPTIMIZE TABLE agriculture_mall.*;"

# 3. 性能分析
iostat -x 1 5
sar -u 1 5

# 4. 安全检查
last | head -20
grep "Failed password" /var/log/secure | tail -10
```

## 📊 监控和告警

### 自动监控脚本

创建监控脚本 `/www/wwwroot/sfap/deploy/health_monitor.sh`：

```bash
#!/bin/bash

# SFAP健康监控脚本
LOG_FILE="/www/logs/health_monitor.log"
ALERT_EMAIL="<EMAIL>"

log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a $LOG_FILE
}

# 检查服务状态
check_services() {
    local failed_services=()
    
    # 检查前端
    if ! curl -f -s http://localhost:8200 > /dev/null; then
        failed_services+=("前端服务")
    fi
    
    # 检查后端
    if ! curl -f -s http://localhost:8081/api/health > /dev/null; then
        failed_services+=("后端服务")
    fi
    
    # 检查AI服务
    if ! curl -f -s http://localhost:5000/health > /dev/null; then
        failed_services+=("AI服务")
    fi
    
    if [ ${#failed_services[@]} -gt 0 ]; then
        log "警告: 以下服务异常: ${failed_services[*]}"
        return 1
    else
        log "所有服务正常运行"
        return 0
    fi
}

# 检查系统资源
check_resources() {
    # 检查磁盘空间
    DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ $DISK_USAGE -gt 80 ]; then
        log "警告: 磁盘使用率过高: ${DISK_USAGE}%"
    fi
    
    # 检查内存使用
    MEM_USAGE=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    if [ $MEM_USAGE -gt 85 ]; then
        log "警告: 内存使用率过高: ${MEM_USAGE}%"
    fi
    
    # 检查CPU负载
    LOAD_AVG=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    if (( $(echo "$LOAD_AVG > 2.0" | bc -l) )); then
        log "警告: CPU负载过高: $LOAD_AVG"
    fi
}

# 主检查流程
main() {
    log "开始健康检查"
    
    if check_services && check_resources; then
        log "系统状态正常"
    else
        log "发现系统问题，请检查"
        # 这里可以添加邮件或钉钉通知
    fi
}

main "$@"
```

### 设置定时监控

```bash
# 添加到crontab
crontab -e

# 每5分钟检查一次服务状态
*/5 * * * * /www/wwwroot/sfap/deploy/health_monitor.sh

# 每小时检查一次系统资源
0 * * * * /www/wwwroot/sfap/deploy/resource_monitor.sh

# 每天凌晨备份数据库
0 2 * * * /www/wwwroot/sfap/deploy/backup_database.sh
```

## 🔧 性能优化

### 1. Nginx优化

编辑 `/www/server/panel/vhost/nginx/sfap.conf`：

```nginx
server {
    listen 8200;
    server_name **************;
    root /www/wwwroot/sfap-frontend;
    index index.html;
    
    # 启用Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary Accept-Encoding;
    }
    
    # 启用HTTP/2（如果支持SSL）
    # listen 443 ssl http2;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
}
```

### 2. MySQL优化

编辑 `/etc/my.cnf`：

```ini
[mysqld]
# 基础配置
max_connections = 200
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2

# 查询缓存
query_cache_type = 1
query_cache_size = 64M

# 慢查询日志
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2
```

### 3. Redis优化

编辑 `/etc/redis.conf`：

```ini
# 内存优化
maxmemory 512mb
maxmemory-policy allkeys-lru

# 持久化优化
save 900 1
save 300 10
save 60 10000

# 网络优化
tcp-keepalive 300
timeout 300
```

### 4. Java应用优化

修改启动参数：

```bash
# 在systemd服务文件中设置JVM参数
ExecStart=/usr/bin/java -Xms512m -Xmx2g -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -jar app.jar
```

## 💾 备份管理

### 自动备份脚本

创建 `/www/wwwroot/sfap/deploy/backup_database.sh`：

```bash
#!/bin/bash

# 数据库自动备份脚本
BACKUP_DIR="/www/backup/database"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="agriculture_mall"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
mysqldump --single-transaction --routines --triggers \
  --default-character-set=utf8mb4 \
  $DB_NAME > $BACKUP_DIR/backup_${DATE}.sql

# 压缩备份文件
gzip $BACKUP_DIR/backup_${DATE}.sql

# 删除7天前的备份
find $BACKUP_DIR -name "backup_*.sql.gz" -mtime +7 -delete

echo "数据库备份完成: backup_${DATE}.sql.gz"
```

### 代码备份策略

```bash
# 每次部署前自动备份
backup_code() {
    local backup_name="code_backup_$(date +%Y%m%d_%H%M%S)"
    local backup_path="/www/backup/code/$backup_name"
    
    mkdir -p "$backup_path"
    
    # 备份前端
    if [ -d "/www/wwwroot/sfap-frontend" ]; then
        cp -r /www/wwwroot/sfap-frontend "$backup_path/"
    fi
    
    # 备份后端
    if [ -d "/www/wwwroot/sfap-backend" ]; then
        cp -r /www/wwwroot/sfap-backend "$backup_path/"
    fi
    
    # 备份AI服务
    if [ -d "/www/wwwroot/sfap-ai" ]; then
        cp -r /www/wwwroot/sfap-ai "$backup_path/"
    fi
    
    echo "代码备份完成: $backup_path"
}
```

## 🔐 安全维护

### 1. 定期安全检查

```bash
# 检查异常登录
last | grep -v "$(whoami)" | head -10

# 检查失败的SSH登录
grep "Failed password" /var/log/secure | tail -20

# 检查开放端口
nmap -sT -O localhost

# 检查文件权限
find /www/wwwroot/sfap -type f -perm 777
```

### 2. 更新安全配置

```bash
# 更新Webhook密钥（每季度）
openssl rand -hex 32

# 更新SSH密钥（每年）
ssh-keygen -t rsa -b 4096 -C "new_key_$(date +%Y)"

# 更新数据库密码（每半年）
mysql -u root -p -e "ALTER USER 'root'@'localhost' IDENTIFIED BY 'new_password';"
```

### 3. 防火墙配置

```bash
# 只开放必要端口
firewall-cmd --permanent --add-port=22/tcp    # SSH
firewall-cmd --permanent --add-port=80/tcp    # HTTP
firewall-cmd --permanent --add-port=443/tcp   # HTTPS
firewall-cmd --permanent --add-port=8200/tcp  # 前端
firewall-cmd --reload
```

## 📈 性能监控

### 1. 应用性能监控

```bash
# 创建性能监控脚本
cat > /www/wwwroot/sfap/deploy/performance_monitor.sh << 'EOF'
#!/bin/bash

# 检查响应时间
check_response_time() {
    local url=$1
    local max_time=$2
    
    response_time=$(curl -o /dev/null -s -w '%{time_total}' $url)
    
    if (( $(echo "$response_time > $max_time" | bc -l) )); then
        echo "警告: $url 响应时间过长: ${response_time}s"
    fi
}

# 检查各服务响应时间
check_response_time "http://localhost:8200" "2.0"
check_response_time "http://localhost:8081/api/health" "1.0"
check_response_time "http://localhost:5000/health" "1.0"
EOF

chmod +x /www/wwwroot/sfap/deploy/performance_monitor.sh
```

### 2. 资源使用监控

```bash
# 创建资源监控脚本
cat > /www/wwwroot/sfap/deploy/resource_monitor.sh << 'EOF'
#!/bin/bash

LOG_FILE="/www/logs/resource_monitor.log"

# 记录系统资源使用情况
{
    echo "=== $(date) ==="
    echo "CPU使用率:"
    top -bn1 | grep "Cpu(s)" | awk '{print $2}' | sed 's/%us,//'
    
    echo "内存使用率:"
    free | awk 'NR==2{printf "%.2f%%\n", $3*100/$2}'
    
    echo "磁盘使用率:"
    df -h | grep -E '(/$|/www)' | awk '{print $5 " " $6}'
    
    echo "网络连接数:"
    netstat -an | grep ESTABLISHED | wc -l
    
    echo "---"
} >> $LOG_FILE
EOF

chmod +x /www/wwwroot/sfap/deploy/resource_monitor.sh
```

## 🔄 版本管理

### 1. 版本标记

```bash
# 在Git中创建版本标签
git tag -a v1.0.0 -m "Release version 1.0.0"
git push origin v1.0.0

# 部署特定版本
./bt-panel-deploy.sh --version=v1.0.0
```

### 2. 回滚策略

```bash
# 快速回滚到上一个版本
./bt-panel-deploy.sh --rollback

# 回滚到指定版本
./bt-panel-deploy.sh --rollback --version=v1.0.0
```

## 📞 维护联系方式

### 紧急联系

- **系统管理员**: [联系方式]
- **开发负责人**: [联系方式]
- **运维负责人**: [联系方式]

### 维护时间窗口

- **日常维护**: 每日 02:00-04:00
- **重大更新**: 每周日 01:00-05:00
- **紧急维护**: 随时（需要审批）

### 维护记录

请在每次维护后更新维护记录：

```bash
# 维护记录模板
echo "$(date): [维护类型] [维护内容] [执行人] [结果]" >> /www/logs/maintenance.log
```

## 📚 相关文档

- [实施步骤指南](./STEP_BY_STEP_GUIDE.md)
- [配置文件说明](./CONFIG_DOCUMENTATION.md)
- [故障排除指南](./TROUBLESHOOTING.md)
- [安全配置指南](./SECURITY_GUIDE.md)
