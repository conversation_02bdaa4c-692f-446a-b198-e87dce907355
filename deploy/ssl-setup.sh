#!/bin/bash

# SFAP SSL证书配置脚本
# 支持Let's Encrypt自动证书和自签名证书

set -e

# 配置变量
DOMAIN="${DOMAIN:-sfap.example.com}"
EMAIL="${EMAIL:-<EMAIL>}"
SSL_DIR="/etc/ssl/certs/sfap"
NGINX_DIR="/etc/nginx"
CERTBOT_DIR="/var/www/certbot"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log() {
    local level=${2:-INFO}
    local message="[$(date '+%Y-%m-%d %H:%M:%S')] [$level] $1"
    
    case $level in
        ERROR) echo -e "${RED}$message${NC}" ;;
        WARN)  echo -e "${YELLOW}$message${NC}" ;;
        SUCCESS) echo -e "${GREEN}$message${NC}" ;;
        INFO) echo -e "${BLUE}$message${NC}" ;;
        *) echo "$message" ;;
    esac
}

# 错误处理
error_exit() {
    log "$1" ERROR
    exit 1
}

# 检查是否为root用户
check_root() {
    if [ "$EUID" -ne 0 ]; then
        error_exit "请使用root用户运行此脚本"
    fi
}

# 创建必要目录
create_directories() {
    log "创建SSL目录..." INFO
    
    mkdir -p "$SSL_DIR"
    mkdir -p "$CERTBOT_DIR"
    mkdir -p "/var/log/letsencrypt"
    
    log "SSL目录创建完成" SUCCESS
}

# 安装Certbot
install_certbot() {
    log "安装Certbot..." INFO
    
    # 安装snapd（如果未安装）
    if ! command -v snap &> /dev/null; then
        apt update
        apt install -y snapd
        systemctl enable snapd
        systemctl start snapd
        
        # 等待snapd启动
        sleep 10
    fi
    
    # 安装certbot
    snap install core; snap refresh core
    snap install --classic certbot
    
    # 创建软链接
    ln -sf /snap/bin/certbot /usr/bin/certbot
    
    log "Certbot安装完成" SUCCESS
}

# 生成DH参数
generate_dhparam() {
    log "生成DH参数..." INFO
    
    if [ ! -f "$SSL_DIR/dhparam.pem" ]; then
        openssl dhparam -out "$SSL_DIR/dhparam.pem" 2048
        log "DH参数生成完成" SUCCESS
    else
        log "DH参数已存在" INFO
    fi
}

# 创建临时Nginx配置
create_temp_nginx_config() {
    log "创建临时Nginx配置..." INFO
    
    cat > "$NGINX_DIR/sites-available/temp-ssl" << EOF
server {
    listen 80;
    listen [::]:80;
    server_name $DOMAIN www.$DOMAIN;
    
    location /.well-known/acme-challenge/ {
        root $CERTBOT_DIR;
        allow all;
    }
    
    location / {
        return 200 'SSL setup in progress...';
        add_header Content-Type text/plain;
    }
}
EOF
    
    # 启用临时配置
    ln -sf "$NGINX_DIR/sites-available/temp-ssl" "$NGINX_DIR/sites-enabled/"
    
    # 测试配置
    nginx -t || error_exit "Nginx配置测试失败"
    
    # 重载Nginx
    systemctl reload nginx
    
    log "临时Nginx配置创建完成" SUCCESS
}

# 获取Let's Encrypt证书
obtain_letsencrypt_cert() {
    log "获取Let's Encrypt证书..." INFO
    
    # 检查域名是否可达
    if ! curl -s "http://$DOMAIN/.well-known/acme-challenge/test" &>/dev/null; then
        log "域名 $DOMAIN 可能无法访问，请检查DNS配置" WARN
    fi
    
    # 获取证书
    certbot certonly \
        --webroot \
        --webroot-path="$CERTBOT_DIR" \
        --email "$EMAIL" \
        --agree-tos \
        --no-eff-email \
        --domains "$DOMAIN" \
        --domains "www.$DOMAIN" \
        --non-interactive || error_exit "Let's Encrypt证书获取失败"
    
    # 复制证书到SSL目录
    cp "/etc/letsencrypt/live/$DOMAIN/fullchain.pem" "$SSL_DIR/"
    cp "/etc/letsencrypt/live/$DOMAIN/privkey.pem" "$SSL_DIR/"
    cp "/etc/letsencrypt/live/$DOMAIN/chain.pem" "$SSL_DIR/"
    
    # 设置权限
    chmod 644 "$SSL_DIR/fullchain.pem"
    chmod 644 "$SSL_DIR/chain.pem"
    chmod 600 "$SSL_DIR/privkey.pem"
    
    log "Let's Encrypt证书获取完成" SUCCESS
}

# 生成自签名证书
generate_self_signed_cert() {
    log "生成自签名证书..." INFO
    
    # 创建私钥
    openssl genrsa -out "$SSL_DIR/privkey.pem" 2048
    
    # 创建证书签名请求
    openssl req -new -key "$SSL_DIR/privkey.pem" -out "$SSL_DIR/cert.csr" -subj "/C=CN/ST=Beijing/L=Beijing/O=SFAP/OU=IT/CN=$DOMAIN"
    
    # 生成自签名证书
    openssl x509 -req -in "$SSL_DIR/cert.csr" -signkey "$SSL_DIR/privkey.pem" -out "$SSL_DIR/fullchain.pem" -days 365 \
        -extensions v3_req -extfile <(cat << EOF
[v3_req]
keyUsage = keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
DNS.1 = $DOMAIN
DNS.2 = www.$DOMAIN
EOF
)
    
    # 复制证书作为链证书
    cp "$SSL_DIR/fullchain.pem" "$SSL_DIR/chain.pem"
    
    # 设置权限
    chmod 644 "$SSL_DIR/fullchain.pem"
    chmod 644 "$SSL_DIR/chain.pem"
    chmod 600 "$SSL_DIR/privkey.pem"
    
    # 清理临时文件
    rm -f "$SSL_DIR/cert.csr"
    
    log "自签名证书生成完成" SUCCESS
    log "注意：自签名证书不被浏览器信任，仅用于测试环境" WARN
}

# 配置SSL安全参数
configure_ssl_security() {
    log "配置SSL安全参数..." INFO
    
    # 创建SSL配置文件
    cat > "$NGINX_DIR/conf.d/ssl-params.conf" << EOF
# SSL安全配置
ssl_protocols TLSv1.2 TLSv1.3;
ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
ssl_prefer_server_ciphers off;
ssl_session_cache shared:SSL:10m;
ssl_session_timeout 10m;
ssl_session_tickets off;

# DH参数
ssl_dhparam $SSL_DIR/dhparam.pem;

# OCSP装订
ssl_stapling on;
ssl_stapling_verify on;
resolver ******* ******* valid=300s;
resolver_timeout 5s;

# 安全头
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
add_header X-Frame-Options DENY always;
add_header X-Content-Type-Options nosniff always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;
EOF
    
    log "SSL安全参数配置完成" SUCCESS
}

# 部署生产Nginx配置
deploy_production_config() {
    log "部署生产Nginx配置..." INFO
    
    # 禁用临时配置
    rm -f "$NGINX_DIR/sites-enabled/temp-ssl"
    
    # 复制生产配置
    cp "/opt/sfap/deploy/nginx-config.conf" "$NGINX_DIR/sites-available/sfap"
    
    # 更新域名
    sed -i "s/sfap.example.com/$DOMAIN/g" "$NGINX_DIR/sites-available/sfap"
    
    # 启用生产配置
    ln -sf "$NGINX_DIR/sites-available/sfap" "$NGINX_DIR/sites-enabled/"
    
    # 测试配置
    nginx -t || error_exit "生产Nginx配置测试失败"
    
    # 重载Nginx
    systemctl reload nginx
    
    log "生产Nginx配置部署完成" SUCCESS
}

# 设置证书自动续期
setup_cert_renewal() {
    log "设置证书自动续期..." INFO
    
    # 创建续期脚本
    cat > "/opt/scripts/renew-ssl.sh" << 'EOF'
#!/bin/bash

# SSL证书续期脚本
LOG_FILE="/var/log/ssl-renewal.log"

log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 续期证书
log "开始证书续期检查..."
certbot renew --quiet --no-self-upgrade

# 检查是否有证书更新
if [ $? -eq 0 ]; then
    log "证书检查完成"
    
    # 复制新证书
    if [ -f "/etc/letsencrypt/live/$DOMAIN/fullchain.pem" ]; then
        cp "/etc/letsencrypt/live/$DOMAIN/fullchain.pem" "/etc/ssl/certs/sfap/"
        cp "/etc/letsencrypt/live/$DOMAIN/privkey.pem" "/etc/ssl/certs/sfap/"
        cp "/etc/letsencrypt/live/$DOMAIN/chain.pem" "/etc/ssl/certs/sfap/"
        
        # 重载Nginx
        systemctl reload nginx
        log "证书已更新，Nginx已重载"
    fi
else
    log "证书续期失败"
fi
EOF
    
    chmod +x "/opt/scripts/renew-ssl.sh"
    
    # 添加到crontab
    (crontab -l 2>/dev/null; echo "0 3 * * * /opt/scripts/renew-ssl.sh") | crontab -
    
    log "证书自动续期设置完成" SUCCESS
}

# 验证SSL配置
verify_ssl_config() {
    log "验证SSL配置..." INFO
    
    # 等待Nginx重载
    sleep 5
    
    # 检查SSL证书
    if openssl s_client -connect "$DOMAIN:443" -servername "$DOMAIN" </dev/null 2>/dev/null | openssl x509 -noout -dates; then
        log "SSL证书验证成功" SUCCESS
    else
        log "SSL证书验证失败" WARN
    fi
    
    # 检查SSL评级（如果可以访问外网）
    if command -v curl &> /dev/null; then
        local ssl_grade=$(curl -s "https://api.ssllabs.com/api/v3/analyze?host=$DOMAIN&publish=off&startNew=on&all=done" | grep -o '"grade":"[^"]*"' | head -1 | cut -d'"' -f4)
        if [ -n "$ssl_grade" ]; then
            log "SSL Labs评级: $ssl_grade" INFO
        fi
    fi
}

# 主函数
main() {
    local cert_type="${1:-letsencrypt}"
    
    log "开始SSL配置，证书类型: $cert_type" INFO
    
    check_root
    create_directories
    generate_dhparam
    
    if [ "$cert_type" = "letsencrypt" ]; then
        install_certbot
        create_temp_nginx_config
        obtain_letsencrypt_cert
        setup_cert_renewal
    elif [ "$cert_type" = "self-signed" ]; then
        generate_self_signed_cert
    else
        error_exit "不支持的证书类型: $cert_type"
    fi
    
    configure_ssl_security
    deploy_production_config
    verify_ssl_config
    
    log "SSL配置完成!" SUCCESS
    log "访问 https://$DOMAIN 验证配置" INFO
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [证书类型]"
    echo ""
    echo "证书类型:"
    echo "  letsencrypt  - 使用Let's Encrypt免费证书（默认）"
    echo "  self-signed  - 生成自签名证书（仅用于测试）"
    echo ""
    echo "环境变量:"
    echo "  DOMAIN  - 域名（默认: sfap.example.com）"
    echo "  EMAIL   - 邮箱地址（默认: <EMAIL>）"
    echo ""
    echo "示例:"
    echo "  DOMAIN=your-domain.com EMAIL=<EMAIL> $0 letsencrypt"
    echo "  $0 self-signed"
}

# 处理命令行参数
case "${1:-}" in
    -h|--help)
        show_help
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
