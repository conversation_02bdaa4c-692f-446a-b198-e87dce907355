# SFAP智慧农业平台完整部署手册

## 📚 目录

- [快速操作触发词](#快速操作触发词)
- [前置准备阶段](#前置准备阶段)
- [代码管理阶段](#代码管理阶段)
- [部署配置阶段](#部署配置阶段)
- [生产部署阶段](#生产部署阶段)
- [标准化模板](#标准化模板)
- [故障排查指南](#故障排查指南)

## 🚀 快速操作触发词

### 推送到Gitee
```bash
# 完整推送流程
git add .
git commit -m "更新：$(date '+%Y-%m-%d %H:%M:%S')"
git push gitee main
```

### 同步双平台
```bash
# 同时推送到GitHub和Gitee
git add .
git commit -m "同步更新：$(date '+%Y-%m-%d %H:%M:%S')"
git push origin main
git push gitee main
```

### 部署生产环境
```bash
# 一键生产环境部署
sudo ./deploy/deploy-all.sh -e production -d your-domain.com -m <EMAIL>
```

### 检查部署状态
```bash
# 验证所有服务状态
docker-compose -f deploy/docker-compose.prod.yml ps
systemctl status sfap
curl -f https://your-domain.com/health
```

## 🔧 前置准备阶段

### 1. 账号注册和配置

#### 1.1 GitHub账号配置
```bash
# 目的：配置GitHub代码托管和CI/CD
# 操作步骤：
1. 访问 https://github.com 注册账号
2. 创建新仓库 "SFAP"
3. 设置仓库为公开或私有
4. 启用GitHub Actions功能

# 验证方法：
# 能够访问仓库页面并看到Actions选项卡
```

#### 1.2 Gitee账号配置
```bash
# 目的：配置国内代码托管平台
# 操作步骤：
1. 访问 https://gitee.com 注册账号
2. 创建新仓库 "sfap"（注意小写）
3. 设置仓库为公开
4. 启用Gitee Go功能

# 验证方法：
# 能够访问仓库页面并看到流水线选项
```

#### 1.3 云服务器配置
```bash
# 目的：准备生产环境服务器
# 推荐配置：
- CPU: 4核心或更多
- 内存: 8GB或更多
- 存储: 50GB SSD
- 操作系统: Ubuntu 22.04 LTS
- 网络: 公网IP + 域名

# 验证方法：
ssh root@your-server-ip
```

### 2. 开发环境搭建

#### 2.1 Git安装和配置
```bash
# Windows系统
# 下载并安装 Git for Windows
# https://git-scm.com/download/win

# Linux系统
sudo apt update
sudo apt install git -y

# 全局配置
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"
git config --global init.defaultBranch main

# 验证安装
git --version
git config --list
```

#### 2.2 SSH密钥生成和配置
```bash
# 目的：实现免密码推送代码
# 生成SSH密钥
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"
# 按回车使用默认路径，设置密码（可选）

# 查看公钥内容
cat ~/.ssh/id_rsa.pub
# 或在Windows上：
type %USERPROFILE%\.ssh\id_rsa.pub

# 配置GitHub SSH密钥
# 1. 复制公钥内容
# 2. 访问 GitHub → Settings → SSH and GPG keys
# 3. 点击 "New SSH key"
# 4. 粘贴公钥内容并保存

# 配置Gitee SSH密钥
# 1. 访问 Gitee → 设置 → SSH公钥
# 2. 粘贴公钥内容并保存

# 验证SSH连接
ssh -T **************
ssh -T *************
# 应该看到欢迎信息
```

#### 2.3 Docker环境安装
```bash
# Ubuntu系统安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 将用户添加到docker组
sudo usermod -aG docker $USER
# 重新登录生效

# 验证安装
docker --version
docker-compose --version
docker run hello-world
```

#### 2.4 Node.js环境安装
```bash
# 使用NodeSource仓库安装Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 验证安装
node --version
npm --version

# 配置npm镜像源（可选）
npm config set registry https://registry.npmmirror.com
```

## 📁 代码管理阶段

### 1. 仓库初始化

#### 1.1 本地仓库初始化
```bash
# 目的：将项目文件夹初始化为Git仓库
cd E:\计算机设计大赛2\V4.0\新建文件夹\新建文件夹\SFAP

# 初始化Git仓库
git init

# 创建.gitignore文件
cat > .gitignore << 'EOF'
# 依赖文件
node_modules/
target/
__pycache__/
*.pyc

# 构建产物
dist/
build/
*.jar

# 日志文件
*.log
logs/

# 环境配置
.env
.env.local
.env.*.local

# IDE配置
.vscode/
.idea/
*.swp
*.swo

# 系统文件
.DS_Store
Thumbs.db

# 临时文件
tmp/
temp/
*.tmp
EOF

# 添加所有文件
git add .

# 首次提交
git commit -m "初始提交：SFAP智慧农业平台项目"

# 验证状态
git status
git log --oneline
```

#### 1.2 远程仓库关联
```bash
# 目的：关联GitHub和Gitee远程仓库
# 添加GitHub远程仓库
git remote <NAME_EMAIL>:Arrbel/SFAP.git

# 添加Gitee远程仓库
git remote <NAME_EMAIL>:arrbel/sfap.git

# 验证远程仓库配置
git remote -v
# 应该看到：
# origin  **************:Arrbel/SFAP.git (fetch)
# origin  **************:Arrbel/SFAP.git (push)
# gitee   *************:arrbel/sfap.git (fetch)
# gitee   *************:arrbel/sfap.git (push)

# 首次推送到GitHub
git push -u origin main

# 首次推送到Gitee
git push -u gitee main

# 验证推送成功
# 访问GitHub和Gitee仓库页面确认文件已上传
```

### 2. 日常Git操作流程

#### 2.1 标准推送流程
```bash
# 目的：将本地更改推送到远程仓库
# 1. 查看文件状态
git status

# 2. 添加更改的文件
git add .                    # 添加所有文件
# 或
git add specific-file.txt    # 添加特定文件

# 3. 提交更改
git commit -m "描述性的提交信息"

# 4. 推送到远程仓库
git push origin main         # 推送到GitHub
git push gitee main         # 推送到Gitee

# 验证推送成功
git log --oneline -5        # 查看最近5次提交
```

#### 2.2 拉取远程更新
```bash
# 目的：获取远程仓库的最新更改
# 拉取GitHub更新
git pull origin main

# 拉取Gitee更新
git pull gitee main

# 如果有冲突，解决后重新提交
git add .
git commit -m "解决合并冲突"
git push origin main
```

#### 2.3 分支管理
```bash
# 创建开发分支
git checkout -b develop

# 切换分支
git checkout main
git checkout develop

# 合并分支
git checkout main
git merge develop

# 删除分支
git branch -d develop
```

## ⚙️ 部署配置阶段

### 1. CI/CD流水线配置

#### 1.1 GitHub Actions配置
```bash
# 目的：配置自动化构建和部署流水线
# 文件位置：.github/workflows/ci-cd.yml
# 配置要点：
1. 触发条件：push到main分支
2. 构建环境：Ubuntu latest
3. 构建步骤：前端、后端、AI服务
4. 部署步骤：Docker镜像构建和推送
5. 通知机制：部署结果通知

# 验证配置
# 1. 推送代码后访问GitHub仓库
# 2. 点击Actions选项卡
# 3. 查看工作流运行状态
```

#### 1.2 Gitee Go配置
```bash
# 目的：配置Gitee平台的CI/CD流水线
# 文件位置：.gitee/workflows/ci-cd.yml
# 配置要点：
1. 与GitHub Actions功能对等
2. 支持国内网络环境
3. 自动化构建和部署

# 验证配置
# 1. 访问Gitee仓库页面
# 2. 点击流水线选项
# 3. 查看流水线运行状态
```

### 2. Docker容器化配置

#### 2.1 Dockerfile配置说明
```bash
# 前端Dockerfile (deploy/frontend-Dockerfile)
# 目的：构建前端应用容器镜像
# 特点：
- 多阶段构建减少镜像大小
- Nginx作为Web服务器
- 支持环境变量配置
- 健康检查机制

# 后端Dockerfile (deploy/backend-Dockerfile)
# 目的：构建后端应用容器镜像
# 特点：
- 基于OpenJDK 17
- Maven多阶段构建
- JVM参数优化
- 非root用户运行

# AI服务Dockerfile (deploy/ai-service-Dockerfile)
# 目的：构建AI服务容器镜像
# 特点：
- Python 3.9环境
- 机器学习依赖优化
- 多进程支持
- 模型文件管理
```

#### 2.2 Docker Compose配置
```bash
# 开发环境 (deploy/docker-compose.yml)
# 目的：本地开发和测试
# 特点：
- 端口映射到宿主机
- 开发模式配置
- 热重载支持

# 生产环境 (deploy/docker-compose.prod.yml)
# 目的：生产环境部署
# 特点：
- 安全配置优化
- 资源限制设置
- 密钥管理
- 监控集成
```

### 3. 服务器环境配置

#### 3.1 基础环境配置
```bash
# 目的：配置生产服务器基础环境
# 脚本：deploy/server-setup.sh
# 包含内容：
1. 系统更新和基础工具安装
2. Docker和Docker Compose安装
3. Node.js、Java、Python环境配置
4. 防火墙和安全配置
5. 用户权限管理

# 执行方法：
sudo ./deploy/server-setup.sh

# 验证配置：
docker --version
node --version
java --version
python3 --version
```

#### 3.2 数据库配置
```bash
# 目的：配置MySQL和Redis数据库
# 脚本：deploy/database-setup.sh
# 包含内容：
1. MySQL 8.0安装和配置
2. Redis安装和配置
3. 数据库用户和权限设置
4. 备份脚本配置
5. 监控脚本配置

# 执行方法：
sudo ./deploy/database-setup.sh

# 验证配置：
mysql -u sfap -p agriculture_mall
redis-cli ping
```

#### 3.3 Nginx和SSL配置
```bash
# 目的：配置Web服务器和HTTPS
# 脚本：deploy/ssl-setup.sh
# 包含内容：
1. Nginx安装和配置
2. Let's Encrypt SSL证书
3. 反向代理配置
4. 安全头设置
5. 自动续期配置

# 执行方法：
sudo DOMAIN=your-domain.com EMAIL=<EMAIL> ./deploy/ssl-setup.sh letsencrypt

# 验证配置：
nginx -t
curl -I https://your-domain.com
```

## 🚀 生产部署阶段

### 1. 一键部署

#### 1.1 完整部署流程
```bash
# 目的：执行完整的生产环境部署
# 脚本：deploy/deploy-all.sh
# 执行命令：
sudo ./deploy/deploy-all.sh -e production -d your-domain.com -m <EMAIL>

# 部署步骤：
1. 系统要求检查
2. 依赖安装
3. 服务器环境配置
4. 数据库配置
5. SSL证书配置
6. 监控系统配置
7. 容器优化
8. 应用部署
9. 健康检查
10. 自动启动配置

# 验证部署：
systemctl status sfap
docker-compose -f deploy/docker-compose.prod.yml ps
curl https://your-domain.com/health
```

#### 1.2 分步部署选项
```bash
# 如果需要分步执行，可以单独运行各个脚本：

# 1. 服务器环境配置
sudo ./deploy/server-setup.sh

# 2. 数据库配置
sudo ./deploy/database-setup.sh

# 3. SSL配置
sudo ./deploy/ssl-setup.sh letsencrypt

# 4. 监控配置
sudo ./deploy/monitoring-setup.sh

# 5. 容器优化
sudo ./deploy/optimize-containers.sh

# 6. 应用启动
docker-compose -f deploy/docker-compose.prod.yml up -d
```

### 2. 监控和维护

#### 2.1 服务状态监控
```bash
# 查看系统服务状态
systemctl status sfap

# 查看容器状态
docker-compose -f deploy/docker-compose.prod.yml ps

# 查看资源使用
docker stats

# 查看日志
docker-compose -f deploy/docker-compose.prod.yml logs -f
```

#### 2.2 监控面板访问
```bash
# Grafana监控面板
# 访问地址：https://your-domain.com:3000
# 默认用户名：admin
# 密码：查看 /root/monitoring_credentials.txt

# Prometheus指标
# 访问地址：https://your-domain.com:9090
```

#### 2.3 备份和恢复
```bash
# 执行备份
sudo /opt/sfap/backup-containers.sh

# 数据库备份
docker exec sfap-mysql-prod mysqldump -u root -p agriculture_mall > backup.sql

# 文件备份
tar -czf uploads_backup.tar.gz /opt/sfap/uploads/
```

### 3. 更新和升级

#### 3.1 应用更新
```bash
# 拉取最新代码
git pull origin main

# 重新构建和部署
docker-compose -f deploy/docker-compose.prod.yml up -d --build

# 验证更新
curl https://your-domain.com/health
```

#### 3.2 系统更新
```bash
# 更新系统包
sudo apt update && sudo apt upgrade -y

# 更新Docker镜像
docker-compose -f deploy/docker-compose.prod.yml pull
docker-compose -f deploy/docker-compose.prod.yml up -d

# 清理旧镜像
docker system prune -f

## 📋 标准化模板

### 1. Git操作命令模板

#### 1.1 日常开发流程模板
```bash
#!/bin/bash
# SFAP项目Git操作标准模板

# 设置项目路径
PROJECT_PATH="E:\计算机设计大赛2\V4.0\新建文件夹\新建文件夹\SFAP"
cd "$PROJECT_PATH"

# 检查当前状态
echo "=== 检查Git状态 ==="
git status

# 添加所有更改
echo "=== 添加文件 ==="
git add .

# 提交更改（带时间戳）
echo "=== 提交更改 ==="
COMMIT_MSG="${1:-更新项目内容}"
git commit -m "$COMMIT_MSG - $(date '+%Y-%m-%d %H:%M:%S')"

# 推送到双平台
echo "=== 推送到GitHub ==="
git push origin main

echo "=== 推送到Gitee ==="
git push gitee main

echo "=== 操作完成 ==="
git log --oneline -3
```

#### 1.2 紧急修复流程模板
```bash
#!/bin/bash
# 紧急修复和热部署模板

# 1. 创建热修复分支
git checkout -b hotfix/$(date +%Y%m%d-%H%M%S)

# 2. 进行修复
echo "请进行必要的代码修复..."
read -p "修复完成后按回车继续..."

# 3. 提交修复
git add .
git commit -m "紧急修复: $1"

# 4. 合并到主分支
git checkout main
git merge hotfix/$(date +%Y%m%d-%H%M%S)

# 5. 推送更新
git push origin main
git push gitee main

# 6. 触发自动部署
echo "代码已推送，CI/CD流水线将自动部署"
```

### 2. 部署脚本配置模板

#### 2.1 环境变量配置模板
```bash
# .env.production 生产环境配置模板
# 复制此文件并根据实际情况修改

# 基本配置
ENVIRONMENT=production
DOMAIN=your-domain.com
EMAIL=<EMAIL>

# 数据库配置
MYSQL_ROOT_PASSWORD=your_secure_mysql_root_password
MYSQL_PASSWORD=your_secure_mysql_password
DB_HOST=mysql
DB_PORT=3306
DB_NAME=agriculture_mall
DB_USER=sfap

# Redis配置
REDIS_PASSWORD=your_secure_redis_password
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0

# Docker配置
DOCKER_REGISTRY=docker.io
DOCKER_NAMESPACE=sfap
IMAGE_TAG=latest

# 监控配置
GRAFANA_PASSWORD=your_secure_grafana_password
PROMETHEUS_RETENTION=200h

# 通知配置
SLACK_WEBHOOK=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
WECHAT_WEBHOOK=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_KEY

# SSL配置
SSL_EMAIL=<EMAIL>
CERTBOT_STAGING=false

# 备份配置
BACKUP_RETENTION_DAYS=7
BACKUP_REMOTE_PATH=s3://your-backup-bucket/sfap/
```

#### 2.2 快速部署脚本模板
```bash
#!/bin/bash
# SFAP快速部署脚本模板

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
    exit 1
}

warn() {
    echo -e "${YELLOW}[WARN] $1${NC}"
}

# 检查环境
check_environment() {
    log "检查部署环境..."

    # 检查Docker
    if ! command -v docker &> /dev/null; then
        error "Docker未安装"
    fi

    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose未安装"
    fi

    # 检查磁盘空间
    available_space=$(df / | awk 'NR==2 {print $4}')
    if [ "$available_space" -lt 5242880 ]; then  # 5GB
        warn "磁盘空间不足5GB，建议清理后再部署"
    fi

    log "环境检查通过"
}

# 部署应用
deploy_application() {
    log "开始部署SFAP应用..."

    # 停止现有服务
    docker-compose -f deploy/docker-compose.prod.yml down --remove-orphans || true

    # 拉取最新镜像
    docker-compose -f deploy/docker-compose.prod.yml pull || warn "镜像拉取失败，将使用本地构建"

    # 启动服务
    docker-compose -f deploy/docker-compose.prod.yml up -d --build

    log "应用部署完成"
}

# 健康检查
health_check() {
    log "执行健康检查..."

    local max_attempts=30
    local attempt=1

    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost/health &>/dev/null; then
            log "健康检查通过"
            return 0
        fi

        warn "健康检查失败，重试 $attempt/$max_attempts"
        sleep 10
        ((attempt++))
    done

    error "健康检查失败，部署可能有问题"
}

# 主函数
main() {
    log "开始SFAP快速部署..."

    check_environment
    deploy_application
    health_check

    log "SFAP部署成功！"
    log "访问地址: https://$(hostname -I | awk '{print $1}')"
}

# 执行主函数
main "$@"
```

### 3. 监控告警配置模板

#### 3.1 Prometheus告警规则模板
```yaml
# prometheus-alerts.yml
groups:
  - name: sfap_alerts
    rules:
      # 服务可用性告警
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "服务 {{ $labels.job }} 不可用"
          description: "服务 {{ $labels.job }} 已经停止响应超过1分钟"

      # CPU使用率告警
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "CPU使用率过高"
          description: "实例 {{ $labels.instance }} CPU使用率超过80%"

      # 内存使用率告警
      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100 > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "内存使用率过高"
          description: "实例 {{ $labels.instance }} 内存使用率超过85%"

      # 磁盘空间告警
      - alert: DiskSpaceLow
        expr: (node_filesystem_avail_bytes{mountpoint="/"} / node_filesystem_size_bytes{mountpoint="/"}) * 100 < 10
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "磁盘空间不足"
          description: "根分区可用空间少于10%"

      # 应用响应时间告警
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "应用响应时间过长"
          description: "95%的请求响应时间超过2秒"
```

#### 3.2 Grafana仪表板配置模板
```json
{
  "dashboard": {
    "title": "SFAP系统监控",
    "panels": [
      {
        "title": "系统概览",
        "type": "stat",
        "targets": [
          {
            "expr": "up",
            "legendFormat": "服务状态"
          }
        ]
      },
      {
        "title": "CPU使用率",
        "type": "graph",
        "targets": [
          {
            "expr": "100 - (avg by(instance) (irate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100)",
            "legendFormat": "CPU使用率 %"
          }
        ]
      },
      {
        "title": "内存使用率",
        "type": "graph",
        "targets": [
          {
            "expr": "(node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100",
            "legendFormat": "内存使用率 %"
          }
        ]
      }
    ]
  }
}
```

## 🔧 故障排查指南

### 1. 常见问题和解决方案

#### 1.1 Git操作问题

**问题：推送到Gitee失败，提示404错误**
```bash
# 原因：仓库名称大小写不匹配
# 解决方案：
git remote -v  # 查看当前配置
git remote set-<NAME_EMAIL>:arrbel/sfap.git  # 注意小写
git push gitee main

# 验证：
ssh -T *************  # 测试SSH连接
```

**问题：SSH密钥认证失败**
```bash
# 原因：SSH密钥未正确配置
# 解决方案：
# 1. 检查SSH密钥是否存在
ls -la ~/.ssh/

# 2. 重新生成SSH密钥
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# 3. 添加到SSH代理
ssh-add ~/.ssh/id_rsa

# 4. 复制公钥到GitHub/Gitee
cat ~/.ssh/id_rsa.pub

# 验证：
ssh -T **************
ssh -T *************
```

**问题：合并冲突**
```bash
# 原因：多人协作时代码冲突
# 解决方案：
# 1. 查看冲突文件
git status

# 2. 手动解决冲突
# 编辑冲突文件，删除冲突标记

# 3. 标记冲突已解决
git add conflicted-file.txt

# 4. 完成合并
git commit -m "解决合并冲突"

# 5. 推送更新
git push origin main
```

#### 1.2 Docker部署问题

**问题：容器启动失败**
```bash
# 诊断步骤：
# 1. 查看容器状态
docker-compose -f deploy/docker-compose.yml ps

# 2. 查看容器日志
docker-compose -f deploy/docker-compose.yml logs container_name

# 3. 检查镜像是否存在
docker images

# 4. 重新构建镜像
docker-compose -f deploy/docker-compose.yml build --no-cache

# 5. 重启服务
docker-compose -f deploy/docker-compose.yml restart container_name
```

**问题：端口冲突**
```bash
# 原因：端口被其他服务占用
# 解决方案：
# 1. 查看端口占用
netstat -tulpn | grep :8080

# 2. 停止占用端口的服务
sudo systemctl stop service_name

# 3. 或修改docker-compose.yml中的端口映射
# ports:
#   - "8081:8080"  # 改为其他端口

# 4. 重新启动服务
docker-compose -f deploy/docker-compose.yml up -d
```

**问题：数据库连接失败**
```bash
# 诊断步骤：
# 1. 检查数据库容器状态
docker-compose -f deploy/docker-compose.yml ps mysql

# 2. 查看数据库日志
docker-compose -f deploy/docker-compose.yml logs mysql

# 3. 测试数据库连接
docker exec -it sfap-mysql mysql -u root -p

# 4. 检查网络连接
docker network ls
docker network inspect sfap_sfap-network

# 5. 重启数据库服务
docker-compose -f deploy/docker-compose.yml restart mysql
```

#### 1.3 SSL证书问题

**问题：Let's Encrypt证书获取失败**
```bash
# 原因：域名解析或网络问题
# 解决方案：
# 1. 检查域名解析
nslookup your-domain.com

# 2. 检查80端口是否开放
curl -I http://your-domain.com

# 3. 手动获取证书
sudo certbot certonly --standalone -d your-domain.com

# 4. 或使用自签名证书（测试环境）
sudo ./deploy/ssl-setup.sh self-signed

# 验证：
sudo certbot certificates
```

**问题：证书过期**
```bash
# 解决方案：
# 1. 手动续期
sudo certbot renew

# 2. 检查自动续期任务
sudo crontab -l | grep certbot

# 3. 重启Nginx
sudo systemctl reload nginx

# 验证：
openssl x509 -in /etc/letsencrypt/live/your-domain.com/cert.pem -text -noout | grep "Not After"
```

### 2. 性能优化建议

#### 2.1 系统性能优化
```bash
# 1. 调整系统参数
echo 'vm.max_map_count=262144' >> /etc/sysctl.conf
echo 'fs.file-max=65536' >> /etc/sysctl.conf
sysctl -p

# 2. 优化Docker配置
# 编辑 /etc/docker/daemon.json
{
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  },
  "storage-driver": "overlay2"
}

# 3. 重启Docker服务
sudo systemctl restart docker
```

#### 2.2 应用性能优化
```bash
# 1. 数据库优化
# 编辑MySQL配置文件
[mysqld]
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
query_cache_size = 64M

# 2. Redis优化
# 编辑Redis配置
maxmemory 512mb
maxmemory-policy allkeys-lru

# 3. Nginx优化
# 编辑Nginx配置
worker_processes auto;
worker_connections 1024;
gzip on;
gzip_comp_level 6;
```

### 3. 监控和日志分析

#### 3.1 日志分析命令
```bash
# 查看应用日志
docker-compose -f deploy/docker-compose.yml logs -f --tail=100

# 查看系统日志
journalctl -u sfap -f

# 查看Nginx访问日志
tail -f /var/log/nginx/access.log

# 查看错误日志
grep -i error /var/log/nginx/error.log

# 分析慢查询
tail -f /var/log/mysql/slow.log
```

#### 3.2 性能监控命令
```bash
# 系统资源监控
htop
iotop
nethogs

# Docker资源监控
docker stats

# 网络连接监控
ss -tulpn
netstat -i

# 磁盘使用监控
df -h
du -sh /opt/sfap/*
```

## 📞 技术支持和联系方式

### 紧急联系方式
- **技术支持邮箱**: <EMAIL>
- **紧急热线**: +86-xxx-xxxx-xxxx
- **在线文档**: https://docs.sfap.com

### 社区支持
- **GitHub Issues**: https://github.com/Arrbel/SFAP/issues
- **技术论坛**: https://forum.sfap.com
- **QQ技术群**: xxxxxxxxx

### 商业支持
- **企业服务**: <EMAIL>
- **定制开发**: <EMAIL>
- **培训服务**: <EMAIL>

---

*最后更新时间：2024年8月*
*版本：v1.0.0*
```
