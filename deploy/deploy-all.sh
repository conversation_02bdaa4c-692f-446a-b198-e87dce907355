#!/bin/bash

# SFAP一键部署脚本
# 完整的生产环境部署流程

set -e

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
ENVIRONMENT="${ENVIRONMENT:-production}"
DOMAIN="${DOMAIN:-sfap.example.com}"
EMAIL="${EMAIL:-<EMAIL>}"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log() {
    local level=${2:-INFO}
    local message="[$(date '+%Y-%m-%d %H:%M:%S')] [$level] $1"
    
    case $level in
        ERROR) echo -e "${RED}$message${NC}" ;;
        WARN)  echo -e "${YELLOW}$message${NC}" ;;
        SUCCESS) echo -e "${GREEN}$message${NC}" ;;
        INFO) echo -e "${BLUE}$message${NC}" ;;
        *) echo "$message" ;;
    esac
}

# 错误处理
error_exit() {
    log "$1" ERROR
    exit 1
}

# 显示横幅
show_banner() {
    echo -e "${GREEN}"
    cat << 'EOF'
   _____ ______      _____  
  / ____|  ____/\   |  __ \ 
 | (___ | |__ /  \  | |__) |
  \___ \|  __/ /\ \ |  ___/ 
  ____) | | / ____ \| |     
 |_____/|_|/_/    \_\_|     
                            
智慧农业平台 - 一键部署脚本
EOF
    echo -e "${NC}"
}

# 检查系统要求
check_requirements() {
    log "检查系统要求..." INFO
    
    # 检查操作系统
    if [[ "$OSTYPE" != "linux-gnu"* ]]; then
        error_exit "仅支持Linux系统"
    fi
    
    # 检查是否为root用户
    if [ "$EUID" -ne 0 ]; then
        error_exit "请使用root用户运行此脚本"
    fi
    
    # 检查系统资源
    local total_mem=$(free -m | awk 'NR==2{print $2}')
    if [ "$total_mem" -lt 4096 ]; then
        log "警告: 系统内存少于4GB，可能影响性能" WARN
    fi
    
    local available_space=$(df / | awk 'NR==2 {print $4}')
    if [ "$available_space" -lt 10485760 ]; then  # 10GB
        error_exit "磁盘空间不足，需要至少10GB可用空间"
    fi
    
    log "系统要求检查通过" SUCCESS
}

# 安装依赖
install_dependencies() {
    log "安装系统依赖..." INFO
    
    # 更新系统
    apt update && apt upgrade -y
    
    # 安装基础工具
    apt install -y curl wget git vim htop tree unzip software-properties-common \
        apt-transport-https ca-certificates gnupg lsb-release
    
    log "系统依赖安装完成" SUCCESS
}

# 配置服务器环境
setup_server() {
    log "配置服务器环境..." INFO
    
    cd "$SCRIPT_DIR"
    
    # 执行服务器配置脚本
    if [ -f "server-setup.sh" ]; then
        chmod +x server-setup.sh
        ./server-setup.sh || error_exit "服务器配置失败"
    else
        error_exit "服务器配置脚本不存在"
    fi
    
    log "服务器环境配置完成" SUCCESS
}

# 配置数据库
setup_database() {
    log "配置数据库..." INFO
    
    cd "$SCRIPT_DIR"
    
    # 执行数据库配置脚本
    if [ -f "database-setup.sh" ]; then
        chmod +x database-setup.sh
        ./database-setup.sh || error_exit "数据库配置失败"
    else
        error_exit "数据库配置脚本不存在"
    fi
    
    log "数据库配置完成" SUCCESS
}

# 配置SSL证书
setup_ssl() {
    log "配置SSL证书..." INFO
    
    cd "$SCRIPT_DIR"
    
    # 执行SSL配置脚本
    if [ -f "ssl-setup.sh" ]; then
        chmod +x ssl-setup.sh
        DOMAIN="$DOMAIN" EMAIL="$EMAIL" ./ssl-setup.sh letsencrypt || {
            log "Let's Encrypt证书获取失败，使用自签名证书" WARN
            DOMAIN="$DOMAIN" ./ssl-setup.sh self-signed
        }
    else
        error_exit "SSL配置脚本不存在"
    fi
    
    log "SSL证书配置完成" SUCCESS
}

# 配置监控
setup_monitoring() {
    log "配置监控系统..." INFO
    
    cd "$SCRIPT_DIR"
    
    # 执行监控配置脚本
    if [ -f "monitoring-setup.sh" ]; then
        chmod +x monitoring-setup.sh
        ./monitoring-setup.sh || error_exit "监控配置失败"
    else
        error_exit "监控配置脚本不存在"
    fi
    
    log "监控系统配置完成" SUCCESS
}

# 优化容器
optimize_containers() {
    log "优化容器配置..." INFO
    
    cd "$SCRIPT_DIR"
    
    # 执行容器优化脚本
    if [ -f "optimize-containers.sh" ]; then
        chmod +x optimize-containers.sh
        ./optimize-containers.sh || error_exit "容器优化失败"
    else
        error_exit "容器优化脚本不存在"
    fi
    
    log "容器优化完成" SUCCESS
}

# 部署应用
deploy_application() {
    log "部署SFAP应用..." INFO
    
    cd "$PROJECT_DIR"
    
    # 选择部署配置
    local compose_file="deploy/docker-compose.yml"
    if [ "$ENVIRONMENT" = "production" ]; then
        compose_file="deploy/docker-compose.prod.yml"
    fi
    
    # 停止现有服务
    docker-compose -f "$compose_file" down --remove-orphans || true
    
    # 拉取最新镜像
    docker-compose -f "$compose_file" pull || log "镜像拉取失败，将使用本地构建" WARN
    
    # 启动服务
    docker-compose -f "$compose_file" up -d --build || error_exit "应用部署失败"
    
    log "SFAP应用部署完成" SUCCESS
}

# 健康检查
health_check() {
    log "执行健康检查..." INFO
    
    local max_attempts=60
    local attempt=1
    local check_interval=10
    
    while [ $attempt -le $max_attempts ]; do
        log "健康检查尝试 $attempt/$max_attempts"
        
        # 检查容器状态
        local unhealthy_containers=$(docker-compose -f deploy/docker-compose.yml ps | grep -v "Up" | grep -v "Name" | wc -l)
        
        if [ "$unhealthy_containers" -eq 0 ]; then
            # 检查HTTP端点
            if curl -f "http://localhost/health" &>/dev/null && \
               curl -f "http://localhost/api/health" &>/dev/null; then
                log "健康检查通过" SUCCESS
                return 0
            fi
        fi
        
        log "健康检查失败，等待 ${check_interval}s 后重试..." WARN
        sleep $check_interval
        ((attempt++))
    done
    
    error_exit "健康检查失败，部署可能有问题"
}

# 配置自动启动
setup_autostart() {
    log "配置自动启动..." INFO
    
    # 创建systemd服务
    cat > /etc/systemd/system/sfap.service << EOF
[Unit]
Description=SFAP Smart Agriculture Platform
Requires=docker.service
After=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=$PROJECT_DIR
ExecStart=/usr/local/bin/docker-compose -f deploy/docker-compose.prod.yml up -d
ExecStop=/usr/local/bin/docker-compose -f deploy/docker-compose.prod.yml down
TimeoutStartSec=0

[Install]
WantedBy=multi-user.target
EOF
    
    # 启用服务
    systemctl daemon-reload
    systemctl enable sfap.service
    
    log "自动启动配置完成" SUCCESS
}

# 生成部署总结
generate_summary() {
    log "生成部署总结..." INFO
    
    cat > /opt/sfap/deployment-summary.txt << EOF
SFAP智慧农业平台部署总结
========================

部署时间: $(date)
部署环境: $ENVIRONMENT
域名: $DOMAIN
邮箱: $EMAIL

服务访问地址:
- 前端应用: https://$DOMAIN
- 后端API: https://$DOMAIN/api
- AI服务: https://$DOMAIN/ai
- Grafana监控: https://$DOMAIN:3000
- Prometheus: https://$DOMAIN:9090

容器状态:
$(docker-compose -f deploy/docker-compose.yml ps)

系统信息:
- 操作系统: $(lsb_release -d | cut -f2)
- 内核版本: $(uname -r)
- Docker版本: $(docker --version)
- Docker Compose版本: $(docker-compose --version)

资源使用:
- CPU: $(nproc) 核心
- 内存: $(free -h | awk 'NR==2{print $2}')
- 磁盘: $(df -h / | awk 'NR==2{print $2}')

重要文件位置:
- 项目目录: $PROJECT_DIR
- 数据目录: /opt/sfap/data
- 日志目录: /opt/sfap/logs
- 配置文件: /opt/sfap/config
- 密钥文件: /opt/sfap/secrets

管理命令:
- 查看服务状态: docker-compose -f deploy/docker-compose.yml ps
- 查看日志: docker-compose -f deploy/docker-compose.yml logs -f
- 重启服务: systemctl restart sfap
- 停止服务: systemctl stop sfap

备份和维护:
- 数据备份脚本: /opt/sfap/backup-containers.sh
- 自动备份: 每天凌晨2点执行
- 日志轮转: 每天执行，保留30天
- 监控告警: 已配置Prometheus + Grafana

部署完成！
EOF
    
    log "部署总结已生成: /opt/sfap/deployment-summary.txt" SUCCESS
}

# 显示完成信息
show_completion() {
    echo -e "${GREEN}"
    cat << EOF

🎉 SFAP智慧农业平台部署完成！

📱 访问地址:
   前端应用: https://$DOMAIN
   管理后台: https://$DOMAIN/admin
   监控面板: https://$DOMAIN:3000

📋 重要信息:
   - 部署总结: /opt/sfap/deployment-summary.txt
   - 数据库凭据: /root/database_credentials.txt
   - 监控凭据: /root/monitoring_credentials.txt

🔧 管理命令:
   - 查看状态: systemctl status sfap
   - 重启服务: systemctl restart sfap
   - 查看日志: docker-compose -f deploy/docker-compose.yml logs -f

📞 技术支持:
   如有问题，请查看日志文件或联系技术支持团队。

EOF
    echo -e "${NC}"
}

# 主函数
main() {
    show_banner
    
    log "开始SFAP一键部署..." INFO
    log "部署环境: $ENVIRONMENT" INFO
    log "域名: $DOMAIN" INFO
    
    check_requirements
    install_dependencies
    setup_server
    setup_database
    setup_ssl
    setup_monitoring
    optimize_containers
    deploy_application
    health_check
    setup_autostart
    generate_summary
    show_completion
    
    log "SFAP部署完成!" SUCCESS
}

# 显示帮助信息
show_help() {
    echo "SFAP智慧农业平台一键部署脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -e, --environment ENV    部署环境 (development|production)"
    echo "  -d, --domain DOMAIN      域名"
    echo "  -m, --email EMAIL        邮箱地址"
    echo "  -h, --help               显示帮助信息"
    echo ""
    echo "环境变量:"
    echo "  ENVIRONMENT    部署环境"
    echo "  DOMAIN         域名"
    echo "  EMAIL          邮箱地址"
    echo ""
    echo "示例:"
    echo "  $0 -e production -d sfap.com -m <EMAIL>"
    echo "  DOMAIN=sfap.com EMAIL=<EMAIL> $0"
}

# 处理命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -d|--domain)
            DOMAIN="$2"
            shift 2
            ;;
        -m|--email)
            EMAIL="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 执行主函数
main "$@"
