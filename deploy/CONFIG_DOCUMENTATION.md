# SFAP自动化部署配置文件说明

## 📋 配置文件概览

本文档详细说明了SFAP自动化部署系统中各个配置文件的作用、参数含义和修改方法。

## 🔧 主要配置文件

### 1. deploy-config.yml - 主配置文件

**文件位置：** `/www/wwwroot/sfap/deploy/deploy-config.yml`

**作用：** 统一管理所有部署相关的配置参数

**主要配置项：**

```yaml
# 项目基本信息
project:
  name: "SFAP智慧农业平台"           # 项目名称
  version: "1.0.0"                    # 项目版本
  repository: "https://gitee.com/your-username/SFAP.git"  # Git仓库地址
  branch: "main"                      # 部署分支

# 服务器配置
server:
  host: "**************"              # 服务器IP地址
  user: "root"                        # SSH用户名
  deploy_path: "/www/wwwroot/sfap"    # 部署根目录
  backup_path: "/www/backup/sfap"     # 备份目录
  log_path: "/www/logs"               # 日志目录

# 服务端口配置
ports:
  frontend: 8200                      # 前端服务端口
  backend: 8081                       # 后端服务端口
  ai_service: 5000                    # AI服务端口
  mysql: 3306                         # MySQL端口
  redis: 6379                         # Redis端口
```

**修改指南：**
- `repository`: 必须修改为你的实际Git仓库地址
- `host`: 修改为你的服务器IP
- `ports`: 根据实际端口配置修改

### 2. auto-deploy.sh - 主部署脚本

**文件位置：** `/www/wwwroot/sfap/deploy/auto-deploy.sh`

**作用：** 执行完整的自动化部署流程

**关键变量：**

```bash
# 项目配置
PROJECT_NAME="SFAP"                                    # 项目名称
REPO_URL="https://gitee.com/your-username/SFAP.git"   # Git仓库地址
BRANCH="main"                                          # 部署分支

# 路径配置
DEPLOY_DIR="/www/wwwroot/sfap"                         # 部署目录
BACKUP_DIR="/www/backup/sfap"                          # 备份目录
LOG_FILE="/www/logs/deploy.log"                        # 日志文件
```

**修改方法：**
```bash
# 使用vim编辑器修改
vim /www/wwwroot/sfap/deploy/auto-deploy.sh

# 修改REPO_URL为你的仓库地址
REPO_URL="https://gitee.com/your-actual-username/SFAP.git"
```

### 3. git-webhook.php - Webhook处理脚本

**文件位置：** `/www/wwwroot/sfap/deploy/git-webhook.php`

**作用：** 接收Git仓库的Webhook请求，触发自动部署

**关键配置：**

```php
$config = [
    'secret' => 'your_webhook_secret_here',    // Webhook密钥
    'branch' => 'main',                        // 监听分支
    'deploy_script' => '/www/wwwroot/sfap/deploy/auto-deploy.sh',  // 部署脚本路径
    'log_file' => '/www/logs/webhook.log',     // Webhook日志
];
```

**安全配置：**
- `secret`: 设置一个复杂的密钥，增强安全性
- `allowed_ips`: 配置允许的IP地址范围

### 4. bt-panel-deploy.sh - 宝塔面板专用脚本

**文件位置：** `/www/wwwroot/sfap/deploy/bt-panel-deploy.sh`

**作用：** 针对宝塔面板环境优化的部署脚本

**环境变量：**

```bash
# 宝塔面板路径
BT_WWW_ROOT="/www/wwwroot"                     # 宝塔网站根目录
BT_BACKUP_ROOT="/www/backup"                   # 宝塔备份目录
BT_LOG_ROOT="/www/logs"                        # 宝塔日志目录

# 项目路径
PROJECT_DIR="$BT_WWW_ROOT/sfap"                # 项目主目录
FRONTEND_DIR="$BT_WWW_ROOT/sfap-frontend"      # 前端部署目录
BACKEND_DIR="$BT_WWW_ROOT/sfap-backend"        # 后端部署目录
AI_SERVICE_DIR="$BT_WWW_ROOT/sfap-ai"          # AI服务目录
```

## 🔐 安全配置说明

### 1. 文件权限设置

```bash
# 脚本文件权限
chmod 755 /www/wwwroot/sfap/deploy/*.sh        # 脚本可执行
chmod 644 /www/wwwroot/sfap/deploy/*.php       # PHP文件可读
chmod 644 /www/wwwroot/sfap/deploy/*.yml       # 配置文件可读

# 目录权限
chown -R www:www /www/wwwroot/sfap             # 设置所有者
chmod -R 755 /www/wwwroot/sfap                 # 设置目录权限
```

### 2. Webhook安全

```php
// 在git-webhook.php中配置
$config = [
    'secret' => 'your_complex_secret_key_here',  // 使用复杂密钥
    'allowed_ips' => [                           // 限制访问IP
        '************/20',    // GitHub IP范围
        '***********/24',     // Gitee IP范围
    ]
];
```

### 3. 数据库安全

```bash
# 数据库备份加密（可选）
mysqldump --single-transaction --routines --triggers \
  agriculture_mall | gzip > backup_$(date +%Y%m%d).sql.gz
```

## 🔄 配置修改流程

### 1. 修改Git仓库地址

```bash
# 步骤1：编辑主配置文件
vim /www/wwwroot/sfap/deploy/deploy-config.yml
# 修改repository字段

# 步骤2：编辑部署脚本
vim /www/wwwroot/sfap/deploy/auto-deploy.sh
# 修改REPO_URL变量

# 步骤3：编辑宝塔专用脚本
vim /www/wwwroot/sfap/deploy/bt-panel-deploy.sh
# 修改REPO_URL变量
```

### 2. 修改服务端口

```bash
# 步骤1：修改配置文件
vim /www/wwwroot/sfap/deploy/deploy-config.yml
# 更新ports部分

# 步骤2：修改Nginx配置
vim /www/server/panel/vhost/nginx/sfap.conf
# 更新listen和proxy_pass端口

# 步骤3：重载Nginx
nginx -t && nginx -s reload
```

### 3. 修改Webhook密钥

```bash
# 步骤1：生成新密钥
openssl rand -hex 32

# 步骤2：更新PHP配置
vim /www/wwwroot/sfap/deploy/git-webhook.php
# 修改secret字段

# 步骤3：在Git仓库中更新Webhook密钥
```

## 📝 配置验证

### 1. 验证配置文件语法

```bash
# 验证YAML语法
python3 -c "import yaml; yaml.safe_load(open('/www/wwwroot/sfap/deploy/deploy-config.yml'))"

# 验证Shell脚本语法
bash -n /www/wwwroot/sfap/deploy/auto-deploy.sh

# 验证PHP语法
php -l /www/wwwroot/sfap/deploy/git-webhook.php
```

### 2. 验证权限设置

```bash
# 检查文件权限
ls -la /www/wwwroot/sfap/deploy/

# 检查目录所有者
ls -ld /www/wwwroot/sfap*
```

### 3. 验证网络连通性

```bash
# 测试Git仓库连接
git ls-remote https://gitee.com/your-username/SFAP.git

# 测试Webhook端点
curl -X POST http://**************:8200/webhook.php
```

## 🔧 常用配置模板

### 1. 开发环境配置

```yaml
environment:
  NODE_ENV: "development"
  SPRING_PROFILES_ACTIVE: "dev"
  MYSQL_DATABASE: "agriculture_mall_dev"
```

### 2. 生产环境配置

```yaml
environment:
  NODE_ENV: "production"
  SPRING_PROFILES_ACTIVE: "prod"
  MYSQL_DATABASE: "agriculture_mall"
```

### 3. 多分支部署配置

```bash
# 在auto-deploy.sh中添加分支判断
case "$BRANCH" in
  "main")
    DEPLOY_ENV="production"
    ;;
  "develop")
    DEPLOY_ENV="staging"
    ;;
  *)
    echo "Unknown branch: $BRANCH"
    exit 1
    ;;
esac
```

## 📚 相关文档

- [实施步骤指南](./STEP_BY_STEP_GUIDE.md)
- [故障排除指南](./TROUBLESHOOTING.md)
- [维护指南](./MAINTENANCE_GUIDE.md)
- [安全配置指南](./SECURITY_GUIDE.md)
