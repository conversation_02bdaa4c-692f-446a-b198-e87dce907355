#!/bin/bash

# SFAP生产服务器环境配置脚本
# 适用于Ubuntu 20.04/22.04 LTS

set -e

# 配置变量
SERVER_USER="sfap"
PROJECT_DIR="/opt/sfap"
NGINX_CONF_DIR="/etc/nginx"
SSL_CERT_DIR="/etc/ssl/certs/sfap"
LOG_DIR="/var/log/sfap"
BACKUP_DIR="/opt/backup/sfap"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log() {
    local level=${2:-INFO}
    local message="[$(date '+%Y-%m-%d %H:%M:%S')] [$level] $1"
    
    case $level in
        ERROR) echo -e "${RED}$message${NC}" ;;
        WARN)  echo -e "${YELLOW}$message${NC}" ;;
        SUCCESS) echo -e "${GREEN}$message${NC}" ;;
        INFO) echo -e "${BLUE}$message${NC}" ;;
        *) echo "$message" ;;
    esac
}

# 错误处理
error_exit() {
    log "$1" ERROR
    exit 1
}

# 检查是否为root用户
check_root() {
    if [ "$EUID" -ne 0 ]; then
        error_exit "请使用root用户运行此脚本"
    fi
}

# 更新系统
update_system() {
    log "更新系统包..." INFO
    
    apt update || error_exit "系统更新失败"
    apt upgrade -y || error_exit "系统升级失败"
    
    # 安装基础工具
    apt install -y curl wget git vim htop tree unzip software-properties-common \
        apt-transport-https ca-certificates gnupg lsb-release || error_exit "基础工具安装失败"
    
    log "系统更新完成" SUCCESS
}

# 配置防火墙
configure_firewall() {
    log "配置防火墙..." INFO
    
    # 安装ufw
    apt install -y ufw || error_exit "UFW安装失败"
    
    # 配置防火墙规则
    ufw --force reset
    ufw default deny incoming
    ufw default allow outgoing
    
    # 允许SSH
    ufw allow ssh
    ufw allow 22/tcp
    
    # 允许HTTP和HTTPS
    ufw allow 80/tcp
    ufw allow 443/tcp
    
    # 允许应用端口
    ufw allow 8080/tcp  # 后端API
    ufw allow 5000/tcp  # AI服务
    ufw allow 3306/tcp  # MySQL (仅内网)
    ufw allow 6379/tcp  # Redis (仅内网)
    
    # 启用防火墙
    ufw --force enable
    
    log "防火墙配置完成" SUCCESS
}

# 创建应用用户
create_app_user() {
    log "创建应用用户..." INFO
    
    # 创建用户
    if ! id "$SERVER_USER" &>/dev/null; then
        useradd -m -s /bin/bash "$SERVER_USER" || error_exit "用户创建失败"
        usermod -aG sudo "$SERVER_USER" || error_exit "用户权限设置失败"
        
        # 设置SSH密钥（如果提供）
        if [ -n "$SSH_PUBLIC_KEY" ]; then
            mkdir -p "/home/<USER>/.ssh"
            echo "$SSH_PUBLIC_KEY" > "/home/<USER>/.ssh/authorized_keys"
            chmod 700 "/home/<USER>/.ssh"
            chmod 600 "/home/<USER>/.ssh/authorized_keys"
            chown -R "$SERVER_USER:$SERVER_USER" "/home/<USER>/.ssh"
        fi
        
        log "用户 $SERVER_USER 创建成功" SUCCESS
    else
        log "用户 $SERVER_USER 已存在" INFO
    fi
}

# 安装Docker
install_docker() {
    log "安装Docker..." INFO
    
    # 卸载旧版本
    apt remove -y docker docker-engine docker.io containerd runc || true
    
    # 添加Docker官方GPG密钥
    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
    
    # 添加Docker仓库
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null
    
    # 安装Docker
    apt update
    apt install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin || error_exit "Docker安装失败"
    
    # 启动Docker服务
    systemctl start docker
    systemctl enable docker
    
    # 将用户添加到docker组
    usermod -aG docker "$SERVER_USER"
    
    # 安装docker-compose
    curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    chmod +x /usr/local/bin/docker-compose
    
    log "Docker安装完成" SUCCESS
}

# 安装Node.js
install_nodejs() {
    log "安装Node.js..." INFO
    
    # 使用NodeSource仓库安装Node.js 18
    curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
    apt install -y nodejs || error_exit "Node.js安装失败"
    
    # 安装全局包
    npm install -g pm2 yarn || error_exit "全局包安装失败"
    
    log "Node.js安装完成" SUCCESS
}

# 安装Java
install_java() {
    log "安装Java..." INFO
    
    # 安装OpenJDK 17
    apt install -y openjdk-17-jdk || error_exit "Java安装失败"
    
    # 设置JAVA_HOME
    echo 'export JAVA_HOME=/usr/lib/jvm/java-17-openjdk-amd64' >> /etc/environment
    echo 'export PATH=$PATH:$JAVA_HOME/bin' >> /etc/environment
    
    log "Java安装完成" SUCCESS
}

# 安装Python
install_python() {
    log "安装Python..." INFO
    
    # 安装Python 3.9和相关工具
    apt install -y python3.9 python3.9-dev python3.9-venv python3-pip || error_exit "Python安装失败"
    
    # 创建软链接
    ln -sf /usr/bin/python3.9 /usr/bin/python3
    ln -sf /usr/bin/python3 /usr/bin/python
    
    # 升级pip
    python3 -m pip install --upgrade pip
    
    log "Python安装完成" SUCCESS
}

# 安装Nginx
install_nginx() {
    log "安装Nginx..." INFO
    
    apt install -y nginx || error_exit "Nginx安装失败"
    
    # 启动Nginx服务
    systemctl start nginx
    systemctl enable nginx
    
    # 创建配置目录
    mkdir -p "$NGINX_CONF_DIR/sites-available"
    mkdir -p "$NGINX_CONF_DIR/sites-enabled"
    mkdir -p "$SSL_CERT_DIR"
    
    log "Nginx安装完成" SUCCESS
}

# 安装MySQL
install_mysql() {
    log "安装MySQL..." INFO
    
    # 设置MySQL root密码
    local mysql_root_password="${MYSQL_ROOT_PASSWORD:-$(openssl rand -base64 32)}"
    
    # 预配置MySQL
    echo "mysql-server mysql-server/root_password password $mysql_root_password" | debconf-set-selections
    echo "mysql-server mysql-server/root_password_again password $mysql_root_password" | debconf-set-selections
    
    # 安装MySQL
    apt install -y mysql-server || error_exit "MySQL安装失败"
    
    # 启动MySQL服务
    systemctl start mysql
    systemctl enable mysql
    
    # 保存密码到文件
    echo "MySQL root password: $mysql_root_password" > /root/mysql_credentials.txt
    chmod 600 /root/mysql_credentials.txt
    
    log "MySQL安装完成，密码已保存到 /root/mysql_credentials.txt" SUCCESS
}

# 安装Redis
install_redis() {
    log "安装Redis..." INFO
    
    apt install -y redis-server || error_exit "Redis安装失败"
    
    # 配置Redis
    sed -i 's/^# maxmemory <bytes>/maxmemory 256mb/' /etc/redis/redis.conf
    sed -i 's/^# maxmemory-policy noeviction/maxmemory-policy allkeys-lru/' /etc/redis/redis.conf
    
    # 启动Redis服务
    systemctl start redis-server
    systemctl enable redis-server
    
    log "Redis安装完成" SUCCESS
}

# 创建项目目录
create_directories() {
    log "创建项目目录..." INFO
    
    # 创建主要目录
    mkdir -p "$PROJECT_DIR"
    mkdir -p "$LOG_DIR"
    mkdir -p "$BACKUP_DIR"
    mkdir -p "/opt/scripts"
    
    # 设置权限
    chown -R "$SERVER_USER:$SERVER_USER" "$PROJECT_DIR"
    chown -R "$SERVER_USER:$SERVER_USER" "$LOG_DIR"
    chown -R "$SERVER_USER:$SERVER_USER" "$BACKUP_DIR"
    
    log "项目目录创建完成" SUCCESS
}

# 配置系统服务
configure_services() {
    log "配置系统服务..." INFO
    
    # 配置logrotate
    cat > /etc/logrotate.d/sfap << EOF
$LOG_DIR/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 $SERVER_USER $SERVER_USER
    postrotate
        systemctl reload nginx > /dev/null 2>&1 || true
    endscript
}
EOF
    
    # 配置crontab备份任务
    (crontab -l 2>/dev/null; echo "0 2 * * * /opt/scripts/backup.sh") | crontab -
    
    log "系统服务配置完成" SUCCESS
}

# 安全加固
security_hardening() {
    log "执行安全加固..." INFO
    
    # 禁用root SSH登录
    sed -i 's/#PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config
    sed -i 's/PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config
    
    # 禁用密码认证（如果有SSH密钥）
    if [ -n "$SSH_PUBLIC_KEY" ]; then
        sed -i 's/#PasswordAuthentication yes/PasswordAuthentication no/' /etc/ssh/sshd_config
        sed -i 's/PasswordAuthentication yes/PasswordAuthentication no/' /etc/ssh/sshd_config
    fi
    
    # 重启SSH服务
    systemctl restart ssh
    
    # 安装fail2ban
    apt install -y fail2ban || error_exit "fail2ban安装失败"
    
    # 配置fail2ban
    cat > /etc/fail2ban/jail.local << EOF
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 3

[sshd]
enabled = true
port = ssh
filter = sshd
logpath = /var/log/auth.log

[nginx-http-auth]
enabled = true
filter = nginx-http-auth
port = http,https
logpath = /var/log/nginx/error.log
EOF
    
    systemctl start fail2ban
    systemctl enable fail2ban
    
    log "安全加固完成" SUCCESS
}

# 主函数
main() {
    log "开始配置SFAP生产服务器环境..." INFO
    
    check_root
    update_system
    configure_firewall
    create_app_user
    install_docker
    install_nodejs
    install_java
    install_python
    install_nginx
    install_mysql
    install_redis
    create_directories
    configure_services
    security_hardening
    
    log "SFAP生产服务器环境配置完成!" SUCCESS
    log "请重新登录以使用户组更改生效" INFO
}

# 执行主函数
main "$@"
