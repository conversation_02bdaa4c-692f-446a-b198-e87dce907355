# SFAP生产环境Docker Compose配置
# 优化的生产环境配置，包含安全性和性能优化

version: '3.8'

services:
  # Nginx反向代理（生产环境）
  nginx:
    image: nginx:1.24-alpine
    container_name: sfap-nginx-prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.prod.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./ssl:/etc/ssl/certs:ro
      - nginx_logs:/var/log/nginx
      - /etc/letsencrypt:/etc/letsencrypt:ro
    depends_on:
      - frontend
      - backend
      - ai-service
    networks:
      - sfap-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 3s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 64M
          cpus: '0.1'

  # 前端服务（生产环境）
  frontend:
    build:
      context: .
      dockerfile: deploy/frontend-Dockerfile
      args:
        - NODE_ENV=production
        - BUILD_ENV=production
    container_name: sfap-frontend-prod
    expose:
      - "80"
    environment:
      - API_BASE_URL=https://api.sfap.com
      - AI_SERVICE_URL=https://ai.sfap.com
      - NODE_ENV=production
    depends_on:
      - backend
    networks:
      - sfap-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 3s
      retries: 3
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 128M
          cpus: '0.25'
        reservations:
          memory: 64M
          cpus: '0.1'

  # 后端服务（生产环境）
  backend:
    build:
      context: ./backend
      dockerfile: ../deploy/backend-Dockerfile
      args:
        - SPRING_PROFILES_ACTIVE=prod
        - BUILD_ENV=production
    container_name: sfap-backend-prod
    expose:
      - "8080"
    environment:
      # Spring配置
      - SPRING_PROFILES_ACTIVE=prod
      - SERVER_PORT=8080
      - MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE=health,info,metrics
      - MANAGEMENT_ENDPOINT_HEALTH_SHOW_DETAILS=when_authorized
      
      # 数据库配置
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_NAME=agriculture_mall
      - DB_USER=sfap
      - DB_PASSWORD_FILE=/run/secrets/mysql_password
      - SPRING_DATASOURCE_URL=*****************************************************************************************************************************************************
      - SPRING_DATASOURCE_USERNAME=sfap
      - SPRING_DATASOURCE_PASSWORD_FILE=/run/secrets/mysql_password
      
      # Redis配置
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD_FILE=/run/secrets/redis_password
      - SPRING_REDIS_HOST=redis
      - SPRING_REDIS_PORT=6379
      - SPRING_REDIS_PASSWORD_FILE=/run/secrets/redis_password
      
      # 安全配置
      - SPRING_SECURITY_REQUIRE_SSL=true
      - SERVER_SSL_ENABLED=false  # SSL在Nginx层处理
      
      # 性能配置
      - SPRING_DATASOURCE_HIKARI_MAXIMUM_POOL_SIZE=30
      - SPRING_DATASOURCE_HIKARI_MINIMUM_IDLE=10
      - SPRING_JPA_PROPERTIES_HIBERNATE_JDBC_BATCH_SIZE=25
      - SPRING_JPA_PROPERTIES_HIBERNATE_ORDER_INSERTS=true
      - SPRING_JPA_PROPERTIES_HIBERNATE_ORDER_UPDATES=true
      
      # 日志配置
      - LOGGING_LEVEL_ROOT=WARN
      - LOGGING_LEVEL_COM_AGRICULTURE=INFO
      - LOGGING_FILE_PATH=/app/logs
      
      # JVM配置
      - JAVA_OPTS=-Xms1g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/app/logs/ -XX:+UseStringDeduplication
      
    volumes:
      - backend_logs:/app/logs
      - backend_uploads:/app/uploads
      - backend_temp:/app/temp
    secrets:
      - mysql_password
      - redis_password
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - sfap-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 120s
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 5G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '0.5'

  # AI服务（生产环境）
  ai-service:
    build:
      context: ./ai-service
      dockerfile: ../deploy/ai-service-Dockerfile
      args:
        - ENVIRONMENT=production
    container_name: sfap-ai-service-prod
    expose:
      - "5000"
    environment:
      - FLASK_ENV=production
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD_FILE=/run/secrets/redis_password
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_NAME=agriculture_mall
      - DB_USER=sfap
      - DB_PASSWORD_FILE=/run/secrets/mysql_password
      - MODEL_PATH=/app/saved_models
      - CACHE_DIR=/app/cache
      - LOG_LEVEL=WARNING
      - WORKERS=4
      - TIMEOUT=300
    volumes:
      - ai_logs:/app/logs
      - ai_data:/app/data:ro
      - ai_models:/app/saved_models:ro
      - ai_cache:/app/cache
    secrets:
      - mysql_password
      - redis_password
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - sfap-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 15s
      retries: 5
      start_period: 120s
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 6G
          cpus: '3.0'
        reservations:
          memory: 2G
          cpus: '1.0'

  # MySQL数据库（生产环境）
  mysql:
    image: mysql:8.0.34
    container_name: sfap-mysql-prod
    expose:
      - "3306"
    environment:
      - MYSQL_ROOT_PASSWORD_FILE=/run/secrets/mysql_root_password
      - MYSQL_DATABASE=agriculture_mall
      - MYSQL_USER=sfap
      - MYSQL_PASSWORD_FILE=/run/secrets/mysql_password
      - MYSQL_CHARACTER_SET_SERVER=utf8mb4
      - MYSQL_COLLATION_SERVER=utf8mb4_unicode_ci
      - TZ=Asia/Shanghai
    volumes:
      - mysql_data:/var/lib/mysql
      - mysql_config:/etc/mysql/conf.d
      - mysql_logs:/var/log/mysql
      - ./database:/docker-entrypoint-initdb.d:ro
      - ./mysql/my.prod.cnf:/etc/mysql/conf.d/custom.cnf:ro
    secrets:
      - mysql_root_password
      - mysql_password
    command: >
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --max_connections=500
      --innodb_buffer_pool_size=2G
      --innodb_log_file_size=256M
      --innodb_flush_log_at_trx_commit=2
      --slow_query_log=1
      --long_query_time=1
      --log_bin=/var/log/mysql/mysql-bin.log
      --binlog_format=ROW
      --expire_logs_days=7
      --sql-mode="STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO"
    networks:
      - sfap-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "sfap", "-p$$(cat /run/secrets/mysql_password)"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 3G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '0.5'

  # Redis缓存（生产环境）
  redis:
    image: redis:7.0-alpine
    container_name: sfap-redis-prod
    expose:
      - "6379"
    environment:
      - TZ=Asia/Shanghai
    command: >
      sh -c "redis-server
      --requirepass $$(cat /run/secrets/redis_password)
      --appendonly yes
      --appendfsync everysec
      --maxmemory 1gb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
      --tcp-keepalive 60
      --timeout 300"
    volumes:
      - redis_data:/data
    secrets:
      - redis_password
    networks:
      - sfap-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 3s
      retries: 5
      start_period: 10s
    deploy:
      resources:
        limits:
          memory: 1.5G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.25'

# 密钥配置
secrets:
  mysql_root_password:
    file: ./secrets/mysql_root_password.txt
  mysql_password:
    file: ./secrets/mysql_password.txt
  redis_password:
    file: ./secrets/redis_password.txt

# 网络配置
networks:
  sfap-network:
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: **********/16
          gateway: **********

# 数据卷配置
volumes:
  mysql_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/sfap/data/mysql
  mysql_config:
    driver: local
  mysql_logs:
    driver: local
  redis_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/sfap/data/redis
  backend_logs:
    driver: local
  backend_uploads:
    driver: local
  backend_temp:
    driver: local
  ai_logs:
    driver: local
  ai_data:
    driver: local
  ai_models:
    driver: local
  ai_cache:
    driver: local
  nginx_logs:
    driver: local
