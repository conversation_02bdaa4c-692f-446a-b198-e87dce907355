# SFAP自动化部署故障排除指南

## 🚨 常见问题及解决方案

本文档收录了SFAP自动化部署过程中可能遇到的问题及其解决方案。

## 📋 问题分类

### 1. 环境配置问题
### 2. Git仓库问题
### 3. 部署脚本问题
### 4. Webhook问题
### 5. 服务启动问题
### 6. 权限问题

---

## 🔧 环境配置问题

### 问题1：软件版本不兼容

**症状：**
```bash
Error: Node.js version 14.x is not supported
Error: Java version 11 found, but 17 required
```

**原因：** 服务器上安装的软件版本与项目要求不匹配

**解决方案：**
```bash
# 检查当前版本
node --version
java -version
python3 --version

# 在宝塔面板中安装正确版本
# 软件商店 → 搜索对应软件 → 安装指定版本

# 或使用命令行安装
# Node.js 16
curl -fsSL https://rpm.nodesource.com/setup_16.x | sudo bash -
sudo yum install -y nodejs

# Java 17
sudo yum install -y java-17-openjdk java-17-openjdk-devel
```

**验证方法：**
```bash
node --version    # 应显示 v16.x.x
java -version     # 应显示 17.x.x
python3 --version # 应显示 3.8+
```

### 问题2：端口被占用

**症状：**
```bash
Error: Port 8081 is already in use
Error: Address already in use
```

**诊断方法：**
```bash
# 检查端口占用
netstat -tlnp | grep :8081
lsof -i :8081

# 查看进程详情
ps aux | grep java
ps aux | grep node
```

**解决方案：**
```bash
# 方案1：停止占用端口的进程
sudo kill -9 <PID>

# 方案2：修改配置使用其他端口
vim /www/wwwroot/sfap/deploy/deploy-config.yml
# 修改ports配置

# 方案3：重启相关服务
systemctl restart sfap-backend
systemctl restart sfap-ai
```

---

## 📦 Git仓库问题

### 问题3：Git仓库连接失败

**症状：**
```bash
fatal: unable to access 'https://gitee.com/username/SFAP.git/': 
Could not resolve host: gitee.com
```

**诊断方法：**
```bash
# 测试网络连接
ping gitee.com
curl -I https://gitee.com

# 测试Git连接
git ls-remote https://gitee.com/your-username/SFAP.git
```

**解决方案：**
```bash
# 方案1：检查网络配置
# 确保服务器可以访问外网

# 方案2：使用SSH方式（推荐）
# 生成SSH密钥
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# 添加公钥到Gitee
cat ~/.ssh/id_rsa.pub
# 复制内容到Gitee → 设置 → SSH公钥

# 修改仓库地址为SSH格式
vim /www/wwwroot/sfap/deploy/auto-deploy.sh
# REPO_URL="*************:your-username/SFAP.git"

# 方案3：配置Git代理（如果需要）
git config --global http.proxy http://proxy-server:port
```

### 问题4：Git权限问题

**症状：**
```bash
Permission denied (publickey).
fatal: Could not read from remote repository.
```

**解决方案：**
```bash
# 检查SSH密钥
ssh -T *************

# 如果失败，重新配置SSH
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"
ssh-add ~/.ssh/id_rsa

# 测试连接
ssh -T *************
# 应显示：Hi username! You've successfully authenticated
```

---

## 🔄 部署脚本问题

### 问题5：脚本权限不足

**症状：**
```bash
bash: ./auto-deploy.sh: Permission denied
```

**解决方案：**
```bash
# 设置执行权限
chmod +x /www/wwwroot/sfap/deploy/*.sh

# 检查权限
ls -la /www/wwwroot/sfap/deploy/

# 设置正确的所有者
chown -R www:www /www/wwwroot/sfap
```

### 问题6：构建失败

**症状：**
```bash
npm ERR! code ELIFECYCLE
npm ERR! errno 1
npm ERR! smart-farmer-assistance-platform@1.0.0 build: `vue-cli-service build`
```

**诊断方法：**
```bash
# 检查Node.js和npm版本
node --version
npm --version

# 手动测试构建
cd /www/wwwroot/sfap
npm install
npm run build
```

**解决方案：**
```bash
# 方案1：清理缓存重新安装
rm -rf node_modules package-lock.json
npm cache clean --force
npm install

# 方案2：使用yarn替代npm
npm install -g yarn
yarn install
yarn build

# 方案3：修复内存不足问题
export NODE_OPTIONS="--max-old-space-size=4096"
npm run build
```

---

## 🌐 Webhook问题

### 问题7：Webhook不触发

**症状：** 代码推送后没有自动部署

**诊断方法：**
```bash
# 检查Webhook日志
tail -f /www/logs/webhook.log

# 检查Nginx配置
nginx -t
curl -X POST http://**************:8200/webhook.php

# 检查PHP-FPM状态
systemctl status php-fpm
```

**解决方案：**
```bash
# 方案1：检查Nginx配置
vim /www/server/panel/vhost/nginx/sfap.conf
# 确保包含webhook.php的location配置

# 方案2：检查PHP配置
php -l /www/wwwroot/sfap/deploy/git-webhook.php

# 方案3：手动测试Webhook
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"ref":"refs/heads/main","commits":[{"message":"test"}]}' \
  http://**************:8200/webhook.php
```

### 问题8：Webhook签名验证失败

**症状：**
```bash
[ERROR] GitHub签名验证失败
[ERROR] Invalid signature
```

**解决方案：**
```bash
# 检查密钥配置
vim /www/wwwroot/sfap/deploy/git-webhook.php
# 确保secret与Git仓库中配置的一致

# 在Gitee中重新配置Webhook
# 仓库设置 → Webhooks → 编辑 → 更新密钥
```

---

## 🚀 服务启动问题

### 问题9：后端服务启动失败

**症状：**
```bash
Error: Could not find or load main class com.agriculture.AgricultureMallApplication
```

**诊断方法：**
```bash
# 检查jar文件
ls -la /www/wwwroot/sfap-backend/
file /www/wwwroot/sfap-backend/app.jar

# 检查Java版本
java -version

# 手动启动测试
cd /www/wwwroot/sfap-backend
java -jar app.jar
```

**解决方案：**
```bash
# 方案1：重新构建jar文件
cd /www/wwwroot/sfap/backend/main
mvn clean package -DskipTests

# 方案2：检查配置文件
vim /www/wwwroot/sfap-backend/application.yml
# 确保数据库连接等配置正确

# 方案3：使用systemd管理服务
systemctl start sfap-backend
systemctl status sfap-backend
journalctl -u sfap-backend -f
```

### 问题10：数据库连接失败

**症状：**
```bash
java.sql.SQLException: Access denied for user 'root'@'localhost'
```

**解决方案：**
```bash
# 检查MySQL服务状态
systemctl status mysql

# 检查数据库连接
mysql -u root -p -e "SHOW DATABASES;"

# 重置数据库权限
mysql -u root -p
> GRANT ALL PRIVILEGES ON agriculture_mall.* TO 'root'@'localhost';
> FLUSH PRIVILEGES;

# 检查配置文件中的数据库配置
vim /www/wwwroot/sfap-backend/application.yml
```

---

## 🔐 权限问题

### 问题11：文件权限错误

**症状：**
```bash
Permission denied: '/www/wwwroot/sfap-frontend/index.html'
```

**解决方案：**
```bash
# 设置正确的文件权限
chown -R www:www /www/wwwroot/sfap*
chmod -R 755 /www/wwwroot/sfap*

# 设置特殊权限
chmod +x /www/wwwroot/sfap/deploy/*.sh
chmod 644 /www/wwwroot/sfap/deploy/*.php
```

---

## 🔍 诊断工具和命令

### 系统状态检查

```bash
# 检查系统资源
df -h                    # 磁盘空间
free -h                  # 内存使用
top                      # CPU使用

# 检查服务状态
systemctl status nginx
systemctl status mysql
systemctl status redis

# 检查端口监听
netstat -tlnp | grep -E ':(8081|8200|5000|3306|6379)'
```

### 日志查看

```bash
# 部署相关日志
tail -f /www/logs/sfap-deploy.log
tail -f /www/logs/webhook.log

# 应用日志
tail -f /www/wwwroot/sfap-backend/backend.log
tail -f /www/wwwroot/sfap-ai/ai-service.log

# 系统日志
tail -f /var/log/nginx/error.log
journalctl -u sfap-backend -f
```

### 网络连接测试

```bash
# 测试服务连通性
curl http://localhost:8200
curl http://localhost:8081/api/health
curl http://localhost:5000/health

# 测试外部连接
curl -I https://gitee.com
ping *******
```

## 🆘 紧急回滚程序

如果部署出现严重问题，可以使用以下命令快速回滚：

```bash
# 自动回滚到上一个版本
cd /www/wwwroot/sfap/deploy
./bt-panel-deploy.sh --rollback

# 手动回滚
LATEST_BACKUP=$(ls -t /www/backup/sfap/ | head -n1)
cp -r /www/backup/sfap/$LATEST_BACKUP/* /www/wwwroot/sfap-frontend/
systemctl restart sfap-backend
systemctl restart sfap-ai
```

## 📞 获取帮助

如果以上解决方案都无法解决问题，请：

1. 收集详细的错误日志
2. 记录问题复现步骤
3. 检查系统环境信息
4. 查看相关配置文件

**日志收集命令：**
```bash
# 创建问题报告
echo "=== 系统信息 ===" > problem_report.txt
uname -a >> problem_report.txt
echo "=== 软件版本 ===" >> problem_report.txt
node --version >> problem_report.txt
java -version >> problem_report.txt
echo "=== 错误日志 ===" >> problem_report.txt
tail -50 /www/logs/sfap-deploy.log >> problem_report.txt
```
