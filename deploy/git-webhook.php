<?php
/**
 * Git Webhook自动部署脚本
 * 适用于GitHub/Gitee等Git托管平台的Webhook
 */

// 配置
$config = [
    'secret' => getenv('WEBHOOK_SECRET') ?: 'your_webhook_secret_here', // Webhook密钥
    'branch' => 'main', // 监听的分支
    'deploy_script' => '/opt/sfap/deploy/auto-deploy.sh', // 部署脚本路径
    'log_file' => '/var/log/sfap-webhook.log', // 日志文件
    'max_log_size' => 10 * 1024 * 1024, // 最大日志文件大小 (10MB)
    'backup_count' => 5, // 保留的日志备份数量
    'allowed_ips' => [ // 允许的IP地址（GitHub/Gitee的Webhook IP）
        '************/20',
        '*************/22',
        '************/22',
        '***********/24',
        '***********/24',
        '127.0.0.1', // 本地测试
        '::1' // IPv6本地
    ]
];

// 日志函数
function writeLog($message) {
    global $config;
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[{$timestamp}] {$message}\n";
    file_put_contents($config['log_file'], $logMessage, FILE_APPEND | LOCK_EX);
}

// 验证IP地址
function isValidIP($ip) {
    global $config;
    
    foreach ($config['allowed_ips'] as $allowedRange) {
        if (strpos($allowedRange, '/') !== false) {
            // CIDR格式
            list($subnet, $mask) = explode('/', $allowedRange);
            if ((ip2long($ip) & ~((1 << (32 - $mask)) - 1)) == ip2long($subnet)) {
                return true;
            }
        } else {
            // 单个IP
            if ($ip === $allowedRange) {
                return true;
            }
        }
    }
    return false;
}

// 验证签名（GitHub）
function verifyGitHubSignature($payload, $signature) {
    global $config;
    $expectedSignature = 'sha1=' . hash_hmac('sha1', $payload, $config['secret']);
    return hash_equals($expectedSignature, $signature);
}

// 验证签名（Gitee）
function verifyGiteeSignature($payload, $signature) {
    global $config;
    $expectedSignature = hash_hmac('sha256', $payload, $config['secret']);
    return hash_equals($expectedSignature, $signature);
}

// 主处理逻辑
function handleWebhook() {
    global $config;
    
    // 获取客户端IP
    $clientIP = $_SERVER['HTTP_X_FORWARDED_FOR'] ?? $_SERVER['REMOTE_ADDR'];
    writeLog("收到来自 {$clientIP} 的Webhook请求");
    
    // 验证IP地址（可选，如果使用CDN可能需要调整）
    if (!isValidIP($clientIP)) {
        writeLog("IP地址验证失败: {$clientIP}");
        http_response_code(403);
        echo json_encode(['error' => 'Forbidden']);
        return;
    }
    
    // 获取请求体
    $payload = file_get_contents('php://input');
    if (empty($payload)) {
        writeLog("空的请求体");
        http_response_code(400);
        echo json_encode(['error' => 'Empty payload']);
        return;
    }
    
    // 验证签名
    $signature = $_SERVER['HTTP_X_HUB_SIGNATURE'] ?? $_SERVER['HTTP_X_GITEE_SIGNATURE'] ?? '';
    if (!empty($config['secret'])) {
        $isGitHub = isset($_SERVER['HTTP_X_HUB_SIGNATURE']);
        $isGitee = isset($_SERVER['HTTP_X_GITEE_SIGNATURE']);
        
        if ($isGitHub && !verifyGitHubSignature($payload, $signature)) {
            writeLog("GitHub签名验证失败");
            http_response_code(403);
            echo json_encode(['error' => 'Invalid signature']);
            return;
        }
        
        if ($isGitee && !verifyGiteeSignature($payload, $signature)) {
            writeLog("Gitee签名验证失败");
            http_response_code(403);
            echo json_encode(['error' => 'Invalid signature']);
            return;
        }
    }
    
    // 解析JSON
    $data = json_decode($payload, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        writeLog("JSON解析失败: " . json_last_error_msg());
        http_response_code(400);
        echo json_encode(['error' => 'Invalid JSON']);
        return;
    }
    
    // 检查分支
    $ref = $data['ref'] ?? '';
    $targetBranch = "refs/heads/{$config['branch']}";
    
    if ($ref !== $targetBranch) {
        writeLog("分支不匹配: {$ref} != {$targetBranch}");
        echo json_encode(['message' => 'Branch not matched, skipping deployment']);
        return;
    }
    
    // 获取提交信息
    $commits = $data['commits'] ?? [];
    $commitCount = count($commits);
    $lastCommit = end($commits);
    $commitMessage = $lastCommit['message'] ?? 'Unknown';
    $author = $lastCommit['author']['name'] ?? 'Unknown';
    
    writeLog("触发部署: {$commitCount} 个提交，最新提交: {$commitMessage} by {$author}");
    
    // 检查是否包含跳过部署的标记
    if (strpos($commitMessage, '[skip deploy]') !== false || 
        strpos($commitMessage, '[deploy skip]') !== false) {
        writeLog("检测到跳过部署标记，取消部署");
        echo json_encode(['message' => 'Deployment skipped due to commit message']);
        return;
    }
    
    // 执行部署脚本
    if (!file_exists($config['deploy_script'])) {
        writeLog("部署脚本不存在: {$config['deploy_script']}");
        http_response_code(500);
        echo json_encode(['error' => 'Deploy script not found']);
        return;
    }
    
    // 异步执行部署脚本
    $command = "nohup bash {$config['deploy_script']} > /dev/null 2>&1 &";
    exec($command, $output, $returnCode);
    
    if ($returnCode === 0) {
        writeLog("部署脚本启动成功");
        echo json_encode([
            'message' => 'Deployment started successfully',
            'commit' => $commitMessage,
            'author' => $author,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    } else {
        writeLog("部署脚本启动失败，返回码: {$returnCode}");
        http_response_code(500);
        echo json_encode(['error' => 'Failed to start deployment script']);
    }
}

// 设置响应头
header('Content-Type: application/json');

// 只处理POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

try {
    handleWebhook();
} catch (Exception $e) {
    writeLog("处理Webhook时发生异常: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}
?>
