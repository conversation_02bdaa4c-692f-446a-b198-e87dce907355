# SFAP智慧农业平台自动化部署方案

## 🎯 解决方案概述

针对你提到的传统部署痛点，我们提供了一套完整的自动化部署解决方案，彻底解决手动打包上传的繁琐流程。

### 🔥 核心优势

1. **一键部署** - 代码提交后自动触发部署，无需手动操作
2. **快速迭代** - 支持热更新，生产环境问题可快速修复
3. **零停机部署** - 滚动更新策略，用户无感知
4. **自动回滚** - 部署失败自动回滚到上一个稳定版本
5. **完整监控** - 部署状态、服务健康状况实时监控

## 📊 技术栈分析

基于你的项目结构分析：

**前端技术栈：**
- Vue.js 2.6 + Element UI + ECharts
- 构建工具：Vue CLI + Webpack
- 部署端口：8200

**后端技术栈：**
- Spring Boot 2.7 + Java 17
- MyBatis Plus + MySQL + Redis
- 部署端口：8081

**AI服务：**
- Python Flask + 机器学习模型
- 部署端口：5000

## 🚀 推荐部署方案

### 方案一：Git Hooks + 自动化脚本（推荐）

**适用场景：** 中小型项目，团队规模较小，追求简单高效

**优势：**
- 配置简单，维护成本低
- 与宝塔面板完美兼容
- 支持多环境部署
- 部署速度快

**架构图：**
```
开发者提交代码 → Git仓库 → Webhook触发 → 自动部署脚本 → 宝塔面板服务
```

### 方案二：Docker + CI/CD（进阶方案）

**适用场景：** 大型项目，多环境管理，团队协作

**优势：**
- 环境一致性好
- 扩展性强
- 支持容器编排
- 更好的资源隔离

## 📋 实施步骤

### 第一步：准备Git仓库

1. **创建代码仓库**（推荐使用Gitee，国内访问速度快）
```bash
# 在Gitee上创建仓库，然后推送代码
git remote add origin https://gitee.com/your-username/SFAP.git
git push -u origin main
```

2. **配置仓库Webhook**
- 进入Gitee仓库设置 → Webhooks
- 添加Webhook URL：`http://**************/webhook.php`
- 选择推送事件触发
- 设置密钥（可选但推荐）

### 第二步：服务器环境配置

1. **上传部署脚本到服务器**
```bash
# 在宝塔面板文件管理中创建目录
mkdir -p /www/wwwroot/sfap/deploy

# 上传以下文件：
- auto-deploy.sh          # 主部署脚本
- bt-panel-deploy.sh      # 宝塔面板专用脚本
- git-webhook.php         # Webhook处理脚本
- deploy-config.yml       # 部署配置文件
```

2. **设置脚本权限**
```bash
chmod +x /www/wwwroot/sfap/deploy/*.sh
```

3. **配置Nginx**
在宝塔面板中添加网站，配置Webhook处理：
```nginx
# 添加到网站配置中
location /webhook.php {
    root /www/wwwroot/sfap/deploy;
    fastcgi_pass unix:/tmp/php-cgi-74.sock;
    fastcgi_index index.php;
    include fastcgi.conf;
}
```

### 第三步：首次部署

1. **手动执行首次部署**
```bash
cd /www/wwwroot/sfap/deploy
./bt-panel-deploy.sh
```

2. **验证部署结果**
- 前端访问：http://**************:8200
- 后端API：http://**************:8081
- 查看日志：`tail -f /www/logs/sfap-deploy.log`

### 第四步：配置自动化

1. **修改配置文件**
编辑 `deploy-config.yml`，更新你的具体配置：
```yaml
project:
  repository: "https://gitee.com/your-username/SFAP.git"
  
server:
  host: "**************"
  
notifications:
  webhook_url: "your_dingtalk_webhook_url"  # 钉钉通知（可选）
```

2. **测试自动部署**
```bash
# 提交一个测试更改
git add .
git commit -m "test: 测试自动部署功能"
git push origin main

# 观察部署日志
tail -f /www/logs/webhook.log
tail -f /www/logs/sfap-deploy.log
```

## 🔧 高级配置

### 多环境部署

支持开发、测试、生产环境的独立部署：

```bash
# 部署到测试环境
./bt-panel-deploy.sh --env=staging

# 部署到生产环境
./bt-panel-deploy.sh --env=production
```

### 蓝绿部署

零停机部署策略：

```bash
# 启用蓝绿部署
./bt-panel-deploy.sh --strategy=blue-green
```

### 自动回滚

部署失败时自动回滚：

```bash
# 手动回滚到上一个版本
./bt-panel-deploy.sh --rollback

# 回滚到指定版本
./bt-panel-deploy.sh --rollback --version=v1.2.3
```

## 📈 监控和通知

### 部署状态监控

1. **实时日志查看**
```bash
# 部署日志
tail -f /www/logs/sfap-deploy.log

# 应用日志
tail -f /www/wwwroot/sfap-backend/backend.log
tail -f /www/wwwroot/sfap-ai/ai-service.log
```

2. **健康检查**
```bash
# 检查所有服务状态
curl http://localhost:8200/health
curl http://localhost:8081/api/health
curl http://localhost:5000/health
```

### 通知集成

支持多种通知方式：

1. **钉钉通知**
```bash
# 在deploy-config.yml中配置钉钉Webhook
notifications:
  webhook_url: "https://oapi.dingtalk.com/robot/send?access_token=YOUR_TOKEN"
```

2. **邮件通知**
```bash
# 配置SMTP邮件通知
notifications:
  email:
    enabled: true
    smtp_host: "smtp.qq.com"
    recipients: ["<EMAIL>"]
```

## 🛠️ 故障排除

### 常见问题

1. **部署脚本权限问题**
```bash
chmod +x /www/wwwroot/sfap/deploy/*.sh
chown -R www:www /www/wwwroot/sfap
```

2. **Node.js版本问题**
```bash
# 在宝塔面板中安装Node.js 16+
# 或使用nvm管理版本
nvm install 16
nvm use 16
```

3. **Java版本问题**
```bash
# 确保使用Java 17
java -version
# 在宝塔面板中配置正确的Java路径
```

4. **端口冲突**
```bash
# 检查端口占用
netstat -tlnp | grep :8081
# 修改配置文件中的端口设置
```

### 回滚操作

如果部署出现问题，可以快速回滚：

```bash
# 自动回滚到上一个版本
./bt-panel-deploy.sh --rollback

# 查看可用的备份版本
ls -la /www/backup/sfap/

# 手动回滚到指定备份
cp -r /www/backup/sfap/backup_20240101_120000/* /www/wwwroot/sfap-frontend/
```

## 📝 最佳实践

1. **代码提交规范**
```bash
# 使用语义化提交信息
git commit -m "feat: 添加新功能"
git commit -m "fix: 修复登录问题"
git commit -m "docs: 更新文档"

# 跳过部署的提交
git commit -m "docs: 更新README [skip deploy]"
```

2. **分支管理**
```bash
# 主分支用于生产环境
main/master → 生产环境自动部署

# 开发分支用于测试环境
develop → 测试环境自动部署

# 功能分支不自动部署
feature/* → 手动部署或不部署
```

3. **数据库迁移**
```bash
# 在部署前备份数据库
mysqldump -u root -p agriculture_mall > backup_$(date +%Y%m%d).sql

# 自动执行数据库迁移
./bt-panel-deploy.sh --migrate
```

## 🎯 效果对比

### 传统部署方式
- ⏱️ 部署时间：30-60分钟
- 🔄 出错率：高（人工操作易出错）
- 📈 迭代速度：慢（每次都要完整流程）
- 🛠️ 维护成本：高

### 自动化部署方式
- ⏱️ 部署时间：3-5分钟
- 🔄 出错率：低（自动化流程）
- 📈 迭代速度：快（一键部署）
- 🛠️ 维护成本：低

## 📞 技术支持

如果在实施过程中遇到问题，可以：

1. 查看详细日志文件
2. 检查服务器环境配置
3. 验证Git仓库配置
4. 测试网络连接

这套方案将彻底解决你的部署痛点，实现真正的一键部署和快速迭代！

---

## 🔧 高级部署配置

### Docker容器化部署

项目已完全支持Docker容器化部署，提供以下配置文件：

#### 核心配置文件
- `deploy/frontend-Dockerfile` - 前端多阶段构建配置
- `deploy/backend-Dockerfile` - 后端Java应用容器配置
- `deploy/ai-service-Dockerfile` - AI服务Python容器配置
- `deploy/docker-compose.yml` - 开发环境编排配置
- `deploy/docker-compose.prod.yml` - 生产环境编排配置

#### 一键部署脚本
```bash
# 完整的生产环境部署
sudo ./deploy/deploy-all.sh -e production -d your-domain.com -m <EMAIL>

# 仅优化容器配置
sudo ./deploy/optimize-containers.sh

# 配置服务器环境
sudo ./deploy/server-setup.sh

# 配置数据库和监控
sudo ./deploy/database-setup.sh
sudo ./deploy/monitoring-setup.sh

# 配置SSL证书
sudo ./deploy/ssl-setup.sh letsencrypt
```

#### 监控和维护
- **Prometheus + Grafana** 完整监控解决方案
- **自动备份** 数据库和文件系统备份策略
- **健康检查** 多层次应用健康监控
- **日志管理** 集中化日志收集和分析

#### 安全配置
- **SSL/TLS** Let's Encrypt自动证书管理
- **防火墙** UFW自动配置和端口管理
- **密钥管理** Docker Secrets安全存储
- **用户权限** 非root用户运行容器

### CI/CD自动化流水线

#### GitHub Actions
- 自动测试、构建和部署
- 多环境支持（开发/测试/生产）
- Docker镜像自动构建和推送
- 部署失败自动回滚

#### Gitee Go
- 国内代码托管平台支持
- 自动化构建和部署流程
- 与现有工作流无缝集成

### 性能优化
- **镜像优化** 多阶段构建减少镜像大小
- **网络优化** 自定义Docker网络配置
- **资源限制** 容器资源使用限制和监控
- **缓存策略** Redis缓存和静态资源缓存

*最后更新时间：2024年8月*
