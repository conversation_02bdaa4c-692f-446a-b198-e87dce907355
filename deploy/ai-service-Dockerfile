# SFAP AI服务多阶段构建Dockerfile
# 基于Python 3.9优化构建，支持机器学习和深度学习

# 构建阶段
FROM python:3.9-slim AS builder

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# 安装构建依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    make \
    cmake \
    build-essential \
    libffi-dev \
    libssl-dev \
    python3-dev \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 设置pip镜像源
RUN pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple

# 复制requirements文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --user --no-cache-dir -r requirements.txt

# 生产阶段
FROM python:3.9-slim AS production

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PATH=/home/<USER>/.local/bin:$PATH \
    TZ=Asia/Shanghai

# 安装运行时依赖
RUN apt-get update && apt-get install -y \
    curl \
    netcat \
    tzdata \
    libgomp1 \
    && rm -rf /var/lib/apt/lists/* \
    && ln -snf /usr/share/zoneinfo/$TZ /etc/localtime \
    && echo $TZ > /etc/timezone

# 创建应用用户
RUN groupadd -r appgroup && \
    useradd -r -g appgroup -d /home/<USER>

# 设置工作目录
WORKDIR /app

# 从构建阶段复制Python包
COPY --from=builder --chown=appuser:appgroup /root/.local /home/<USER>/.local

# 复制应用代码
COPY --chown=appuser:appgroup . .

# 创建必要目录
RUN mkdir -p logs data temp saved_models cache \
    && chown -R appuser:appgroup /app

# 创建启动脚本
RUN cat > /app/start.sh << 'EOF'
#!/bin/bash

# 等待Redis连接（如果使用）
if [ -n "$REDIS_HOST" ]; then
    echo "Waiting for Redis connection..."
    while ! nc -z ${REDIS_HOST} ${REDIS_PORT:-6379}; do
        echo "Waiting for Redis..."
        sleep 2
    done
    echo "Redis is ready!"
fi

# 等待数据库连接（如果使用）
if [ -n "$DB_HOST" ]; then
    echo "Waiting for database connection..."
    while ! nc -z ${DB_HOST} ${DB_PORT:-3306}; do
        echo "Waiting for database..."
        sleep 2
    done
    echo "Database is ready!"
fi

# 设置Python路径
export PYTHONPATH=/app:$PYTHONPATH

# 启动应用
echo "Starting SFAP AI Service..."
if [ -f "app.py" ]; then
    exec python app.py
elif [ -f "main.py" ]; then
    exec python main.py
elif [ -f "server.py" ]; then
    exec python server.py
else
    echo "No main application file found!"
    exit 1
fi
EOF

RUN chmod +x /app/start.sh && chown appuser:appgroup /app/start.sh

# 切换到应用用户
USER appuser

# 暴露端口
EXPOSE 5000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:5000/health || curl -f http://localhost:5000/ || exit 1

# 设置标签
LABEL maintainer="SFAP Team" \
      version="1.0" \
      description="SFAP AI Service Application"

# 启动命令
ENTRYPOINT ["/app/start.sh"]
