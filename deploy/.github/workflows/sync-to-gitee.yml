name: Sync to G<PERSON><PERSON>

on:
  push:
    branches: [ main, develop ]
  workflow_dispatch:

jobs:
  sync:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout
      uses: actions/checkout@v3
      with:
        fetch-depth: 0
    
    - name: Sync to <PERSON><PERSON><PERSON>
      uses: wearerequired/git-mirror-action@master
      env:
        SSH_PRIVATE_KEY: ${{ secrets.GITEE_SSH_PRIVATE_KEY }}
      with:
        source-repo: **************:${{ github.repository }}.git
        destination-repo: *************:your-username/SFAP.git

    - name: Build Gitee Pages
      uses: yanglbme/gitee-pages-action@main
      with:
        gitee-username: your-gitee-username
        gitee-password: ${{ secrets.GITEE_PASSWORD }}
        gitee-repo: your-username/SFAP
        branch: main
