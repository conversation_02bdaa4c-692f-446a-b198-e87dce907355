# SFAP自动化部署配置文件

# 项目基本信息
project:
  name: "SFAP智慧农业平台"
  version: "1.0.0"
  repository: "https://github.com/your-username/SFAP.git"
  branch: "main"

# 服务器配置
server:
  host: "**************"
  user: "root"
  deploy_path: "/www/wwwroot/sfap"
  backup_path: "/www/backup/sfap"
  log_path: "/www/logs"

# 服务端口配置
ports:
  frontend: 8200
  backend: 8081
  ai_service: 5000
  crawler_service: 5001
  mysql: 3306
  redis: 6379
  nginx: 80

# 构建配置
build:
  frontend:
    node_version: "16"
    build_command: "npm run build"
    output_dir: "dist"
    
  backend:
    java_version: "17"
    build_command: "mvn clean package -DskipTests"
    output_file: "target/agriculture-mall-1.0.0.jar"
    
  ai_service:
    python_version: "3.9"
    requirements_file: "requirements.txt"
    main_file: "app.py"

# 部署策略
deployment:
  strategy: "rolling" # rolling, blue-green, recreate
  max_unavailable: 1
  health_check:
    enabled: true
    timeout: 30
    retries: 3
    endpoints:
      - "http://localhost:8200"
      - "http://localhost:8081/api/health"
      - "http://localhost:5000/health"

# 备份策略
backup:
  enabled: true
  retention_days: 7
  max_backups: 5
  exclude_patterns:
    - "node_modules"
    - "*.log"
    - "venv"
    - ".git"

# 通知配置
notifications:
  enabled: true
  webhook_url: "" # 钉钉、企业微信等Webhook地址
  email:
    enabled: false
    smtp_host: ""
    smtp_port: 587
    username: ""
    password: ""
    recipients: []

# 环境变量
environment:
  production:
    NODE_ENV: "production"
    SPRING_PROFILES_ACTIVE: "prod"
    MYSQL_HOST: "localhost"
    MYSQL_PORT: "3306"
    MYSQL_DATABASE: "agriculture_mall"
    REDIS_HOST: "localhost"
    REDIS_PORT: "6379"
    
  staging:
    NODE_ENV: "staging"
    SPRING_PROFILES_ACTIVE: "staging"
    MYSQL_HOST: "localhost"
    MYSQL_PORT: "3306"
    MYSQL_DATABASE: "agriculture_mall_staging"
    REDIS_HOST: "localhost"
    REDIS_PORT: "6379"

# 监控配置
monitoring:
  enabled: true
  metrics:
    - cpu_usage
    - memory_usage
    - disk_usage
    - response_time
  alerts:
    cpu_threshold: 80
    memory_threshold: 85
    disk_threshold: 90
    response_time_threshold: 5000

# 安全配置
security:
  webhook_secret: "your_webhook_secret_here"
  allowed_ips:
    - "************/20"  # GitHub
    - "*************/22" # GitHub
    - "***********/24"   # Gitee
    - "***********/24"   # Gitee
  ssl:
    enabled: false
    cert_path: ""
    key_path: ""

# 数据库配置
database:
  backup:
    enabled: true
    schedule: "0 2 * * *" # 每天凌晨2点备份
    retention_days: 30
  migration:
    auto_migrate: false
    migration_path: "database/migrations"

# 缓存配置
cache:
  redis:
    enabled: true
    clear_on_deploy: false
  file_cache:
    enabled: true
    clear_on_deploy: true

# 日志配置
logging:
  level: "INFO"
  max_size: "100MB"
  max_files: 10
  compress: true
  
# 性能优化
optimization:
  frontend:
    gzip: true
    cache_control: "public, max-age=31536000"
    minify: true
  backend:
    jvm_options: "-Xms512m -Xmx2g -XX:+UseG1GC"
  nginx:
    worker_processes: "auto"
    worker_connections: 1024
    gzip: true
    gzip_types: "text/plain text/css application/json application/javascript text/xml application/xml"
