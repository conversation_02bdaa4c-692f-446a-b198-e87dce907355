#!/bin/bash

# SFAP智慧农业平台快速操作脚本
# 提供常用操作的快速执行功能

set -e

# 项目配置
PROJECT_PATH="E:\计算机设计大赛2\V4.0\新建文件夹\新建文件夹\SFAP"
GITHUB_REPO="**************:Arrbel/SFAP.git"
GITEE_REPO="*************:arrbel/sfap.git"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
    exit 1
}

warn() {
    echo -e "${YELLOW}[WARN] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

# 显示帮助信息
show_help() {
    echo "SFAP智慧农业平台快速操作脚本"
    echo ""
    echo "用法: $0 [操作]"
    echo ""
    echo "可用操作:"
    echo "  push-gitee          推送到Gitee"
    echo "  sync-dual           同步双平台"
    echo "  deploy-prod         部署生产环境"
    echo "  check-status        检查部署状态"
    echo "  backup-data         备份数据"
    echo "  update-app          更新应用"
    echo "  restart-services    重启服务"
    echo "  view-logs           查看日志"
    echo "  health-check        健康检查"
    echo "  cleanup             清理资源"
    echo ""
    echo "示例:"
    echo "  $0 push-gitee"
    echo "  $0 sync-dual"
    echo "  $0 deploy-prod"
}

# 推送到Gitee
push_to_gitee() {
    log "开始推送到Gitee..."
    
    cd "$PROJECT_PATH" || error "无法进入项目目录"
    
    # 检查Git状态
    if ! git status &>/dev/null; then
        error "当前目录不是Git仓库"
    fi
    
    # 添加所有更改
    info "添加文件更改..."
    git add .
    
    # 检查是否有更改
    if git diff --cached --quiet; then
        warn "没有文件更改，跳过提交"
        return 0
    fi
    
    # 提交更改
    local commit_msg="更新项目：$(date '+%Y-%m-%d %H:%M:%S')"
    info "提交更改: $commit_msg"
    git commit -m "$commit_msg"
    
    # 推送到Gitee
    info "推送到Gitee..."
    git push gitee main || error "推送到Gitee失败"
    
    log "推送到Gitee完成"
}

# 同步双平台
sync_dual_platform() {
    log "开始同步双平台..."
    
    cd "$PROJECT_PATH" || error "无法进入项目目录"
    
    # 添加所有更改
    info "添加文件更改..."
    git add .
    
    # 检查是否有更改
    if git diff --cached --quiet; then
        warn "没有文件更改，跳过提交"
        return 0
    fi
    
    # 提交更改
    local commit_msg="同步更新：$(date '+%Y-%m-%d %H:%M:%S')"
    info "提交更改: $commit_msg"
    git commit -m "$commit_msg"
    
    # 推送到GitHub
    info "推送到GitHub..."
    git push origin main || warn "推送到GitHub失败"
    
    # 推送到Gitee
    info "推送到Gitee..."
    git push gitee main || warn "推送到Gitee失败"
    
    log "双平台同步完成"
}

# 部署生产环境
deploy_production() {
    log "开始部署生产环境..."
    
    cd "$PROJECT_PATH" || error "无法进入项目目录"
    
    # 检查部署脚本是否存在
    if [ ! -f "deploy/deploy-all.sh" ]; then
        error "部署脚本不存在: deploy/deploy-all.sh"
    fi
    
    # 设置执行权限
    chmod +x deploy/deploy-all.sh
    
    # 获取域名和邮箱
    local domain="${DOMAIN:-sfap.example.com}"
    local email="${EMAIL:-<EMAIL>}"
    
    info "域名: $domain"
    info "邮箱: $email"
    
    # 执行部署
    sudo ./deploy/deploy-all.sh -e production -d "$domain" -m "$email" || error "生产环境部署失败"
    
    log "生产环境部署完成"
}

# 检查部署状态
check_deployment_status() {
    log "检查部署状态..."
    
    cd "$PROJECT_PATH" || error "无法进入项目目录"
    
    # 检查系统服务状态
    info "检查系统服务状态..."
    if systemctl is-active --quiet sfap; then
        echo -e "${GREEN}✓ SFAP系统服务运行正常${NC}"
    else
        echo -e "${RED}✗ SFAP系统服务未运行${NC}"
    fi
    
    # 检查Docker容器状态
    info "检查Docker容器状态..."
    if [ -f "deploy/docker-compose.prod.yml" ]; then
        docker-compose -f deploy/docker-compose.prod.yml ps
    else
        docker-compose -f deploy/docker-compose.yml ps
    fi
    
    # 检查健康端点
    info "检查应用健康状态..."
    local health_checks=(
        "http://localhost/health"
        "http://localhost/api/health"
        "http://localhost/ai/health"
    )
    
    for endpoint in "${health_checks[@]}"; do
        if curl -f "$endpoint" &>/dev/null; then
            echo -e "${GREEN}✓ $endpoint 正常${NC}"
        else
            echo -e "${RED}✗ $endpoint 异常${NC}"
        fi
    done
    
    log "部署状态检查完成"
}

# 备份数据
backup_data() {
    log "开始备份数据..."
    
    cd "$PROJECT_PATH" || error "无法进入项目目录"
    
    # 检查备份脚本
    if [ -f "/opt/sfap/backup-containers.sh" ]; then
        info "执行容器备份..."
        sudo /opt/sfap/backup-containers.sh
    else
        warn "容器备份脚本不存在，执行手动备份..."
        
        # 手动备份数据库
        info "备份数据库..."
        local backup_dir="/tmp/sfap_backup_$(date +%Y%m%d_%H%M%S)"
        mkdir -p "$backup_dir"
        
        # MySQL备份
        if docker ps | grep -q mysql; then
            docker exec sfap-mysql-prod mysqldump -u root -p agriculture_mall > "$backup_dir/mysql_backup.sql" 2>/dev/null || warn "MySQL备份失败"
        fi
        
        # Redis备份
        if docker ps | grep -q redis; then
            docker exec sfap-redis-prod redis-cli --rdb "$backup_dir/redis_backup.rdb" 2>/dev/null || warn "Redis备份失败"
        fi
        
        # 文件备份
        if [ -d "/opt/sfap/uploads" ]; then
            tar -czf "$backup_dir/uploads_backup.tar.gz" -C /opt/sfap uploads/ || warn "文件备份失败"
        fi
        
        info "备份文件保存在: $backup_dir"
    fi
    
    log "数据备份完成"
}

# 更新应用
update_application() {
    log "开始更新应用..."
    
    cd "$PROJECT_PATH" || error "无法进入项目目录"
    
    # 拉取最新代码
    info "拉取最新代码..."
    git pull origin main || warn "拉取GitHub代码失败"
    git pull gitee main || warn "拉取Gitee代码失败"
    
    # 重新构建和部署
    info "重新构建和部署..."
    if [ -f "deploy/docker-compose.prod.yml" ]; then
        docker-compose -f deploy/docker-compose.prod.yml up -d --build
    else
        docker-compose -f deploy/docker-compose.yml up -d --build
    fi
    
    # 等待服务启动
    info "等待服务启动..."
    sleep 30
    
    # 验证更新
    info "验证更新..."
    if curl -f http://localhost/health &>/dev/null; then
        log "应用更新成功"
    else
        error "应用更新失败，请检查日志"
    fi
}

# 重启服务
restart_services() {
    log "开始重启服务..."
    
    cd "$PROJECT_PATH" || error "无法进入项目目录"
    
    # 重启Docker服务
    info "重启Docker容器..."
    if [ -f "deploy/docker-compose.prod.yml" ]; then
        docker-compose -f deploy/docker-compose.prod.yml restart
    else
        docker-compose -f deploy/docker-compose.yml restart
    fi
    
    # 重启系统服务
    info "重启系统服务..."
    if systemctl is-enabled --quiet sfap; then
        sudo systemctl restart sfap
    fi
    
    # 等待服务启动
    info "等待服务启动..."
    sleep 30
    
    log "服务重启完成"
}

# 查看日志
view_logs() {
    log "查看应用日志..."
    
    cd "$PROJECT_PATH" || error "无法进入项目目录"
    
    echo "选择要查看的日志:"
    echo "1) 所有服务日志"
    echo "2) 前端日志"
    echo "3) 后端日志"
    echo "4) AI服务日志"
    echo "5) 数据库日志"
    echo "6) Nginx日志"
    echo "7) 系统日志"
    
    read -p "请选择 (1-7): " choice
    
    case $choice in
        1)
            if [ -f "deploy/docker-compose.prod.yml" ]; then
                docker-compose -f deploy/docker-compose.prod.yml logs -f --tail=100
            else
                docker-compose -f deploy/docker-compose.yml logs -f --tail=100
            fi
            ;;
        2)
            docker-compose logs -f frontend --tail=100
            ;;
        3)
            docker-compose logs -f backend --tail=100
            ;;
        4)
            docker-compose logs -f ai-service --tail=100
            ;;
        5)
            docker-compose logs -f mysql --tail=100
            ;;
        6)
            if [ -f "/var/log/nginx/access.log" ]; then
                tail -f /var/log/nginx/access.log
            else
                docker-compose logs -f nginx --tail=100
            fi
            ;;
        7)
            journalctl -u sfap -f
            ;;
        *)
            warn "无效选择"
            ;;
    esac
}

# 健康检查
health_check() {
    log "执行健康检查..."
    
    local checks_passed=0
    local total_checks=0
    
    # 检查系统资源
    info "检查系统资源..."
    ((total_checks++))
    
    local cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}')
    local mem_usage=$(free | awk 'NR==2{printf "%.1f", $3*100/$2}')
    local disk_usage=$(df / | awk 'NR==2{print $5}' | sed 's/%//')
    
    echo "CPU使用率: ${cpu_usage}%"
    echo "内存使用率: ${mem_usage}%"
    echo "磁盘使用率: ${disk_usage}%"
    
    if [ "${cpu_usage%.*}" -lt 80 ] && [ "${mem_usage%.*}" -lt 85 ] && [ "$disk_usage" -lt 90 ]; then
        echo -e "${GREEN}✓ 系统资源正常${NC}"
        ((checks_passed++))
    else
        echo -e "${RED}✗ 系统资源异常${NC}"
    fi
    
    # 检查服务状态
    info "检查服务状态..."
    ((total_checks++))
    
    if systemctl is-active --quiet sfap; then
        echo -e "${GREEN}✓ SFAP服务正常${NC}"
        ((checks_passed++))
    else
        echo -e "${RED}✗ SFAP服务异常${NC}"
    fi
    
    # 检查容器状态
    info "检查容器状态..."
    ((total_checks++))
    
    local unhealthy_containers=$(docker ps --filter "health=unhealthy" --format "{{.Names}}" | wc -l)
    if [ "$unhealthy_containers" -eq 0 ]; then
        echo -e "${GREEN}✓ 所有容器健康${NC}"
        ((checks_passed++))
    else
        echo -e "${RED}✗ 发现 $unhealthy_containers 个不健康容器${NC}"
    fi
    
    # 检查网络连接
    info "检查网络连接..."
    ((total_checks++))
    
    if curl -f http://localhost/health &>/dev/null; then
        echo -e "${GREEN}✓ 网络连接正常${NC}"
        ((checks_passed++))
    else
        echo -e "${RED}✗ 网络连接异常${NC}"
    fi
    
    # 总结
    echo ""
    echo "健康检查结果: $checks_passed/$total_checks 项通过"
    
    if [ "$checks_passed" -eq "$total_checks" ]; then
        log "所有健康检查通过"
    else
        warn "部分健康检查失败，请检查系统状态"
    fi
}

# 清理资源
cleanup_resources() {
    log "开始清理资源..."
    
    # 清理Docker资源
    info "清理Docker资源..."
    docker system prune -f
    docker volume prune -f
    docker network prune -f
    
    # 清理日志文件
    info "清理日志文件..."
    if [ -d "/opt/sfap/logs" ]; then
        find /opt/sfap/logs -name "*.log" -mtime +7 -delete
    fi
    
    # 清理临时文件
    info "清理临时文件..."
    rm -rf /tmp/sfap_*
    
    # 清理旧备份
    info "清理旧备份..."
    if [ -d "/opt/backup" ]; then
        find /opt/backup -name "sfap_backup_*" -mtime +7 -delete
    fi
    
    log "资源清理完成"
}

# 主函数
main() {
    case "${1:-}" in
        push-gitee)
            push_to_gitee
            ;;
        sync-dual)
            sync_dual_platform
            ;;
        deploy-prod)
            deploy_production
            ;;
        check-status)
            check_deployment_status
            ;;
        backup-data)
            backup_data
            ;;
        update-app)
            update_application
            ;;
        restart-services)
            restart_services
            ;;
        view-logs)
            view_logs
            ;;
        health-check)
            health_check
            ;;
        cleanup)
            cleanup_resources
            ;;
        -h|--help|help)
            show_help
            ;;
        "")
            show_help
            ;;
        *)
            error "未知操作: $1"
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
