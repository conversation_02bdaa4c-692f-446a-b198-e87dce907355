/**
 * 前端配置检查脚本
 * 验证环境变量和API地址配置是否正确
 */

// 模拟环境变量（在实际运行时会被真实的环境变量替换）
const mockEnv = {
  NODE_ENV: 'production',
  VUE_APP_BASE_API: 'http://120.26.140.157:8081',
  VUE_APP_BASE_URL: 'http://120.26.140.157:8200',
  VUE_APP_BACKEND_URL: 'http://120.26.140.157:8081'
};

console.log('🔍 前端配置检查开始...\n');

// 1. 检查环境变量
console.log('1. 环境变量检查');
console.log('==================');
console.log('NODE_ENV:', process.env.NODE_ENV || mockEnv.NODE_ENV);
console.log('VUE_APP_BASE_API:', process.env.VUE_APP_BASE_API || mockEnv.VUE_APP_BASE_API);
console.log('VUE_APP_BASE_URL:', process.env.VUE_APP_BASE_URL || mockEnv.VUE_APP_BASE_URL);
console.log('VUE_APP_BACKEND_URL:', process.env.VUE_APP_BACKEND_URL || mockEnv.VUE_APP_BACKEND_URL);

// 2. 检查API地址配置
console.log('\n2. API地址配置检查');
console.log('==================');

const baseAPI = process.env.VUE_APP_BASE_API || mockEnv.VUE_APP_BASE_API;
const expectedAPI = 'http://120.26.140.157:8081';

if (baseAPI === expectedAPI) {
  console.log('✅ API地址配置正确:', baseAPI);
} else {
  console.log('❌ API地址配置错误:');
  console.log('  当前配置:', baseAPI);
  console.log('  期望配置:', expectedAPI);
}

// 3. 检查WebSocket地址
console.log('\n3. WebSocket地址检查');
console.log('==================');

const wsURL = baseAPI.replace('http', 'ws');
const expectedWsURL = 'ws://120.26.140.157:8081';

if (wsURL === expectedWsURL) {
  console.log('✅ WebSocket地址正确:', wsURL);
} else {
  console.log('❌ WebSocket地址错误:');
  console.log('  当前配置:', wsURL);
  console.log('  期望配置:', expectedWsURL);
}

// 4. 模拟API调用测试
console.log('\n4. API调用测试');
console.log('==================');

const testAPIs = [
  { name: '根路径', url: `${baseAPI}/` },
  { name: '健康检查', url: `${baseAPI}/health` },
  { name: '新闻列表', url: `${baseAPI}/api/news` },
  { name: '热门新闻', url: `${baseAPI}/api/news/hot` },
  { name: '商品列表', url: `${baseAPI}/api/products` }
];

// 在浏览器环境中测试API
if (typeof fetch !== 'undefined') {
  testAPIs.forEach(async (api, index) => {
    try {
      console.log(`测试 ${api.name} ...`);
      const response = await fetch(api.url, {
        method: 'GET',
        mode: 'cors',
        credentials: 'include'
      });
      
      if (response.ok) {
        console.log(`✅ ${api.name} 连接成功 (${response.status})`);
      } else {
        console.log(`❌ ${api.name} 连接失败 (${response.status})`);
      }
    } catch (error) {
      console.log(`❌ ${api.name} 连接错误:`, error.message);
    }
  });
} else {
  console.log('ℹ️ 非浏览器环境，跳过实际API测试');
  testAPIs.forEach(api => {
    console.log(`📋 ${api.name}: ${api.url}`);
  });
}

// 5. 生成配置报告
console.log('\n5. 配置报告');
console.log('==================');

const configReport = {
  environment: process.env.NODE_ENV || mockEnv.NODE_ENV,
  apiBaseURL: baseAPI,
  websocketURL: wsURL,
  frontendURL: process.env.VUE_APP_BASE_URL || mockEnv.VUE_APP_BASE_URL,
  configurationStatus: baseAPI === expectedAPI ? 'CORRECT' : 'INCORRECT',
  recommendations: []
};

if (baseAPI !== expectedAPI) {
  configReport.recommendations.push('修复 VUE_APP_BASE_API 环境变量');
}

if (process.env.NODE_ENV !== 'production') {
  configReport.recommendations.push('确保生产环境使用 NODE_ENV=production');
}

console.log('配置状态:', configReport.configurationStatus);
console.log('环境:', configReport.environment);
console.log('API地址:', configReport.apiBaseURL);
console.log('WebSocket地址:', configReport.websocketURL);
console.log('前端地址:', configReport.frontendURL);

if (configReport.recommendations.length > 0) {
  console.log('\n⚠️ 建议修复:');
  configReport.recommendations.forEach((rec, index) => {
    console.log(`  ${index + 1}. ${rec}`);
  });
} else {
  console.log('\n✅ 配置检查通过，无需修复');
}

// 6. 输出测试用的curl命令
console.log('\n6. 测试命令');
console.log('==================');
console.log('手动测试API连接的curl命令:');
testAPIs.forEach(api => {
  console.log(`curl "${api.url}"`);
});

console.log('\n🔍 前端配置检查完成');

// 如果在Node.js环境中运行，导出配置报告
if (typeof module !== 'undefined' && module.exports) {
  module.exports = configReport;
}
