#!/bin/bash

# 智慧农业平台推荐系统API测试脚本
# 测试所有推荐相关的API接口

echo "=========================================="
echo "智慧农业平台推荐系统API测试"
echo "=========================================="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
SERVER_IP="**************"
BACKEND_PORT="8081"
BASE_URL="http://$SERVER_IP:$BACKEND_PORT"

# 测试计数器
total_tests=0
passed_tests=0
failed_tests=0

# 测试函数
test_api() {
    local test_name=$1
    local url=$2
    local expected_status=${3:-200}
    local method=${4:-GET}
    
    total_tests=$((total_tests + 1))
    echo -n "[$total_tests] $test_name ... "
    
    # 使用curl测试API
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "%{http_code}" -o /tmp/api_response.json "$url" 2>/dev/null)
    else
        response=$(curl -s -w "%{http_code}" -X "$method" -H "Content-Type: application/json" -o /tmp/api_response.json "$url" 2>/dev/null)
    fi
    
    if [ "$response" = "$expected_status" ]; then
        echo -e "${GREEN}✅ 通过 ($response)${NC}"
        
        # 检查响应内容
        if [ -f /tmp/api_response.json ]; then
            response_content=$(cat /tmp/api_response.json)
            if echo "$response_content" | grep -q '"code":200'; then
                data_count=$(echo "$response_content" | grep -o '"data":\[.*\]' | grep -o '\[.*\]' | grep -o ',' | wc -l)
                data_count=$((data_count + 1))
                echo "    📊 返回数据: $data_count 条"
            elif echo "$response_content" | grep -q '"code":'; then
                error_msg=$(echo "$response_content" | grep -o '"message":"[^"]*"' | cut -d'"' -f4)
                echo "    ⚠️ 业务错误: $error_msg"
            fi
        fi
        
        passed_tests=$((passed_tests + 1))
        return 0
    else
        echo -e "${RED}❌ 失败 ($response)${NC}"
        
        # 显示错误详情
        if [ -f /tmp/api_response.json ]; then
            error_content=$(cat /tmp/api_response.json)
            if [ ${#error_content} -lt 200 ]; then
                echo "    📄 错误详情: $error_content"
            else
                echo "    📄 错误详情: ${error_content:0:100}..."
            fi
        fi
        
        failed_tests=$((failed_tests + 1))
        return 1
    fi
}

echo -e "${BLUE}🔍 1. 后端服务基础检查${NC}"
echo "=================================="

# 测试后端基础服务
test_api "后端服务健康检查" "$BASE_URL/"
test_api "后端API根路径" "$BASE_URL/api"

echo ""
echo -e "${BLUE}🎯 2. 新推荐API接口测试 (Mall)${NC}"
echo "=================================="

# 测试新的Mall推荐API
test_api "热门推荐API" "$BASE_URL/api/mall/recommendations/hot?limit=6"
test_api "新品推荐API" "$BASE_URL/api/mall/recommendations/new?limit=8"
test_api "个性化推荐API" "$BASE_URL/api/mall/recommendations/personalized?limit=10"
test_api "个性化推荐API(带用户ID)" "$BASE_URL/api/mall/recommendations/personalized?userId=1&limit=10"
test_api "相似商品推荐API" "$BASE_URL/api/mall/recommendations/similar/1?limit=5"
test_api "推荐统计API" "$BASE_URL/api/mall/recommendations/stats?algorithmType=hot&days=7"

echo ""
echo -e "${BLUE}🔄 3. 原有推荐API接口测试 (Products)${NC}"
echo "=================================="

# 测试原有的Products推荐API
test_api "原有个性化推荐API" "$BASE_URL/api/products/recommended?limit=10"
test_api "原有热门推荐API" "$BASE_URL/api/products/hot?limit=6"
test_api "原有新品推荐API" "$BASE_URL/api/products/new?limit=8"
test_api "原有相似商品推荐API" "$BASE_URL/api/products/similar/1?limit=5"

echo ""
echo -e "${BLUE}🛒 4. 商品相关API测试${NC}"
echo "=================================="

# 测试商品相关API
test_api "商品列表API" "$BASE_URL/api/mall/products?page=1&size=5"
test_api "热门商品API" "$BASE_URL/api/mall/products/hot?limit=5"
test_api "新品API" "$BASE_URL/api/mall/products/new?limit=5"
test_api "精选商品API" "$BASE_URL/api/mall/products/featured?limit=5"

echo ""
echo -e "${BLUE}📊 5. 推荐点击记录测试${NC}"
echo "=================================="

# 测试推荐点击记录（POST请求）
echo -n "[$(($total_tests + 1))] 推荐点击记录API (Mall) ... "
total_tests=$((total_tests + 1))

click_response=$(curl -s -w "%{http_code}" -X POST \
    -H "Content-Type: application/x-www-form-urlencoded" \
    -d "userId=1&productId=1&recommendationType=hot" \
    -o /tmp/click_response.json \
    "$BASE_URL/api/mall/recommendations/click" 2>/dev/null)

if [ "$click_response" = "200" ]; then
    echo -e "${GREEN}✅ 通过 ($click_response)${NC}"
    passed_tests=$((passed_tests + 1))
else
    echo -e "${RED}❌ 失败 ($click_response)${NC}"
    failed_tests=$((failed_tests + 1))
fi

echo ""
echo -e "${BLUE}🌐 6. 跨域和请求头测试${NC}"
echo "=================================="

# 测试CORS配置
total_tests=$((total_tests + 1))
echo -n "[$total_tests] CORS配置测试 ... "

cors_response=$(curl -s -w "%{http_code}" \
    -H "Origin: http://$SERVER_IP:8200" \
    -H "Access-Control-Request-Method: GET" \
    -H "Access-Control-Request-Headers: Content-Type" \
    -X OPTIONS \
    -o /tmp/cors_response.json \
    "$BASE_URL/api/mall/recommendations/hot" 2>/dev/null)

if [ "$cors_response" = "200" ] || [ "$cors_response" = "204" ]; then
    echo -e "${GREEN}✅ CORS配置正常 ($cors_response)${NC}"
    passed_tests=$((passed_tests + 1))
else
    echo -e "${RED}❌ CORS配置异常 ($cors_response)${NC}"
    failed_tests=$((failed_tests + 1))
fi

echo ""
echo -e "${BLUE}📈 7. 性能和响应时间测试${NC}"
echo "=================================="

# 测试响应时间
echo "测试API响应时间..."
for api in "hot" "new" "personalized"; do
    echo -n "  $api API响应时间: "
    start_time=$(date +%s%N)
    curl -s "$BASE_URL/api/mall/recommendations/$api?limit=5" > /dev/null
    end_time=$(date +%s%N)
    duration=$(( (end_time - start_time) / 1000000 ))
    
    if [ $duration -lt 1000 ]; then
        echo -e "${GREEN}${duration}ms ✅${NC}"
    elif [ $duration -lt 3000 ]; then
        echo -e "${YELLOW}${duration}ms ⚠️${NC}"
    else
        echo -e "${RED}${duration}ms ❌${NC}"
    fi
done

echo ""
echo -e "${BLUE}📋 8. 测试结果汇总${NC}"
echo "=================================="

echo "总测试数: $total_tests"
echo -e "通过测试: ${GREEN}$passed_tests${NC}"
echo -e "失败测试: ${RED}$failed_tests${NC}"

success_rate=$(( passed_tests * 100 / total_tests ))
echo "成功率: $success_rate%"

echo ""
echo -e "${BLUE}🔧 9. 问题诊断和建议${NC}"
echo "=================================="

if [ $failed_tests -eq 0 ]; then
    echo -e "${GREEN}🎉 所有推荐API测试通过！${NC}"
    echo ""
    echo "✅ 推荐系统状态良好，可以正常使用以下功能："
    echo "• 热门商品推荐"
    echo "• 新品推荐"
    echo "• 个性化推荐"
    echo "• 相似商品推荐"
    echo "• 推荐点击统计"
elif [ $success_rate -ge 80 ]; then
    echo -e "${YELLOW}⚠️ 大部分推荐功能正常，但有少量问题需要解决${NC}"
    echo ""
    echo "建议检查："
    echo "• 确保后端服务正常运行"
    echo "• 检查数据库中是否有足够的商品数据"
    echo "• 验证推荐算法配置"
elif [ $success_rate -ge 60 ]; then
    echo -e "${YELLOW}⚠️ 推荐系统部分功能正常，需要进一步配置${NC}"
    echo ""
    echo "需要检查："
    echo "• 推荐API路径映射"
    echo "• 数据库商品数据质量"
    echo "• 推荐服务实现"
else
    echo -e "${RED}❌ 推荐系统存在较多问题，需要全面检查${NC}"
    echo ""
    echo "紧急检查项："
    echo "• 后端服务是否正常启动"
    echo "• MallRecommendationController是否正确部署"
    echo "• 数据库连接是否正常"
    echo "• 推荐服务依赖是否完整"
fi

echo ""
echo -e "${BLUE}🚀 10. 下一步操作建议${NC}"
echo "=================================="

if [ $failed_tests -eq 0 ]; then
    echo "1. 推荐系统已就绪，可以开始使用"
    echo "2. 建议进行前端集成测试"
    echo "3. 配置推荐算法参数优化"
else
    echo "1. 根据失败的测试项进行针对性修复"
    echo "2. 检查后端日志获取详细错误信息"
    echo "3. 运行数据库检查脚本: mysql < check-recommendation-data.sql"
    echo "4. 重新运行此测试脚本确认修复效果"
fi

echo ""
echo "测试完成时间: $(date)"

# 清理临时文件
rm -f /tmp/api_response.json /tmp/click_response.json /tmp/cors_response.json

exit $failed_tests
