<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SFAP农业百科功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .category-btn {
            display: inline-block;
            margin: 5px;
            padding: 8px 16px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
        }
        .category-btn:hover {
            background: #2980b9;
        }
        .category-btn.active {
            background: #e74c3c;
        }
        .article-list {
            margin-top: 20px;
        }
        .article-item {
            border: 1px solid #ddd;
            margin: 10px 0;
            padding: 15px;
            border-radius: 4px;
            background: #fafafa;
        }
        .article-title {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        .article-meta {
            color: #7f8c8d;
            font-size: 14px;
        }
        .loading {
            text-align: center;
            color: #7f8c8d;
            padding: 20px;
        }
        .error {
            color: #e74c3c;
            background: #fdf2f2;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #f5c6cb;
        }
        .success {
            color: #27ae60;
            background: #d4edda;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #c3e6cb;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>SFAP农业百科功能测试页面</h1>
    
    <!-- 分类筛选测试 -->
    <div class="test-section">
        <h2 class="test-title">1. 分类筛选功能测试</h2>
        <p>点击下面的分类按钮测试分类筛选功能：</p>
        
        <div class="category-buttons">
            <button class="category-btn" onclick="testCategoryFilter('')">全部分类</button>
            <button class="category-btn" onclick="testCategoryFilter('1')">种植技术</button>
            <button class="category-btn" onclick="testCategoryFilter('2')">养殖技术</button>
            <button class="category-btn" onclick="testCategoryFilter('3')">农业技术</button>
            <button class="category-btn" onclick="testCategoryFilter('4')">植物保护</button>
            <button class="category-btn" onclick="testCategoryFilter('5')">农产品加工</button>
            <button class="category-btn" onclick="testCategoryFilter('6')">农业经济</button>
        </div>
        
        <div id="category-test-result" class="test-result"></div>
        <div id="article-list" class="article-list"></div>
    </div>
    
    <!-- 浏览量统计测试 -->
    <div class="test-section">
        <h2 class="test-title">2. 浏览量统计功能测试</h2>
        <p>点击下面的按钮测试浏览量增加功能：</p>
        
        <button class="category-btn" onclick="testViewCount(1)">测试文章1浏览量</button>
        <button class="category-btn" onclick="testViewCount(2)">测试文章2浏览量</button>
        <button class="category-btn" onclick="testViewCount(3)">测试文章3浏览量</button>
        
        <div id="view-test-result" class="test-result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8081/api/encyclopedia';
        let currentCategory = '';
        
        // 测试分类筛选功能
        async function testCategoryFilter(categoryId) {
            currentCategory = categoryId;
            
            // 更新按钮状态
            document.querySelectorAll('.category-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            const resultDiv = document.getElementById('category-test-result');
            const articleDiv = document.getElementById('article-list');
            
            resultDiv.innerHTML = '<div class="loading">正在加载分类数据...</div>';
            articleDiv.innerHTML = '';
            
            try {
                const url = categoryId ? 
                    `${API_BASE}/list?categoryId=${categoryId}&page=1&size=10` : 
                    `${API_BASE}/list?page=1&size=10`;
                
                console.log('请求URL:', url);
                
                const response = await fetch(url);
                const data = await response.json();
                
                console.log('API响应:', data);
                
                if (data.code === 200) {
                    const articles = data.data.list || [];
                    const total = data.data.total || 0;
                    
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ 分类筛选成功！
                            <br>分类ID: ${categoryId || '全部'}
                            <br>找到文章: ${total} 篇
                            <br>当前页文章: ${articles.length} 篇
                        </div>
                    `;
                    
                    if (articles.length > 0) {
                        articleDiv.innerHTML = articles.map(article => `
                            <div class="article-item">
                                <div class="article-title">${article.title}</div>
                                <div class="article-meta">
                                    分类: ${article.categoryName} (ID: ${article.categoryId}) | 
                                    浏览量: ${article.viewsCount || article.views || 0} | 
                                    发布时间: ${article.publishDate || article.createTime}
                                </div>
                            </div>
                        `).join('');
                    } else {
                        articleDiv.innerHTML = '<div class="loading">该分类下暂无文章</div>';
                    }
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ 请求失败: ${data.message}</div>`;
                }
            } catch (error) {
                console.error('分类筛选测试失败:', error);
                resultDiv.innerHTML = `<div class="error">❌ 网络错误: ${error.message}</div>`;
            }
        }
        
        // 测试浏览量增加功能
        async function testViewCount(articleId) {
            const resultDiv = document.getElementById('view-test-result');
            
            resultDiv.innerHTML = '<div class="loading">正在测试浏览量增加...</div>';
            
            try {
                // 先获取当前浏览量
                const detailResponse = await fetch(`${API_BASE}/${articleId}`);
                const detailData = await detailResponse.json();
                
                if (detailData.code !== 200) {
                    throw new Error('获取文章详情失败');
                }
                
                const beforeViews = detailData.data.viewsCount || detailData.data.views || 0;
                
                // 增加浏览量
                const viewResponse = await fetch(`${API_BASE}/${articleId}/view`);
                const viewData = await viewResponse.json();
                
                if (viewData.code !== 200) {
                    throw new Error('增加浏览量失败');
                }
                
                // 再次获取浏览量验证
                const afterResponse = await fetch(`${API_BASE}/${articleId}`);
                const afterData = await afterResponse.json();
                
                const afterViews = afterData.data.viewsCount || afterData.data.views || 0;
                
                resultDiv.innerHTML = `
                    <div class="success">
                        ✅ 浏览量增加成功！
                        <br>文章ID: ${articleId}
                        <br>文章标题: ${detailData.data.title}
                        <br>增加前浏览量: ${beforeViews}
                        <br>增加后浏览量: ${afterViews}
                        <br>增加数量: ${afterViews - beforeViews}
                    </div>
                `;
                
            } catch (error) {
                console.error('浏览量测试失败:', error);
                resultDiv.innerHTML = `<div class="error">❌ 测试失败: ${error.message}</div>`;
            }
        }
        
        // 页面加载时默认加载全部文章
        window.onload = function() {
            testCategoryFilter('');
        };
    </script>
</body>
</html>
