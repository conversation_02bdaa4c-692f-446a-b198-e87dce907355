-- 创建缺失的存储过程
-- 解决 DatabaseMonitorService 调用失败的问题

USE agriculture_mall;

-- 1. 创建系统健康检查存储过程
DELIMITER //
DROP PROCEDURE IF EXISTS `GetSystemHealth`//
CREATE PROCEDURE `GetSystemHealth`()
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        -- 如果出错，返回基本的健康状态
        SELECT 
            'Database' as component,
            'HEALTHY' as status,
            0 as recent_errors,
            0 as avg_response_time,
            NOW() as check_time;
    END;
    
    -- 检查数据库连接状态
    SELECT 
        'Database' as component,
        CASE 
            WHEN CONNECTION_ID() IS NOT NULL THEN 'HEALTHY'
            ELSE 'UNHEALTHY'
        END as status,
        0 as recent_errors,
        0 as avg_response_time,
        NOW() as check_time
    
    UNION ALL
    
    -- 检查表状态
    SELECT 
        'Tables' as component,
        CASE 
            WHEN (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE()) > 0 
            THEN 'HEALTHY'
            ELSE 'UNHEALTHY'
        END as status,
        (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE()) as recent_errors,
        0 as avg_response_time,
        NOW() as check_time
    
    UNION ALL
    
    -- 检查用户表
    SELECT 
        'Users' as component,
        CASE 
            WHEN (SELECT COUNT(*) FROM user WHERE deleted = 0) > 0 
            THEN 'HEALTHY'
            ELSE 'WARNING'
        END as status,
        (SELECT COUNT(*) FROM user WHERE deleted = 0) as recent_errors,
        0 as avg_response_time,
        NOW() as check_time
    
    UNION ALL
    
    -- 检查商品表
    SELECT 
        'Products' as component,
        CASE 
            WHEN (SELECT COUNT(*) FROM product WHERE deleted = 0) > 0 
            THEN 'HEALTHY'
            ELSE 'WARNING'
        END as status,
        (SELECT COUNT(*) FROM product WHERE deleted = 0) as recent_errors,
        0 as avg_response_time,
        NOW() as check_time;
        
END //
DELIMITER ;

-- 2. 创建商品热度更新存储过程
DELIMITER //
DROP PROCEDURE IF EXISTS `UpdateProductPopularity`//
CREATE PROCEDURE `UpdateProductPopularity`()
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- 更新商品热度（基于浏览量、销量等）
    UPDATE product p 
    SET popularity_score = COALESCE(
        (SELECT 
            (COALESCE(view_count, 0) * 0.3 + 
             COALESCE(sales_count, 0) * 0.7) 
        ), 0
    )
    WHERE p.deleted = 0;
    
    COMMIT;
END //
DELIMITER ;

-- 3. 创建清理过期数据存储过程
DELIMITER //
DROP PROCEDURE IF EXISTS `CleanExpiredData`//
CREATE PROCEDURE `CleanExpiredData`()
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- 清理过期的购物车项目（30天前）
    DELETE FROM cart_item 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
    
    -- 清理过期的会话数据（如果有的话）
    -- DELETE FROM user_session 
    -- WHERE last_access_time < DATE_SUB(NOW(), INTERVAL 7 DAY);
    
    COMMIT;
END //
DELIMITER ;

-- 4. 创建日常统计生成存储过程（如果不存在）
DELIMITER //
DROP PROCEDURE IF EXISTS `GenerateDailyStats`//
CREATE PROCEDURE `GenerateDailyStats`(IN stat_date DATE)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- 检查是否存在system_stats表，如果不存在则创建
    CREATE TABLE IF NOT EXISTS `system_stats` (
        `id` bigint NOT NULL AUTO_INCREMENT COMMENT '统计ID',
        `stat_date` date NOT NULL COMMENT '统计日期',
        `total_users` int DEFAULT '0' COMMENT '总用户数',
        `active_users` int DEFAULT '0' COMMENT '活跃用户数',
        `new_users` int DEFAULT '0' COMMENT '新增用户数',
        `total_products` int DEFAULT '0' COMMENT '总商品数',
        `online_products` int DEFAULT '0' COMMENT '在售商品数',
        `new_products` int DEFAULT '0' COMMENT '新增商品数',
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `uk_stat_date` (`stat_date`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统统计表';
    
    -- 删除当日已存在的统计数据
    DELETE FROM `system_stats` WHERE `stat_date` = stat_date;
    
    -- 插入新的统计数据
    INSERT INTO `system_stats` (
        `stat_date`,
        `total_users`,
        `active_users`,
        `new_users`,
        `total_products`,
        `online_products`,
        `new_products`
    )
    SELECT 
        stat_date,
        (SELECT COUNT(*) FROM `user` WHERE `deleted` = 0) as total_users,
        (SELECT COUNT(*) FROM `user` WHERE DATE(`last_login_time`) = stat_date AND `deleted` = 0) as active_users,
        (SELECT COUNT(*) FROM `user` WHERE DATE(`created_at`) = stat_date AND `deleted` = 0) as new_users,
        (SELECT COUNT(*) FROM `product` WHERE `deleted` = 0) as total_products,
        (SELECT COUNT(*) FROM `product` WHERE `deleted` = 0 AND `status` = 1) as online_products,
        (SELECT COUNT(*) FROM `product` WHERE DATE(`created_at`) = stat_date AND `deleted` = 0) as new_products;
    
    COMMIT;
END //
DELIMITER ;

-- 5. 创建清理旧日志存储过程
DELIMITER //
DROP PROCEDURE IF EXISTS `CleanOldLogs`//
CREATE PROCEDURE `CleanOldLogs`()
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- 如果存在日志表，则清理旧数据
    -- 这里使用动态SQL来避免表不存在的错误
    SET @sql = 'SELECT 1'; -- 默认语句
    
    -- 检查并清理api_access_log表（如果存在）
    SELECT COUNT(*) INTO @table_exists 
    FROM information_schema.tables 
    WHERE table_schema = DATABASE() 
    AND table_name = 'api_access_log';
    
    IF @table_exists > 0 THEN
        DELETE FROM `api_access_log` 
        WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 30 DAY);
    END IF;
    
    -- 检查并清理error_log表（如果存在）
    SELECT COUNT(*) INTO @table_exists 
    FROM information_schema.tables 
    WHERE table_schema = DATABASE() 
    AND table_name = 'error_log';
    
    IF @table_exists > 0 THEN
        DELETE FROM `error_log` 
        WHERE `resolved` = 1 
        AND `resolved_at` < DATE_SUB(NOW(), INTERVAL 90 DAY);
    END IF;
    
    COMMIT;
END //
DELIMITER ;

-- 验证存储过程创建
SELECT 
    ROUTINE_NAME as '存储过程名称',
    ROUTINE_TYPE as '类型',
    CREATED as '创建时间'
FROM information_schema.ROUTINES 
WHERE ROUTINE_SCHEMA = DATABASE() 
AND ROUTINE_TYPE = 'PROCEDURE'
ORDER BY ROUTINE_NAME;
