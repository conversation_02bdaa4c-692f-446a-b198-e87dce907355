<script>
// import { getWeather7d, getWeather24h } from '@/path/to/weather.js'; // 确保路径正确
import weatherService from '@/api/weather.js'; // Corrected import

export default {
    data() {
        return {
            weatherData: [],
            // hourlyWeatherData: [] // Removed as getWeather24h is not available
        };
    },
    created() {
        this.fetchWeatherData();
        // this.fetchHourlyWeatherData(); // Removed call
    },
    methods: {
        async fetchWeatherData() {
            const cityId = '101010100'; // Assuming Beijing city ID, adjust if needed
            console.log(`正在获取 北京 (ID: ${cityId}) 的天气数据...`);
            try {
                // const data = await getWeather7d(cityId);
                const data = await weatherService.getWeather7d(cityId); // Corrected call
                this.weatherData = data;
                console.log('天气数据获取成功:', data);
            } catch (error) {
                console.error('天气数据获取错误:', error);
            }
        },
        // Removed fetchHourlyWeatherData method
        // async fetchHourlyWeatherData() {
        //     const cityId = '101010100';
        //     console.log(`正在获取 北京 (ID: ${cityId}) 的24小时天气数据...`);
        //     try {
        //         const data = await getWeather24h(cityId);
        //         this.hourlyWeatherData = data;
        //         console.log('24小时天气数据获取成功:', data);
        //     } catch (error) {
        //         console.error('24小时天气数据获取错误:', error);
        //     }
        // }
    }
};
</script>