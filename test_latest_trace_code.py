import requests
import json

# 测试最新的溯源码
trace_code = "SFAPS250723230460290SF08"
url = "http://localhost:8081/api/traceability/query"

data = {
    "traceCode": trace_code,
    "source": "web",
    "location": "test",
    "ipAddress": "127.0.0.1",
    "deviceInfo": "Test Browser",
    "userId": None
}

print(f"Testing latest trace code: {trace_code}")
print(f"API: {url}")
print(f"Request data: {json.dumps(data, indent=2, ensure_ascii=False)}")

try:
    response = requests.post(url, json=data, headers={'Content-Type': 'application/json'})
    print(f"Status Code: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print("✅ Query successful!")
        print(f"Product Name: {result['data']['productName']}")
        print(f"Producer: {result['data']['producerName']}")
        print(f"Status: {result['data']['status']}")
    else:
        print("❌ Query failed!")
        print(f"Response: {response.text}")
        
except Exception as e:
    print(f"❌ Request failed: {e}")