#!/bin/bash

# 生产环境图片目录结构设置脚本
# 服务器: 120.26.140.157

echo "=== 设置生产环境图片目录结构 ==="

# 基础路径
BASE_PATH="/www/wwwroot/test.com/backend/uploads"

# 1. 创建主要图片目录
echo "1. 创建图片目录结构..."

# 创建基础目录
mkdir -p "$BASE_PATH"
mkdir -p "$BASE_PATH/images"

# 创建各类图片子目录
mkdir -p "$BASE_PATH/images/products"      # 商品图片
mkdir -p "$BASE_PATH/images/avatars"       # 用户头像
mkdir -p "$BASE_PATH/images/news"          # 新闻图片
mkdir -p "$BASE_PATH/images/encyclopedia"  # 百科图片
mkdir -p "$BASE_PATH/images/categories"    # 分类图片
mkdir -p "$BASE_PATH/images/banners"       # 轮播图片
mkdir -p "$BASE_PATH/images/qrcodes"       # 二维码图片
mkdir -p "$BASE_PATH/images/certificates"  # 证书图片

# 创建其他文件类型目录
mkdir -p "$BASE_PATH/documents"            # 文档文件
mkdir -p "$BASE_PATH/videos"               # 视频文件
mkdir -p "$BASE_PATH/temp"                 # 临时文件

echo "✅ 目录结构创建完成"

# 2. 设置目录权限
echo "2. 设置目录权限..."

# 设置所有者为www用户
chown -R www:www "$BASE_PATH"

# 设置目录权限：755 (rwxr-xr-x)
find "$BASE_PATH" -type d -exec chmod 755 {} \;

# 设置文件权限：644 (rw-r--r--)
find "$BASE_PATH" -type f -exec chmod 644 {} \;

echo "✅ 权限设置完成"

# 3. 创建默认占位图片
echo "3. 创建默认占位图片..."

# 创建简单的占位图片（如果系统有ImageMagick）
if command -v convert >/dev/null 2>&1; then
    # 商品默认图片
    convert -size 300x300 xc:lightgray -gravity center -pointsize 20 -annotate +0+0 "商品图片" "$BASE_PATH/images/products/default-product.jpg"
    
    # 用户默认头像
    convert -size 150x150 xc:lightblue -gravity center -pointsize 16 -annotate +0+0 "默认头像" "$BASE_PATH/images/avatars/default-avatar.jpg"
    
    # 新闻默认图片
    convert -size 400x200 xc:lightgreen -gravity center -pointsize 18 -annotate +0+0 "新闻图片" "$BASE_PATH/images/news/default-news.jpg"
    
    # 百科默认图片
    convert -size 300x200 xc:lightyellow -gravity center -pointsize 16 -annotate +0+0 "百科图片" "$BASE_PATH/images/encyclopedia/default-encyclopedia.jpg"
    
    echo "✅ 默认占位图片创建完成"
else
    echo "⚠️ ImageMagick未安装，跳过占位图片创建"
fi

# 4. 创建.htaccess文件（如果使用Apache）
echo "4. 创建访问控制文件..."

cat > "$BASE_PATH/.htaccess" << 'EOF'
# 图片资源访问控制
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # 允许直接访问图片文件
    RewriteCond %{REQUEST_FILENAME} -f
    RewriteRule \.(jpg|jpeg|png|gif|webp|svg)$ - [L]
    
    # 防止直接访问其他文件
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule . - [F,L]
</IfModule>

# 设置缓存策略
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
</IfModule>

# 安全设置
Options -Indexes
EOF

echo "✅ 访问控制文件创建完成"

# 5. 显示目录结构
echo "5. 目录结构概览:"
echo "===================="
tree "$BASE_PATH" -L 3 2>/dev/null || find "$BASE_PATH" -type d | head -20

# 6. 显示访问URL格式
echo ""
echo "6. 图片访问URL格式:"
echo "===================="
echo "商品图片: http://120.26.140.157:8081/uploads/images/products/图片名.jpg"
echo "用户头像: http://120.26.140.157:8081/uploads/images/avatars/头像名.jpg"
echo "新闻图片: http://120.26.140.157:8081/uploads/images/news/图片名.jpg"
echo "百科图片: http://120.26.140.157:8081/uploads/images/encyclopedia/图片名.jpg"

# 7. 检查磁盘空间
echo ""
echo "7. 磁盘空间检查:"
echo "===================="
df -h "$BASE_PATH" | tail -1

# 8. 创建监控脚本
echo ""
echo "8. 创建图片目录监控脚本..."

cat > "$BASE_PATH/../monitor-images.sh" << 'EOF'
#!/bin/bash
# 图片目录监控脚本

BASE_PATH="/www/wwwroot/test.com/backend/uploads"

echo "=== 图片目录监控报告 ==="
echo "时间: $(date)"
echo ""

# 统计各目录文件数量
echo "文件数量统计:"
echo "商品图片: $(find $BASE_PATH/images/products -name "*.jpg" -o -name "*.png" -o -name "*.gif" | wc -l)"
echo "用户头像: $(find $BASE_PATH/images/avatars -name "*.jpg" -o -name "*.png" -o -name "*.gif" | wc -l)"
echo "新闻图片: $(find $BASE_PATH/images/news -name "*.jpg" -o -name "*.png" -o -name "*.gif" | wc -l)"
echo "百科图片: $(find $BASE_PATH/images/encyclopedia -name "*.jpg" -o -name "*.png" -o -name "*.gif" | wc -l)"

# 统计目录大小
echo ""
echo "目录大小统计:"
du -sh $BASE_PATH/images/*

# 检查权限
echo ""
echo "权限检查:"
ls -la $BASE_PATH/images/

# 检查磁盘空间
echo ""
echo "磁盘空间:"
df -h $BASE_PATH
EOF

chmod +x "$BASE_PATH/../monitor-images.sh"

echo "✅ 监控脚本创建完成: $BASE_PATH/../monitor-images.sh"

echo ""
echo "=== 图片目录设置完成 ==="
echo ""
echo "📁 主要目录:"
echo "  - 商品图片: $BASE_PATH/images/products/"
echo "  - 用户头像: $BASE_PATH/images/avatars/"
echo "  - 新闻图片: $BASE_PATH/images/news/"
echo "  - 百科图片: $BASE_PATH/images/encyclopedia/"
echo ""
echo "🔗 访问格式:"
echo "  http://120.26.140.157:8081/uploads/images/products/图片名"
echo ""
echo "🛠️ 管理命令:"
echo "  监控脚本: $BASE_PATH/../monitor-images.sh"
echo "  权限修复: chown -R www:www $BASE_PATH && chmod -R 755 $BASE_PATH"
