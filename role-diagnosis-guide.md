# SFAP用户角色问题诊断和修复指南

## 问题概述

SFAP农品汇平台中出现了用户角色识别问题，主要表现为：
- 管理员用户被错误识别为普通用户
- 数据库中role和user_type字段不一致
- 权限验证失败，导致功能访问异常

## 诊断工具和API

### 1. 数据库诊断SQL
执行以下SQL文件进行数据库层面的诊断：
```sql
-- 执行诊断SQL
source backend/main/src/main/resources/db/diagnose_user_roles.sql
```

### 2. 调试API接口

#### 2.1 用户角色调试接口
```bash
# 检查指定用户角色
GET /api/debug/user-role/check/{userId}

# 检查所有管理员用户
GET /api/debug/user-role/check-all-admins

# 获取角色统计信息
GET /api/debug/user-role/role-statistics
```

#### 2.2 角色修复接口
```bash
# 一键修复所有角色问题
POST /api/debug/role-fix/fix-all

# 诊断所有角色问题
GET /api/debug/role-fix/diagnose

# 仅修复管理员角色
POST /api/debug/role-fix/fix-admins

# 重置指定用户角色
POST /api/debug/role-fix/reset-user/{userId}?targetRole=admin
```

## 常见问题和解决方案

### 问题1: 管理员用户被识别为普通用户

**症状**:
- 用户名包含"admin"但role字段不是"admin"
- 无法访问管理员功能
- 权限验证失败

**诊断**:
```bash
curl -X GET "http://localhost:8081/api/debug/role-fix/diagnose"
```

**修复**:
```bash
# 自动修复所有管理员角色
curl -X POST "http://localhost:8081/api/debug/role-fix/fix-admins"

# 或者手动重置特定用户
curl -X POST "http://localhost:8081/api/debug/role-fix/reset-user/18?targetRole=admin"
```

### 问题2: role和user_type字段不一致

**症状**:
- 数据库中role="admin"但user_type="user"
- 或者相反的情况

**诊断**:
```sql
SELECT id, username, role, user_type 
FROM user 
WHERE role != COALESCE(user_type, 'user') 
  AND deleted = 0;
```

**修复**:
```bash
# 一键修复所有不一致问题
curl -X POST "http://localhost:8081/api/debug/role-fix/fix-all"
```

### 问题3: 销售者角色与申请状态不匹配

**症状**:
- 销售者申请已通过但用户角色仍是普通用户
- 或申请未通过但用户已是销售者角色

**诊断**:
```sql
SELECT sa.user_id, u.username, sa.status, u.role, u.user_type
FROM seller_application sa
LEFT JOIN user u ON sa.user_id = u.id
WHERE (sa.status = 1 AND u.role != 'seller')
   OR (sa.status != 1 AND u.role = 'seller');
```

**修复**:
通过一键修复API会自动处理销售者角色问题。

## 预防措施

### 1. 定时任务监控
系统已配置定时任务自动检查和修复角色问题：
- 每小时检查一次角色一致性
- 每天凌晨2点进行深度检查

### 2. 代码层面防护

#### 2.1 User实体类增强
确保setUserRole方法同时更新role和user_type：
```java
public void setUserRole(String role) {
    this.role = role;
    this.userType = role;
}
```

#### 2.2 权限验证增强
在AuthorizationService中增加多重验证：
```java
public boolean hasRole(User user, String requiredRole) {
    // 检查role字段
    if (requiredRole.equals(user.getRole())) {
        return true;
    }
    // 检查user_type字段
    if (requiredRole.equals(user.getUserType())) {
        return true;
    }
    // 特殊处理管理员用户名
    if ("admin".equals(requiredRole) && 
        user.getUsername().toLowerCase().contains("admin")) {
        return true;
    }
    return false;
}
```

### 3. 数据库约束
添加数据库触发器确保角色一致性：
```sql
DELIMITER //
CREATE TRIGGER user_role_consistency_trigger
BEFORE UPDATE ON user
FOR EACH ROW
BEGIN
    -- 如果更新了role，同步user_type
    IF NEW.role != OLD.role THEN
        SET NEW.user_type = NEW.role;
    END IF;
    
    -- 如果更新了user_type，同步role
    IF NEW.user_type != OLD.user_type THEN
        SET NEW.role = NEW.user_type;
    END IF;
    
    -- 特殊处理管理员用户名
    IF NEW.username LIKE '%admin%' AND NEW.role != 'admin' THEN
        SET NEW.role = 'admin';
        SET NEW.user_type = 'admin';
    END IF;
END //
DELIMITER ;
```

## 应急处理流程

### 1. 发现问题时
1. 立即执行诊断API获取问题详情
2. 记录问题用户的详细信息
3. 执行一键修复API

### 2. 修复后验证
1. 重新登录受影响的用户账号
2. 测试权限功能是否正常
3. 检查数据库数据一致性

### 3. 监控和预警
1. 查看定时任务日志
2. 定期执行诊断API
3. 监控用户反馈

## 测试用例

### 测试管理员角色修复
```bash
# 1. 故意破坏管理员角色
UPDATE user SET role='user' WHERE username='admin_new';

# 2. 执行修复
curl -X POST "http://localhost:8081/api/debug/role-fix/fix-admins"

# 3. 验证修复结果
SELECT id, username, role, user_type FROM user WHERE username='admin_new';
```

### 测试权限验证
```bash
# 使用修复后的管理员账号访问管理员接口
curl -X GET "http://localhost:8081/api/admin/dashboard/stats" \
     -H "X-User-Id: 18" \
     -H "Authorization: Bearer admin-token"
```

## 日志监控

关键日志位置和内容：
```
# 角色修复日志
2024-12-01 10:00:00 INFO  RoleConsistencyTask - 开始定时检查用户角色一致性
2024-12-01 10:00:01 WARN  RoleConsistencyTask - 发现 1 个用户角色不一致
2024-12-01 10:00:02 INFO  UserRoleSyncService - 修复管理员角色: userId=18, username=admin_new

# 权限验证日志
2024-12-01 10:01:00 INFO  AuthorizationService - 用户权限验证: userId=18, role=admin, required=admin
```

## 联系支持

如果问题仍然存在，请提供以下信息：
1. 诊断API的完整输出
2. 相关用户的数据库记录
3. 应用程序日志
4. 问题复现步骤

通过以上诊断和修复流程，可以有效解决SFAP平台中的用户角色识别问题，并建立长期的预防机制。
