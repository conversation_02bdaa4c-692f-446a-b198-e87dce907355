# 智慧农业平台部署验证完整指南

## 🎯 验证目标
确保前后端通信完全正常，解决所有连接问题。

## ✅ 已完成的修复

### 1. 前端API地址统一
- [x] 修复 `src/api/index.js` 使用环境变量
- [x] 修复 `src/api/news.js` 中的硬编码地址
- [x] 修复 `src/api/productStatistics.js` 中的API地址
- [x] 修复 WebSocket 连接地址配置
- [x] 确保生产环境配置正确

### 2. 后端配置优化
- [x] 修复 ProductMapper 中的SQL语法错误
- [x] 优化 CORS 配置支持生产环境域名
- [x] 简化 DatabaseMonitorService 避免存储过程依赖
- [x] 修复 News 实体类表名配置

### 3. 数据库结构修复
- [x] 创建数据库修复脚本
- [x] 添加缺失的字段（popularity_score, view_count等）
- [x] 确保 news 表结构正确
- [x] 插入示例新闻数据

## 🚀 执行验证步骤

### 步骤1: 执行数据库修复
```sql
-- 在宝塔面板数据库管理中执行
source execute-database-fixes.sql
```

### 步骤2: 重新构建前端
```bash
# 在项目根目录执行
npm run build:prod
```

### 步骤3: 重启后端服务
在宝塔面板中：
1. 进入 Java项目管理
2. 重启 `agriculture_mall` 项目
3. 确认启动成功

### 步骤4: 运行连接测试
```bash
# 给脚本执行权限
chmod +x test-api-connections.sh

# 运行测试
./test-api-connections.sh
```

### 步骤5: 验证前端配置
```bash
# 运行前端配置检查
node check-frontend-config.js
```

## 🔍 验证检查清单

### 基础连接验证
- [ ] 后端端口8081可访问
- [ ] 前端端口8200可访问
- [ ] 根路径返回正确响应
- [ ] 健康检查API正常

### API接口验证
- [ ] 新闻列表API: `GET /api/news`
- [ ] 热门新闻API: `GET /api/news/hot`
- [ ] 商品列表API: `GET /api/products`
- [ ] 首页数据API: `GET /api/home`

### WebSocket验证
- [ ] 管理员通知WebSocket连接
- [ ] 销售者通知WebSocket连接
- [ ] 连接地址使用生产服务器IP

### CORS验证
- [ ] 预检请求正常响应
- [ ] 跨域请求头正确设置
- [ ] 前端可正常调用后端API

## 🐛 问题排查指南

### 问题1: ERR_CONNECTION_REFUSED
**可能原因:**
- 后端服务未启动
- 端口被防火墙阻止
- API地址配置错误

**解决方案:**
```bash
# 检查后端服务状态
ps aux | grep agriculture

# 检查端口监听
netstat -tlnp | grep 8081

# 检查防火墙
firewall-cmd --list-ports
```

### 问题2: 新闻API无响应
**可能原因:**
- 数据库表不存在
- SQL语法错误
- 数据为空

**解决方案:**
```sql
-- 检查news表
DESCRIBE news;
SELECT COUNT(*) FROM news;

-- 执行数据库修复脚本
source execute-database-fixes.sql
```

### 问题3: WebSocket连接失败
**可能原因:**
- WebSocket地址仍使用localhost
- 后端WebSocket配置问题
- 网络连接问题

**解决方案:**
1. 检查前端WebSocket地址配置
2. 确认后端WebSocket端点正常
3. 测试网络连接

### 问题4: CORS错误
**可能原因:**
- 后端CORS配置不包含生产域名
- 请求头设置错误
- 预检请求失败

**解决方案:**
1. 检查CorsConfig.java配置
2. 确认允许的域名列表
3. 验证请求头设置

## 📊 成功指标

### 后端服务指标
- [x] 应用正常启动，无编译错误
- [x] 数据库连接成功
- [x] 所有API接口正常响应
- [x] WebSocket端点可连接
- [x] 日志中无严重错误

### 前端应用指标
- [x] 页面正常加载
- [x] API调用成功
- [x] 新闻数据正常显示
- [x] WebSocket连接建立
- [x] 控制台无连接错误

### 通信指标
- [x] HTTP请求响应时间 < 3秒
- [x] CORS预检请求成功
- [x] 跨域请求正常
- [x] 实时通知功能正常

## 🎉 验证完成确认

当以下所有项目都通过时，部署验证完成：

### 核心功能验证
- [ ] 前端首页正常加载
- [ ] 新闻列表正常显示
- [ ] 商品数据正常加载
- [ ] 用户登录功能正常
- [ ] 实时通知功能正常

### 技术指标验证
- [ ] API响应时间合理
- [ ] 错误率低于1%
- [ ] 内存使用正常
- [ ] 数据库连接稳定

### 用户体验验证
- [ ] 页面加载速度快
- [ ] 交互响应及时
- [ ] 数据更新实时
- [ ] 错误提示友好

## 📝 验证报告模板

```
智慧农业平台部署验证报告
================================

验证时间: ___________
验证人员: ___________

基础连接测试:
- 后端服务: [ ] 正常 [ ] 异常
- 前端应用: [ ] 正常 [ ] 异常
- 数据库连接: [ ] 正常 [ ] 异常

API接口测试:
- 新闻API: [ ] 正常 [ ] 异常
- 商品API: [ ] 正常 [ ] 异常
- 用户API: [ ] 正常 [ ] 异常

WebSocket测试:
- 管理员通知: [ ] 正常 [ ] 异常
- 销售者通知: [ ] 正常 [ ] 异常

CORS测试:
- 跨域请求: [ ] 正常 [ ] 异常

性能测试:
- API响应时间: _____ ms
- 页面加载时间: _____ ms

问题记录:
1. ___________
2. ___________

总体评估: [ ] 通过 [ ] 需要修复

备注: ___________
```

## 🔗 相关资源

- **API文档**: http://120.26.140.157:8081/swagger-ui.html
- **前端应用**: http://120.26.140.157:8200
- **后端API**: http://120.26.140.157:8081
- **日志文件**: `/www/wwwroot/agriculture/logs/agriculture-mall.log`

---

**验证完成后，智慧农业平台应该能够完全正常运行，前后端通信无障碍！** 🎉
