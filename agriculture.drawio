<mxfile host="app.diagrams.net" modified="2023-06-10T10:30:00.000Z" agent="5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36" etag="3456789012" version="14.7.7" type="device">
  <diagram id="agriculture-er-diagram" name="农智汇平台ER图">
    <mxGraphModel dx="1422" dy="762" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- 标题 -->
        <mxCell id="title" value="农智汇平台ER图" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=24;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="359.5" y="20" width="450" height="40" as="geometry" />
        </mxCell>
        
        <!-- 用户实体 -->
        <mxCell id="user_entity" value="用户 (User)" style="swimlane;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;rounded=1;fontSize=14;fontStyle=1;strokeWidth=2;resizeParent=0;resizeLast=1;shadow=0;dashed=0;align=center;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="80" y="100" width="200" height="180" as="geometry" />
        </mxCell>
        <mxCell id="user_attributes" value="+ id: int (PK)&#xa;+ username: varchar(50)&#xa;+ password: varchar(100)&#xa;+ email: varchar(100)&#xa;+ phone: varchar(20)&#xa;+ role: enum&#xa;+ avatar: varchar(255)&#xa;+ createTime: datetime&#xa;+ updateTime: datetime&#xa;+ status: int" style="align=left;strokeColor=none;fillColor=none;spacingLeft=4;fontSize=12;verticalAlign=top;resizable=0;rotatable=0;part=1;" vertex="1" parent="user_entity">
          <mxGeometry y="30" width="200" height="150" as="geometry" />
        </mxCell>
        
        <!-- 地址实体 -->
        <mxCell id="address_entity" value="地址 (Address)" style="swimlane;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;rounded=1;fontSize=14;fontStyle=1;strokeWidth=2;resizeParent=0;resizeLast=1;shadow=0;dashed=0;align=center;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="80" y="330" width="200" height="170" as="geometry" />
        </mxCell>
        <mxCell id="address_attributes" value="+ id: int (PK)&#xa;+ userId: int (FK)&#xa;+ province: varchar(50)&#xa;+ city: varchar(50)&#xa;+ district: varchar(50)&#xa;+ detail: varchar(255)&#xa;+ receiver: varchar(50)&#xa;+ phone: varchar(20)&#xa;+ isDefault: boolean&#xa;+ createTime: datetime" style="align=left;strokeColor=none;fillColor=none;spacingLeft=4;fontSize=12;verticalAlign=top;resizable=0;rotatable=0;part=1;" vertex="1" parent="address_entity">
          <mxGeometry y="30" width="200" height="140" as="geometry" />
        </mxCell>
        
        <!-- 商品实体 -->
        <mxCell id="product_entity" value="商品 (Product)" style="swimlane;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;rounded=1;fontSize=14;fontStyle=1;strokeWidth=2;resizeParent=0;resizeLast=1;shadow=0;dashed=0;align=center;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="360" y="100" width="200" height="200" as="geometry" />
        </mxCell>
        <mxCell id="product_attributes" value="+ id: int (PK)&#xa;+ name: varchar(100)&#xa;+ description: text&#xa;+ price: decimal(10,2)&#xa;+ stock: int&#xa;+ image: varchar(255)&#xa;+ categoryId: int (FK)&#xa;+ unit: varchar(20)&#xa;+ createdBy: int (FK)&#xa;+ createTime: datetime&#xa;+ updateTime: datetime&#xa;+ status: int&#xa;+ salesCount: int" style="align=left;strokeColor=none;fillColor=none;spacingLeft=4;fontSize=12;verticalAlign=top;resizable=0;rotatable=0;part=1;" vertex="1" parent="product_entity">
          <mxGeometry y="30" width="200" height="170" as="geometry" />
        </mxCell>
        
        <!-- 分类实体 -->
        <mxCell id="category_entity" value="分类 (Category)" style="swimlane;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;rounded=1;fontSize=14;fontStyle=1;strokeWidth=2;resizeParent=0;resizeLast=1;shadow=0;dashed=0;align=center;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="360" y="350" width="200" height="150" as="geometry" />
        </mxCell>
        <mxCell id="category_attributes" value="+ id: int (PK)&#xa;+ name: varchar(50)&#xa;+ parentId: int&#xa;+ level: int&#xa;+ sort: int&#xa;+ icon: varchar(255)&#xa;+ createTime: datetime&#xa;+ updateTime: datetime" style="align=left;strokeColor=none;fillColor=none;spacingLeft=4;fontSize=12;verticalAlign=top;resizable=0;rotatable=0;part=1;" vertex="1" parent="category_entity">
          <mxGeometry y="30" width="200" height="120" as="geometry" />
        </mxCell>
        
        <!-- 订单实体 -->
        <mxCell id="order_entity" value="订单 (Order)" style="swimlane;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;rounded=1;fontSize=14;fontStyle=1;strokeWidth=2;resizeParent=0;resizeLast=1;shadow=0;dashed=0;align=center;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="640" y="100" width="200" height="200" as="geometry" />
        </mxCell>
        <mxCell id="order_attributes" value="+ id: int (PK)&#xa;+ orderNo: varchar(50)&#xa;+ userId: int (FK)&#xa;+ totalAmount: decimal(10,2)&#xa;+ payAmount: decimal(10,2)&#xa;+ freightAmount: decimal(10,2)&#xa;+ status: int&#xa;+ payType: int&#xa;+ payTime: datetime&#xa;+ addressId: int (FK)&#xa;+ createTime: datetime&#xa;+ updateTime: datetime&#xa;+ note: varchar(500)" style="align=left;strokeColor=none;fillColor=none;spacingLeft=4;fontSize=12;verticalAlign=top;resizable=0;rotatable=0;part=1;" vertex="1" parent="order_entity">
          <mxGeometry y="30" width="200" height="170" as="geometry" />
        </mxCell>
        
        <!-- 订单项实体 -->
        <mxCell id="order_item_entity" value="订单项 (OrderItem)" style="swimlane;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;rounded=1;fontSize=14;fontStyle=1;strokeWidth=2;resizeParent=0;resizeLast=1;shadow=0;dashed=0;align=center;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="640" y="350" width="200" height="170" as="geometry" />
        </mxCell>
        <mxCell id="order_item_attributes" value="+ id: int (PK)&#xa;+ orderId: int (FK)&#xa;+ productId: int (FK)&#xa;+ productName: varchar(100)&#xa;+ productImage: varchar(255)&#xa;+ price: decimal(10,2)&#xa;+ quantity: int&#xa;+ totalPrice: decimal(10,2)&#xa;+ createTime: datetime" style="align=left;strokeColor=none;fillColor=none;spacingLeft=4;fontSize=12;verticalAlign=top;resizable=0;rotatable=0;part=1;" vertex="1" parent="order_item_entity">
          <mxGeometry y="30" width="200" height="140" as="geometry" />
        </mxCell>
        
        <!-- 购物车实体 -->
        <mxCell id="cart_entity" value="购物车 (Cart)" style="swimlane;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;rounded=1;fontSize=14;fontStyle=1;strokeWidth=2;resizeParent=0;resizeLast=1;shadow=0;dashed=0;align=center;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="640" y="570" width="200" height="150" as="geometry" />
        </mxCell>
        <mxCell id="cart_attributes" value="+ id: int (PK)&#xa;+ userId: int (FK)&#xa;+ productId: int (FK)&#xa;+ quantity: int&#xa;+ price: decimal(10,2)&#xa;+ selected: boolean&#xa;+ createTime: datetime&#xa;+ updateTime: datetime" style="align=left;strokeColor=none;fillColor=none;spacingLeft=4;fontSize=12;verticalAlign=top;resizable=0;rotatable=0;part=1;" vertex="1" parent="cart_entity">
          <mxGeometry y="30" width="200" height="120" as="geometry" />
        </mxCell>
        
        <!-- 百科实体 -->
        <mxCell id="encyclopedia_entity" value="百科 (Encyclopedia)" style="swimlane;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;rounded=1;fontSize=14;fontStyle=1;strokeWidth=2;resizeParent=0;resizeLast=1;shadow=0;dashed=0;align=center;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="920" y="100" width="200" height="200" as="geometry" />
        </mxCell>
        <mxCell id="encyclopedia_attributes" value="+ id: int (PK)&#xa;+ title: varchar(100)&#xa;+ content: text&#xa;+ summary: varchar(255)&#xa;+ image: varchar(255)&#xa;+ categoryId: int (FK)&#xa;+ views: int&#xa;+ authorId: int (FK)&#xa;+ createTime: datetime&#xa;+ updateTime: datetime&#xa;+ status: int&#xa;+ tags: varchar(255)" style="align=left;strokeColor=none;fillColor=none;spacingLeft=4;fontSize=12;verticalAlign=top;resizable=0;rotatable=0;part=1;" vertex="1" parent="encyclopedia_entity">
          <mxGeometry y="30" width="200" height="170" as="geometry" />
        </mxCell>
        
        <!-- 百科评论实体 -->
        <mxCell id="encyclopedia_comment_entity" value="百科评论 (EncyclopediaComment)" style="swimlane;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;rounded=1;fontSize=14;fontStyle=1;strokeWidth=2;resizeParent=0;resizeLast=1;shadow=0;dashed=0;align=center;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="920" y="350" width="200" height="150" as="geometry" />
        </mxCell>
        <mxCell id="encyclopedia_comment_attributes" value="+ id: int (PK)&#xa;+ encyclopediaId: int (FK)&#xa;+ userId: int (FK)&#xa;+ content: text&#xa;+ parentId: int&#xa;+ createTime: datetime&#xa;+ updateTime: datetime&#xa;+ status: int" style="align=left;strokeColor=none;fillColor=none;spacingLeft=4;fontSize=12;verticalAlign=top;resizable=0;rotatable=0;part=1;" vertex="1" parent="encyclopedia_comment_entity">
          <mxGeometry y="30" width="200" height="120" as="geometry" />
        </mxCell>
        
        <!-- 价格数据实体 -->
        <mxCell id="price_entity" value="价格数据 (Price)" style="swimlane;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;rounded=1;fontSize=14;fontStyle=1;strokeWidth=2;resizeParent=0;resizeLast=1;shadow=0;dashed=0;align=center;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="80" y="550" width="200" height="170" as="geometry" />
        </mxCell>
        <mxCell id="price_attributes" value="+ id: int (PK)&#xa;+ productId: int (FK)&#xa;+ price: decimal(10,2)&#xa;+ region: varchar(50)&#xa;+ date: date&#xa;+ source: varchar(50)&#xa;+ volume: int&#xa;+ unit: varchar(20)&#xa;+ createTime: datetime&#xa;+ updateTime: datetime" style="align=left;strokeColor=none;fillColor=none;spacingLeft=4;fontSize=12;verticalAlign=top;resizable=0;rotatable=0;part=1;" vertex="1" parent="price_entity">
          <mxGeometry y="30" width="200" height="140" as="geometry" />
        </mxCell>
        
        <!-- 价格指数实体 -->
        <mxCell id="price_index_entity" value="价格指数 (PriceIndex)" style="swimlane;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;rounded=1;fontSize=14;fontStyle=1;strokeWidth=2;resizeParent=0;resizeLast=1;shadow=0;dashed=0;align=center;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="360" y="550" width="200" height="170" as="geometry" />
        </mxCell>
        <mxCell id="price_index_attributes" value="+ id: int (PK)&#xa;+ category: varchar(50)&#xa;+ value: decimal(10,2)&#xa;+ change: decimal(10,2)&#xa;+ trend: varchar(10)&#xa;+ date: date&#xa;+ volume: varchar(50)&#xa;+ unit: varchar(20)&#xa;+ description: varchar(255)&#xa;+ createTime: datetime" style="align=left;strokeColor=none;fillColor=none;spacingLeft=4;fontSize=12;verticalAlign=top;resizable=0;rotatable=0;part=1;" vertex="1" parent="price_index_entity">
          <mxGeometry y="30" width="200" height="140" as="geometry" />
        </mxCell>
        
        <!-- 天气数据实体 -->
        <mxCell id="weather_entity" value="天气数据 (Weather)" style="swimlane;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;rounded=1;fontSize=14;fontStyle=1;strokeWidth=2;resizeParent=0;resizeLast=1;shadow=0;dashed=0;align=center;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="920" y="550" width="200" height="190" as="geometry" />
        </mxCell>
        <mxCell id="weather_attributes" value="+ id: int (PK)&#xa;+ city: varchar(50)&#xa;+ date: date&#xa;+ weather: varchar(50)&#xa;+ temperature: decimal(5,2)&#xa;+ humidity: int&#xa;+ windSpeed: decimal(5,2)&#xa;+ windDir: varchar(20)&#xa;+ pressure: int&#xa;+ vis: decimal(5,2)&#xa;+ createTime: datetime" style="align=left;strokeColor=none;fillColor=none;spacingLeft=4;fontSize=12;verticalAlign=top;resizable=0;rotatable=0;part=1;" vertex="1" parent="weather_entity">
          <mxGeometry y="30" width="200" height="160" as="geometry" />
        </mxCell>
        
        <!-- 关系线 - 优化后使用正交连接器而非实体关系边样式 -->
        <!-- 用户-地址 -->
        <mxCell id="user_address_relation" value="" style="endArrow=ERoneToMany;startArrow=ERmandOne;fontSize=12;html=1;rounded=0;edgeStyle=orthogonalEdgeStyle;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="user_attributes" target="address_entity">
          <mxGeometry width="100" height="100" relative="1" as="geometry">
            <mxPoint x="450" y="400" as="sourcePoint" />
            <mxPoint x="550" y="300" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 用户-商品 -->
        <mxCell id="user_product_relation" value="" style="endArrow=ERoneToMany;startArrow=ERmandOne;fontSize=12;html=1;rounded=0;edgeStyle=orthogonalEdgeStyle;exitX=1;exitY=0.25;exitDx=0;exitDy=0;entryX=0;entryY=0.25;entryDx=0;entryDy=0;" edge="1" parent="1" source="user_attributes" target="product_attributes">
          <mxGeometry width="100" height="100" relative="1" as="geometry">
            <mxPoint x="450" y="400" as="sourcePoint" />
            <mxPoint x="550" y="300" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 商品-分类 -->
        <mxCell id="product_category_relation" value="" style="endArrow=ERoneToMany;startArrow=ERmandOne;fontSize=12;html=1;rounded=0;edgeStyle=orthogonalEdgeStyle;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="category_entity" target="product_attributes">
          <mxGeometry width="100" height="100" relative="1" as="geometry">
            <mxPoint x="450" y="400" as="sourcePoint" />
            <mxPoint x="550" y="300" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 用户-订单 -->
        <mxCell id="user_order_relation" value="" style="endArrow=ERoneToMany;startArrow=ERmandOne;fontSize=12;html=1;rounded=0;edgeStyle=orthogonalEdgeStyle;exitX=1;exitY=0.25;exitDx=0;exitDy=0;entryX=0;entryY=0.25;entryDx=0;entryDy=0;" edge="1" parent="1" source="user_attributes" target="order_attributes">
          <mxGeometry width="100" height="100" relative="1" as="geometry">
            <mxPoint x="450" y="400" as="sourcePoint" />
            <mxPoint x="550" y="300" as="targetPoint" />
            <Array as="points">
              <mxPoint x="320" y="118" />
              <mxPoint x="320" y="73" />
              <mxPoint x="620" y="73" />
              <mxPoint x="620" y="118" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <!-- 订单-订单项 -->
        <mxCell id="order_orderitem_relation" value="" style="endArrow=ERoneToMany;startArrow=ERmandOne;fontSize=12;html=1;rounded=0;edgeStyle=orthogonalEdgeStyle;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="order_attributes" target="order_item_entity">
          <mxGeometry width="100" height="100" relative="1" as="geometry">
            <mxPoint x="450" y="400" as="sourcePoint" />
            <mxPoint x="550" y="300" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 商品-订单项 -->
        <mxCell id="product_orderitem_relation" value="" style="endArrow=ERoneToMany;startArrow=ERmandOne;fontSize=12;html=1;rounded=0;edgeStyle=orthogonalEdgeStyle;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="product_attributes" target="order_item_attributes">
          <mxGeometry width="100" height="100" relative="1" as="geometry">
            <mxPoint x="450" y="400" as="sourcePoint" />
            <mxPoint x="550" y="300" as="targetPoint" />
            <Array as="points">
              <mxPoint x="600" y="185" />
              <mxPoint x="600" y="445" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <!-- 用户-购物车 -->
        <mxCell id="user_cart_relation" value="" style="endArrow=ERoneToMany;startArrow=ERmandOne;fontSize=12;html=1;rounded=0;edgeStyle=orthogonalEdgeStyle;exitX=0;exitY=0.75;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="user_attributes" target="cart_attributes">
          <mxGeometry width="100" height="100" relative="1" as="geometry">
            <mxPoint x="450" y="400" as="sourcePoint" />
            <mxPoint x="550" y="300" as="targetPoint" />
            <Array as="points">
              <mxPoint x="40" y="143" />
              <mxPoint x="40" y="640" />
              <mxPoint x="640" y="640" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <!-- 商品-购物车 -->
        <mxCell id="product_cart_relation" value="" style="endArrow=ERoneToMany;startArrow=ERmandOne;fontSize=12;html=1;rounded=0;edgeStyle=orthogonalEdgeStyle;exitX=0.75;exitY=1;exitDx=0;exitDy=0;entryX=0.25;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="product_attributes" target="cart_entity">
          <mxGeometry width="100" height="100" relative="1" as="geometry">
            <mxPoint x="450" y="400" as="sourcePoint" />
            <mxPoint x="550" y="300" as="targetPoint" />
            <Array as="points">
              <mxPoint x="510" y="300" />
              <mxPoint x="510" y="520" />
              <mxPoint x="690" y="520" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <!-- 用户-百科 -->
        <mxCell id="user_encyclopedia_relation" value="" style="endArrow=ERoneToMany;startArrow=ERmandOne;fontSize=12;html=1;rounded=0;edgeStyle=orthogonalEdgeStyle;exitX=0.75;exitY=0;exitDx=0;exitDy=0;entryX=0;entryY=0.25;entryDx=0;entryDy=0;" edge="1" parent="1" source="user_entity" target="encyclopedia_attributes">
          <mxGeometry width="100" height="100" relative="1" as="geometry">
            <mxPoint x="450" y="400" as="sourcePoint" />
            <mxPoint x="550" y="300" as="targetPoint" />
            <Array as="points">
              <mxPoint x="230" y="80" />
              <mxPoint x="860" y="80" />
              <mxPoint x="860" y="173" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <!-- 百科-评论 -->
        <mxCell id="encyclopedia_comment_relation" value="" style="endArrow=ERoneToMany;startArrow=ERmandOne;fontSize=12;html=1;rounded=0;edgeStyle=orthogonalEdgeStyle;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="encyclopedia_attributes" target="encyclopedia_comment_entity">
          <mxGeometry width="100" height="100" relative="1" as="geometry">
            <mxPoint x="450" y="400" as="sourcePoint" />
            <mxPoint x="550" y="300" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 用户-评论 -->
        <mxCell id="user_comment_relation" value="" style="endArrow=ERoneToMany;startArrow=ERmandOne;fontSize=12;html=1;rounded=0;edgeStyle=orthogonalEdgeStyle;exitX=1;exitY=0.75;exitDx=0;exitDy=0;entryX=0;entryY=0.75;entryDx=0;entryDy=0;" edge="1" parent="1" source="user_attributes" target="encyclopedia_comment_attributes">
          <mxGeometry width="100" height="100" relative="1" as="geometry">
            <mxPoint x="450" y="400" as="sourcePoint" />
            <mxPoint x="550" y="300" as="targetPoint" />
            <Array as="points">
              <mxPoint x="320" y="143" />
              <mxPoint x="320" y="470" />
              <mxPoint x="880" y="470" />
              <mxPoint x="880" y="420" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <!-- 商品-价格 -->
        <mxCell id="product_price_relation" value="" style="endArrow=ERoneToMany;startArrow=ERmandOne;fontSize=12;html=1;rounded=0;edgeStyle=orthogonalEdgeStyle;exitX=0.25;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="product_attributes" target="price_entity">
          <mxGeometry width="100" height="100" relative="1" as="geometry">
            <mxPoint x="450" y="400" as="sourcePoint" />
            <mxPoint x="550" y="300" as="targetPoint" />
            <Array as="points">
              <mxPoint x="410" y="520" />
              <mxPoint x="180" y="520" />
            </Array>
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>