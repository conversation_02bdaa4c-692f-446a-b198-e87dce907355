-- =====================================================
-- SFAP用户认证系统完整修复脚本
-- 修复登录认证失败和角色不一致问题
-- =====================================================

-- 1. 检查所有用户的当前状态
SELECT
    '=== 所有用户状态检查 ===' as section,
    NULL as id, NULL as username, NULL as password_length, NULL as password_prefix, NULL as role, NULL as user_type, NULL as status;

SELECT
    id, username,
    CHAR_LENGTH(password) as password_length,
    LEFT(password, 10) as password_prefix,
    role, user_type, status,
    CASE
        WHEN password LIKE '$2a$%' OR password LIKE '$2b$%' OR password LIKE '$2y$%' THEN 'BCrypt'
        WHEN CHAR_LENGTH(password) = 32 THEN 'MD5'
        WHEN CHAR_LENGTH(password) < 20 THEN 'Plain'
        ELSE 'Unknown'
    END as password_format,
    CASE
        WHEN role != COALESCE(user_type, 'normal') THEN 'Role Mismatch'
        WHEN status != 1 THEN 'Disabled'
        ELSE 'OK'
    END as issue_status,
    created_at, updated_at
FROM user
WHERE deleted = 0
ORDER BY
    CASE WHEN username LIKE '%admin%' THEN 0 ELSE 1 END,
    id;

-- 2. 为所有测试用户设置标准密码
-- 密码 "12341234" 的BCrypt加密结果: $2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi

-- 2.1 创建或更新test_user
INSERT INTO user (username, password, nickname, role, user_type, status, created_at, updated_at, deleted)
VALUES ('test_user', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'test_user', 'user', 'normal', 1, NOW(), NOW(), 0)
ON DUPLICATE KEY UPDATE
    password = '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
    role = 'user',
    user_type = 'normal',
    status = 1,
    updated_at = NOW();

-- 2.2 创建或更新test_user2
INSERT INTO user (username, password, nickname, role, user_type, status, created_at, updated_at, deleted)
VALUES ('test_user2', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'test_user2', 'user', 'normal', 1, NOW(), NOW(), 0)
ON DUPLICATE KEY UPDATE
    password = '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
    role = 'user',
    user_type = 'normal',
    status = 1,
    updated_at = NOW();

-- 3. 修复管理员用户权限
-- 3.1 修复ID为18的管理员用户
UPDATE user
SET role = 'admin',
    user_type = 'admin',
    status = 1,
    updated_at = NOW()
WHERE id = 18 AND deleted = 0;

-- 3.2 修复所有admin用户的权限
UPDATE user
SET role = 'admin',
    user_type = 'admin',
    status = 1,
    updated_at = NOW()
WHERE username LIKE '%admin%' AND deleted = 0;

-- 4. 修复所有角色不一致的用户
UPDATE user
SET role = CASE
    WHEN username LIKE '%admin%' THEN 'admin'
    WHEN user_type = 'seller' OR role = 'seller' THEN 'seller'
    WHEN user_type = 'admin' OR role = 'admin' THEN 'admin'
    ELSE 'user'
END,
user_type = CASE
    WHEN username LIKE '%admin%' THEN 'admin'
    WHEN user_type = 'seller' OR role = 'seller' THEN 'seller'
    WHEN user_type = 'admin' OR role = 'admin' THEN 'admin'
    ELSE 'normal'
END,
updated_at = NOW()
WHERE deleted = 0
  AND (role IS NULL OR user_type IS NULL OR
       (role = 'user' AND user_type != 'normal') OR
       (role = 'admin' AND user_type != 'admin') OR
       (role = 'seller' AND user_type != 'seller') OR
       (user_type = 'normal' AND role != 'user') OR
       (user_type = 'admin' AND role != 'admin') OR
       (user_type = 'seller' AND role != 'seller'));

-- 5. 确保所有用户状态正常
UPDATE user
SET status = 1,
    updated_at = NOW()
WHERE status != 1 AND deleted = 0;

-- 6. 验证修复结果
SELECT
    '=== 修复结果验证 ===' as section,
    NULL as id, NULL as username, NULL as role, NULL as user_type, NULL as status, NULL as password_format, NULL as role_status;

SELECT
    id, username, role, user_type, status,
    CASE
        WHEN password LIKE '$2a$%' OR password LIKE '$2b$%' OR password LIKE '$2y$%' THEN 'BCrypt'
        ELSE 'Non-BCrypt'
    END as password_format,
    CASE
        WHEN role = 'admin' AND user_type = 'admin' THEN '✅ 管理员'
        WHEN role = 'seller' AND user_type = 'seller' THEN '✅ 销售者'
        WHEN role = 'user' AND user_type = 'normal' THEN '✅ 普通用户'
        ELSE '❌ 角色不一致'
    END as role_status
FROM user
WHERE deleted = 0
ORDER BY
    CASE WHEN username LIKE '%admin%' THEN 0 ELSE 1 END,
    id;

-- 7. 特别验证关键用户
SELECT
    '=== 关键用户验证 ===' as section,
    NULL as username, NULL as id, NULL as role, NULL as user_type, NULL as status, NULL as verification_status;

SELECT
    username, id, role, user_type, status,
    CASE
        WHEN username = 'test_user' AND role = 'user' AND user_type = 'normal' AND status = 1 THEN '✅ test_user正常'
        WHEN username = 'test_user2' AND role = 'user' AND user_type = 'normal' AND status = 1 THEN '✅ test_user2正常'
        WHEN username LIKE '%admin%' AND role = 'admin' AND user_type = 'admin' AND status = 1 THEN '✅ 管理员正常'
        WHEN id = 18 AND role = 'admin' AND user_type = 'admin' AND status = 1 THEN '✅ ID18管理员正常'
        ELSE '❌ 需要检查'
    END as verification_status
FROM user
WHERE (username IN ('test_user', 'test_user2') OR username LIKE '%admin%' OR id = 18)
  AND deleted = 0
ORDER BY username;

-- 8. 最终统计
SELECT
    '=== 用户角色分布统计 ===' as section,
    NULL as role_type, NULL as count, NULL as percentage;

SELECT
    CONCAT(role, '/', user_type) as role_type,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM user WHERE deleted = 0), 2) as percentage
FROM user
WHERE deleted = 0
GROUP BY role, user_type
ORDER BY count DESC;

-- 9. 密码格式统计
SELECT
    '=== 密码格式统计 ===' as section,
    NULL as format_type, NULL as count, NULL as percentage;

SELECT
    CASE
        WHEN password LIKE '$2a$%' OR password LIKE '$2b$%' OR password LIKE '$2y$%' THEN 'BCrypt'
        WHEN CHAR_LENGTH(password) = 32 THEN 'MD5'
        WHEN CHAR_LENGTH(password) < 20 THEN 'Plain'
        ELSE 'Unknown'
    END as format_type,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM user WHERE deleted = 0), 2) as percentage
FROM user
WHERE deleted = 0
GROUP BY format_type
ORDER BY count DESC;

-- 修复完成提示
SELECT '=== 修复完成 ===' as message,
       '所有用户密码和角色已修复，可以正常登录' as status;
