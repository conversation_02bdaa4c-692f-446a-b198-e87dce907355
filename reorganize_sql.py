#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SFAP农品汇平台 - SQL文件重组和修复脚本
解决字段名不匹配和依赖关系错误
"""

import re
import os
import shutil
from datetime import datetime
from collections import defaultdict

class SQLReorganizer:
    def __init__(self, input_file):
        self.input_file = input_file
        self.content = ""
        self.tables = []
        self.views = []
        self.procedures = []
        self.triggers = []
        self.indexes = []
        self.constraints = []
        self.inserts = defaultdict(list)
        
    def read_file(self):
        """读取SQL文件"""
        with open(self.input_file, 'r', encoding='utf-8') as f:
            self.content = f.read()
        print(f"✅ 已读取文件: {self.input_file}")
        print(f"📄 文件大小: {len(self.content)} 字符")
    
    def parse_sql_statements(self):
        """解析SQL语句"""
        print("🔍 正在解析SQL语句...")
        
        # 分割SQL语句
        statements = re.split(r';\s*\n', self.content)
        
        for stmt in statements:
            stmt = stmt.strip()
            if not stmt:
                continue
                
            # 表结构
            if re.match(r'CREATE TABLE', stmt, re.IGNORECASE):
                self.tables.append(stmt)
            
            # 视图
            elif re.match(r'CREATE.*VIEW', stmt, re.IGNORECASE):
                self.views.append(stmt)
            
            # 存储过程和函数
            elif re.match(r'CREATE.*PROCEDURE|CREATE.*FUNCTION', stmt, re.IGNORECASE):
                self.procedures.append(stmt)
            
            # 触发器
            elif re.match(r'CREATE.*TRIGGER', stmt, re.IGNORECASE):
                self.triggers.append(stmt)
            
            # 索引
            elif re.match(r'CREATE.*INDEX', stmt, re.IGNORECASE):
                self.indexes.append(stmt)
            
            # 约束
            elif re.match(r'ALTER TABLE.*ADD CONSTRAINT', stmt, re.IGNORECASE):
                self.constraints.append(stmt)
            
            # 插入数据
            elif re.match(r'INSERT INTO', stmt, re.IGNORECASE):
                table_match = re.search(r'INSERT INTO\s+`?(\w+)`?', stmt, re.IGNORECASE)
                if table_match:
                    table_name = table_match.group(1)
                    self.inserts[table_name].append(stmt)
        
        print(f"📊 解析结果:")
        print(f"   - 表结构: {len(self.tables)} 个")
        print(f"   - 视图: {len(self.views)} 个")
        print(f"   - 存储过程: {len(self.procedures)} 个")
        print(f"   - 触发器: {len(self.triggers)} 个")
        print(f"   - 索引: {len(self.indexes)} 个")
        print(f"   - 约束: {len(self.constraints)} 个")
        print(f"   - 数据插入: {len(self.inserts)} 个表")
    
    def fix_view_field_names(self):
        """修复视图中的字段名问题"""
        print("🔧 正在修复视图字段名...")
        
        field_mappings = {
            'is_deleted': 'deleted',
            'is_active': 'status',
            'is_enabled': 'status'
        }
        
        fixed_views = []
        for view in self.views:
            original_view = view
            for old_field, new_field in field_mappings.items():
                # 修复字段名
                view = re.sub(rf'`?{old_field}`?', f'`{new_field}`', view)
                view = re.sub(rf'\.{old_field}', f'.{new_field}', view)
            
            if view != original_view:
                print(f"   ✅ 修复了视图字段名")
            
            fixed_views.append(view)
        
        self.views = fixed_views
    
    def sort_tables_by_dependencies(self):
        """根据外键依赖关系排序表"""
        print("🔄 正在排序表的创建顺序...")
        
        # 定义表的依赖关系（基础表在前，依赖表在后）
        table_order = [
            'user',
            'category', 
            'product',
            'address',
            'order',
            'order_item',
            'cart',
            'cart_item',
            'product_review',
            'product_favorite',
            'traceability_record',
            'seller_application',
            'seller_shop',
            'price_data',
            'agriculture_news',
            'news_images',
            'encyclopedia',
            'user_search_history',
            'system_config',
            'api_log',
            'error_log'
        ]
        
        # 按照依赖顺序重新排列表
        sorted_tables = []
        remaining_tables = self.tables.copy()
        
        for table_name in table_order:
            for table_stmt in remaining_tables:
                if f'CREATE TABLE `{table_name}`' in table_stmt or f'CREATE TABLE {table_name}' in table_stmt:
                    sorted_tables.append(table_stmt)
                    remaining_tables.remove(table_stmt)
                    break
        
        # 添加剩余的表
        sorted_tables.extend(remaining_tables)
        self.tables = sorted_tables
        
        print(f"   ✅ 已排序 {len(self.tables)} 个表")
    
    def generate_reorganized_sql(self):
        """生成重组后的SQL文件"""
        print("📝 正在生成重组后的SQL文件...")
        
        output_lines = []
        
        # 文件头部
        output_lines.extend([
            "/*",
            " SFAP农品汇平台数据库 - 重组修复版",
            f" 重组时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            " 修复内容:",
            " - 字段名不匹配问题",
            " - 依赖关系排序",
            " - SQL语句结构优化",
            "*/",
            "",
            "SET NAMES utf8mb4;",
            "SET FOREIGN_KEY_CHECKS = 0;",
            "SET SQL_MODE = 'NO_AUTO_VALUE_ON_ZERO';",
            "SET AUTOCOMMIT = 0;",
            "START TRANSACTION;",
            "",
            "-- =====================================================",
            "-- 1. 数据库和表结构创建阶段",
            "-- =====================================================",
            ""
        ])
        
        # 1. 表结构
        for i, table in enumerate(self.tables, 1):
            output_lines.append(f"-- -----------------------------------------------------")
            output_lines.append(f"-- 表 {i}: {self._extract_table_name(table)}")
            output_lines.append(f"-- -----------------------------------------------------")
            output_lines.append(table + ";")
            output_lines.append("")
        
        # 2. 索引和约束
        if self.indexes or self.constraints:
            output_lines.extend([
                "-- =====================================================",
                "-- 2. 索引和约束创建阶段", 
                "-- =====================================================",
                ""
            ])
            
            for index in self.indexes:
                output_lines.append(index + ";")
                output_lines.append("")
            
            for constraint in self.constraints:
                output_lines.append(constraint + ";")
                output_lines.append("")
        
        # 3. 视图和存储过程
        if self.views or self.procedures:
            output_lines.extend([
                "-- =====================================================",
                "-- 3. 视图和存储过程创建阶段",
                "-- =====================================================",
                ""
            ])
            
            for view in self.views:
                output_lines.append("-- 视图定义")
                output_lines.append(view + ";")
                output_lines.append("")
            
            for procedure in self.procedures:
                output_lines.append("-- 存储过程定义")
                output_lines.append(procedure + ";")
                output_lines.append("")
        
        # 4. 触发器
        if self.triggers:
            output_lines.extend([
                "-- =====================================================",
                "-- 4. 触发器创建阶段",
                "-- =====================================================",
                ""
            ])
            
            for trigger in self.triggers:
                output_lines.append(trigger + ";")
                output_lines.append("")
        
        # 5. 数据插入
        if self.inserts:
            output_lines.extend([
                "-- =====================================================",
                "-- 5. 数据插入阶段",
                "-- =====================================================",
                ""
            ])
            
            # 按照表的依赖顺序插入数据
            table_order = ['user', 'category', 'product', 'address', 'order', 'order_item']
            
            for table_name in table_order:
                if table_name in self.inserts:
                    output_lines.append(f"-- {table_name} 表数据")
                    for insert_stmt in self.inserts[table_name]:
                        output_lines.append(insert_stmt + ";")
                    output_lines.append("")
            
            # 插入其他表的数据
            for table_name, insert_stmts in self.inserts.items():
                if table_name not in table_order:
                    output_lines.append(f"-- {table_name} 表数据")
                    for insert_stmt in insert_stmts:
                        output_lines.append(insert_stmt + ";")
                    output_lines.append("")
        
        # 文件结尾
        output_lines.extend([
            "-- =====================================================",
            "-- 完成重组",
            "-- =====================================================",
            "",
            "COMMIT;",
            "SET FOREIGN_KEY_CHECKS = 1;",
            "",
            "-- 重组完成"
        ])
        
        return "\n".join(output_lines)
    
    def _extract_table_name(self, table_stmt):
        """提取表名"""
        match = re.search(r'CREATE TABLE\s+`?(\w+)`?', table_stmt, re.IGNORECASE)
        return match.group(1) if match else "unknown"
    
    def save_reorganized_file(self, output_content):
        """保存重组后的文件"""
        # 创建备份
        backup_file = f"{self.input_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(self.input_file, backup_file)
        print(f"✅ 已创建备份: {backup_file}")
        
        # 保存重组后的文件
        output_file = self.input_file.replace('.sql', '_reorganized.sql')
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(output_content)
        
        print(f"✅ 重组完成: {output_file}")
        return output_file

def main():
    """主函数"""
    print("=" * 60)
    print("🛠️  SFAP农品汇平台 - SQL文件重组修复工具")
    print("=" * 60)
    
    input_file = r"E:\计算机设计大赛2\V4.0\新建文件夹\新建文件夹\SFAP\backend\agriculture_mall.sql"
    
    if not os.path.exists(input_file):
        print(f"❌ 错误: 找不到文件 {input_file}")
        return
    
    # 创建重组器
    reorganizer = SQLReorganizer(input_file)
    
    try:
        # 执行重组步骤
        reorganizer.read_file()
        reorganizer.parse_sql_statements()
        reorganizer.fix_view_field_names()
        reorganizer.sort_tables_by_dependencies()
        
        # 生成重组后的内容
        reorganized_content = reorganizer.generate_reorganized_sql()
        
        # 保存文件
        output_file = reorganizer.save_reorganized_file(reorganized_content)
        
        print("\n" + "=" * 60)
        print("🎉 SQL文件重组完成!")
        print("=" * 60)
        print(f"📁 输出文件: {output_file}")
        print("\n📋 修复内容:")
        print("✅ 修复了视图字段名不匹配问题")
        print("✅ 按依赖关系重新排序了表结构")
        print("✅ 优化了SQL语句执行顺序")
        print("✅ 添加了详细的注释说明")
        
        print("\n💡 使用重组后的文件导入:")
        print(f"mysql -u root -p < \"{output_file}\"")
        
    except Exception as e:
        print(f"❌ 重组过程中发生错误: {str(e)}")

if __name__ == "__main__":
    main()
