# 智慧农业平台生产环境部署检查清单

## 🚀 部署前准备

### 1. 服务器环境检查
- [ ] 服务器IP: **************
- [ ] Java 17 已安装并配置
- [ ] MySQL 服务正常运行
- [ ] Redis 服务正常运行
- [ ] 宝塔面板已安装

### 2. 数据库配置
- [ ] 数据库 `agriculture_mall` 已创建
- [ ] 数据库用户权限已配置
- [ ] 执行权限修复脚本: `fix-database-permissions.sql`
- [ ] 测试数据库连接

### 3. 目录权限设置
```bash
# 执行部署脚本
chmod +x deploy-setup.sh
./deploy-setup.sh
```

## 🔧 配置文件优化

### 1. application.yml 主要修改
- [x] 激活生产环境: `spring.profiles.active: prod`
- [x] 数据库连接优化
- [x] 文件上传路径配置
- [x] CORS跨域配置
- [x] 日志配置优化

### 2. 关键配置项
```yaml
# 数据库连接
spring.datasource.url: ********************************************
spring.datasource.username: root
spring.datasource.password: fan13965711955

# 文件上传路径
file.upload.path: /www/wwwroot/agriculture/uploads

# 静态资源路径
spring.web.resources.static-locations: file:/www/wwwroot/agriculture/uploads/
```

## 🌐 网络访问配置

### 1. 端口开放
- [ ] 8081 (后端API端口)
- [ ] 8200 (前端访问端口)
- [ ] 3306 (MySQL端口，仅内网)
- [ ] 6379 (Redis端口，仅内网)

### 2. CORS跨域配置
已配置允许的域名：
- http://**************
- http://**************:8200
- http://**************:8081
- https://**************
- https://**************:8200

### 3. 防火墙设置
```bash
# 开放必要端口
firewall-cmd --permanent --add-port=8081/tcp
firewall-cmd --permanent --add-port=8200/tcp
firewall-cmd --reload
```

## 📁 文件结构检查

### 1. 后端文件位置
```
/www/wwwroot/test.com/backend/target/
├── agriculture-mall-1.0.0.jar
└── classes/
```

### 2. 上传目录结构
```
/www/wwwroot/agriculture/uploads/
├── avatars/          # 用户头像
├── products/         # 商品图片
├── qrcodes/          # 溯源二维码
└── seller/           # 销售者文件
```

### 3. 日志目录
```
/www/wwwroot/agriculture/logs/
└── agriculture-mall.log
```

## 🔍 部署验证

### 1. 服务启动检查
- [ ] 在宝塔面板中启动Java项目
- [ ] 检查进程是否正常运行: `ps aux | grep agriculture`
- [ ] 检查端口监听: `netstat -tlnp | grep 8081`

### 2. API接口测试
```bash
# 测试根路径
curl http://**************:8081/

# 测试健康检查
curl http://**************:8081/health

# 测试API信息
curl http://**************:8081/api
```

### 3. 数据库连接测试
- [ ] 查看启动日志中的数据库连接信息
- [ ] 确认没有 "Access denied" 错误
- [ ] 确认 HikariPool 启动成功

### 4. 文件上传测试
- [ ] 检查上传目录权限
- [ ] 测试文件上传功能
- [ ] 验证静态资源访问

## 🐛 常见问题排查

### 1. 数据库连接失败
```sql
-- 执行权限修复
GRANT ALL PRIVILEGES ON agriculture_mall.* TO 'root'@'%';
FLUSH PRIVILEGES;
```

### 2. 端口被占用
```bash
# 查看端口占用
lsof -i :8081
# 杀死占用进程
kill -9 <PID>
```

### 3. 文件权限问题
```bash
# 修复权限
chown -R www:www /www/wwwroot/agriculture/
chmod -R 755 /www/wwwroot/agriculture/uploads/
```

### 4. 跨域访问问题
- 检查CORS配置是否包含前端域名
- 确认浏览器控制台没有CORS错误
- 验证OPTIONS预检请求正常

## 📊 监控和维护

### 1. 日志监控
```bash
# 实时查看日志
tail -f /www/wwwroot/agriculture/logs/agriculture-mall.log

# 查看错误日志
grep ERROR /www/wwwroot/agriculture/logs/agriculture-mall.log
```

### 2. 性能监控
- [ ] 监控CPU和内存使用率
- [ ] 监控数据库连接池状态
- [ ] 监控API响应时间

### 3. 定期维护
- [ ] 定期清理日志文件
- [ ] 定期备份数据库
- [ ] 定期更新系统补丁

## ✅ 部署完成确认

- [ ] 后端服务正常启动
- [ ] 数据库连接正常
- [ ] API接口可正常访问
- [ ] 文件上传功能正常
- [ ] 跨域配置正确
- [ ] 日志记录正常
- [ ] 前端可正常访问后端API

---

**部署完成时间**: ___________  
**部署人员**: ___________  
**验证人员**: ___________
