name: SFAP Gitee Go CI/CD

trigger:
  push:
    branches:
      - main
      - develop
  pull_request:
    branches:
      - main

variables:
  NODE_VERSION: '16'
  JAVA_VERSION: '11'
  PYTHON_VERSION: '3.9'

stages:
  # 代码检查和测试阶段
  - stage: test
    displayName: '代码检查和测试'
    jobs:
      - job: frontend_test
        displayName: '前端测试'
        pool:
          vmImage: 'ubuntu-latest'
        steps:
          - task: NodeTool@0
            inputs:
              versionSpec: $(NODE_VERSION)
            displayName: '安装 Node.js'

          - script: |
              npm ci
              npm run lint
              npm run test:unit
            displayName: '前端代码检查和测试'

          - script: npm run build
            displayName: '构建前端'

          - task: PublishBuildArtifacts@1
            inputs:
              pathToPublish: 'dist'
              artifactName: 'frontend-dist'
            displayName: '发布前端构建产物'

      - job: backend_test
        displayName: '后端测试'
        pool:
          vmImage: 'ubuntu-latest'
        steps:
          - task: JavaToolInstaller@0
            inputs:
              versionSpec: $(JAVA_VERSION)
              jdkArchitectureOption: 'x64'
              jdkSourceOption: 'PreInstalled'
            displayName: '安装 Java'

          - script: |
              cd backend
              mvn clean compile
              mvn test
              mvn package -DskipTests
            displayName: '后端编译、测试和打包'

          - task: PublishBuildArtifacts@1
            inputs:
              pathToPublish: 'backend/target'
              artifactName: 'backend-jar'
            displayName: '发布后端构建产物'

      - job: ai_service_test
        displayName: 'AI服务测试'
        pool:
          vmImage: 'ubuntu-latest'
        steps:
          - task: UsePythonVersion@0
            inputs:
              versionSpec: $(PYTHON_VERSION)
            displayName: '安装 Python'

          - script: |
              cd ai-service
              pip install -r requirements.txt
              python -m pytest tests/ || echo "No tests found"
            displayName: 'AI服务测试'

  # 构建Docker镜像阶段
  - stage: build
    displayName: '构建Docker镜像'
    dependsOn: test
    condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/main'))
    jobs:
      - job: docker_build
        displayName: '构建Docker镜像'
        pool:
          vmImage: 'ubuntu-latest'
        steps:
          - task: DownloadBuildArtifacts@0
            inputs:
              buildType: 'current'
              downloadType: 'specific'
              artifactName: 'frontend-dist'
              downloadPath: '$(System.ArtifactsDirectory)'
            displayName: '下载前端构建产物'

          - task: DownloadBuildArtifacts@0
            inputs:
              buildType: 'current'
              downloadType: 'specific'
              artifactName: 'backend-jar'
              downloadPath: '$(System.ArtifactsDirectory)'
            displayName: '下载后端构建产物'

          - script: |
              cp -r $(System.ArtifactsDirectory)/frontend-dist/* dist/
              cp -r $(System.ArtifactsDirectory)/backend-jar/* backend/target/
            displayName: '复制构建产物'

          - task: Docker@2
            inputs:
              containerRegistry: 'docker-hub-connection'
              repository: '$(DOCKER_USERNAME)/sfap-frontend'
              command: 'buildAndPush'
              Dockerfile: './deploy/frontend-Dockerfile'
              tags: 'latest'
            displayName: '构建并推送前端镜像'

          - task: Docker@2
            inputs:
              containerRegistry: 'docker-hub-connection'
              repository: '$(DOCKER_USERNAME)/sfap-backend'
              command: 'buildAndPush'
              Dockerfile: './deploy/backend-Dockerfile'
              buildContext: './backend'
              tags: 'latest'
            displayName: '构建并推送后端镜像'

          - task: Docker@2
            inputs:
              containerRegistry: 'docker-hub-connection'
              repository: '$(DOCKER_USERNAME)/sfap-ai-service'
              command: 'buildAndPush'
              Dockerfile: './deploy/ai-service-Dockerfile'
              buildContext: './ai-service'
              tags: 'latest'
            displayName: '构建并推送AI服务镜像'

  # 部署阶段
  - stage: deploy
    displayName: '部署到生产环境'
    dependsOn: build
    condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/main'))
    jobs:
      - deployment: deploy_production
        displayName: '生产环境部署'
        pool:
          vmImage: 'ubuntu-latest'
        environment: 'production'
        strategy:
          runOnce:
            deploy:
              steps:
                - script: |
                    echo "部署到生产服务器..."
                    # 这里添加实际的部署脚本
                    ssh $(PROD_USER)@$(PROD_HOST) "cd /opt/sfap && docker-compose pull && docker-compose down && docker-compose up -d"
                  displayName: '执行部署'

                - script: |
                    echo "清理旧的Docker镜像..."
                    ssh $(PROD_USER)@$(PROD_HOST) "docker system prune -f"
                  displayName: '清理资源'
