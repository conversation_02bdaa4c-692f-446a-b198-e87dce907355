import requests
import json

# 测试特定的溯源码
trace_code = "SFAPS25072400196030WNXF2"
url = "http://localhost:8081/api/traceability/query"

data = {
    "traceCode": trace_code,
    "source": "web",
    "location": "未知位置",
    "ipAddress": "127.0.0.1",
    "deviceInfo": "Test Browser",
    "userId": None
}

print(f"测试溯源码: {trace_code}")
print(f"API地址: {url}")
print(f"请求数据: {json.dumps(data, indent=2, ensure_ascii=False)}")

try:
    response = requests.post(url, json=data, headers={'Content-Type': 'application/json'})
    print(f"\n状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print("✅ 查询成功!")
        print(f"产品名称: {result['data']['productName']}")
        print(f"生产者: {result['data']['producerName']}")
        print(f"状态: {result['data']['status']}")
        print(f"二维码URL: {result['data']['qrCodeUrl']}")
    else:
        print("❌ 查询失败!")
        print(f"响应内容: {response.text}")
        
except Exception as e:
    print(f"❌ 请求失败: {e}")