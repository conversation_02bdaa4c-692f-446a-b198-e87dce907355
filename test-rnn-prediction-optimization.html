<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RNN预测功能优化验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e4e7ed;
            border-radius: 6px;
        }
        .test-title {
            color: #409eff;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
        }
        .success {
            color: #67c23a;
        }
        .error {
            color: #f56c6c;
        }
        .warning {
            color: #e6a23c;
        }
        .loading {
            color: #409eff;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f0f9ff;
            border-left: 4px solid #409eff;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        button {
            background: #409eff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #66b1ff;
        }
        .optimization-item {
            padding: 10px;
            margin: 10px 0;
            border-left: 4px solid #67c23a;
            background: #f0f9ff;
        }
        .issue-item {
            padding: 10px;
            margin: 10px 0;
            border-left: 4px solid #f56c6c;
            background: #fef0f0;
        }
        .product-list {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>SFAP农品汇平台 - RNN预测功能优化验证</h1>
        
        <div class="test-section">
            <div class="test-title">优化内容总结</div>
            
            <div class="optimization-item">
                <h4>✅ 1. RNN预测数据流修复</h4>
                <ul>
                    <li>修复Vue响应式对象问题，使用深拷贝避免数据访问异常</li>
                    <li>标准化预测结果数据格式，统一字段名称</li>
                    <li>添加默认置信度和置信区间计算</li>
                    <li>兼容predicted_price和price字段</li>
                </ul>
            </div>
            
            <div class="optimization-item">
                <h4>✅ 2. 农产品分类数据优化</h4>
                <ul>
                    <li>将通用分类映射为具体有数据的产品</li>
                    <li>按价格数据量排序显示产品</li>
                    <li>显示产品的分类信息和数据统计</li>
                    <li>过滤无价格数据的产品</li>
                </ul>
            </div>
            
            <div class="optimization-item">
                <h4>✅ 3. 数据验证增强</h4>
                <ul>
                    <li>预测前检查产品是否有足够历史数据</li>
                    <li>提供数据不足时的明确提示</li>
                    <li>显示产品的最新数据日期</li>
                    <li>智能推荐有数据的产品组合</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">1. 数据库产品分析</div>
            <button onclick="analyzeProductData()">分析产品数据分布</button>
            <div id="product-analysis-results"></div>
        </div>

        <div class="test-section">
            <div class="test-title">2. RNN预测功能测试</div>
            <button onclick="testRNNPrediction()">测试RNN预测</button>
            <button onclick="testMultipleProducts()">测试多个产品预测</button>
            <div id="rnn-test-results"></div>
        </div>

        <div class="test-section">
            <div class="test-title">3. 前端数据格式验证</div>
            <button onclick="testDataFormats()">验证数据格式</button>
            <div id="format-test-results"></div>
        </div>

        <div class="test-section">
            <div class="test-title">4. 产品映射测试</div>
            <button onclick="testProductMapping()">测试产品映射</button>
            <div id="mapping-test-results"></div>
        </div>

        <div class="test-section">
            <div class="test-title">前端测试指南</div>
            <div>
                <h4>验证步骤：</h4>
                <ol>
                    <li><strong>访问预测页面</strong>: <a href="http://localhost:8080/#/ai/prediction" target="_blank">http://localhost:8080/#/ai/prediction</a></li>
                    <li><strong>验证产品选择器</strong>:
                        <ul>
                            <li>应显示具体产品名称而非通用分类</li>
                            <li>产品按数据量排序显示</li>
                            <li>显示产品的分类和数据统计信息</li>
                        </ul>
                    </li>
                    <li><strong>验证预测功能</strong>:
                        <ul>
                            <li>选择有数据的产品（如"桑黄"）</li>
                            <li>选择有数据的地区（如"山东"）</li>
                            <li>运行RNN预测</li>
                            <li>验证预测结果图表正常显示</li>
                        </ul>
                    </li>
                    <li><strong>验证数据验证</strong>:
                        <ul>
                            <li>选择无数据的组合</li>
                            <li>验证是否给出明确提示</li>
                            <li>检查推荐功能是否工作</li>
                        </ul>
                    </li>
                </ol>
                
                <h4>推荐测试组合：</h4>
                <div class="product-list">
                    <strong>有数据的产品组合：</strong><br>
                    • 桑黄 + 山东 (4条价格记录)<br>
                    • 桦树茸 + 山东 (3条价格记录)<br>
                    • 茯苓 + 山东 (3条价格记录)<br>
                    • 灵芝 + 山东 (3条价格记录)<br>
                    • 西瓜 + 山东 (有价格数据)<br>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showResult(containerId, title, status, data) {
            const container = document.getElementById(containerId);
            const statusClass = status === 'success' ? 'success' : status === 'error' ? 'error' : status === 'warning' ? 'warning' : 'loading';
            
            let html = `<h4 class="${statusClass}">${title}</h4>`;
            if (data) {
                html += `<div class="result">${JSON.stringify(data, null, 2)}</div>`;
            }
            
            container.innerHTML = html;
        }

        async function analyzeProductData() {
            showResult('product-analysis-results', '正在分析产品数据分布...', 'loading');
            
            try {
                // 获取分类数据
                const categoriesResponse = await fetch('http://localhost:5000/api/v1/crawl_data/categories');
                const categoriesData = await categoriesResponse.json();
                
                // 获取产品数据
                const productsResponse = await fetch('http://localhost:5000/api/v1/crawl_data/products?limit=100');
                const productsData = await productsResponse.json();
                
                const analysis = {
                    totalCategories: categoriesData.data.categories.length,
                    categoriesWithData: categoriesData.data.categories.filter(c => c.price_count > 0).length,
                    totalProducts: productsData.data.products.length,
                    productsWithData: productsData.data.products.filter(p => p.price_count > 0).length,
                    topProductsByData: productsData.data.products
                        .filter(p => p.price_count > 0)
                        .sort((a, b) => b.price_count - a.price_count)
                        .slice(0, 10)
                        .map(p => ({
                            name: p.product_name,
                            category: p.category_name,
                            priceCount: p.price_count,
                            latestDate: p.latest_date
                        }))
                };
                
                showResult('product-analysis-results', '✅ 产品数据分析完成', 'success', analysis);
                
            } catch (error) {
                showResult('product-analysis-results', `❌ 产品数据分析失败: ${error.message}`, 'error');
            }
        }

        async function testRNNPrediction() {
            showResult('rnn-test-results', '正在测试RNN预测功能...', 'loading');
            
            try {
                // 测试有数据的产品
                const testCases = [
                    { product: '桑黄', region: '山东' },
                    { product: '桦树茸', region: '山东' },
                    { product: '茯苓', region: '山东' }
                ];
                
                const results = [];
                
                for (const testCase of testCases) {
                    try {
                        const response = await fetch('http://localhost:5000/api/v1/predict_rnn', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({
                                category: testCase.product,
                                region: testCase.region,
                                product_name: testCase.product,
                                forecast_days: 3
                            })
                        });
                        
                        const data = await response.json();
                        
                        results.push({
                            product: testCase.product,
                            region: testCase.region,
                            success: response.ok && data.data && data.data.predictions,
                            predictionsCount: data.data && data.data.predictions ? data.data.predictions.length : 0,
                            message: data.message,
                            samplePrediction: data.data && data.data.predictions && data.data.predictions[0] ? {
                                date: data.data.predictions[0].date,
                                price: data.data.predictions[0].predicted_price || data.data.predictions[0].price,
                                hasConfidence: !!data.data.predictions[0].confidence
                            } : null
                        });
                    } catch (error) {
                        results.push({
                            product: testCase.product,
                            region: testCase.region,
                            success: false,
                            error: error.message
                        });
                    }
                }
                
                showResult('rnn-test-results', '✅ RNN预测测试完成', 'success', results);
                
            } catch (error) {
                showResult('rnn-test-results', `❌ RNN预测测试失败: ${error.message}`, 'error');
            }
        }

        async function testMultipleProducts() {
            showResult('rnn-test-results', '正在测试多个产品预测...', 'loading');
            
            try {
                // 获取有数据的产品列表
                const response = await fetch('http://localhost:5000/api/v1/crawl_data/products?limit=50');
                const data = await response.json();
                
                const productsWithData = data.data.products
                    .filter(p => p.price_count > 0)
                    .slice(0, 5); // 测试前5个
                
                const testResults = [];
                
                for (const product of productsWithData) {
                    try {
                        const predResponse = await fetch('http://localhost:5000/api/v1/predict_rnn', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({
                                category: product.product_name,
                                region: '山东', // 使用固定地区
                                product_name: product.product_name,
                                forecast_days: 3
                            })
                        });
                        
                        const predData = await predResponse.json();
                        
                        testResults.push({
                            product: product.product_name,
                            category: product.category_name,
                            originalPriceCount: product.price_count,
                            predictionSuccess: predResponse.ok && predData.data && predData.data.predictions,
                            predictionsCount: predData.data && predData.data.predictions ? predData.data.predictions.length : 0,
                            message: predData.message
                        });
                    } catch (error) {
                        testResults.push({
                            product: product.product_name,
                            category: product.category_name,
                            originalPriceCount: product.price_count,
                            predictionSuccess: false,
                            error: error.message
                        });
                    }
                }
                
                showResult('rnn-test-results', '✅ 多产品预测测试完成', 'success', testResults);
                
            } catch (error) {
                showResult('rnn-test-results', `❌ 多产品预测测试失败: ${error.message}`, 'error');
            }
        }

        async function testDataFormats() {
            showResult('format-test-results', '正在验证数据格式...', 'loading');
            
            try {
                // 测试预测结果数据格式
                const response = await fetch('http://localhost:5000/api/v1/predict_rnn', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        category: '桑黄',
                        region: '山东',
                        product_name: '桑黄',
                        forecast_days: 3
                    })
                });
                
                const data = await response.json();
                
                if (data.data && data.data.predictions && data.data.predictions.length > 0) {
                    const prediction = data.data.predictions[0];
                    
                    const formatValidation = {
                        hasRequiredFields: {
                            date: !!prediction.date,
                            predicted_price: !!(prediction.predicted_price || prediction.price),
                            confidence: !!prediction.confidence
                        },
                        fieldTypes: {
                            date: typeof prediction.date,
                            predicted_price: typeof (prediction.predicted_price || prediction.price),
                            confidence: typeof prediction.confidence
                        },
                        sampleData: prediction,
                        dataStructureValid: !!(prediction.date && (prediction.predicted_price || prediction.price))
                    };
                    
                    showResult('format-test-results', '✅ 数据格式验证完成', 'success', formatValidation);
                } else {
                    showResult('format-test-results', '❌ 预测数据为空', 'error', data);
                }
                
            } catch (error) {
                showResult('format-test-results', `❌ 数据格式验证失败: ${error.message}`, 'error');
            }
        }

        async function testProductMapping() {
            showResult('mapping-test-results', '正在测试产品映射...', 'loading');
            
            try {
                // 获取分类数据
                const categoriesResponse = await fetch('http://localhost:5000/api/v1/crawl_data/categories');
                const categoriesData = await categoriesResponse.json();
                
                // 测试每个有数据的分类
                const mappingResults = [];
                
                for (const category of categoriesData.data.categories.filter(c => c.price_count > 0).slice(0, 5)) {
                    try {
                        const productsResponse = await fetch(`http://localhost:5000/api/v1/crawl_data/products?category=${encodeURIComponent(category.name)}&limit=10`);
                        const productsData = await productsResponse.json();
                        
                        const productsWithData = productsData.data.products.filter(p => p.price_count > 0);
                        
                        mappingResults.push({
                            categoryName: category.name,
                            totalProducts: productsData.data.products.length,
                            productsWithData: productsWithData.length,
                            topProducts: productsWithData.slice(0, 3).map(p => ({
                                name: p.product_name,
                                priceCount: p.price_count,
                                latestDate: p.latest_date
                            }))
                        });
                    } catch (error) {
                        mappingResults.push({
                            categoryName: category.name,
                            error: error.message
                        });
                    }
                }
                
                showResult('mapping-test-results', '✅ 产品映射测试完成', 'success', mappingResults);
                
            } catch (error) {
                showResult('mapping-test-results', `❌ 产品映射测试失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动分析产品数据
        window.onload = function() {
            analyzeProductData();
        };
    </script>
</body>
</html>
