-- 修复特定商品图片路径问题
-- 针对控制台报错的具体图片文件进行修复

-- 开始事务
START TRANSACTION;

-- 显示修复前的问题图片
SELECT '=== 修复前的问题图片路径 ===' as info;
SELECT id, name, image 
FROM product 
WHERE image LIKE '/images/products/%'
ORDER BY id;

-- 修复商品表中的错误图片路径
UPDATE product 
SET image = REPLACE(image, '/images/products/', '/uploads/images/products/')
WHERE image LIKE '/images/products/%';

-- 修复商品图片表中的错误图片路径
UPDATE product_image 
SET image_url = REPLACE(image_url, '/images/products/', '/uploads/images/products/')
WHERE image_url LIKE '/images/products/%';

-- 显示修复后的结果
SELECT '=== 修复后的图片路径 ===' as info;
SELECT id, name, image 
FROM product 
WHERE image LIKE '/uploads/images/products/%'
ORDER BY id;

-- 统计修复结果
SELECT '=== 修复统计 ===' as info;
SELECT 
    COUNT(CASE WHEN image LIKE '/uploads/images/products/%' THEN 1 END) as correct_format_count,
    COUNT(CASE WHEN image LIKE '/images/products/%' THEN 1 END) as wrong_format_count,
    COUNT(CASE WHEN image IS NOT NULL AND image != '' THEN 1 END) as total_with_image
FROM product;

-- 检查是否还有其他格式的图片路径需要修复
SELECT '=== 其他格式的图片路径 ===' as info;
SELECT DISTINCT 
    CASE 
        WHEN image IS NULL OR image = '' THEN '空路径'
        WHEN image LIKE 'http%' THEN '完整URL'
        WHEN image LIKE '/uploads/images/products/%' THEN '✅ 正确格式'
        WHEN image LIKE '/images/products/%' THEN '❌ 错误格式'
        WHEN image LIKE 'uploads/images/products/%' THEN '❌ 缺少/前缀'
        WHEN image LIKE 'images/products/%' THEN '❌ 缺少/uploads前缀'
        WHEN image NOT LIKE '/%' AND image NOT LIKE 'http%' THEN '❌ 只有文件名'
        ELSE '❌ 其他格式'
    END as path_format,
    COUNT(*) as count
FROM product 
GROUP BY path_format
ORDER BY count DESC;

-- 提交事务
COMMIT;

SELECT '=== 图片路径修复完成 ===' as info;
