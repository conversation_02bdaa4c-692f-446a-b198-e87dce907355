{"name": "smart-farmer-assistance-platform", "version": "1.0.0", "description": "Smart Farmer Assistance Platform", "private": true, "scripts": {"serve": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve", "build": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"animate.css": "^4.1.1", "axios": "^1.8.4", "core-js": "^3.6.5", "date-fns": "^2.28.0", "echarts": "^4.9.0", "echarts-wordcloud": "^1.1.3", "element-ui": "^2.15.14", "firebase": "^8.10.1", "jsqr": "^1.4.0", "lodash": "^4.17.21", "qrcode": "^1.5.4", "vue": "^2.6.11", "vue-echarts": "^4.0.2", "vue-router": "^3.2.0", "vuex": "^3.4.0"}, "devDependencies": {"@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-transform-optional-chaining": "^7.25.9", "@babel/preset-env": "^7.26.9", "@types/body-parser": "^1.19.5", "@types/connect": "^3.4.38", "@types/connect-history-api-fallback": "^1.5.4", "@types/express": "^5.0.1", "@types/glob": "^8.1.0", "@types/http-errors": "^2.0.4", "@types/long": "^4.0.2", "@types/mime": "^3.0.4", "@types/minimist": "^1.2.5", "@types/node": "^22.13.15", "@types/range-parser": "^1.2.7", "@types/send": "^0.17.4", "@types/serve-static": "^1.15.7", "@types/source-list-map": "^0.1.6", "@types/webpack": "^5.28.5", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "autoprefixer": "^10.4.21", "babel-eslint": "^10.1.0", "babel-loader": "^8.2.5", "cache-loader": "^4.1.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "postcss": "^8.5.3", "postcss-preset-env": "^10.1.5", "sass": "^1.86.1", "sass-loader": "^8.0.2", "thread-loader": "^2.1.3", "vue-loader": "^15.9.8", "vue-template-compiler": "^2.6.11", "webpack": "^4.46.0"}}