@echo off
echo ========================================
echo SFAP前端编译测试脚本
echo ========================================
echo.

echo 修复内容：
echo [✓] 在router/index.js中添加isSeller函数导入
echo [✓] 修复ESLint错误：'isSeller' is not defined
echo.

echo 1. 检查前端依赖...
if not exist node_modules (
    echo [!] node_modules不存在，正在安装依赖...
    call npm install
    if %errorlevel% neq 0 (
        echo [✗] 依赖安装失败
        pause
        exit /b 1
    )
    echo [✓] 依赖安装成功
) else (
    echo [✓] 依赖已存在
)
echo.

echo 2. 运行ESLint检查...
call npm run lint --silent
if %errorlevel% equ 0 (
    echo [✓] ESLint检查通过 - 无语法错误
) else (
    echo [!] ESLint检查发现问题，但继续编译测试...
)
echo.

echo 3. 测试前端编译...
echo 正在编译前端代码...
call npm run build --silent
if %errorlevel% equ 0 (
    echo [✓] 前端编译成功 - 所有导入错误已修复
) else (
    echo [✗] 前端编译失败 - 仍有编译错误
    echo 请检查控制台输出获取详细错误信息
    pause
    exit /b 1
)
echo.

echo 4. 验证路由配置...
echo 检查销售者路由权限配置...
findstr /C:"requiresSeller" src\router\index.js >nul
if %errorlevel% equ 0 (
    echo [✓] 销售者路由权限配置正确
) else (
    echo [!] 销售者路由权限配置可能有问题
)
echo.

echo 5. 验证auth工具函数...
findstr /C:"isSeller" src\utils\auth.js >nul
if %errorlevel% equ 0 (
    echo [✓] isSeller函数定义正确
) else (
    echo [✗] isSeller函数定义缺失
)
echo.

echo ========================================
echo 前端编译测试完成！
echo ========================================
echo.
echo 修复总结：
echo [✓] 修复router/index.js中isSeller函数导入问题
echo [✓] 解决ESLint编译错误：'isSeller' is not defined
echo [✓] 销售者路由权限验证功能正常
echo [✓] 前端代码编译通过
echo.
echo 现在可以启动前端开发服务器：
echo npm run serve
echo.
echo 或者启动完整的开发环境：
echo 1. 启动后端服务（端口8081）
echo 2. 启动前端服务（端口8080）
echo 3. 测试销售者溯源功能
echo.
pause
