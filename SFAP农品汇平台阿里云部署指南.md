# SFAP农品汇平台阿里云部署指南

## 📋 部署概览

**服务器信息：**
- 服务器IP：**************
- 操作系统：Linux (推荐 CentOS 7+ 或 Ubuntu 18.04+)
- 管理面板：宝塔面板

**端口分配：**
- 前端服务：8200 (Vue.js)
- 后端服务：8081 (Spring Boot)
- AI预测服务：5000 (Flask)
- 新闻爬取服务：5001 (Flask)
- 价格爬取服务：命令行工具（预留API端口8002）
- 数据库：3306 (MySQL)
- 缓存：6379 (Redis)
- Web服务器：80/443 (Nginx)

## 🛠️ 一、部署环境准备

### 1.1 宝塔面板必需软件安装

#### 基础软件栈
```bash
# 通过宝塔面板软件商店安装以下软件：
- Nginx 1.20+
- MySQL 8.0
- Redis 6.0+
- Java 11 (OpenJDK)
- Node.js 16+
- Python 3.8+
- PM2 (Node.js进程管理器)
```

#### 软件版本要求
| 软件 | 最低版本 | 推荐版本 | 说明 |
|------|----------|----------|------|
| Java | 11 | 11 | Spring Boot运行环境 |
| Node.js | 14 | 16+ | 前端构建和运行 |
| Python | 3.8 | 3.9+ | AI服务和爬虫服务 |
| MySQL | 8.0 | 8.0+ | 主数据库 |
| Redis | 6.0 | 6.2+ | 缓存和会话存储 |
| Nginx | 1.18 | 1.20+ | 反向代理和静态文件服务 |

### 1.2 系统依赖安装

#### Python依赖
```bash
# 安装Python包管理器
pip3 install --upgrade pip

# 安装虚拟环境
pip3 install virtualenv

# 系统级依赖
yum install -y gcc gcc-c++ python3-devel mysql-devel
# 或 Ubuntu/Debian:
# apt-get install -y gcc g++ python3-dev libmysqlclient-dev
```

#### Node.js全局包
```bash
npm install -g pm2 @vue/cli
```

## 📦 二、项目打包部署详细步骤

### 2.1 前端项目部署

#### 2.1.1 本地构建
```bash
# 在本地开发环境执行
cd /path/to/SFAP
npm install
npm run build
```

#### 2.1.2 上传到服务器
```bash
# 将dist目录上传到服务器
scp -r dist/ root@**************:/www/wwwroot/sfap-frontend/
```

#### 2.1.3 Nginx配置
在宝塔面板中创建网站，配置如下：
```nginx
server {
    listen 8200;
    server_name **************;
    root /www/wwwroot/sfap-frontend/dist;
    index index.html;

    # 前端路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # API代理到后端
    location /api/ {
        proxy_pass http://127.0.0.1:8081;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 文件上传大小限制
    client_max_body_size 10M;
}
```

### 2.2 后端Spring Boot部署

#### 2.2.1 本地构建
```bash
cd backend/main
mvn clean package -Dmaven.test.skip=true
```

#### 2.2.2 上传JAR包
```bash
scp target/agriculture-mall-1.0.jar root@**************:/www/wwwroot/sfap-backend/
```

#### 2.2.3 创建启动脚本
```bash
# /www/wwwroot/sfap-backend/start.sh
#!/bin/bash
cd /www/wwwroot/sfap-backend
nohup java -jar -Xms512m -Xmx1024m \
  -Dspring.profiles.active=prod \
  agriculture-mall-1.0.jar > app.log 2>&1 &
echo $! > app.pid
```

#### 2.2.4 创建停止脚本
```bash
# /www/wwwroot/sfap-backend/stop.sh
#!/bin/bash
cd /www/wwwroot/sfap-backend
if [ -f app.pid ]; then
    kill $(cat app.pid)
    rm app.pid
fi
```

### 2.3 AI预测服务部署

#### 2.3.1 创建虚拟环境
```bash
cd /www/wwwroot/sfap-ai
python3 -m venv venv
source venv/bin/activate
```

#### 2.3.2 安装依赖
```bash
# 上传ai-service目录到服务器
pip install -r requirements.txt
```

#### 2.3.3 创建启动脚本
```bash
# /www/wwwroot/sfap-ai/start.sh
#!/bin/bash
cd /www/wwwroot/sfap-ai
source venv/bin/activate
export PORT=5000
export DEBUG=false
nohup python app.py > ai-service.log 2>&1 &
echo $! > ai-service.pid
```

### 2.4 新闻爬取服务部署

#### 2.4.1 创建虚拟环境
```bash
cd /www/wwwroot/sfap-news-crawler
python3 -m venv venv
source venv/bin/activate
```

#### 2.4.2 安装依赖
```bash
# 上传backend/python目录到服务器
pip install -r requirements.txt
```

#### 2.4.3 创建启动脚本
```bash
# /www/wwwroot/sfap-news-crawler/start.sh
#!/bin/bash
cd /www/wwwroot/sfap-news-crawler
source venv/bin/activate
nohup python app.py > news-crawler.log 2>&1 &
echo $! > news-crawler.pid
```

### 2.5 价格爬取服务部署

#### 2.5.1 创建虚拟环境
```bash
cd /www/wwwroot/sfap-price-crawler
python3 -m venv venv
source venv/bin/activate
```

#### 2.5.2 安装依赖
```bash
# 上传backend/datacrawl目录到服务器
pip install -r requirements.txt
```

#### 2.5.3 配置环境变量
```bash
# 创建.env文件
cp .env.example .env
# 编辑.env文件，配置数据库连接等信息
```

#### 2.5.4 创建定时任务脚本
```bash
# /www/wwwroot/sfap-price-crawler/run_crawl.sh
#!/bin/bash
cd /www/wwwroot/sfap-price-crawler
source venv/bin/activate
python main.py --category 水果,蔬菜 >> crawl.log 2>&1
```

#### 2.5.5 设置定时任务
```bash
# 添加到crontab
crontab -e
# 每天凌晨2点执行价格数据爬取
0 2 * * * /www/wwwroot/sfap-price-crawler/run_crawl.sh
```

## 🗄️ 三、数据库配置

### 3.1 MySQL数据库设置

#### 3.1.1 创建数据库
```sql
CREATE DATABASE agriculture_mall CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'sfap_user'@'localhost' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON agriculture_mall.* TO 'sfap_user'@'localhost';
FLUSH PRIVILEGES;
```

#### 3.1.2 导入数据库结构
```bash
# 上传数据库文件到服务器
mysql -u root -p agriculture_mall < database/full_database.sql
```

#### 3.1.3 MySQL配置优化
在宝塔面板MySQL配置中添加：
```ini
[mysqld]
max_connections = 200
innodb_buffer_pool_size = 256M
innodb_log_file_size = 64M
query_cache_size = 32M
```

### 3.2 Redis配置

#### 3.2.1 基础配置
```conf
# /etc/redis/redis.conf
bind 127.0.0.1
port 6379
maxmemory 128mb
maxmemory-policy allkeys-lru
```

## 📁 四、配置文件修改清单

### 4.1 前端配置修改

#### 4.1.1 vue.config.js (已修改)
- 开发服务器端口：8080 → 8200
- 代理配置已更新

#### 4.1.2 生产环境变量
创建 `.env.production` 文件：
```env
VUE_APP_BASE_API=http://**************:8081
VUE_APP_AI_SERVICE_URL=http://**************:5000
NODE_ENV=production
```

### 4.2 后端配置修改

#### 4.2.1 application.yml (已修改)
- 生产环境CORS配置已更新
- 文件上传路径配置
- 数据库连接配置需要更新密码

#### 4.2.2 生产环境数据库配置
```yaml
spring:
  datasource:
    url: ******************************************************************************************************************************
    username: sfap_user
    password: your_secure_password
```

### 4.3 AI服务配置修改

#### 4.3.1 环境变量配置
创建 `/www/wwwroot/sfap-ai/.env` 文件：
```env
PORT=5000
DEBUG=false
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=sfap_user
DB_PASSWORD=your_secure_password
DB_DATABASE=agriculture_mall
```

### 4.4 新闻爬取服务配置

#### 4.4.1 数据库连接配置
更新 `backend/python/db_manager.py` 中的数据库连接信息。

### 4.5 价格爬取服务配置

#### 4.5.1 环境变量配置
创建 `/www/wwwroot/sfap-price-crawler/.env` 文件：
```env
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=sfap_user
DB_PASSWORD=your_secure_password
DB_DATABASE=agriculture_mall

# API配置（预留）
API_HOST=0.0.0.0
API_PORT=8002
API_SECRET_KEY=your_secret_key_here
```

## 🌐 五、Nginx反向代理配置

### 5.1 主站点配置
```nginx
server {
    listen 80;
    server_name **************;
    
    # 重定向到前端应用
    location / {
        proxy_pass http://127.0.0.1:8200;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
    
    # API代理
    location /api/ {
        proxy_pass http://127.0.0.1:8081;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
    
    # AI服务代理
    location /ai-api/ {
        proxy_pass http://127.0.0.1:5000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    # 新闻爬取服务代理
    location /news-api/ {
        proxy_pass http://127.0.0.1:5001/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    # 价格爬取服务代理（预留，当前为命令行工具）
    location /price-api/ {
        proxy_pass http://127.0.0.1:8002/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    # 静态文件服务
    location /uploads/ {
        alias /www/wwwroot/agriculture/uploads/;
        expires 1y;
    }
}
```

## 🔧 六、关键注意事项

### 6.1 文件上传路径配置

#### 6.1.1 创建上传目录
```bash
mkdir -p /www/wwwroot/agriculture/uploads/{avatars,images,qrcodes,seller,exports}
chown -R www:www /www/wwwroot/agriculture/uploads
chmod -R 755 /www/wwwroot/agriculture/uploads
```

#### 6.1.2 QR码存储路径
确保后端配置中的QR码存储路径正确：
```yaml
file:
  upload:
    path: /www/wwwroot/agriculture/uploads
    avatar-dir: /www/wwwroot/agriculture/uploads/avatars
```

### 6.2 权限和安全配置

#### 6.2.1 防火墙配置
```bash
# 开放必要端口
firewall-cmd --permanent --add-port=80/tcp
firewall-cmd --permanent --add-port=443/tcp
firewall-cmd --permanent --add-port=8200/tcp
firewall-cmd --permanent --add-port=8081/tcp
firewall-cmd --permanent --add-port=5000/tcp
firewall-cmd --permanent --add-port=5001/tcp
firewall-cmd --permanent --add-port=8002/tcp
firewall-cmd --reload
```

#### 6.2.2 文件权限设置
```bash
# 设置应用目录权限
chown -R www:www /www/wwwroot/sfap-*
chmod -R 755 /www/wwwroot/sfap-*

# 设置日志文件权限
chmod 644 /www/wwwroot/sfap-*/logs/*
```

### 6.3 SSL证书配置（可选）

如果需要HTTPS访问，在宝塔面板中配置SSL证书：
1. 申请免费SSL证书
2. 配置强制HTTPS跳转
3. 更新前端API地址为HTTPS

## 🚀 七、服务启动顺序

### 7.1 启动脚本
创建 `/www/wwwroot/start-all.sh`：
```bash
#!/bin/bash
echo "启动SFAP农品汇平台服务..."

# 启动MySQL和Redis (通过宝塔面板管理)
echo "确保MySQL和Redis已启动"

# 启动后端服务
echo "启动后端服务..."
cd /www/wwwroot/sfap-backend && ./start.sh

# 等待后端启动
sleep 10

# 启动AI服务
echo "启动AI预测服务..."
cd /www/wwwroot/sfap-ai && ./start.sh

# 启动新闻爬取服务
echo "启动新闻爬取服务..."
cd /www/wwwroot/sfap-news-crawler && ./start.sh

# 设置价格爬取定时任务
echo "设置价格爬取定时任务..."
# 价格爬取服务通过crontab定时执行，无需启动常驻进程

# 启动前端服务 (通过Nginx)
echo "启动前端服务..."
systemctl restart nginx

echo "所有服务启动完成！"
echo "访问地址: http://**************"
```

### 7.2 停止脚本
创建 `/www/wwwroot/stop-all.sh`：
```bash
#!/bin/bash
echo "停止SFAP农品汇平台服务..."

# 停止各个服务
cd /www/wwwroot/sfap-backend && ./stop.sh
cd /www/wwwroot/sfap-ai && [ -f ai-service.pid ] && kill $(cat ai-service.pid) && rm ai-service.pid
cd /www/wwwroot/sfap-news-crawler && [ -f news-crawler.pid ] && kill $(cat news-crawler.pid) && rm news-crawler.pid

echo "所有服务已停止！"
```

## ✅ 八、部署验证步骤

### 8.1 服务状态检查
```bash
# 检查端口占用
netstat -tlnp | grep -E ':(80|8081|8200|5000|5001|8002|3306|6379)'

# 检查进程状态
ps aux | grep -E '(java|python|nginx)'

# 检查日志
tail -f /www/wwwroot/sfap-backend/app.log
tail -f /www/wwwroot/sfap-ai/ai-service.log
tail -f /www/wwwroot/sfap-crawler/crawler.log
```

### 8.2 功能测试清单

#### 8.2.1 基础功能测试
- [ ] 前端页面正常访问 (http://**************)
- [ ] 用户注册登录功能
- [ ] 商品浏览和搜索
- [ ] 文件上传功能
- [ ] 图片显示正常

#### 8.2.2 API接口测试
```bash
# 测试后端API
curl http://**************:8081/api/health

# 测试AI服务
curl http://**************:5000/api/v1/health

# 测试新闻爬取服务
curl http://**************:5001/api/crawler/status

# 测试价格爬取服务（命令行工具，检查日志）
tail -f /www/wwwroot/sfap-price-crawler/crawl.log
```

#### 8.2.3 数据库连接测试
```bash
# 连接MySQL测试
mysql -u sfap_user -p agriculture_mall -e "SELECT COUNT(*) FROM users;"

# 连接Redis测试
redis-cli ping
```

### 8.3 性能测试

#### 8.3.1 负载测试
```bash
# 使用ab工具进行简单负载测试
ab -n 100 -c 10 http://**************/

# 监控系统资源
top
free -h
df -h
```

## 🔍 九、常见问题排查指南

### 9.1 服务启动失败

#### 9.1.1 端口冲突
```bash
# 查看端口占用
lsof -i :8081
# 杀死占用进程
kill -9 PID
```

#### 9.1.2 权限问题
```bash
# 检查文件权限
ls -la /www/wwwroot/sfap-*
# 修复权限
chown -R www:www /www/wwwroot/sfap-*
```

### 9.2 数据库连接问题

#### 9.2.1 连接被拒绝
- 检查MySQL服务状态
- 验证用户名密码
- 检查防火墙设置

#### 9.2.2 字符编码问题
- 确保数据库字符集为utf8mb4
- 检查连接字符串编码设置

### 9.3 文件上传问题

#### 9.3.1 上传失败
- 检查上传目录权限
- 验证Nginx文件大小限制
- 检查磁盘空间

### 9.4 跨域问题

#### 9.4.1 CORS错误
- 检查后端CORS配置
- 验证Nginx代理设置
- 确认前端API地址配置

## 📞 十、技术支持

### 10.1 日志文件位置
- 后端日志：`/www/wwwroot/sfap-backend/app.log`
- AI服务日志：`/www/wwwroot/sfap-ai/ai-service.log`
- 新闻爬取服务日志：`/www/wwwroot/sfap-news-crawler/news-crawler.log`
- 价格爬取服务日志：`/www/wwwroot/sfap-price-crawler/crawl.log`
- Nginx日志：`/www/wwwroot/logs/`

### 10.2 监控建议
- 设置服务自动重启
- 配置日志轮转
- 监控磁盘空间
- 定期备份数据库

---

**部署完成后，请访问 http://************** 验证系统是否正常运行。**

**文档版本**: v1.0  
**创建日期**: 2025-01-25  
**适用版本**: SFAP农品汇平台 v4.0
