#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SFAP农品汇平台 - MySQL排序规则修复脚本
用于将 utf8mb4_0900_ai_ci 替换为兼容的 utf8mb4_unicode_ci
"""

import re
import os
import shutil
from datetime import datetime

def fix_mysql_collation(input_file, output_file=None):
    """
    修复MySQL排序规则兼容性问题
    
    Args:
        input_file (str): 输入的SQL文件路径
        output_file (str): 输出的SQL文件路径，如果为None则覆盖原文件
    """
    
    if not os.path.exists(input_file):
        print(f"❌ 错误: 文件 {input_file} 不存在")
        return False
    
    # 如果没有指定输出文件，则创建备份并覆盖原文件
    if output_file is None:
        # 创建备份
        backup_file = f"{input_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(input_file, backup_file)
        print(f"✅ 已创建备份文件: {backup_file}")
        output_file = input_file
    
    try:
        # 读取原文件
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"📖 正在处理文件: {input_file}")
        print(f"📄 文件大小: {len(content)} 字符")
        
        # 统计替换次数
        original_count = len(re.findall(r'utf8mb4_0900_ai_ci', content))
        print(f"🔍 发现 {original_count} 处需要替换的排序规则")
        
        if original_count == 0:
            print("✅ 文件中没有发现需要替换的排序规则")
            return True
        
        # 执行替换
        replacements = [
            # 替换字段级别的排序规则
            (r'CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci', 
             'CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci'),
            
            # 替换表级别的排序规则
            (r'COLLATE = utf8mb4_0900_ai_ci', 
             'COLLATE = utf8mb4_unicode_ci'),
            
            # 替换数据库级别的排序规则
            (r'COLLATE utf8mb4_0900_ai_ci', 
             'COLLATE utf8mb4_unicode_ci'),
            
            # 替换CREATE DATABASE语句中的排序规则
            (r'DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci', 
             'DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci'),
        ]
        
        modified_content = content
        total_replacements = 0
        
        for pattern, replacement in replacements:
            matches = len(re.findall(pattern, modified_content))
            if matches > 0:
                modified_content = re.sub(pattern, replacement, modified_content)
                total_replacements += matches
                print(f"🔄 替换模式 '{pattern}': {matches} 处")
        
        # 写入修复后的文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(modified_content)
        
        print(f"✅ 修复完成!")
        print(f"📊 总共替换: {total_replacements} 处")
        print(f"💾 输出文件: {output_file}")
        
        # 验证修复结果
        remaining_count = len(re.findall(r'utf8mb4_0900_ai_ci', modified_content))
        if remaining_count == 0:
            print("✅ 验证通过: 所有不兼容的排序规则已成功替换")
        else:
            print(f"⚠️  警告: 仍有 {remaining_count} 处未替换的排序规则")
        
        return True
        
    except Exception as e:
        print(f"❌ 处理文件时发生错误: {str(e)}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🛠️  SFAP农品汇平台 - MySQL排序规则修复工具")
    print("=" * 60)
    
    # 输入文件路径
    input_file = r"E:\计算机设计大赛2\V4.0\新建文件夹\新建文件夹\SFAP\backend\agriculture_mall.sql"
    
    # 检查文件是否存在
    if not os.path.exists(input_file):
        print(f"❌ 错误: 找不到文件 {input_file}")
        print("请确认文件路径是否正确")
        return
    
    print(f"📁 目标文件: {input_file}")
    
    # 执行修复
    success = fix_mysql_collation(input_file)
    
    if success:
        print("\n" + "=" * 60)
        print("🎉 修复完成! 现在可以尝试重新导入数据库")
        print("=" * 60)
        print("\n📋 后续步骤:")
        print("1. 使用修复后的SQL文件导入数据库")
        print("2. 如果仍有问题，请检查MySQL版本兼容性")
        print("3. 建议使用 utf8mb4_unicode_ci 作为默认排序规则")
        print("\n💡 导入命令示例:")
        print(f"mysql -u root -p < \"{input_file}\"")
    else:
        print("\n❌ 修复失败，请检查错误信息")

if __name__ == "__main__":
    main()
