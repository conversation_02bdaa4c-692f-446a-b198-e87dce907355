## 3. 溯源事件管理API

### 3.1 添加溯源事件
- **URL**: `/api/v1/trace/events`
- **方法**: POST
- **权限**: ROLE_SELLER (只能添加自己溯源记录的事件) 或 ROLE_ADMIN
- **请求体**:
```json
{
  "traceRecordId": 12345,
  "eventType": "PLANTING",
  "eventDate": "2025-03-15T10:00:00",
  "description": "进行富硒苹果幼苗定植，共2000株。",
  "location": "大棚A区",
  "responsiblePerson": "张三",
  "attachments": {
    "images": [
      "http://yourdomain.com/upload/image1.jpg",
      "http://yourdomain.com/upload/image2.jpg"
    ],
    "videos": []
  }
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "溯源事件添加成功",
  "data": {
    "eventId": 1001
  }
}
```

### 3.2 更新溯源事件
- **URL**: `/api/v1/trace/events/{id}`
- **方法**: PUT
- **权限**: ROLE_SELLER (只能更新自己溯源记录的事件) 或 ROLE_ADMIN
- **请求体**:
```json
{
  "description": "进行富硒苹果幼苗定植，共2200株。优质苗株率95%。",
  "location": "大棚A区",
  "responsiblePerson": "张三",
  "attachments": {
    "images": [
      "http://yourdomain.com/upload/image1.jpg",
      "http://yourdomain.com/upload/image2.jpg",
      "http://yourdomain.com/upload/image3.jpg"
    ],
    "videos": []
  }
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "溯源事件更新成功",
  "data": null
}
```

### 3.3 删除溯源事件
- **URL**: `/api/v1/trace/events/{id}`
- **方法**: DELETE
- **权限**: ROLE_SELLER (只能删除自己溯源记录的事件) 或 ROLE_ADMIN
- **响应**:
```json
{
  "code": 200,
  "message": "溯源事件删除成功",
  "data": null
}
```

### 3.4 获取溯源记录的事件列表
- **URL**: `/api/v1/trace/records/{recordId}/events`
- **方法**: GET
- **权限**: 公开API或ROLE_USER（根据溯源记录的状态判断）
- **参数**:
  - eventType: 事件类型过滤（可选）
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "eventId": 1001,
      "eventType": "PLANTING",
      "eventDate": "2025-03-15T10:00:00",
      "description": "进行富硒苹果幼苗定植，共2200株。优质苗株率95%。",
      "location": "大棚A区",
      "responsiblePerson": "张三",
      "attachments": {
        "images": [
          "http://yourdomain.com/upload/image1.jpg",
          "http://yourdomain.com/upload/image2.jpg",
          "http://yourdomain.com/upload/image3.jpg"
        ],
        "videos": []
      },
      "createdAt": "2025-03-15T10:30:00"
    },
    // ...更多事件
  ]
}
```

## 4. 溯源码管理API

### 4.1 生成溯源二维码
- **URL**: `/api/v1/trace/codes/generate/{traceRecordId}`
- **方法**: POST
- **权限**: ROLE_SELLER (自己的记录) 或 ROLE_ADMIN
- **响应**:
```json
{
  "code": 200,
  "message": "二维码生成成功",
  "data": {
    "qrCodeUrl": "http://yourdomain.com/qrcode/ABCDEF1234567890.png"
  }
}
```

### 4.2 验证溯源码
- **URL**: `/api/v1/trace/codes/verify/{code}`
- **方法**: GET
- **权限**: 公开API
- **响应**:
```json
{
  "code": 200,
  "message": "验证成功",
  "data": {
    "isValid": true,
    "traceRecordId": 12345,
    "productName": "富硒苹果"
  }
}
```

### 4.3 获取溯源码统计信息
- **URL**: `/api/v1/trace/codes/{code}/stats`
- **方法**: GET
- **权限**: ROLE_SELLER (自己的记录) 或 ROLE_ADMIN
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "traceCode": "ABCDEF1234567890",
    "scanCount": 156,
    "lastScanTime": "2025-04-15T14:30:22",
    "scanTimesPerDay": [
      {"date": "2025-04-15", "count": 32},
      {"date": "2025-04-14", "count": 28},
      {"date": "2025-04-13", "count": 45},
      // ...更多数据
    ]
  }
}
```

## 5. 溯源信息查询API

### 5.1 通过溯源码查询溯源信息
- **URL**: `/api/v1/trace/query`
- **方法**: GET
- **权限**: 公开API
- **参数**:
  - code: 溯源码
- **响应**:
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "traceRecordId": 12345,
    "traceCode": "ABCDEF1234567890",
    "productName": "富硒苹果",
    "farmName": "阳光有机农场",
    "producerId": 501,
    "producerName": "张农夫",
    "creationDate": "2025-03-15",
    "harvestDate": "2025-08-25",
    "packagingDate": "2025-08-26",
    "qrCodeUrl": "http://yourdomain.com/qrcode/ABCDEF1234567890.png",
    "events": [
      {
        "eventId": 1001,
        "eventType": "PLANTING",
        "eventTypeText": "种植",
        "eventDate": "2025-03-15T10:00:00",
        "description": "进行富硒苹果幼苗定植，共2200株。优质苗株率95%。",
        "location": "大棚A区",
        "responsiblePerson": "张三",
        "attachments": {
          "images": ["http://yourdomain.com/upload/image1.jpg", "http://yourdomain.com/upload/image2.jpg"],
          "videos": []
        }
      },
      {
        "eventId": 1002,
        "eventType": "FERTILIZING",
        "eventTypeText": "施肥",
        "eventDate": "2025-04-01T09:30:00",
        "description": "施用农家肥100公斤。",
        "location": "大棚A区",
        "responsiblePerson": "李四",
        "attachments": {
          "images": ["http://yourdomain.com/upload/image3.jpg"],
          "videos": []
        }
      },
      // ...更多事件
    ],
    "certificates": [
      {
        "certificateId": 501,
        "certificateType": "ORGANIC",
        "certificateTypeText": "有机认证",
        "certificateNo": "ORG2025001",
        "issuingAuthority": "国家有机产品认证中心",
        "issueDate": "2025-01-15",
        "validUntil": "2026-01-14",
        "certificateUrl": "http://yourdomain.com/cert/cert1.jpg"
      },
      // ...更多认证
    ],
    "logistics": [
      {
        "logisticsId": 701,
        "carrier": "快速物流",
        "transportType": "ROAD",
        "transportTypeText": "公路运输",
        "departureTime": "2025-08-27T08:00:00",
        "arrivalTime": "2025-08-28T15:30:00",
        "origin": "山东烟台",
        "destination": "北京市朝阳区",
        "temperature": 5.2,
        "humidity": 65.3,
        "status": "DELIVERED",
        "statusText": "已送达"
      },
      // ...更多物流记录
    ]
  }
}
```