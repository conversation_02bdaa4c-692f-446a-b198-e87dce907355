# 溯源模块API设计文档

## 1. API设计原则

### 1.1 RESTful API设计规范
- 使用标准HTTP方法表示操作：
  - GET：查询资源
  - POST：创建资源
  - PUT：更新资源
  - DELETE：删除资源
- URL使用名词而非动词，例如`/api/v1/traces`而不是`/api/v1/getTraces`
- 版本控制：在URL中包含API版本号，例如`/api/v1/...`
- 统一响应格式

### 1.2 通用响应结构
所有API响应均使用以下JSON格式：
```json
{
  "code": 200,           // 业务状态码，200表示成功
  "message": "success",  // 状态描述
  "data": { ... }        // 业务数据
}
```

### 1.3 错误处理
错误响应采用HTTP状态码 + 业务状态码的方式：
```json
{
  "code": 400001,        // 业务错误码
  "message": "参数错误：溯源码不存在",  // 错误描述
  "data": null           // 通常为null
}
```

### 1.4 分页请求参数
分页查询统一使用以下参数：
- page：当前页码，从1开始，默认为1
- size：每页记录数，默认为10
- sort：排序字段，默认为id
- order：排序方式，asc（升序）或desc（降序），默认为desc

### 1.5 分页响应结构
分页查询响应使用以下结构：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 100,          // 总记录数
    "pages": 10,           // 总页数
    "current": 1,          // 当前页码
    "size": 10,            // 每页记录数
    "items": [ ... ]       // 当前页数据项列表
  }
}
```

## 2. 溯源主记录管理API

### 2.1 创建溯源记录
- **URL**: `/api/v1/trace/records`
- **方法**: POST
- **权限**: ROLE_SELLER
- **请求体**:
```json
{
  "productId": 101,
  "productName": "富硒苹果",
  "farmName": "阳光农场",
  "creationDate": "2025-03-15",
  "harvestDate": "2025-08-20",
  "packagingDate": "2025-08-22"
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "溯源记录创建成功",
  "data": {
    "traceRecordId": 12345,
    "traceCode": "ABCDEF1234567890"
  }
}
```

### 2.2 获取溯源记录详情
- **URL**: `/api/v1/trace/records/{id}`
- **方法**: GET
- **权限**: ROLE_SELLER (自己的记录) 或 ROLE_ADMIN
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 12345,
    "productId": 101,
    "productName": "富硒苹果",
    "traceCode": "ABCDEF1234567890",
    "farmName": "阳光农场",
    "producerId": 501,
    "creationDate": "2025-03-15",
    "harvestDate": "2025-08-20",
    "packagingDate": "2025-08-22",
    "status": 2,
    "qrCodeUrl": "http://yourdomain.com/qrcode/ABCDEF1234567890.png",
    "createdAt": "2025-03-10T08:30:00",
    "updatedAt": "2025-03-15T10:45:00"
  }
}
```

### 2.3 更新溯源记录
- **URL**: `/api/v1/trace/records/{id}`
- **方法**: PUT
- **权限**: ROLE_SELLER (自己的记录) 或 ROLE_ADMIN
- **请求体**:
```json
{
  "farmName": "阳光有机农场",
  "harvestDate": "2025-08-25",
  "packagingDate": "2025-08-26"
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "溯源记录更新成功",
  "data": {
    "traceRecordId": 12345
  }
}
```

### 2.4 获取生产者的溯源记录列表
- **URL**: `/api/v1/trace/records/producer`
- **方法**: GET
- **权限**: ROLE_SELLER
- **参数**: 
  - page: 页码
  - size: 每页记录数
  - status: 状态过滤（可选）
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 25,
    "pages": 3,
    "current": 1,
    "size": 10,
    "items": [
      {
        "id": 12345,
        "productName": "富硒苹果",
        "traceCode": "ABCDEF1234567890",
        "farmName": "阳光农场",
        "status": 2,
        "createdAt": "2025-03-10T08:30:00"
      },
      // ...更多记录
    ]
  }
}
```

### 2.5 更新溯源记录状态
- **URL**: `/api/v1/trace/records/{id}/status`
- **方法**: PUT
- **权限**: ROLE_SELLER (自己的记录) 或 ROLE_ADMIN
- **请求体**:
```json
{
  "status": 2  // 0-草稿，1-待审核，2-已发布，3-已下架，4-异常
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "状态更新成功",
  "data": null
}
```