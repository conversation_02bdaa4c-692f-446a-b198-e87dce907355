## 12. 搜索API

### 12.1 搜索商品
- **URL**: `/api/v1/search/products`
- **方法**: GET
- **权限**: 公开API
- **参数**:
  - keyword: 搜索关键词
  - categoryId: 分类ID（可选）
  - minPrice: 最低价格（可选）
  - maxPrice: 最高价格（可选）
  - tags: 标签，多个用逗号分隔（可选）
  - region: 产地地区（可选）
  - hasTrace: 是否有溯源信息（可选，true/false）
  - sort: 排序字段（可选，price/sales/rating/time）
  - order: 排序方式（asc/desc）
  - page: 页码
  - size: 每页记录数
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 56,
    "pages": 6,
    "current": 1,
    "size": 10,
    "aggregations": {
      "categories": [
        {"id": 7, "name": "富士苹果", "count": 25},
        {"id": 8, "name": "国光苹果", "count": 15},
        {"id": 9, "name": "红星苹果", "count": 16}
      ],
      "priceRanges": [
        {"range": "0-10", "count": 5},
        {"range": "10-20", "count": 30},
        {"range": "20-50", "count": 21}
      ],
      "regions": [
        {"region": "山东", "count": 30},
        {"region": "陕西", "count": 15},
        {"region": "甘肃", "count": 11}
      ],
      "tags": [
        {"tag": "有机", "count": 20},
        {"tag": "绿色食品", "count": 15},
        {"tag": "无农药", "count": 10}
      ]
    },
    "suggestedKeywords": ["红富士苹果", "有机苹果", "烟台苹果"],
    "items": [
      {
        "id": 101,
        "name": "有机红富士苹果",
        "description": "山东烟台特产，有机种植，自然成熟...",
        "categoryId": 7,
        "categoryName": "富士苹果",
        "price": 15.80,
        "mainImage": "http://yourdomain.com/products/apple1.jpg",
        "sellerId": 1,
        "sellerName": "阳光农场",
        "rating": 4.8,
        "sales": 1580,
        "tags": ["有机", "无农药", "新鲜"],
        "hasTraceability": true,
        "highlight": {
          "name": "<em>有机</em>红富士<em>苹果</em>",
          "description": "山东烟台特产，<em>有机</em>种植，自然成熟..."
        }
      },
      // ...更多商品
    ]
  }
}
```

### 12.2 搜索店铺
- **URL**: `/api/v1/search/sellers`
- **方法**: GET
- **权限**: 公开API
- **参数**:
  - keyword: 搜索关键词
  - categoryId: 主营分类ID（可选）
  - region: 地区（可选）
  - minRating: 最低评分（可选）
  - sort: 排序字段（可选，rating/productCount/followCount）
  - order: 排序方式（asc/desc）
  - page: 页码
  - size: 每页记录数
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 15,
    "pages": 2,
    "current": 1,
    "size": 10,
    "aggregations": {
      "regions": [
        {"region": "山东", "count": 5},
        {"region": "河南", "count": 4},
        {"region": "湖北", "count": 3},
        {"region": "江苏", "count": 3}
      ],
      "categories": [
        {"id": 1, "name": "水果", "count": 8},
        {"id": 2, "name": "蔬菜", "count": 5},
        {"id": 3, "name": "粮油", "count": 2}
      ],
      "ratings": [
        {"range": "4.5-5.0", "count": 8},
        {"range": "4.0-4.5", "count": 5},
        {"range": "3.5-4.0", "count": 2}
      ]
    },
    "items": [
      {
        "id": 1,
        "name": "阳光农场",
        "logo": "http://yourdomain.com/sellers/logo1.jpg",
        "description": "专注有机水果种植，提供健康安全的农产品",
        "mainCategories": ["水果", "蔬菜"],
        "rating": 4.9,
        "productCount": 25,
        "followCount": 1250,
        "region": "山东",
        "highlight": {
          "name": "<em>阳光</em>农场",
          "description": "专注<em>有机</em>水果种植，提供健康安全的农产品"
        }
      },
      // ...更多店铺
    ]
  }
}
```

### 12.3 获取热门搜索
- **URL**: `/api/v1/search/hot-keywords`
- **方法**: GET
- **权限**: 公开API
- **参数**:
  - type: 类型（可选，0-全部，1-商品，2-店铺，默认0）
  - size: 返回数量（可选，默认10）
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {"keyword": "苹果", "count": 2560},
    {"keyword": "有机蔬菜", "count": 1850},
    {"keyword": "大米", "count": 1620},
    {"keyword": "西红柿", "count": 1450},
    {"keyword": "鸡蛋", "count": 1230},
    {"keyword": "土豆", "count": 980},
    {"keyword": "草莓", "count": 850},
    {"keyword": "西瓜", "count": 720},
    {"keyword": "猪肉", "count": 650},
    {"keyword": "白菜", "count": 580}
  ]
}
```

### 12.4 获取搜索历史
- **URL**: `/api/v1/search/history`
- **方法**: GET
- **权限**: ROLE_USER
- **参数**:
  - size: 返回数量（可选，默认10）
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {"keyword": "有机苹果", "time": "2025-01-15T14:30:00"},
    {"keyword": "西红柿", "time": "2025-01-14T16:20:00"},
    {"keyword": "大米", "time": "2025-01-13T10:45:00"},
    {"keyword": "农场", "time": "2025-01-12T09:30:00"},
    {"keyword": "草莓", "time": "2025-01-10T19:20:00"}
  ]
}
```

### 12.5 清空搜索历史
- **URL**: `/api/v1/search/history`
- **方法**: DELETE
- **权限**: ROLE_USER
- **响应**:
```json
{
  "code": 200,
  "message": "搜索历史已清空",
  "data": null
}
```

## 13. AI助手API

### 13.1 获取农业知识问答
- **URL**: `/api/v1/ai/qa`
- **方法**: POST
- **权限**: 公开API
- **请求体**:
```json
{
  "question": "苹果种植需要什么样的土壤条件？",
  "conversationId": "conv_12345",
  "previousMessages": [
    {"role": "user", "content": "我想种植苹果树"},
    {"role": "assistant", "content": "好的，种植苹果树需要考虑多种因素，包括气候、土壤、水分等条件。您想了解哪个方面的具体信息？"}
  ]
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "answer": "苹果树对土壤条件有一定要求，理想的土壤应具备以下特点：\n\n1. 土壤类型：苹果树适宜生长在排水良好的砂质壤土或黏质壤土中。\n\n2. pH值：土壤pH值最好在5.5-6.5之间，微酸性至中性土壤最适合苹果树生长。\n\n3. 有机质含量：富含有机质的土壤有利于苹果树的生长，建议有机质含量在2%以上。\n\n4. 排水性能：苹果树根系不耐水湿，需要良好的排水系统，避免积水导致根系腐烂。\n\n5. 土层深度：苹果树根系发达，要求土层厚度在1米以上，以便根系充分生长。\n\n6. 养分含量：土壤应含有充足的氮、磷、钾和微量元素，特别是硼、锌等微量元素对苹果树生长结果十分重要。\n\n如果您当地的土壤条件不理想，可以通过改良土壤来创造适合苹果树生长的环境。",
    "sources": [
      {
        "title": "苹果树种植技术指南",
        "url": "http://example.com/apple-cultivation-guide",
        "snippet": "苹果树适宜生长在排水良好的砂质壤土或黏质壤土中，pH值在5.5-6.5之间..."
      },
      {
        "title": "果树栽培学",
        "url": "http://example.com/fruit-tree-cultivation",
        "snippet": "苹果树根系不耐水湿，需要良好的排水系统，避免积水导致根系腐烂..."
      }
    ],
    "conversationId": "conv_12345",
    "relatedQuestions": [
      "苹果树种植需要什么样的气候条件？",
      "苹果树施肥方法有哪些？",
      "苹果树如何进行水分管理？",
      "苹果树常见病虫害防治方法"
    ],
    "recommendations": [
      {
        "type": "product",
        "id": 101,
        "name": "有机红富士苹果",
        "image": "http://yourdomain.com/products/apple1.jpg",
        "price": 15.80
      },
      {
        "type": "article",
        "id": 201,
        "title": "苹果树四季管理技术",
        "summary": "本文详细介绍了苹果树从春季到冬季的全方位管理技术...",
        "url": "http://yourdomain.com/articles/201"
      }
    ]
  }
}
```

### 13.2 获取农产品价格预测
- **URL**: `/api/v1/ai/price-prediction`
- **方法**: POST
- **权限**: ROLE_USER
- **请求体**:
```json
{
  "productId": 101,
  "region": "全国",
  "days": 30
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "productId": 101,
    "productName": "有机红富士苹果",
    "region": "全国",
    "currentPrice": 15.80,
    "predictions": [
      {"date": "2025-01-16", "price": 15.85, "confidence": 0.95},
      {"date": "2025-01-17", "price": 15.90, "confidence": 0.94},
      {"date": "2025-01-18", "price": 15.95, "confidence": 0.93},
      // ...更多预测日期
      {"date": "2025-02-14", "price": 16.50, "confidence": 0.75}
    ],
    "trendAnalysis": "根据历史数据和季节性因素分析，预计未来30天内苹果价格将呈现缓慢上涨趋势，波动幅度较小。主要影响因素包括春节临近的消费需求增加和库存水平下降。建议在价格较低时适量采购。",
    "influencingFactors": [
      {"factor": "季节性需求", "impact": "positive", "weight": 0.4},
      {"factor": "库存水平", "impact": "positive", "weight": 0.3},
      {"factor": "运输成本", "impact": "positive", "weight": 0.2},
      {"factor": "进口竞争", "impact": "negative", "weight": 0.1}
    ],
    "historicalAccuracy": 0.92
  }
}
```

### 13.3 获取个性化商品推荐
- **URL**: `/api/v1/ai/recommendations`
- **方法**: GET
- **权限**: ROLE_USER
- **参数**:
  - type: 推荐类型（可选，0-综合，1-基于浏览历史，2-基于购买历史，3-猜你喜欢，默认0）
  - size: 返回数量（可选，默认10）
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "userPreferenceAnalysis": {
      "favoriteCategoryIds": [1, 3, 7],
      "favoriteCategoryNames": ["水果", "苹果", "富士苹果"],
      "pricePreference": "中高端",
      "tastePreference": ["酸甜", "脆"],
      "frequentPurchaseTime": "周末"
    },
    "recommendations": [
      {
        "id": 101,
        "name": "有机红富士苹果",
        "price": 15.80,
        "mainImage": "http://yourdomain.com/products/apple1.jpg",
        "rating": 4.8,
        "reasonForRecommendation": "基于您对富士苹果的浏览记录推荐",
        "similarityScore": 0.95
      },
      {
        "id": 205,
        "name": "新鲜西红柿",
        "price": 5.50,
        "mainImage": "http://yourdomain.com/products/tomato1.jpg",
        "rating": 4.7,
        "reasonForRecommendation": "与您最近购买的商品搭配推荐",
        "similarityScore": 0.85
      },
      // ...更多推荐
    ]
  }
}
```

### 13.4 获取种植建议
- **URL**: `/api/v1/ai/cultivation-advice`
- **方法**: POST
- **权限**: ROLE_USER
- **请求体**:
```json
{
  "crop": "苹果",
  "region": "山东",
  "soilType": "砂质壤土",
  "season": "春季",
  "cultivationArea": 10,
  "cultivationExperience": 2
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "crop": "苹果",
    "varietyRecommendations": [
      {"name": "红富士", "suitability": 0.95, "reason": "适合山东地区砂质壤土栽培，抗病性强，产量高"},
      {"name": "国光", "suitability": 0.85, "reason": "适合山东地区气候，储存性好，但产量略低"},
      {"name": "嘎啦", "suitability": 0.80, "reason": "早熟品种，适合春季栽培，但病虫害防治难度较大"}
    ],
    "cultivationPlan": {
      "preparation": [
        "土壤选择：选择阳光充足、排水良好的砂质壤土，地下水位需在1.5米以下。",
        "土壤检测：栽植前对土壤进行pH值、养分等检测，根据检测结果进行改良。",
        "整地：深翻土地30-40厘米，施入基肥，建议使用有机肥和磷肥。",
        "规划：规划合理的行株距，建议行距4-5米，株距2-3米。"
      ],
      "planting": [
        "苗木选择：选择2年生优质苗木，根系发达，无病虫害。",
        "定植时间：春季3月中旬至4月初最佳。",
        "定植方法：挖60厘米见方、深60厘米的树坑，施足基肥，栽植时将苗木根系舒展。",
        "定植后管理：浇足定根水，覆盖地膜减少水分蒸发。"
      ],
      "management": {
        "irrigation": "根据土壤墒情适时灌水，春季保持土壤湿润，夏季控制水量防止徒长，秋季适当控水促进果实着色。",
        "fertilization": "分期施肥：春季施氮肥促进生长，花前施硼肥促进坐果，果实膨大期施钾肥提高品质。",
        "pruning": "第一年以培养树形为主，后期注重通风透光，促进花芽形成。",
        "pestControl": "主要防治苹果蠹蛾、红蜘蛛等害虫和炭疽病、白粉病等病害。"
      }
    },
    "timelineSchedule": [
      {"time": "2月下旬", "task": "整地、规划、施基肥"},
      {"time": "3月中旬", "task": "选购苗木、定植"},
      {"time": "4月上旬", "task": "第一次追肥、病虫害防治"},
      {"time": "5月", "task": "花期管理、疏花疏果"},
      {"time": "6-7月", "task": "夏季修剪、病虫害防治"},
      {"time": "8-9月", "task": "果实膨大期管理"},
      {"time": "10月", "task": "果实采收"},
      {"time": "11-12月", "task": "冬季修剪、防寒"}
    ],
    "expectedYield": {
      "firstYear": "几乎无产量",
      "secondYear": "约50-100公斤/亩",
      "thirdYear": "约300-500公斤/亩",
      "fourthYearOnwards": "约1000-1500公斤/亩（盛果期）"
    },
    "investmentEstimate": {
      "initialInvestment": {
        "landPreparation": 2000,
        "seedlings": 5000,
        "irrigationSystem": 3000,
        "other": 1000,
        "total": 11000
      },
      "annualCost": {
        "fertilizer": 1500,
        "pesticide": 1000,
        "irrigation": 800,
        "labor": 3000,
        "total": 6300
      },
      "estimatedReturn": {
        "timeToBreakEven": "4-5年",
        "annualReturnAfterMaturity": "约15000-25000元/亩（根据市场价格波动）"
      }
    },
    "marketAnalysis": "山东地区苹果市场前景看好，尤其是优质有机苹果供不应求。建议考虑与当地合作社合作销售或发展农场直销模式。",
    "relatedServices": [
      {
        "type": "product",
        "id": 501,
        "name": "有机苹果专用肥",
        "price": 120.00
      },
      {
        "type": "service",
        "id": 601,
        "name": "苹果园规划设计服务",
        "price": 2000.00
      }
    ]
  }
}
```

### 13.5 获取营养分析
- **URL**: `/api/v1/ai/nutrition-analysis`
- **方法**: POST
- **权限**: ROLE_USER
- **请求体**:
```json
{
  "productIds": [101, 205, 306]
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "products": [
      {
        "id": 101,
        "name": "有机红富士苹果",
        "nutrition": {
          "calories": 52,
          "protein": 0.3,
          "fat": 0.2,
          "carbohydrates": 13.8,
          "fiber": 2.4,
          "vitamins": [
            {"name": "维生素C", "value": 4.6, "unit": "mg", "dailyValue": "5%"},
            {"name": "维生素A", "value": 54, "unit": "IU", "dailyValue": "1%"}
          ],
          "minerals": [
            {"name": "钾", "value": 107, "unit": "mg", "dailyValue": "3%"}
          ]
        }
      },
      // ...更多产品
    ],
    "combinedAnalysis": {
      "totalCalories": 285,
      "macronutrients": {
        "protein": {"value": 7.8, "unit": "g", "dailyValue": "16%"},
        "fat": {"value": 2.5, "unit": "g", "dailyValue": "4%"},
        "carbohydrates": {"value": 59.2, "unit": "g", "dailyValue": "20%"},
        "fiber": {"value": 6.5, "unit": "g", "dailyValue": "26%"}
      },
      "vitaminCoverage": [
        {"name": "维生素C", "coverage": "85%"},
        {"name": "维生素A", "coverage": "30%"},
        {"name": "维生素B6", "coverage": "25%"}
      ],
      "mineralCoverage": [
        {"name": "钾", "coverage": "20%"},
        {"name": "铁", "coverage": "15%"},
        {"name": "镁", "coverage": "12%"}
      ]
    },
    "healthBenefits": [
      "所选食物富含膳食纤维，有助于促进肠道健康",
      "维生素C含量丰富，增强免疫力",
      "钾含量适中，有助于维持正常血压"
    ],
    "suggestions": [
      "建议适当添加一些富含蛋白质的食物，如豆制品或坚果",
      "可以增加一些橙色或深绿色蔬菜，提高维生素A的摄入"
    ],
    "recommendations": [
      {
        "id": 401,
        "name": "有机核桃",
        "price": 58.00,
        "reason": "补充蛋白质和健康脂肪"
      },
      {
        "id": 402,
        "name": "有机胡萝卜",
        "price": 6.50,
        "reason": "补充维生素A"
      }
    ]
  }
}
``` 