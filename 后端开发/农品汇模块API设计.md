# 农品汇模块API设计文档

## 1. API设计原则

### 1.1 RESTful API设计规范
- 使用标准HTTP方法表示操作：
  - GET：查询资源
  - POST：创建资源
  - PUT：更新资源
  - DELETE：删除资源
- URL使用名词而非动词，例如`/api/v1/products`而不是`/api/v1/getProducts`
- 版本控制：在URL中包含API版本号，例如`/api/v1/...`
- 统一响应格式

### 1.2 通用响应结构
所有API响应均使用以下JSON格式：
```json
{
  "code": 200,           // 业务状态码，200表示成功
  "message": "success",  // 状态描述
  "data": { ... }        // 业务数据
}
```

### 1.3 错误处理
错误响应采用HTTP状态码 + 业务状态码的方式：
```json
{
  "code": 400001,        // 业务错误码
  "message": "参数错误：产品ID不存在",  // 错误描述
  "data": null           // 通常为null
}
```

### 1.4 分页请求参数
分页查询统一使用以下参数：
- page：当前页码，从1开始，默认为1
- size：每页记录数，默认为10
- sort：排序字段，默认为id
- order：排序方式，asc（升序）或desc（降序），默认为desc

### 1.5 分页响应结构
分页查询响应使用以下结构：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 100,          // 总记录数
    "pages": 10,           // 总页数
    "current": 1,          // 当前页码
    "size": 10,            // 每页记录数
    "items": [ ... ]       // 当前页数据项列表
  }
}
```

## 2. 农产品管理API

### 2.1 获取农产品列表
- **URL**: `/api/v1/products`
- **方法**: GET
- **权限**: 公开API
- **参数**: 
  - page: 页码
  - size: 每页记录数
  - categoryId: 分类ID（可选）
  - keyword: 搜索关键词（可选）
  - minPrice: 最低价格（可选）
  - maxPrice: 最高价格（可选）
  - sort: 排序字段（可选，price/createTime）
  - order: 排序方式（asc/desc）
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 156,
    "pages": 16,
    "current": 1,
    "size": 10,
    "items": [
      {
        "id": 101,
        "name": "有机红富士苹果",
        "description": "山东烟台特产，有机种植，自然成熟...",
        "categoryId": 7,
        "categoryName": "富士苹果",
        "price": 15.80,
        "stock": 1000,
        "origin": "山东烟台",
        "sellerId": 1,
        "sellerName": "阳光农场",
        "mainImage": "http://yourdomain.com/products/apple1.jpg",
        "rating": 4.8,
        "status": 1,
        "createdAt": "2025-01-15T10:30:00"
      },
      // ...更多产品
    ]
  }
}
```

### 2.2 获取农产品详情
- **URL**: `/api/v1/products/{id}`
- **方法**: GET
- **权限**: 公开API
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 101,
    "name": "有机红富士苹果",
    "description": "山东烟台特产，有机种植，自然成熟，不打蜡，不用催熟剂，口感甜脆，果肉细腻。",
    "categoryId": 7,
    "categoryName": "富士苹果",
    "price": 15.80,
    "stock": 1000,
    "origin": "山东烟台",
    "sellerId": 1,
    "sellerName": "阳光农场",
    "images": [
      {"id": 1001, "url": "http://yourdomain.com/products/apple1.jpg", "type": 0, "sortOrder": 1},
      {"id": 1002, "url": "http://yourdomain.com/products/apple2.jpg", "type": 0, "sortOrder": 2},
      {"id": 1003, "url": "http://yourdomain.com/products/apple_detail1.jpg", "type": 1, "sortOrder": 1}
    ],
    "tags": ["有机", "无农药", "新鲜", "水果", "苹果"],
    "rating": 4.8,
    "reviewCount": 125,
    "sales": 1580,
    "hasTraceability": true,
    "traceabilityCode": "TR2025010100001",
    "status": 1,
    "createdAt": "2025-01-15T10:30:00",
    "updatedAt": "2025-01-15T10:30:00"
  }
}
```

### 2.3 创建农产品
- **URL**: `/api/v1/products`
- **方法**: POST
- **权限**: ROLE_SELLER
- **请求体**:
```json
{
  "name": "有机红富士苹果",
  "description": "山东烟台特产，有机种植，自然成熟，不打蜡，不用催熟剂，口感甜脆，果肉细腻。",
  "categoryId": 7,
  "price": 15.80,
  "stock": 1000,
  "origin": "山东烟台",
  "images": [
    {"url": "http://yourdomain.com/products/apple1.jpg", "type": 0, "sortOrder": 1},
    {"url": "http://yourdomain.com/products/apple2.jpg", "type": 0, "sortOrder": 2},
    {"url": "http://yourdomain.com/products/apple_detail1.jpg", "type": 1, "sortOrder": 1}
  ],
  "tags": ["有机", "无农药", "新鲜", "水果", "苹果"]
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "产品创建成功",
  "data": {
    "productId": 101
  }
}
```

### 2.4 更新农产品
- **URL**: `/api/v1/products/{id}`
- **方法**: PUT
- **权限**: ROLE_SELLER (只能更新自己的产品) 或 ROLE_ADMIN
- **请求体**:
```json
{
  "name": "特级有机红富士苹果",
  "description": "山东烟台特产，有机种植，自然成熟，不打蜡，不用催熟剂，口感甜脆，果肉细腻。富含维生素和矿物质。",
  "price": 16.80,
  "stock": 800,
  "images": [
    {"url": "http://yourdomain.com/products/apple1_new.jpg", "type": 0, "sortOrder": 1},
    {"url": "http://yourdomain.com/products/apple2_new.jpg", "type": 0, "sortOrder": 2}
  ],
  "tags": ["有机", "无农药", "新鲜", "水果", "苹果", "特级"]
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "产品更新成功",
  "data": null
}
```

### 2.5 删除农产品
- **URL**: `/api/v1/products/{id}`
- **方法**: DELETE
- **权限**: ROLE_SELLER (只能删除自己的产品) 或 ROLE_ADMIN
- **响应**:
```json
{
  "code": 200,
  "message": "产品删除成功",
  "data": null
}
``` 