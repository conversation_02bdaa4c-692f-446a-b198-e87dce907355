## 6. 认证信息管理API

### 6.1 添加认证信息
- **URL**: `/api/v1/trace/certificates`
- **方法**: POST
- **权限**: ROLE_SELLER (只能添加自己溯源记录的认证) 或 ROLE_ADMIN
- **请求体**:
```json
{
  "traceRecordId": 12345,
  "certificateType": "ORGANIC",
  "certificateNo": "ORG2025001",
  "issuingAuthority": "国家有机产品认证中心",
  "issueDate": "2025-01-15",
  "validUntil": "2026-01-14",
  "certificateUrl": "http://yourdomain.com/cert/cert1.jpg",
  "description": "有机产品认证，认证范围包括种植、收获和包装过程"
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "认证信息添加成功",
  "data": {
    "certificateId": 501
  }
}
```

### 6.2 更新认证信息
- **URL**: `/api/v1/trace/certificates/{id}`
- **方法**: PUT
- **权限**: ROLE_SELLER (只能更新自己溯源记录的认证) 或 ROLE_ADMIN
- **请求体**:
```json
{
  "certificateType": "ORGANIC",
  "certificateNo": "ORG2025001",
  "issuingAuthority": "国家有机产品认证中心",
  "issueDate": "2025-01-15",
  "validUntil": "2026-01-14",
  "certificateUrl": "http://yourdomain.com/cert/cert1_updated.jpg",
  "description": "有机产品认证，认证范围包括种植、收获和包装过程，符合国家有机产品标准GB/T 19630-2019"
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "认证信息更新成功",
  "data": null
}
```

### 6.3 删除认证信息
- **URL**: `/api/v1/trace/certificates/{id}`
- **方法**: DELETE
- **权限**: ROLE_SELLER (只能删除自己溯源记录的认证) 或 ROLE_ADMIN
- **响应**:
```json
{
  "code": 200,
  "message": "认证信息删除成功",
  "data": null
}
```

### 6.4 获取溯源记录的认证信息列表
- **URL**: `/api/v1/trace/records/{recordId}/certificates`
- **方法**: GET
- **权限**: 公开API或ROLE_USER（根据溯源记录的状态判断）
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "certificateId": 501,
      "certificateType": "ORGANIC",
      "certificateTypeText": "有机认证",
      "certificateNo": "ORG2025001",
      "issuingAuthority": "国家有机产品认证中心",
      "issueDate": "2025-01-15",
      "validUntil": "2026-01-14",
      "certificateUrl": "http://yourdomain.com/cert/cert1.jpg",
      "description": "有机产品认证，认证范围包括种植、收获和包装过程",
      "createdAt": "2025-01-16T10:30:00"
    },
    // ...更多认证
  ]
}
```

## 7. 物流信息管理API

### 7.1 添加物流信息
- **URL**: `/api/v1/trace/logistics`
- **方法**: POST
- **权限**: ROLE_SELLER (只能添加自己溯源记录的物流) 或 ROLE_ADMIN
- **请求体**:
```json
{
  "traceRecordId": 12345,
  "carrierName": "快速物流公司",
  "transportType": "ROAD",
  "departureTime": "2025-09-17T08:00:00",
  "arrivalTime": "2025-09-18T15:30:00",
  "origin": "山东烟台",
  "destination": "北京市朝阳区",
  "temperature": 5.2,
  "humidity": 65.3,
  "status": 1
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "物流信息添加成功",
  "data": {
    "logisticsId": 701
  }
}
```

### 7.2 更新物流信息
- **URL**: `/api/v1/trace/logistics/{id}`
- **方法**: PUT
- **权限**: ROLE_SELLER (只能更新自己溯源记录的物流) 或 ROLE_ADMIN
- **请求体**:
```json
{
  "carrierName": "快速物流公司",
  "arrivalTime": "2025-09-18T16:45:00",
  "temperature": 5.5,
  "humidity": 66.0,
  "status": 3
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "物流信息更新成功",
  "data": null
}
```

### 7.3 删除物流信息
- **URL**: `/api/v1/trace/logistics/{id}`
- **方法**: DELETE
- **权限**: ROLE_SELLER (只能删除自己溯源记录的物流) 或 ROLE_ADMIN
- **响应**:
```json
{
  "code": 200,
  "message": "物流信息删除成功",
  "data": null
}
```

### 7.4 获取溯源记录的物流信息列表
- **URL**: `/api/v1/trace/records/{recordId}/logistics`
- **方法**: GET
- **权限**: 公开API或ROLE_USER（根据溯源记录的状态判断）
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "logisticsId": 701,
      "carrierName": "快速物流公司",
      "transportType": "ROAD",
      "transportTypeText": "公路运输",
      "departureTime": "2025-09-17T08:00:00",
      "arrivalTime": "2025-09-18T16:45:00",
      "origin": "山东烟台",
      "destination": "北京市朝阳区",
      "temperature": 5.5,
      "humidity": 66.0,
      "status": 3,
      "statusText": "已签收",
      "createdAt": "2025-09-17T08:05:00",
      "updatedAt": "2025-09-18T16:50:00"
    },
    // ...更多物流记录
  ]
}
```

## 8. 溯源报告API

### 8.1 生成溯源报告
- **URL**: `/api/v1/trace/reports/{recordId}`
- **方法**: GET
- **权限**: 公开API或ROLE_USER（根据溯源记录的状态判断）
- **参数**:
  - format: 报告格式，可选值为"html"、"pdf"，默认为"html"
- **响应**:
HTML格式时返回报告内容，PDF格式时返回文件流（application/pdf）

### 8.2 获取溯源报告模板列表
- **URL**: `/api/v1/trace/report-templates`
- **方法**: GET
- **权限**: ROLE_SELLER 或 ROLE_ADMIN
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "name": "标准溯源报告",
      "description": "包含产品基本信息、全部生产环节、认证信息和物流信息的完整报告",
      "thumbnail": "http://yourdomain.com/templates/template1.jpg"
    },
    {
      "id": 2,
      "name": "简洁溯源报告",
      "description": "包含产品基本信息和关键生产环节的简化报告",
      "thumbnail": "http://yourdomain.com/templates/template2.jpg"
    },
    // ...更多模板
  ]
}
```

## 9. 统计分析API

### 9.1 获取溯源扫码统计
- **URL**: `/api/v1/trace/stats/scans`
- **方法**: GET
- **权限**: ROLE_SELLER 或 ROLE_ADMIN
- **参数**:
  - startDate: 起始日期，格式为"yyyy-MM-dd"
  - endDate: 结束日期，格式为"yyyy-MM-dd"
  - productId: 产品ID（可选）
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "totalScans": 1256,
    "dailyScans": [
      {"date": "2025-04-01", "count": 45},
      {"date": "2025-04-02", "count": 52},
      // ...更多日期数据
    ],
    "productScans": [
      {"productId": 101, "productName": "富硒苹果", "count": 450},
      {"productId": 102, "productName": "有机青菜", "count": 320},
      // ...更多产品数据
    ],
    "regionDistribution": [
      {"region": "北京", "count": 320},
      {"region": "上海", "count": 280},
      // ...更多地区数据
    ]
  }
}
```

### 9.2 获取溯源总体统计
- **URL**: `/api/v1/trace/stats/overview`
- **方法**: GET
- **权限**: ROLE_SELLER 或 ROLE_ADMIN
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "totalTraceRecords": 56,
    "totalProducts": 23,
    "totalScans": 4580,
    "certificateCount": 35,
    "recordsByStatus": {
      "draft": 10,
      "pendingReview": 5,
      "published": 36,
      "offline": 4,
      "exception": 1
    },
    "eventTypeDistribution": {
      "planting": 56,
      "fertilizing": 120,
      "pestControl": 85,
      "harvesting": 56,
      "packaging": 56,
      "storage": 48,
      "logistics": 62
    }
  }
}
```

## 10. 销售者申请API

### 10.1 提交销售者申请
- **URL**: `/api/v1/user/apply-seller`
- **方法**: POST
- **权限**: ROLE_USER
- **请求体**:
```json
{
  "farmName": "希望农场",
  "contactPerson": "陈小明",
  "contactPhone": "13712345678",
  "address": "山东省临沂市沂蒙山区希望村",
  "qualificationDocUrls": [
    "http://yourdomain.com/uploads/qualification1.jpg",
    "http://yourdomain.com/uploads/qualification2.jpg"
  ],
  "description": "家族经营的有机果园，种植面积50亩，主要种植苹果、梨等水果。"
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "申请提交成功，等待审核",
  "data": {
    "applicationId": 201,
    "status": 0,
    "applyDate": "2025-01-20T14:30:00"
  }
}
```

### 10.2 获取销售者申请状态
- **URL**: `/api/v1/user/seller-application`
- **方法**: GET
- **权限**: ROLE_USER
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "applicationId": 201,
    "farmName": "希望农场",
    "applyDate": "2025-01-20T14:30:00",
    "status": 1,
    "statusText": "已通过",
    "auditComment": "资料齐全，审核通过",
    "auditDate": "2025-01-21T10:15:00"
  }
}
```

### 10.3 审核销售者申请（管理员）
- **URL**: `/api/v1/admin/seller-applications/{id}/audit`
- **方法**: PUT
- **权限**: ROLE_ADMIN
- **请求体**:
```json
{
  "status": 1,  // 0-待审核，1-已通过，2-已拒绝
  "auditComment": "资料齐全，审核通过"
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "审核完成",
  "data": null
}
```

### 10.4 获取销售者申请列表（管理员）
- **URL**: `/api/v1/admin/seller-applications`
- **方法**: GET
- **权限**: ROLE_ADMIN
- **参数**:
  - page: 页码
  - size: 每页记录数
  - status: 状态过滤（可选）
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 25,
    "pages": 3,
    "current": 1,
    "size": 10,
    "items": [
      {
        "id": 201,
        "userId": 123,
        "username": "chen_xm",
        "farmName": "希望农场",
        "contactPerson": "陈小明",
        "applyDate": "2025-01-20T14:30:00",
        "status": 0,
        "statusText": "待审核"
      },
      // ...更多记录
    ]
  }
}
```

## 11. 文件上传API

### 11.1 上传图片
- **URL**: `/api/v1/upload/image`
- **方法**: POST
- **权限**: ROLE_USER
- **请求体**: multipart/form-data
  - file: 图片文件
  - type: 图片类型（"event", "certificate", "product", "qualification"）
- **响应**:
```json
{
  "code": 200,
  "message": "上传成功",
  "data": {
    "url": "http://yourdomain.com/uploads/20250120/abc123.jpg",
    "filename": "abc123.jpg"
  }
}
```

### 11.2 上传视频
- **URL**: `/api/v1/upload/video`
- **方法**: POST
- **权限**: ROLE_USER
- **请求体**: multipart/form-data
  - file: 视频文件
  - type: 视频类型（"event", "promotion"）
- **响应**:
```json
{
  "code": 200,
  "message": "上传成功",
  "data": {
    "url": "http://yourdomain.com/uploads/videos/20250120/xyz789.mp4",
    "filename": "xyz789.mp4",
    "duration": 15.5,
    "thumbnailUrl": "http://yourdomain.com/uploads/videos/20250120/xyz789_thumb.jpg"
  }
}
``` 