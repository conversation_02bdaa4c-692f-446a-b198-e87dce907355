## 8. 用户评价与评论API

### 8.1 提交商品评价
- **URL**: `/api/v1/reviews`
- **方法**: POST
- **权限**: ROLE_USER
- **请求体**:
```json
{
  "orderId": "ORD20250115001",
  "productId": 101,
  "rating": 5,
  "content": "苹果非常新鲜，口感很好，物流速度快，包装完好无损。",
  "images": [
    "http://yourdomain.com/uploads/review1.jpg",
    "http://yourdomain.com/uploads/review2.jpg"
  ],
  "anonymous": false
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "评价提交成功",
  "data": {
    "reviewId": 3001
  }
}
```

### 8.2 获取商品评价列表
- **URL**: `/api/v1/products/{id}/reviews`
- **方法**: GET
- **权限**: 公开API
- **参数**:
  - page: 页码
  - size: 每页记录数
  - rating: 评分过滤（可选，1-5）
  - hasImage: 是否有图片（可选，true/false）
  - sort: 排序字段（可选，time/rating/usefulness）
  - order: 排序方式（asc/desc）
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 125,
    "pages": 13,
    "current": 1,
    "size": 10,
    "summary": {
      "avgRating": 4.8,
      "ratingCount": {
        "5": 100,
        "4": 20,
        "3": 3,
        "2": 1,
        "1": 1
      },
      "hasImageCount": 80
    },
    "items": [
      {
        "id": 3001,
        "userId": 123,
        "username": "user123",
        "userAvatar": "http://yourdomain.com/avatars/avatar1.jpg",
        "anonymous": false,
        "rating": 5,
        "content": "苹果非常新鲜，口感很好，物流速度快，包装完好无损。",
        "images": [
          "http://yourdomain.com/uploads/review1.jpg",
          "http://yourdomain.com/uploads/review2.jpg"
        ],
        "likeCount": 15,
        "createdAt": "2025-01-16T10:30:00",
        "hasOrderPurchase": true,
        "reply": {
          "content": "感谢您的好评，我们会继续提供优质的产品和服务！",
          "replyTime": "2025-01-16T15:20:00"
        }
      },
      // ...更多评价
    ]
  }
}
```

### 8.3 回复商品评价
- **URL**: `/api/v1/reviews/{id}/reply`
- **方法**: POST
- **权限**: ROLE_SELLER (只能回复自己商品的评价) 或 ROLE_ADMIN
- **请求体**:
```json
{
  "content": "感谢您的好评，我们会继续提供优质的产品和服务！"
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "回复成功",
  "data": null
}
```

### 8.4 点赞商品评价
- **URL**: `/api/v1/reviews/{id}/like`
- **方法**: PUT
- **权限**: ROLE_USER
- **响应**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "likeCount": 16
  }
}
```

### 8.5 取消点赞商品评价
- **URL**: `/api/v1/reviews/{id}/like`
- **方法**: DELETE
- **权限**: ROLE_USER
- **响应**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "likeCount": 15
  }
}
```

### 8.6 获取用户评价列表
- **URL**: `/api/v1/user/reviews`
- **方法**: GET
- **权限**: ROLE_USER
- **参数**:
  - page: 页码
  - size: 每页记录数
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 25,
    "pages": 3,
    "current": 1,
    "size": 10,
    "items": [
      {
        "id": 3001,
        "productId": 101,
        "productName": "有机红富士苹果",
        "productImage": "http://yourdomain.com/products/apple1.jpg",
        "rating": 5,
        "content": "苹果非常新鲜，口感很好，物流速度快，包装完好无损。",
        "images": [
          "http://yourdomain.com/uploads/review1.jpg",
          "http://yourdomain.com/uploads/review2.jpg"
        ],
        "likeCount": 15,
        "createdAt": "2025-01-16T10:30:00",
        "hasReply": true,
        "reply": {
          "content": "感谢您的好评，我们会继续提供优质的产品和服务！",
          "replyTime": "2025-01-16T15:20:00"
        }
      },
      // ...更多评价
    ]
  }
}
```

### 8.7 删除商品评价
- **URL**: `/api/v1/reviews/{id}`
- **方法**: DELETE
- **权限**: ROLE_USER (只能删除自己的评价) 或 ROLE_ADMIN
- **响应**:
```json
{
  "code": 200,
  "message": "评价已删除",
  "data": null
}
```

## 9. 优惠券API

### 9.1 获取优惠券列表
- **URL**: `/api/v1/coupons`
- **方法**: GET
- **权限**: ROLE_USER
- **参数**:
  - status: 状态过滤（可选，0-未使用，1-已使用，2-已过期，3-已领取）
  - page: 页码
  - size: 每页记录数
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 15,
    "pages": 2,
    "current": 1,
    "size": 10,
    "items": [
      {
        "id": 5001,
        "name": "新人专享优惠",
        "type": 1,
        "typeText": "满减券",
        "value": 10.00,
        "minOrderAmount": 100.00,
        "status": 0,
        "statusText": "未使用",
        "validFrom": "2025-01-01T00:00:00",
        "validTo": "2025-01-31T23:59:59",
        "description": "新用户首单满100减10元",
        "useLimit": ["水果", "蔬菜"],
        "receiveTime": "2025-01-05T10:00:00"
      },
      // ...更多优惠券
    ]
  }
}
```

### 9.2 领取优惠券
- **URL**: `/api/v1/coupons/{id}/receive`
- **方法**: POST
- **权限**: ROLE_USER
- **响应**:
```json
{
  "code": 200,
  "message": "优惠券领取成功",
  "data": null
}
```

### 9.3 创建优惠券（管理端）
- **URL**: `/api/v1/admin/coupons`
- **方法**: POST
- **权限**: ROLE_ADMIN
- **请求体**:
```json
{
  "name": "618购物节优惠",
  "type": 1,
  "value": 20.00,
  "minOrderAmount": 200.00,
  "validFrom": "2025-06-01T00:00:00",
  "validTo": "2025-06-18T23:59:59",
  "description": "618活动期间，满200减20元",
  "useLimit": ["全场通用"],
  "totalCount": 1000,
  "perUserLimit": 1
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "优惠券创建成功",
  "data": {
    "couponId": 5002
  }
}
```

## 10. 收藏与关注API

### 10.1 收藏商品
- **URL**: `/api/v1/favorites/products`
- **方法**: POST
- **权限**: ROLE_USER
- **请求体**:
```json
{
  "productId": 101
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "商品已收藏",
  "data": null
}
```

### 10.2 取消收藏商品
- **URL**: `/api/v1/favorites/products/{productId}`
- **方法**: DELETE
- **权限**: ROLE_USER
- **响应**:
```json
{
  "code": 200,
  "message": "已取消收藏",
  "data": null
}
```

### 10.3 获取收藏商品列表
- **URL**: `/api/v1/favorites/products`
- **方法**: GET
- **权限**: ROLE_USER
- **参数**:
  - page: 页码
  - size: 每页记录数
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 28,
    "pages": 3,
    "current": 1,
    "size": 10,
    "items": [
      {
        "id": 101,
        "name": "有机红富士苹果",
        "price": 15.80,
        "mainImage": "http://yourdomain.com/products/apple1.jpg",
        "favoriteTime": "2025-01-10T10:30:00",
        "inStock": true
      },
      // ...更多收藏商品
    ]
  }
}
```

### 10.4 关注店铺
- **URL**: `/api/v1/follows/sellers`
- **方法**: POST
- **权限**: ROLE_USER
- **请求体**:
```json
{
  "sellerId": 1
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "已关注店铺",
  "data": null
}
```

### 10.5 取消关注店铺
- **URL**: `/api/v1/follows/sellers/{sellerId}`
- **方法**: DELETE
- **权限**: ROLE_USER
- **响应**:
```json
{
  "code": 200,
  "message": "已取消关注",
  "data": null
}
```

### 10.6 获取关注店铺列表
- **URL**: `/api/v1/follows/sellers`
- **方法**: GET
- **权限**: ROLE_USER
- **参数**:
  - page: 页码
  - size: 每页记录数
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 10,
    "pages": 1,
    "current": 1,
    "size": 10,
    "items": [
      {
        "id": 1,
        "name": "阳光农场",
        "logo": "http://yourdomain.com/sellers/logo1.jpg",
        "description": "专注有机水果种植，提供健康安全的农产品",
        "followTime": "2025-01-05T14:20:00",
        "productCount": 25,
        "rating": 4.9,
        "followCount": 1250
      },
      // ...更多关注店铺
    ]
  }
}
```

## 11. 店铺管理API

### 11.1 获取店铺信息
- **URL**: `/api/v1/sellers/{id}`
- **方法**: GET
- **权限**: 公开API
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "name": "阳光农场",
    "logo": "http://yourdomain.com/sellers/logo1.jpg",
    "banner": "http://yourdomain.com/sellers/banner1.jpg",
    "description": "专注有机水果种植，提供健康安全的农产品",
    "contactPerson": "陈小明",
    "contactPhone": "***********",
    "address": "山东省临沂市沂蒙山区希望村",
    "businessLicense": "http://yourdomain.com/sellers/license1.jpg",
    "certifications": [
      {
        "name": "有机产品认证",
        "imageUrl": "http://yourdomain.com/sellers/cert1.jpg"
      }
    ],
    "rating": 4.9,
    "productCount": 25,
    "followCount": 1250,
    "orderCount": 5680,
    "createTime": "2024-01-01T00:00:00",
    "categoryDistribution": [
      {"category": "水果", "count": 15},
      {"category": "蔬菜", "count": 10}
    ]
  }
}
```

### 11.2 获取店铺商品列表
- **URL**: `/api/v1/sellers/{id}/products`
- **方法**: GET
- **权限**: 公开API
- **参数**:
  - page: 页码
  - size: 每页记录数
  - sort: 排序字段（可选）
  - order: 排序方式（asc/desc）
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 25,
    "pages": 3,
    "current": 1,
    "size": 10,
    "items": [
      {
        "id": 101,
        "name": "有机红富士苹果",
        "description": "山东烟台特产，有机种植，自然成熟...",
        "price": 15.80,
        "mainImage": "http://yourdomain.com/products/apple1.jpg",
        "sales": 1580,
        "rating": 4.8,
        "inStock": true
      },
      // ...更多商品
    ]
  }
}
```

### 11.3 更新店铺信息
- **URL**: `/api/v1/sellers/{id}`
- **方法**: PUT
- **权限**: ROLE_SELLER (只能更新自己的店铺) 或 ROLE_ADMIN
- **请求体**:
```json
{
  "name": "阳光有机农场",
  "logo": "http://yourdomain.com/sellers/new_logo1.jpg",
  "banner": "http://yourdomain.com/sellers/new_banner1.jpg",
  "description": "专注有机水果蔬菜种植，提供健康安全的农产品",
  "contactPerson": "陈大明",
  "contactPhone": "13812345678"
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "店铺信息更新成功",
  "data": null
}
``` 