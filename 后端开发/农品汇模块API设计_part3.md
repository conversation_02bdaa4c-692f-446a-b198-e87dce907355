## 5. 购物车API

### 5.1 获取购物车内容
- **URL**: `/api/v1/cart`
- **方法**: GET
- **权限**: ROLE_USER
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "cartId": "cart_1001",
    "totalItems": 3,
    "totalQuantity": 11,
    "totalAmount": 258.50,
    "items": [
      {
        "id": 2001,
        "productId": 101,
        "productName": "有机红富士苹果",
        "price": 15.80,
        "quantity": 5,
        "amount": 79.00,
        "mainImage": "http://yourdomain.com/products/apple1.jpg",
        "checked": true,
        "inStock": true,
        "addTime": "2025-01-15T15:30:00"
      },
      {
        "id": 2002,
        "productId": 205,
        "productName": "新鲜西红柿",
        "price": 5.50,
        "quantity": 3,
        "amount": 16.50,
        "mainImage": "http://yourdomain.com/products/tomato1.jpg",
        "checked": true,
        "inStock": true,
        "addTime": "2025-01-15T15:31:20"
      },
      {
        "id": 2003,
        "productId": 306,
        "productName": "有机大米",
        "price": 54.00,
        "quantity": 3,
        "amount": 162.00,
        "mainImage": "http://yourdomain.com/products/rice1.jpg",
        "checked": true,
        "inStock": true,
        "addTime": "2025-01-15T15:33:45"
      }
    ]
  }
}
```

### 5.2 添加商品到购物车
- **URL**: `/api/v1/cart`
- **方法**: POST
- **权限**: ROLE_USER
- **请求体**:
```json
{
  "productId": 101,
  "quantity": 2
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "商品已添加到购物车",
  "data": {
    "cartItemId": 2001
  }
}
```

### 5.3 更新购物车商品数量
- **URL**: `/api/v1/cart/{itemId}`
- **方法**: PUT
- **权限**: ROLE_USER
- **请求体**:
```json
{
  "quantity": 5
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "购物车已更新",
  "data": {
    "itemId": 2001,
    "quantity": 5,
    "amount": 79.00
  }
}
```

### 5.4 选择或取消选择购物车商品
- **URL**: `/api/v1/cart/{itemId}/check`
- **方法**: PUT
- **权限**: ROLE_USER
- **请求体**:
```json
{
  "checked": true
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": null
}
```

### 5.5 删除购物车商品
- **URL**: `/api/v1/cart/{itemId}`
- **方法**: DELETE
- **权限**: ROLE_USER
- **响应**:
```json
{
  "code": 200,
  "message": "商品已从购物车移除",
  "data": null
}
```

### 5.6 清空购物车
- **URL**: `/api/v1/cart/clear`
- **方法**: DELETE
- **权限**: ROLE_USER
- **响应**:
```json
{
  "code": 200,
  "message": "购物车已清空",
  "data": null
}
```

## 6. 订单管理API

### 6.1 创建订单
- **URL**: `/api/v1/orders`
- **方法**: POST
- **权限**: ROLE_USER
- **请求体**:
```json
{
  "addressId": 101,
  "items": [
    {"cartItemId": 2001},
    {"cartItemId": 2002},
    {"cartItemId": 2003}
  ],
  "paymentType": 1,
  "couponId": 5001,
  "note": "请在周六下午送达，谢谢！"
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "订单创建成功",
  "data": {
    "orderId": "ORD20250115001",
    "orderAmount": 248.50,
    "paymentUrl": "http://yourdomain.com/pay/ORD20250115001"
  }
}
```

### 6.2 获取订单列表
- **URL**: `/api/v1/orders`
- **方法**: GET
- **权限**: ROLE_USER
- **参数**:
  - page: 页码
  - size: 每页记录数
  - status: 订单状态（可选，0-待付款，1-待发货，2-待收货，3-已完成，4-已取消）
  - startDate: 起始日期（可选）
  - endDate: 结束日期（可选）
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 28,
    "pages": 3,
    "current": 1,
    "size": 10,
    "items": [
      {
        "id": "ORD20250115001",
        "orderTime": "2025-01-15T16:30:00",
        "status": 1,
        "statusText": "待发货",
        "totalAmount": 248.50,
        "itemCount": 3,
        "productSnapshot": [
          {
            "productId": 101,
            "productName": "有机红富士苹果",
            "quantity": 5,
            "mainImage": "http://yourdomain.com/products/apple1.jpg"
          },
          {
            "productId": 205,
            "productName": "新鲜西红柿",
            "quantity": 3,
            "mainImage": "http://yourdomain.com/products/tomato1.jpg"
          }
          // 第三个商品省略
        ]
      },
      // ...更多订单
    ]
  }
}
```

### 6.3 获取订单详情
- **URL**: `/api/v1/orders/{id}`
- **方法**: GET
- **权限**: ROLE_USER
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "ORD20250115001",
    "orderTime": "2025-01-15T16:30:00",
    "paymentTime": "2025-01-15T16:35:00",
    "deliveryTime": null,
    "receivingTime": null,
    "status": 1,
    "statusText": "待发货",
    "totalAmount": 248.50,
    "paymentAmount": 248.50,
    "discountAmount": 10.00,
    "expressAmount": 10.00,
    "paymentType": 1,
    "paymentTypeText": "微信支付",
    "deliveryType": 0,
    "deliveryTypeText": "快递",
    "expressName": "中通快递",
    "expressNumber": "ZT12345678",
    "note": "请在周六下午送达，谢谢！",
    "address": {
      "id": 101,
      "name": "张三",
      "phone": "13712345678",
      "province": "北京市",
      "city": "北京市",
      "district": "朝阳区",
      "detail": "建国路88号",
      "isDefault": true
    },
    "items": [
      {
        "productId": 101,
        "productName": "有机红富士苹果",
        "price": 15.80,
        "quantity": 5,
        "amount": 79.00,
        "mainImage": "http://yourdomain.com/products/apple1.jpg"
      },
      {
        "productId": 205,
        "productName": "新鲜西红柿",
        "price": 5.50,
        "quantity": 3,
        "amount": 16.50,
        "mainImage": "http://yourdomain.com/products/tomato1.jpg"
      },
      {
        "productId": 306,
        "productName": "有机大米",
        "price": 54.00,
        "quantity": 3,
        "amount": 162.00,
        "mainImage": "http://yourdomain.com/products/rice1.jpg"
      }
    ],
    "timeline": [
      {"time": "2025-01-15T16:30:00", "status": 0, "text": "订单已创建"},
      {"time": "2025-01-15T16:35:00", "status": 1, "text": "支付成功"}
    ]
  }
}
```

### 6.4 取消订单
- **URL**: `/api/v1/orders/{id}/cancel`
- **方法**: PUT
- **权限**: ROLE_USER
- **请求体**:
```json
{
  "reason": "需要修改订单信息"
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "订单已取消",
  "data": null
}
```

### 6.5 确认收货
- **URL**: `/api/v1/orders/{id}/receive`
- **方法**: PUT
- **权限**: ROLE_USER
- **响应**:
```json
{
  "code": 200,
  "message": "已确认收货",
  "data": null
}
```

### 6.6 申请退款
- **URL**: `/api/v1/orders/{id}/refund`
- **方法**: POST
- **权限**: ROLE_USER
- **请求体**:
```json
{
  "reason": "商品质量问题",
  "description": "收到的苹果已经有部分腐烂",
  "refundAmount": 79.00,
  "images": [
    "http://yourdomain.com/uploads/refund1.jpg",
    "http://yourdomain.com/uploads/refund2.jpg"
  ]
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "退款申请已提交",
  "data": {
    "refundId": "REF20250116001"
  }
}
```

### 6.7 获取订单统计
- **URL**: `/api/v1/orders/stats`
- **方法**: GET
- **权限**: ROLE_USER
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "unpaid": 3,     // 待付款
    "unshipped": 5,  // 待发货
    "unreceipt": 2,  // 待收货
    "completed": 18, // 已完成
    "refunding": 1   // 退款中
  }
}
```

## 7. 收货地址API

### 7.1 获取收货地址列表
- **URL**: `/api/v1/addresses`
- **方法**: GET
- **权限**: ROLE_USER
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 101,
      "name": "张三",
      "phone": "13712345678",
      "province": "北京市",
      "city": "北京市",
      "district": "朝阳区",
      "detail": "建国路88号",
      "isDefault": true,
      "createdAt": "2025-01-01T10:00:00"
    },
    {
      "id": 102,
      "name": "李四",
      "phone": "13712345679",
      "province": "上海市",
      "city": "上海市",
      "district": "浦东新区",
      "detail": "张杨路500号",
      "isDefault": false,
      "createdAt": "2025-01-10T14:20:00"
    }
  ]
}
```

### 7.2 获取收货地址详情
- **URL**: `/api/v1/addresses/{id}`
- **方法**: GET
- **权限**: ROLE_USER
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 101,
    "name": "张三",
    "phone": "13712345678",
    "province": "北京市",
    "city": "北京市",
    "district": "朝阳区",
    "detail": "建国路88号",
    "zipCode": "100010",
    "isDefault": true,
    "createdAt": "2025-01-01T10:00:00",
    "updatedAt": "2025-01-01T10:00:00"
  }
}
```

### 7.3 添加收货地址
- **URL**: `/api/v1/addresses`
- **方法**: POST
- **权限**: ROLE_USER
- **请求体**:
```json
{
  "name": "王五",
  "phone": "13712345680",
  "province": "广东省",
  "city": "广州市",
  "district": "天河区",
  "detail": "天河路385号",
  "zipCode": "510000",
  "isDefault": false
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "地址添加成功",
  "data": {
    "addressId": 103
  }
}
```

### 7.4 更新收货地址
- **URL**: `/api/v1/addresses/{id}`
- **方法**: PUT
- **权限**: ROLE_USER
- **请求体**:
```json
{
  "name": "王五",
  "phone": "13712345681",
  "province": "广东省",
  "city": "广州市",
  "district": "天河区",
  "detail": "天河路388号",
  "zipCode": "510000",
  "isDefault": true
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "地址更新成功",
  "data": null
}
```

### 7.5 删除收货地址
- **URL**: `/api/v1/addresses/{id}`
- **方法**: DELETE
- **权限**: ROLE_USER
- **响应**:
```json
{
  "code": 200,
  "message": "地址删除成功",
  "data": null
}
```

### 7.6 设为默认地址
- **URL**: `/api/v1/addresses/{id}/default`
- **方法**: PUT
- **权限**: ROLE_USER
- **响应**:
```json
{
  "code": 200,
  "message": "已设为默认地址",
  "data": null
}
``` 