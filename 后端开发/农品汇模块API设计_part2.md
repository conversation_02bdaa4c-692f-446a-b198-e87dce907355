## 3. 分类管理API

### 3.1 获取分类列表
- **URL**: `/api/v1/categories`
- **方法**: GET
- **权限**: 公开API
- **参数**:
  - parentId: 父分类ID（可选，不提供则获取所有顶级分类）
  - level: 层级（可选）
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "name": "水果",
      "parentId": null,
      "level": 1,
      "sortOrder": 1,
      "iconUrl": "http://yourdomain.com/icons/fruit.png",
      "children": [
        {
          "id": 3,
          "name": "苹果",
          "parentId": 1,
          "level": 2,
          "sortOrder": 1,
          "iconUrl": "http://yourdomain.com/icons/apple.png",
          "children": [
            {
              "id": 7,
              "name": "富士苹果",
              "parentId": 3,
              "level": 3,
              "sortOrder": 1,
              "iconUrl": null,
              "children": []
            },
            // ...更多子分类
          ]
        },
        // ...更多子分类
      ]
    },
    // ...更多顶级分类
  ]
}
```

### 3.2 获取分类详情
- **URL**: `/api/v1/categories/{id}`
- **方法**: GET
- **权限**: 公开API
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 3,
    "name": "苹果",
    "parentId": 1,
    "parentName": "水果",
    "level": 2,
    "sortOrder": 1,
    "iconUrl": "http://yourdomain.com/icons/apple.png",
    "children": [
      {
        "id": 7,
        "name": "富士苹果",
        "level": 3,
        "sortOrder": 1,
        "iconUrl": null
      },
      // ...更多子分类
    ],
    "createdAt": "2025-01-01T00:00:00",
    "updatedAt": "2025-01-01T00:00:00"
  }
}
```

### 3.3 创建分类
- **URL**: `/api/v1/categories`
- **方法**: POST
- **权限**: ROLE_ADMIN
- **请求体**:
```json
{
  "name": "国光苹果",
  "parentId": 3,
  "sortOrder": 2,
  "iconUrl": "http://yourdomain.com/icons/guoguang.png"
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "分类创建成功",
  "data": {
    "categoryId": 8,
    "level": 3
  }
}
```

### 3.4 更新分类
- **URL**: `/api/v1/categories/{id}`
- **方法**: PUT
- **权限**: ROLE_ADMIN
- **请求体**:
```json
{
  "name": "国光苹果(特级)",
  "sortOrder": 3,
  "iconUrl": "http://yourdomain.com/icons/guoguang_premium.png"
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "分类更新成功",
  "data": null
}
```

### 3.5 删除分类
- **URL**: `/api/v1/categories/{id}`
- **方法**: DELETE
- **权限**: ROLE_ADMIN
- **响应**:
```json
{
  "code": 200,
  "message": "分类删除成功",
  "data": null
}
```

## 4. 农产品价格API

### 4.1 获取产品价格历史
- **URL**: `/api/v1/prices/{productId}/history`
- **方法**: GET
- **权限**: 公开API
- **参数**:
  - startDate: 起始日期（可选）
  - endDate: 结束日期（可选）
  - priceType: 价格类型（可选，"retail"零售或"wholesale"批发）
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "productId": 101,
    "productName": "有机红富士苹果",
    "priceHistory": [
      {"date": "2025-01-15", "price": 15.80, "priceType": "retail"},
      {"date": "2025-01-14", "price": 14.50, "priceType": "retail"},
      {"date": "2025-01-13", "price": 14.20, "priceType": "retail"},
      {"date": "2025-01-12", "price": 14.00, "priceType": "retail"},
      {"date": "2025-01-11", "price": 13.80, "priceType": "retail"}
    ],
    "averagePrice": 14.46,
    "maxPrice": 15.80,
    "minPrice": 13.80,
    "trend": "up"  // up上升，down下降，stable稳定
  }
}
```

### 4.2 获取价格指数
- **URL**: `/api/v1/prices/index`
- **方法**: GET
- **权限**: 公开API
- **参数**:
  - categoryId: 分类ID（可选）
  - region: 区域（可选）
  - days: 天数（可选，默认7天）
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "currentIndex": 105.6,
    "previousIndex": 102.3,
    "changeRate": 3.23,  // 涨跌幅(%)
    "dailyIndices": [
      {"date": "2025-01-15", "index": 105.6},
      {"date": "2025-01-14", "index": 104.8},
      {"date": "2025-01-13", "index": 103.5},
      {"date": "2025-01-12", "index": 103.2},
      {"date": "2025-01-11", "index": 102.7},
      {"date": "2025-01-10", "index": 102.5},
      {"date": "2025-01-09", "index": 102.3}
    ],
    "categoryIndices": [
      {"categoryId": 1, "categoryName": "水果", "index": 108.2, "changeRate": 4.5},
      {"categoryId": 2, "categoryName": "蔬菜", "index": 103.1, "changeRate": 1.8},
      // ...更多分类
    ]
  }
}
```

### 4.3 获取价格分析
- **URL**: `/api/v1/prices/analysis`
- **方法**: GET
- **权限**: 公开API
- **参数**:
  - productId: 产品ID
  - days: 分析天数（可选，默认30天）
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "productId": 101,
    "productName": "有机红富士苹果",
    "currentPrice": 15.80,
    "averagePrice": 14.46,
    "priceVolatility": 5.23,  // 价格波动率(%)
    "priceRange": {
      "min": 13.80,
      "max": 15.80
    },
    "trend": "up",
    "seasonalityFactor": 1.05,  // 季节性因子
    "forecastPrices": [
      {"date": "2025-01-16", "price": 15.85},
      {"date": "2025-01-17", "price": 15.90},
      {"date": "2025-01-18", "price": 15.95},
      // ...更多预测
    ],
    "marketAnalysis": "当前苹果价格呈上升趋势，主要受季节性因素和供需关系影响。预计后续价格将稳中有升，波动空间有限。"
  }
}
```

### 4.4 创建价格记录
- **URL**: `/api/v1/prices`
- **方法**: POST
- **权限**: ROLE_SELLER 或 ROLE_ADMIN
- **请求体**:
```json
{
  "productId": 101,
  "price": 16.20,
  "recordDate": "2025-01-16",
  "priceType": "retail",
  "source": "官方售价"
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "价格记录创建成功",
  "data": {
    "priceId": 1001
  }
}
``` 