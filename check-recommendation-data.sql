-- 智慧农业平台推荐系统数据检查脚本
-- 检查商品数据是否足够支持推荐功能

-- 1. 检查商品总数
SELECT '=== 商品数据总览 ===' as info;
SELECT 
    COUNT(*) as total_products,
    COUNT(CASE WHEN status = 1 AND deleted = 0 THEN 1 END) as active_products,
    COUNT(CASE WHEN status = 1 AND deleted = 0 AND stock > 0 THEN 1 END) as available_products,
    COUNT(CASE WHEN is_hot = 1 AND status = 1 AND deleted = 0 THEN 1 END) as hot_products,
    COUNT(CASE WHEN is_new = 1 AND status = 1 AND deleted = 0 THEN 1 END) as new_products,
    COUNT(CASE WHEN is_featured = 1 AND status = 1 AND deleted = 0 THEN 1 END) as featured_products
FROM product;

-- 2. 检查热门商品推荐数据
SELECT '=== 热门商品推荐数据检查 ===' as info;
SELECT 
    id,
    name,
    price,
    sales_count,
    view_count,
    rating,
    stock,
    is_hot,
    created_at,
    CASE 
        WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) AND sales_count >= 10 THEN '✅ 符合热门条件'
        WHEN sales_count < 10 THEN '❌ 销量不足'
        WHEN created_at < DATE_SUB(NOW(), INTERVAL 7 DAY) THEN '❌ 创建时间过早'
        ELSE '❌ 其他问题'
    END as hot_status
FROM product 
WHERE deleted = 0 AND status = 1 
ORDER BY sales_count DESC, rating DESC, view_count DESC 
LIMIT 10;

-- 3. 检查新品推荐数据
SELECT '=== 新品推荐数据检查 ===' as info;
SELECT 
    id,
    name,
    price,
    rating,
    stock,
    is_new,
    created_at,
    DATEDIFF(NOW(), created_at) as days_since_created,
    CASE 
        WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN '✅ 符合新品条件'
        ELSE '❌ 创建时间过早'
    END as new_status
FROM product 
WHERE deleted = 0 AND status = 1 
ORDER BY created_at DESC, rating DESC 
LIMIT 10;

-- 4. 检查商品分类分布
SELECT '=== 商品分类分布 ===' as info;
SELECT 
    c.name as category_name,
    COUNT(p.id) as product_count,
    COUNT(CASE WHEN p.status = 1 AND p.deleted = 0 THEN 1 END) as active_count,
    AVG(p.price) as avg_price,
    MAX(p.sales_count) as max_sales
FROM product p
LEFT JOIN category c ON p.category_id = c.id
WHERE p.deleted = 0
GROUP BY c.id, c.name
ORDER BY product_count DESC;

-- 5. 检查商品图片配置
SELECT '=== 商品图片配置检查 ===' as info;
SELECT 
    COUNT(*) as total_products,
    COUNT(CASE WHEN image IS NOT NULL AND image != '' THEN 1 END) as has_image,
    COUNT(CASE WHEN image LIKE '/uploads/images/products/%' THEN 1 END) as correct_image_path,
    COUNT(CASE WHEN image LIKE 'http%' THEN 1 END) as external_image,
    COUNT(CASE WHEN image IS NULL OR image = '' THEN 1 END) as no_image
FROM product 
WHERE deleted = 0 AND status = 1;

-- 6. 显示一些商品图片路径示例
SELECT '=== 商品图片路径示例 ===' as info;
SELECT 
    id,
    name,
    image,
    CASE 
        WHEN image IS NULL OR image = '' THEN '❌ 无图片'
        WHEN image LIKE '/uploads/images/products/%' THEN '✅ 正确路径'
        WHEN image LIKE 'http%' THEN '⚠️ 外部链接'
        ELSE '❌ 错误格式'
    END as image_status
FROM product 
WHERE deleted = 0 AND status = 1 
LIMIT 10;

-- 7. 检查推荐算法需要的字段
SELECT '=== 推荐算法字段检查 ===' as info;
SELECT 
    COUNT(*) as total_products,
    COUNT(CASE WHEN sales_count IS NOT NULL THEN 1 END) as has_sales_count,
    COUNT(CASE WHEN view_count IS NOT NULL THEN 1 END) as has_view_count,
    COUNT(CASE WHEN rating IS NOT NULL THEN 1 END) as has_rating,
    COUNT(CASE WHEN category_id IS NOT NULL THEN 1 END) as has_category,
    COUNT(CASE WHEN brand IS NOT NULL AND brand != '' THEN 1 END) as has_brand,
    COUNT(CASE WHEN origin IS NOT NULL AND origin != '' THEN 1 END) as has_origin
FROM product 
WHERE deleted = 0 AND status = 1;

-- 8. 检查最近的商品活动
SELECT '=== 最近商品活动 ===' as info;
SELECT 
    DATE(created_at) as date,
    COUNT(*) as products_created,
    AVG(price) as avg_price
FROM product 
WHERE deleted = 0 AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY DATE(created_at)
ORDER BY date DESC
LIMIT 10;

-- 9. 模拟热门商品推荐查询
SELECT '=== 模拟热门商品推荐查询 ===' as info;
SELECT 
    id,
    name,
    price,
    sales_count,
    view_count,
    rating,
    image,
    '热门推荐' as recommendation_type
FROM product 
WHERE deleted = 0 AND status = 1 
    AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) 
    AND sales_count >= 10 
ORDER BY sales_count DESC, rating DESC, view_count DESC 
LIMIT 6;

-- 10. 模拟新品推荐查询
SELECT '=== 模拟新品推荐查询 ===' as info;
SELECT 
    id,
    name,
    price,
    rating,
    stock,
    image,
    created_at,
    '新品推荐' as recommendation_type
FROM product 
WHERE deleted = 0 AND status = 1 
    AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) 
ORDER BY created_at DESC, rating DESC 
LIMIT 8;

-- 11. 如果没有足够数据，提供修复建议
SELECT '=== 数据修复建议 ===' as info;

-- 检查是否需要更新商品标记
SELECT 
    CASE 
        WHEN (SELECT COUNT(*) FROM product WHERE deleted = 0 AND status = 1 AND sales_count >= 10) < 6 
        THEN '建议：更新部分商品的销量数据以支持热门推荐'
        ELSE '热门商品数据充足'
    END as hot_products_suggestion
UNION ALL
SELECT 
    CASE 
        WHEN (SELECT COUNT(*) FROM product WHERE deleted = 0 AND status = 1 AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)) < 8 
        THEN '建议：更新部分商品的创建时间或添加新商品以支持新品推荐'
        ELSE '新品数据充足'
    END as new_products_suggestion
UNION ALL
SELECT 
    CASE 
        WHEN (SELECT COUNT(*) FROM product WHERE deleted = 0 AND status = 1 AND (image IS NULL OR image = '')) > 0 
        THEN '建议：为没有图片的商品添加图片路径'
        ELSE '商品图片配置完整'
    END as image_suggestion;

-- 12. 提供数据修复SQL（如果需要）
SELECT '=== 数据修复SQL示例 ===' as info;
SELECT '-- 如果热门商品不足，可以执行以下SQL更新销量数据：' as fix_sql
UNION ALL
SELECT 'UPDATE product SET sales_count = sales_count + 50 WHERE id IN (SELECT id FROM (SELECT id FROM product WHERE deleted = 0 AND status = 1 ORDER BY rating DESC LIMIT 10) t);' as fix_sql
UNION ALL
SELECT '-- 如果新品不足，可以执行以下SQL更新创建时间：' as fix_sql
UNION ALL
SELECT 'UPDATE product SET created_at = DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 25) DAY) WHERE id IN (SELECT id FROM (SELECT id FROM product WHERE deleted = 0 AND status = 1 ORDER BY rating DESC LIMIT 15) t);' as fix_sql
UNION ALL
SELECT '-- 如果图片路径不正确，可以执行以下SQL修复：' as fix_sql
UNION ALL
SELECT 'UPDATE product SET image = CONCAT(\"/uploads/images/products/\", LOWER(REPLACE(name, \" \", \"-\")), \".jpg\") WHERE deleted = 0 AND status = 1 AND (image IS NULL OR image = \"\");' as fix_sql;
