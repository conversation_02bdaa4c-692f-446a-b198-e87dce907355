# SFAP平台溯源查询快速测试指南

## 🎯 5分钟快速测试

### 测试目标
验证SFAP平台普通用户溯源查询功能是否正常工作

### 测试环境
- **前端**: http://localhost:8080
- **后端**: http://localhost:8081

---

## 🚀 测试步骤

### 步骤1: 访问查询页面
```
打开浏览器，访问: http://localhost:8080/trace
```

### 步骤2: 输入测试溯源码
```
在输入框中输入: SFAP25071410001001A1B2
点击"查询"按钮
```

### 步骤3: 验证查询结果
检查是否显示以下信息：

#### ✅ 产品基本信息
- 产品名称: **有机菠菜**
- 农场名称: **绿源有机农场**
- 生产者: **fanohhh**
- 质量等级: **A级**
- 采收日期: **2025-07-01**

#### ✅ 生产时间轴 (5个事件)
- 播种 (2025-06-15)
- 施肥 (2025-06-20)
- 浇水 (2025-06-25)
- 采收 (2025-07-01)
- 包装 (2025-07-02)

#### ✅ 认证信息 (2个证书)
- 有机产品认证 (有效)
- 质量检测报告 (有效)

#### ✅ 物流轨迹 (2个阶段)
- 农场 → 配送中心 (冷链运输)
- 配送中心 → 零售终端 (冷藏配送)

---

## 🔍 其他测试溯源码

### 完整数据测试码 ⭐⭐⭐⭐⭐
```
SFAP25071410001001A1B2 - 有机菠菜 (推荐)
SFAP25071410021002B2C3 - 精品小白菜
```

### 部分数据测试码 ⭐⭐⭐⭐
```
SFAP25071410031003C3D4 - 农家生菜
```

### 基础数据测试码 ⭐⭐⭐
```
SFAP25071410041004D4E5 - 有机韭菜
SFAP25071410051005E5F6 - 新鲜芹菜
```

---

## 🧪 高级测试 (可选)

### URL直达测试
```
访问: http://localhost:8080/trace/SFAP25071410001001A1B2
验证: 自动加载查询结果
```

### API接口测试
```bash
# 基础查询API
curl "http://localhost:8081/api/traceability/query/SFAP25071410001001A1B2"

# 详情查询API
curl "http://localhost:8081/api/traceability/detail/SFAP25071410001001A1B2"
```

### 错误处理测试
```
输入无效码: INVALID123
验证: 显示错误提示
```

---

## ❌ 常见问题

### 问题1: 查询无结果
**解决方案**:
1. 检查溯源码是否正确 (26位，以SFAP开头)
2. 确认后端服务运行在8081端口
3. 检查数据库连接

### 问题2: 页面无法访问
**解决方案**:
1. 确认前端服务运行在8080端口
2. 检查 `npm run serve` 是否正常
3. 清除浏览器缓存

### 问题3: 数据显示不完整
**解决方案**:
1. 使用推荐的完整数据测试码
2. 检查数据库中的关联数据
3. 查看浏览器控制台错误

---

## ✅ 测试成功标准

### 基础功能 ✅
- [ ] 查询页面正常加载
- [ ] 输入溯源码后能查询
- [ ] 返回产品基本信息
- [ ] 显示查询结果页面

### 数据完整性 ✅
- [ ] 产品信息完整显示
- [ ] 生产时间轴正确渲染
- [ ] 认证信息正常展示
- [ ] 物流轨迹显示正常

### 用户体验 ✅
- [ ] 页面响应速度快 (<3秒)
- [ ] 界面美观易用
- [ ] 错误提示清晰
- [ ] 移动端适配良好

---

## 📊 测试报告

### 测试结果记录
```
测试时间: ___________
测试人员: ___________

基础功能: ✅ / ❌
数据完整性: ✅ / ❌
用户体验: ✅ / ❌

总体评价: ___________
```

### 发现的问题
```
1. ________________
2. ________________
3. ________________
```

### 改进建议
```
1. ________________
2. ________________
3. ________________
```

---

**快速测试指南版本**: v1.0  
**预计测试时间**: 5-15分钟  
**最后更新**: 2025-07-14
