-- 检查数据库表结构脚本
-- 验证新闻表和其他关键表的存在和结构

USE agriculture_mall;

-- 1. 查看所有表
SELECT '=== 数据库中的所有表 ===' as message;
SHOW TABLES;

-- 2. 检查新闻相关的表
SELECT '=== 检查新闻相关表 ===' as message;
SHOW TABLES LIKE '%news%';

-- 3. 如果存在agriculture_news表，查看其结构
SELECT '=== agriculture_news表结构 ===' as message;
DESCRIBE agriculture_news;

-- 4. 如果存在news表，查看其结构
SELECT '=== news表结构 ===' as message;
DESCRIBE news;

-- 5. 检查product表的字段（特别是popularity_score等字段）
SELECT '=== product表结构检查 ===' as message;
DESCRIBE product;

-- 6. 检查product表中是否有缺失的字段
SELECT '=== 检查product表字段 ===' as message;
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'agriculture_mall' 
  AND TABLE_NAME = 'product' 
  AND COLUMN_NAME IN ('popularity_score', 'view_count', 'sales_count', 'rating');

-- 7. 检查search_keyword表
SELECT '=== search_keyword表结构 ===' as message;
DESCRIBE search_keyword;

-- 8. 统计各表的数据量
SELECT '=== 各表数据统计 ===' as message;

SELECT 
    TABLE_NAME as '表名',
    TABLE_ROWS as '估计行数',
    ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) as '大小(MB)'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'agriculture_mall'
ORDER BY TABLE_ROWS DESC;

-- 9. 检查新闻表的数据（如果存在）
SELECT '=== 新闻表数据检查 ===' as message;

-- 检查agriculture_news表的数据
SELECT COUNT(*) as agriculture_news_count FROM agriculture_news WHERE 1=1;

-- 检查news表的数据
SELECT COUNT(*) as news_count FROM news WHERE 1=1;

-- 10. 显示新闻表的示例数据
SELECT '=== 新闻表示例数据 ===' as message;

-- 显示agriculture_news表的前5条数据
SELECT id, title, category, status, created_at 
FROM agriculture_news 
ORDER BY created_at DESC 
LIMIT 5;

-- 显示news表的前5条数据
SELECT id, title, category, status, created_at 
FROM news 
ORDER BY created_at DESC 
LIMIT 5;
