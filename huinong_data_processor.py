#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
惠农网农产品价格数据处理脚本
功能：数据清洗、分析、格式化为SFAP AI预测服务格式
"""

import json
import pandas as pd
from datetime import datetime
import statistics

class HuinongDataProcessor:
    def __init__(self, json_file='huinong_prices_20250124.json', csv_file='huinong_prices_20250124.csv'):
        self.json_file = json_file
        self.csv_file = csv_file
        self.data = None
        self.df = None
        
    def load_data(self):
        """加载JSON和CSV数据"""
        try:
            with open(self.json_file, 'r', encoding='utf-8') as f:
                self.data = json.load(f)
            
            self.df = pd.read_csv(self.csv_file, encoding='utf-8')
            print(f"✅ 成功加载数据：{len(self.df)} 条记录")
            return True
        except Exception as e:
            print(f"❌ 数据加载失败：{e}")
            return False
    
    def generate_quality_report(self):
        """生成数据质量报告"""
        if self.df is None:
            return None
            
        report = {
            "数据概览": {
                "总记录数": len(self.df),
                "有效记录数": len(self.df.dropna()),
                "产品种类": self.df['产品名称'].nunique(),
                "覆盖地区数": self.df['产地'].nunique(),
                "数据日期范围": f"{self.df['日期'].min()} 至 {self.df['日期'].max()}"
            },
            "价格分析": {
                "最低价格": f"{self.df['价格'].min():.2f} 元/斤",
                "最高价格": f"{self.df['价格'].max():.2f} 元/斤",
                "平均价格": f"{self.df['价格'].mean():.2f} 元/斤",
                "价格中位数": f"{self.df['价格'].median():.2f} 元/斤",
                "价格标准差": f"{self.df['价格'].std():.2f}"
            },
            "地区分布": self.df['产地'].value_counts().to_dict(),
            "数据完整性": {
                "价格缺失率": f"{(self.df['价格'].isna().sum() / len(self.df) * 100):.1f}%",
                "产地缺失率": f"{(self.df['产地'].isna().sum() / len(self.df) * 100):.1f}%",
                "日期缺失率": f"{(self.df['日期'].isna().sum() / len(self.df) * 100):.1f}%"
            }
        }
        
        return report
    
    def format_for_ai_service(self):
        """格式化数据为SFAP AI预测服务格式"""
        if self.df is None:
            return None
            
        ai_format = {
            "metadata": {
                "source": "惠农网",
                "collection_time": datetime.now().isoformat(),
                "data_type": "agricultural_prices",
                "version": "1.0"
            },
            "features": [
                "product_name",
                "region", 
                "price",
                "date",
                "category",
                "price_change"
            ],
            "data": []
        }
        
        for _, row in self.df.iterrows():
            record = {
                "product_name": row['产品名称'],
                "region": row['产地'],
                "price": float(row['价格']),
                "date": row['日期'],
                "category": row['分类'],
                "price_change": row['涨跌幅'],
                "unit": row['单位']
            }
            ai_format["data"].append(record)
            
        return ai_format
    
    def save_ai_format(self, output_file='huinong_ai_format.json'):
        """保存AI服务格式的数据"""
        ai_data = self.format_for_ai_service()
        if ai_data:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(ai_data, f, ensure_ascii=False, indent=2)
            print(f"✅ AI格式数据已保存到：{output_file}")
            return True
        return False
    
    def print_summary(self):
        """打印数据摘要"""
        if self.df is None:
            print("❌ 没有可用数据")
            return
            
        print("\n" + "="*50)
        print("📊 惠农网农产品价格数据摘要")
        print("="*50)
        
        # 基本统计
        print(f"📈 数据记录数：{len(self.df)} 条")
        print(f"🏷️  产品种类：{self.df['产品名称'].nunique()} 种")
        print(f"🌍 覆盖地区：{self.df['产地'].nunique()} 个")
        print(f"💰 价格范围：{self.df['价格'].min():.2f} - {self.df['价格'].max():.2f} 元/斤")
        print(f"📅 数据日期：{self.df['日期'].iloc[0]}")
        
        # 价格统计
        print("\n💹 价格分析：")
        print(f"   平均价格：{self.df['价格'].mean():.2f} 元/斤")
        print(f"   价格中位数：{self.df['价格'].median():.2f} 元/斤")
        print(f"   价格标准差：{self.df['价格'].std():.2f}")
        
        # 地区分布
        print("\n🗺️  主要产地分布：")
        top_regions = self.df['产地'].value_counts().head(5)
        for region, count in top_regions.items():
            print(f"   {region}: {count} 条记录")
            
        print("\n" + "="*50)

def main():
    """主函数"""
    print("🚀 启动惠农网数据处理程序...")
    
    processor = HuinongDataProcessor()
    
    # 加载数据
    if not processor.load_data():
        return
    
    # 打印摘要
    processor.print_summary()
    
    # 生成质量报告
    quality_report = processor.generate_quality_report()
    if quality_report:
        print("\n📋 数据质量报告：")
        for category, details in quality_report.items():
            print(f"\n{category}:")
            if isinstance(details, dict):
                for key, value in details.items():
                    print(f"  {key}: {value}")
            else:
                print(f"  {details}")
    
    # 保存AI格式数据
    processor.save_ai_format()
    
    print("\n✅ 数据处理完成！")
    print("📁 生成的文件：")
    print("   - huinong_prices_20250124.json (原始JSON数据)")
    print("   - huinong_prices_20250124.csv (CSV格式数据)")
    print("   - huinong_ai_format.json (AI服务格式数据)")

if __name__ == "__main__":
    main()