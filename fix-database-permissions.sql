-- 修复数据库权限脚本
-- 解决 "Access denied for user 'root'@'**************'" 问题

-- 1. 查看当前用户权限
SELECT user, host FROM mysql.user WHERE user='root';

-- 2. 删除可能存在的限制性权限
DELETE FROM mysql.user WHERE user='root' AND host='**************';

-- 3. 确保root用户可以从任何地方连接
-- 方法1: 更新现有用户
UPDATE mysql.user SET host='%' WHERE user='root' AND host='localhost';

-- 方法2: 创建新的root用户（如果上面的方法不行）
CREATE USER 'root'@'%' IDENTIFIED BY 'fan13965711955';
GRANT ALL PRIVILEGES ON *.* TO 'root'@'%' WITH GRANT OPTION;

-- 4. 确保agriculture_mall数据库的权限
GRANT ALL PRIVILEGES ON agriculture_mall.* TO 'root'@'%';
GRANT ALL PRIVILEGES ON agriculture_mall.* TO 'root'@'localhost';

-- 5. 刷新权限
FLUSH PRIVILEGES;

-- 6. 验证权限设置
SHOW GRANTS FOR 'root'@'%';

-- 7. 测试连接
SELECT 'Database permissions fixed successfully!' as status;
