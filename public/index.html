<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    
    <!-- 预加载关键资源 -->
    <link rel="preload" href="<%= BASE_URL %>favicon.ico" as="image">
    <link rel="icon" href="<%= BASE_URL %>favicon.ico">

    <!-- 预加载Element UI字体文件 -->
    <link rel="preload" href="/node_modules/element-ui/lib/theme-chalk/fonts/element-icons.woff2" as="font" type="font/woff2" crossorigin="anonymous">
    <link rel="preload" href="/node_modules/element-ui/lib/theme-chalk/fonts/element-icons.woff" as="font" type="font/woff" crossorigin="anonymous">

    <!-- 预加载关键CSS -->
    <link rel="preload" href="/node_modules/element-ui/lib/theme-chalk/index.css" as="style">

    <!-- DNS预解析 -->
    <link rel="dns-prefetch" href="//localhost:8081">
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    <link rel="dns-prefetch" href="//cdn.jsdelivr.net">

    <!-- 缓存控制 -->
    <meta http-equiv="Cache-Control" content="max-age=31536000">

    <!-- 图标字体预加载样式 -->
    <style>
      /* 确保图标字体立即可用 */
      @font-face {
        font-family: 'element-icons';
        src: url('/node_modules/element-ui/lib/theme-chalk/fonts/element-icons.woff2') format('woff2'),
             url('/node_modules/element-ui/lib/theme-chalk/fonts/element-icons.woff') format('woff');
        font-display: swap;
      }

      /* 防止图标闪烁 */
      [class^="el-icon-"], [class*=" el-icon-"] {
        font-family: 'element-icons' !important;
        speak: none;
        font-style: normal;
        font-weight: normal;
        font-variant: normal;
        text-transform: none;
        line-height: 1;
        vertical-align: baseline;
        display: inline-block;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }
    </style>
    
    <title><%= htmlWebpackPlugin.options.title %></title>
  </head>
  <body>
    <noscript>
      <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div id="app"></div>
    <!-- built files will be auto injected -->
  </body>
</html>