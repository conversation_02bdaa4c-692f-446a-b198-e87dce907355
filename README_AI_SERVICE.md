# SFAP农品汇平台AI价格预测服务

## 📋 项目概述

SFAP农品汇平台AI价格预测服务是一个基于机器学习的智能农产品价格预测系统，为农产品交易提供科学的价格预测和趋势分析。该系统集成了RNN神经网络和ARIMA时间序列两种先进的预测算法，能够为用户提供准确、可靠的价格预测服务。

### 🎯 核心功能

- **智能价格预测**: 基于RNN/LSTM神经网络和ARIMA时间序列模型的双算法预测
- **模型管理**: 完整的AI模型生命周期管理，包括训练、评估、部署和监控
- **数据管理**: 支持多种数据源的历史价格数据管理和质量验证
- **可视化分析**: 丰富的图表展示和趋势分析功能
- **API服务**: 完整的RESTful API接口，支持前后端集成

### 🏆 技术优势

- **双模型架构**: RNN适合复杂非线性预测，ARIMA适合趋势季节性分析
- **自动参数优化**: 智能参数选择和模型调优
- **实时预测**: 快速响应的在线预测服务
- **高可用性**: 完善的错误处理和服务监控
- **易于集成**: 标准化API接口，支持多种前端框架

## 🏗️ 技术架构

### 系统架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   SFAP前端      │    │   SFAP后端      │    │   AI预测服务    │
│  (Vue 2 + UI)   │◄──►│ (Spring Boot)   │◄──►│ (Python Flask)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户界面      │    │   业务逻辑      │    │   AI算法引擎    │
│ - 价格预测页面  │    │ - 用户管理      │    │ - RNN/LSTM      │
│ - 模型管理界面  │    │ - 产品管理      │    │ - ARIMA         │
│ - 数据上传功能  │    │ - 数据接口      │    │ - 模型训练      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   MySQL数据库   │
                       │ - 用户数据      │
                       │ - 产品数据      │
                       │ - 价格历史      │
                       │ - 模型信息      │
                       └─────────────────┘
```

### 技术栈

#### 后端服务 (AI预测服务)
- **框架**: Python Flask 2.3.3
- **机器学习**: 
  - TensorFlow 2.13.0 (深度学习)
  - scikit-learn 1.3.0 (机器学习工具)
  - statsmodels 0.14.0 (统计模型)
  - pmdarima 2.0.3 (自动ARIMA)
- **数据处理**: 
  - pandas 2.0.3 (数据分析)
  - numpy 1.24.3 (数值计算)
- **数据库**: PyMySQL 1.1.0 (MySQL连接)
- **其他**: python-dotenv 1.0.0 (环境配置)

#### 前端集成
- **框架**: Vue 2 + Element UI
- **图表**: ECharts
- **HTTP客户端**: Axios
- **状态管理**: Vuex

#### 数据库
- **主数据库**: MySQL 8.0+ (SFAP业务数据)
- **AI数据表**: 
  - `ai_models` (模型信息)
  - `ai_predictions` (预测结果)
  - `ai_training_logs` (训练日志)

## 🚀 快速开始

### 环境要求

- **Python**: >= 3.8 (推荐 3.9+)
- **内存**: >= 4GB (推荐 8GB+)
- **磁盘空间**: >= 2GB
- **网络**: 需要访问PyPI (pip安装依赖)
- **数据库**: MySQL 8.0+ (与SFAP共享)

### 安装步骤

#### 1. 克隆项目
```bash
# 项目已包含在SFAP平台中
cd E:\计算机设计大赛2\V4.0\新建文件夹\SFAP\ai-service
```

#### 2. 安装Python依赖
```bash
# 自动安装（推荐）
python start.py

# 手动安装
pip install -r requirements.txt
```

#### 3. 配置环境变量
编辑 `.env` 文件：
```env
# 服务配置
PORT=5000
DEBUG=True
FLASK_ENV=development

# 数据库配置（与SFAP共享）
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=fan13965711955
DB_NAME=agriculture_mall

# 模型配置
MODEL_SAVE_PATH=./saved_models
MAX_MODEL_SIZE=100MB
MODEL_CACHE_TTL=3600

# 日志配置
LOG_LEVEL=INFO
LOG_FILE_MAX_SIZE=10MB
LOG_BACKUP_COUNT=5
```

#### 4. 启动服务
```bash
# 使用启动脚本（推荐）
python start.py

# 直接启动
python app.py
```

#### 5. 验证服务
```bash
# 健康检查
curl http://localhost:5000/api/v1/health

# 或在浏览器中访问
http://localhost:5000/api/v1/health
```

### 预期输出

服务启动成功后，您应该看到：

```
==================================================
SFAP农品汇平台AI预测服务启动检查
==================================================
✓ Python版本: 3.x.x
✓ 所有依赖包已安装
✓ 数据库连接正常
✓ 所有检查通过，启动AI预测服务...
==================================================

服务地址: http://localhost:5000
调试模式: True
API文档: http://localhost:5000/api/v1/health

按 Ctrl+C 停止服务
```

## 📚 API接口文档

### 基础信息

- **服务地址**: http://localhost:5000
- **API版本**: v1
- **数据格式**: JSON
- **字符编码**: UTF-8
- **认证方式**: 无需认证（内部服务）

### 核心接口

#### 1. 健康检查

**接口**: `GET /api/v1/health`

**描述**: 检查AI服务运行状态

**响应示例**:
```json
{
  "code": 200,
  "message": "服务正常",
  "data": {
    "status": "healthy",
    "version": "1.0.0",
    "uptime": 3600,
    "models_loaded": 5,
    "memory_usage": "45%",
    "cpu_usage": "12%"
  }
}
```

#### 2. RNN模型训练

**接口**: `POST /api/v1/train_rnn`

**描述**: 训练RNN神经网络价格预测模型

**请求参数**:
```json
{
  "category": "apple",           // 农产品类别
  "region": "山东省",            // 地区
  "history_data": [              // 历史价格数据
    {
      "date": "2024-01-01",
      "price": 5.2,
      "volume": 1000,
      "quality_grade": "A"
    }
  ],
  "model_params": {              // 模型参数（可选）
    "sequence_length": 30,
    "hidden_size": 128,
    "num_layers": 3,
    "dropout": 0.2,
    "learning_rate": 0.001,
    "epochs": 100,
    "batch_size": 32
  }
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "模型训练完成",
  "data": {
    "model_id": "rnn_apple_山东省_20250124_143022",
    "model_params": {
      "sequence_length": 30,
      "hidden_size": 128,
      "num_layers": 3
    },
    "training_metrics": {
      "mape": 5.2,
      "rmse": 0.15,
      "mae": 0.12,
      "accuracy": 94.8
    },
    "training_time": 180
  }
}
```

#### 3. ARIMA模型训练

**接口**: `POST /api/v1/train_arima`

**描述**: 训练ARIMA时间序列价格预测模型

**请求参数**:
```json
{
  "category": "apple",
  "region": "山东省", 
  "history_data": [
    {
      "date": "2024-01-01",
      "price": 5.2
    }
  ],
  "model_params": {              // 可选，不提供则自动选择
    "p": 1,                      // 自回归阶数
    "d": 1,                      // 差分阶数  
    "q": 1,                      // 移动平均阶数
    "seasonal": true,            // 是否季节性
    "seasonal_periods": 12       // 季节周期
  }
}
```

#### 4. RNN价格预测

**接口**: `POST /api/v1/predict_rnn`

**描述**: 使用RNN模型进行价格预测

**请求参数**:
```json
{
  "category": "apple",
  "region": "山东省",
  "history_data": [...],         // 最近的历史数据
  "forecast_days": 7,            // 预测天数
  "confidence_level": 0.95,      // 置信水平
  "model_id": "rnn_apple_...",   // 可选，指定模型
  "external_factors": {          // 可选，外部因素
    "weather_forecast": "sunny",
    "seasonal_factor": true,
    "policy_impact": "neutral"
  }
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "预测完成",
  "data": {
    "model_info": {
      "model_id": "rnn_apple_山东省_20250124_143022",
      "model_type": "RNN(30,128,3)",
      "training_date": "2025-01-24",
      "accuracy": 94.8
    },
    "predictions": [
      {
        "date": "2025-01-25",
        "price": 5.35,
        "confidence": 0.92,
        "upper_bound": 5.68,
        "lower_bound": 5.02,
        "trend": "up"
      }
    ],
    "summary": {
      "avg_price": 5.42,
      "price_change": 0.038,       // 3.8%上涨
      "volatility": 0.12,
      "trend_direction": "upward",
      "confidence_avg": 0.89
    }
  }
}
```

#### 5. ARIMA价格预测

**接口**: `POST /api/v1/predict_arima`

**描述**: 使用ARIMA模型进行价格预测

**请求参数**: 与RNN预测类似，但不支持external_factors

#### 6. 获取模型列表

**接口**: `GET /api/v1/models`

**查询参数**:
- `category`: 农产品类别（可选）
- `region`: 地区（可选）
- `model_type`: 模型类型 RNN/ARIMA（可选）
- `page`: 页码（默认1）
- `size`: 每页大小（默认10）

**响应示例**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "models": [
      {
        "model_id": "rnn_apple_山东省_20250124_143022",
        "model_type": "RNN",
        "category": "apple",
        "region": "山东省",
        "accuracy": 94.8,
        "status": "active",
        "created_at": "2025-01-24T14:30:22Z",
        "training_metrics": {
          "mape": 5.2,
          "rmse": 0.15
        }
      }
    ],
    "pagination": {
      "page": 1,
      "size": 10,
      "total": 25,
      "pages": 3
    }
  }
}
```

#### 7. 数据上传

**接口**: `POST /api/v1/data/upload`

**描述**: 上传历史价格数据用于模型训练

**请求参数**:
```json
{
  "category": "apple",
  "region": "山东省",
  "data": [
    {
      "date": "2024-01-01",
      "price": 5.2,
      "volume": 1000,
      "quality_grade": "A",
      "market_name": "农品汇平台"
    }
  ],
  "data_source": "platform"       // platform/manual/api
}
```

#### 8. 数据验证

**接口**: `POST /api/v1/data/validate`

**描述**: 验证数据质量和格式

**请求参数**:
```json
{
  "data": [
    {
      "date": "2024-01-01",
      "price": 5.2,
      "volume": 1000
    }
  ]
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "数据验证完成",
  "data": {
    "total_records": 100,
    "valid_records": 95,
    "quality_score": 95.0,
    "issues": [
      {
        "type": "missing_value",
        "count": 3,
        "fields": ["volume"],
        "details": ["记录5: 缺少volume字段"]
      },
      {
        "type": "outlier", 
        "count": 2,
        "description": "价格异常值",
        "details": ["记录23: 价格异常值 -1.5"]
      }
    ]
  }
}
```

### 错误响应格式

所有接口在出错时返回统一格式：

```json
{
  "code": 400,
  "message": "请求参数错误",
  "error": {
    "type": "INVALID_PARAMS",
    "details": "category字段不能为空"
  }
}
```

常见错误码：
- `400`: 请求参数错误
- `404`: 资源不存在
- `500`: 服务器内部错误
- `503`: 服务不可用

## 🔗 前后端集成

### SFAP前端集成

#### 1. 路由配置

在 `src/router/index.js` 中添加AI价格预测路由：

```javascript
{
  path: '/ai/price-prediction',
  name: 'AIPricePredictionCenter',
  component: () => import('@/views/ai/PricePredictionCenter.vue'),
  meta: {
    title: 'AI价格预测中心',
    requiresAuth: true
  }
}
```

#### 2. 导航菜单

在 `src/components/layout/Navbar.vue` 中添加菜单项：

```vue
<el-menu-item index="/ai/price-prediction" v-if="isLoggedIn">
  <i class="el-icon-cpu" />
  <span>AI价格预测</span>
</el-menu-item>
```

#### 3. API调用示例

```javascript
import { aiPredictionAPI } from '@/api/ai-prediction'

// 健康检查
const checkAIService = async () => {
  try {
    const response = await aiPredictionAPI.healthCheck()
    console.log('AI服务状态:', response.data)
  } catch (error) {
    console.error('AI服务不可用:', error)
  }
}

// RNN预测
const predictPrice = async () => {
  try {
    const params = {
      category: 'apple',
      region: '山东省',
      history_data: historyData,
      forecast_days: 7,
      confidence_level: 0.95
    }

    const result = await aiPredictionAPI.predictRNN(params)
    console.log('预测结果:', result.data)

    // 处理预测结果
    this.predictionResult = result.data
    this.showPredictionChart()

  } catch (error) {
    this.$message.error('预测失败: ' + error.message)
  }
}

// 模型训练
const trainModel = async () => {
  try {
    const params = {
      category: 'apple',
      region: '山东省',
      history_data: trainingData,
      model_params: {
        sequence_length: 30,
        hidden_size: 128,
        epochs: 100
      }
    }

    const result = await aiPredictionAPI.trainRNN(params)
    this.$message.success('模型训练完成')

  } catch (error) {
    this.$message.error('训练失败: ' + error.message)
  }
}
```

#### 4. 组件使用

```vue
<template>
  <div class="price-prediction">
    <!-- AI价格预测中心 -->
    <PricePredictionCenter />

    <!-- 或单独使用组件 -->
    <PricePredictionPanel
      @prediction-complete="handlePredictionComplete"
      @prediction-error="handlePredictionError"
    />

    <PredictionResultDisplay
      :result="predictionResult"
      :model-type="modelType"
    />
  </div>
</template>

<script>
import {
  PricePredictionCenter,
  PricePredictionPanel,
  PredictionResultDisplay
} from '@/components/ai'

export default {
  components: {
    PricePredictionCenter,
    PricePredictionPanel,
    PredictionResultDisplay
  },

  methods: {
    handlePredictionComplete(result) {
      this.predictionResult = result
      this.$message.success('预测完成')
    },

    handlePredictionError(error) {
      this.$message.error('预测失败: ' + error.message)
    }
  }
}
</script>
```

### SFAP后端集成

#### 1. 添加AI服务调用

在Spring Boot后端中添加AI服务调用：

```java
@Service
public class AIPredictionService {

    private static final String AI_SERVICE_URL = "http://localhost:5000/api/v1";

    @Autowired
    private RestTemplate restTemplate;

    /**
     * 调用AI服务进行价格预测
     */
    public AIPredictionResult predictPrice(AIPredictionRequest request) {
        try {
            String url = AI_SERVICE_URL + "/predict_rnn";

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<AIPredictionRequest> entity = new HttpEntity<>(request, headers);

            ResponseEntity<AIPredictionResponse> response = restTemplate.postForEntity(
                url, entity, AIPredictionResponse.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                return response.getBody().getData();
            } else {
                throw new RuntimeException("AI预测服务调用失败");
            }

        } catch (Exception e) {
            log.error("调用AI预测服务失败", e);
            throw new RuntimeException("AI预测服务不可用: " + e.getMessage());
        }
    }

    /**
     * 检查AI服务健康状态
     */
    public boolean checkAIServiceHealth() {
        try {
            String url = AI_SERVICE_URL + "/health";
            ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);
            return response.getStatusCode() == HttpStatus.OK;
        } catch (Exception e) {
            return false;
        }
    }
}
```

#### 2. 添加控制器接口

```java
@RestController
@RequestMapping("/api/ai")
public class AIPredictionController {

    @Autowired
    private AIPredictionService aiPredictionService;

    @PostMapping("/predict")
    public Result<AIPredictionResult> predictPrice(@RequestBody AIPredictionRequest request) {
        try {
            // 参数验证
            if (StringUtils.isEmpty(request.getCategory()) ||
                StringUtils.isEmpty(request.getRegion())) {
                return Result.error("参数不完整");
            }

            // 调用AI服务
            AIPredictionResult result = aiPredictionService.predictPrice(request);

            return Result.success(result);

        } catch (Exception e) {
            log.error("价格预测失败", e);
            return Result.error("预测失败: " + e.getMessage());
        }
    }

    @GetMapping("/health")
    public Result<Boolean> checkHealth() {
        boolean isHealthy = aiPredictionService.checkAIServiceHealth();
        return Result.success(isHealthy);
    }
}
```

#### 3. 配置RestTemplate

```java
@Configuration
public class AIServiceConfig {

    @Bean
    public RestTemplate restTemplate() {
        RestTemplate restTemplate = new RestTemplate();

        // 设置超时时间
        HttpComponentsClientHttpRequestFactory factory =
            new HttpComponentsClientHttpRequestFactory();
        factory.setConnectTimeout(5000);
        factory.setReadTimeout(30000);

        restTemplate.setRequestFactory(factory);

        return restTemplate;
    }
}
```

### 数据库集成

#### 1. AI相关数据表

```sql
-- AI模型信息表
CREATE TABLE ai_models (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    model_id VARCHAR(100) NOT NULL UNIQUE COMMENT '模型唯一标识',
    model_type ENUM('RNN', 'ARIMA') NOT NULL COMMENT '模型类型',
    category VARCHAR(50) NOT NULL COMMENT '农产品类别',
    region VARCHAR(50) NOT NULL COMMENT '地区',
    model_params JSON COMMENT '模型参数',
    training_metrics JSON COMMENT '训练指标',
    accuracy DECIMAL(5,2) COMMENT '准确率',
    status ENUM('training', 'active', 'deprecated') DEFAULT 'training',
    file_path VARCHAR(255) COMMENT '模型文件路径',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_category_region (category, region),
    INDEX idx_model_type (model_type),
    INDEX idx_status (status)
) COMMENT 'AI预测模型信息表';

-- AI预测结果表
CREATE TABLE ai_predictions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    model_id VARCHAR(100) NOT NULL COMMENT '使用的模型ID',
    category VARCHAR(50) NOT NULL COMMENT '农产品类别',
    region VARCHAR(50) NOT NULL COMMENT '地区',
    forecast_days INT NOT NULL COMMENT '预测天数',
    predictions JSON NOT NULL COMMENT '预测结果数据',
    summary JSON COMMENT '预测摘要',
    confidence_level DECIMAL(3,2) COMMENT '置信水平',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_model_id (model_id),
    INDEX idx_category_region (category, region),
    INDEX idx_created_at (created_at)
) COMMENT 'AI预测结果表';

-- AI训练日志表
CREATE TABLE ai_training_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    model_id VARCHAR(100) NOT NULL COMMENT '模型ID',
    training_status ENUM('started', 'completed', 'failed') NOT NULL,
    training_params JSON COMMENT '训练参数',
    training_metrics JSON COMMENT '训练指标',
    error_message TEXT COMMENT '错误信息',
    training_time INT COMMENT '训练耗时(秒)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_model_id (model_id),
    INDEX idx_status (training_status)
) COMMENT 'AI模型训练日志表';
```

#### 2. 数据同步

```java
@Service
public class DataSyncService {

    /**
     * 同步产品价格历史数据到AI服务
     */
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void syncPriceHistoryToAI() {
        try {
            // 获取昨天的价格数据
            List<ProductPriceHistory> priceHistory = getPriceHistoryByDate(
                LocalDate.now().minusDays(1));

            // 按类别和地区分组
            Map<String, List<ProductPriceHistory>> groupedData =
                priceHistory.stream()
                    .collect(Collectors.groupingBy(
                        p -> p.getCategory() + "_" + p.getRegion()));

            // 同步到AI服务
            for (Map.Entry<String, List<ProductPriceHistory>> entry : groupedData.entrySet()) {
                syncDataToAIService(entry.getValue());
            }

            log.info("价格数据同步完成，共同步{}条记录", priceHistory.size());

        } catch (Exception e) {
            log.error("价格数据同步失败", e);
        }
    }

    private void syncDataToAIService(List<ProductPriceHistory> data) {
        // 转换数据格式
        List<AIPriceData> aiData = data.stream()
            .map(this::convertToAIFormat)
            .collect(Collectors.toList());

        // 调用AI服务上传接口
        aiPredictionService.uploadData(aiData);
    }
}
```

## 🧪 测试指南

### 单元测试

运行AI服务的API测试：

```bash
cd ai-service
python test_api.py
```

### 集成测试

#### 1. 前端集成测试

```javascript
// 在浏览器控制台中测试
// 1. 检查AI服务连接
fetch('http://localhost:5000/api/v1/health')
  .then(response => response.json())
  .then(data => console.log('AI服务状态:', data))

// 2. 测试预测接口
const testPrediction = async () => {
  const response = await fetch('http://localhost:5000/api/v1/predict_rnn', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      category: 'apple',
      region: '山东省',
      history_data: [
        { date: '2024-01-01', price: 5.0 },
        { date: '2024-01-02', price: 5.1 }
      ],
      forecast_days: 3
    })
  })

  const result = await response.json()
  console.log('预测结果:', result)
}
```

#### 2. 后端集成测试

```java
@SpringBootTest
public class AIPredictionIntegrationTest {

    @Autowired
    private AIPredictionService aiPredictionService;

    @Test
    public void testAIServiceHealth() {
        boolean isHealthy = aiPredictionService.checkAIServiceHealth();
        assertTrue("AI服务应该是健康的", isHealthy);
    }

    @Test
    public void testPricePrediction() {
        AIPredictionRequest request = new AIPredictionRequest();
        request.setCategory("apple");
        request.setRegion("山东省");
        request.setForecastDays(7);

        AIPredictionResult result = aiPredictionService.predictPrice(request);

        assertNotNull("预测结果不应为空", result);
        assertEquals("预测天数应该正确", 7, result.getPredictions().size());
    }
}
```

### 性能测试

使用Apache Bench进行压力测试：

```bash
# 测试健康检查接口
ab -n 1000 -c 10 http://localhost:5000/api/v1/health

# 测试预测接口（需要准备测试数据文件）
ab -n 100 -c 5 -p prediction_data.json -T application/json \
   http://localhost:5000/api/v1/predict_rnn
```

## 🐛 故障排除

### 常见问题

#### 1. AI服务启动失败

**问题**: `python start.py` 执行失败

**解决方案**:
```bash
# 检查Python版本
python --version  # 需要 >= 3.8

# 检查pip版本
pip --version

# 手动安装依赖
pip install -r requirements.txt

# 如果网络问题，使用国内镜像
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

#### 2. 依赖包安装失败

**问题**: TensorFlow或其他包安装失败

**解决方案**:
```bash
# 升级pip
python -m pip install --upgrade pip

# 安装Microsoft Visual C++ 14.0（Windows）
# 下载并安装 Microsoft C++ Build Tools

# 分别安装问题包
pip install tensorflow==2.13.0
pip install scikit-learn==1.3.0
pip install pmdarima==2.0.3

# 如果仍有问题，使用conda
conda install tensorflow scikit-learn
```

#### 3. 数据库连接失败

**问题**: `数据库连接失败`

**解决方案**:
```bash
# 检查MySQL服务状态
net start mysql  # Windows
sudo systemctl start mysql  # Linux

# 检查数据库配置
# 编辑 .env 文件，确认数据库参数正确
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=fan13965711955
DB_NAME=agriculture_mall

# 测试数据库连接
mysql -h localhost -u root -p agriculture_mall
```

#### 4. 前端跨域问题

**问题**: 前端无法访问AI服务

**解决方案**:

在AI服务中已配置CORS，如果仍有问题：

```python
# 在 app.py 中添加更宽松的CORS配置
from flask_cors import CORS

app = Flask(__name__)
CORS(app, origins=['http://localhost:8080', 'http://localhost:8081'])
```

或在前端配置代理：

```javascript
// vue.config.js
module.exports = {
  devServer: {
    proxy: {
      '/ai-api': {
        target: 'http://localhost:5000',
        changeOrigin: true,
        pathRewrite: {
          '^/ai-api': '/api/v1'
        }
      }
    }
  }
}
```

#### 5. 模型训练内存不足

**问题**: 训练时出现内存错误

**解决方案**:
```python
# 减少批次大小
model_params = {
    "batch_size": 16,  # 从32减少到16
    "sequence_length": 20  # 从30减少到20
}

# 或者使用CPU训练
import os
os.environ['CUDA_VISIBLE_DEVICES'] = '-1'
```

#### 6. 预测结果异常

**问题**: 预测结果不合理

**解决方案**:
1. **检查输入数据**:
   - 确保数据格式正确
   - 检查价格数据是否有异常值
   - 确认数据量足够（建议 > 50个数据点）

2. **检查模型状态**:
   - 确认模型训练完成
   - 检查模型准确率
   - 验证模型参数

3. **调整预测参数**:
   - 减少预测天数
   - 调整置信水平
   - 使用不同的模型类型

### 日志分析

#### 1. 查看AI服务日志

```bash
# 查看实时日志
tail -f ai-service/logs/ai_service.log

# 查看错误日志
grep "ERROR" ai-service/logs/ai_service.log

# 查看特定时间的日志
grep "2025-01-24" ai-service/logs/ai_service.log
```

#### 2. 日志级别配置

在 `.env` 文件中调整日志级别：

```env
LOG_LEVEL=DEBUG  # DEBUG, INFO, WARNING, ERROR
```

#### 3. 常见错误日志

```
ERROR - 模型训练失败: 数据量不足
解决: 确保训练数据至少有30个数据点

ERROR - 预测失败: 模型文件不存在
解决: 重新训练模型或检查模型文件路径

ERROR - 数据库连接超时
解决: 检查数据库服务状态和网络连接
```

### 性能优化

#### 1. 模型性能优化

```python
# 使用GPU加速（如果可用）
import tensorflow as tf
if tf.config.list_physical_devices('GPU'):
    print("GPU可用，使用GPU训练")
else:
    print("使用CPU训练")

# 模型量化（减少模型大小）
converter = tf.lite.TFLiteConverter.from_saved_model(model_path)
converter.optimizations = [tf.lite.Optimize.DEFAULT]
tflite_model = converter.convert()
```

#### 2. 缓存优化

```python
# 启用预测结果缓存
CACHE_PREDICTION_RESULTS=True
CACHE_TTL=3600  # 1小时

# 使用Redis缓存（可选）
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=1
```

#### 3. 并发优化

```python
# 在 app.py 中配置
if __name__ == '__main__':
    app.run(
        host='0.0.0.0',
        port=5000,
        threaded=True,  # 启用多线程
        processes=1     # 单进程多线程
    )
```

## 📞 技术支持

### 联系方式

- **项目文档**: 本README文件
- **API文档**: http://localhost:5000/api/v1/health
- **日志文件**: `ai-service/logs/`
- **配置文件**: `ai-service/.env`

### 常用命令

```bash
# 启动AI服务
cd ai-service && python start.py

# 检查服务状态
curl http://localhost:5000/api/v1/health

# 查看日志
tail -f ai-service/logs/ai_service.log

# 重启服务
# Ctrl+C 停止，然后重新运行 python start.py

# 清理缓存
rm -rf ai-service/saved_models/*
rm -rf ai-service/logs/*
```

### 开发调试

```bash
# 启用调试模式
export DEBUG=True
export LOG_LEVEL=DEBUG

# 使用Python调试器
python -m pdb start.py

# 性能分析
python -m cProfile -o profile.stats start.py
```

---

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进项目。

---

**SFAP农品汇平台AI价格预测服务** - 让农产品价格预测更智能、更准确！
