# SFAP平台二维码生成修复脚本

## 🎯 修复目标

解决SFAP平台溯源模块二维码文件缺失问题，确保所有溯源码都有对应的二维码文件。

## 📋 修复步骤

### 步骤1: 创建qrcodes目录

```bash
# 在项目根目录执行
mkdir -p uploads/qrcodes
chmod 755 uploads/qrcodes
```

### 步骤2: 启动后端服务

```bash
# 确保后端服务运行在8081端口
cd backend
mvn spring-boot:run
```

### 步骤3: 测试二维码生成功能

```bash
# 测试基础功能
curl -X GET "http://localhost:8081/api/qrcode/test"
```

**预期响应**:
```json
{
  "success": true,
  "message": "测试二维码生成成功",
  "testTraceCode": "SFAP25071410001001TEST",
  "qrCodeUrl": "/uploads/qrcodes/qr_SFAP25071410001001TEST.png"
}
```

### 步骤4: 批量生成所有二维码

```bash
# 为所有已发布的溯源记录生成二维码
curl -X POST "http://localhost:8081/api/qrcode/batch/generate-all" \
  -H "Content-Type: application/json"
```

**预期响应**:
```json
{
  "success": true,
  "message": "批量生成完成",
  "totalCount": 10,
  "successCount": 10,
  "failedCount": 0,
  "successRate": 100.0
}
```

### 步骤5: 验证二维码文件生成

```bash
# 检查qrcodes目录
ls -la uploads/qrcodes/

# 预期看到以下文件
# qr_SFAP25071410001001A1B2.png
# qr_SFAP25071410021002B2C3.png
# qr_SFAP25071410031003C3D4.png
# ... 等等
```

### 步骤6: 验证二维码可访问性

```bash
# 通过浏览器访问测试
# http://localhost:8080/uploads/qrcodes/qr_SFAP25071410001001A1B2.png
```

## 🔧 手动修复方案（如果API失败）

### 方案1: 使用管理员界面

1. 登录管理员账户
2. 访问: `http://localhost:8080/admin/traceability-center`
3. 点击"系统设置"或相关管理功能
4. 查找"批量生成二维码"功能

### 方案2: 直接调用Service方法

如果需要在代码中直接调用：

```java
@Autowired
private QRCodeBatchGenerator qrCodeBatchGenerator;

// 生成所有二维码
QRCodeBatchGenerator.GenerationResult result = qrCodeBatchGenerator.generateAllQRCodes();
System.out.println("生成结果: " + result.getSuccessCount() + "/" + result.getTotalCount());
```

### 方案3: 数据库直接操作

如果需要重置二维码URL：

```sql
-- 清空现有的二维码URL（谨慎操作）
UPDATE traceability_record SET qr_code_url = NULL WHERE qr_code_url IS NOT NULL;

-- 然后重新生成
```

## 🧪 验证测试

### 测试1: 单个二维码生成

```bash
# 为特定溯源码生成二维码
curl -X POST "http://localhost:8081/api/qrcode/generate/SFAP25071410001001A1B2" \
  -H "Content-Type: application/json"
```

### 测试2: 二维码内容验证

使用二维码扫描工具扫描生成的二维码，应该得到：
```
http://localhost:8080/trace/SFAP25071410001001A1B2
```

### 测试3: 前端查询验证

1. 访问: `http://localhost:8080/trace`
2. 输入: `SFAP25071410001001A1B2`
3. 查询结果页面应该显示二维码图片

## ⚠️ 故障排除

### 问题1: 目录权限错误

```bash
# 解决方案
sudo chmod -R 755 uploads/
sudo chown -R $USER:$USER uploads/
```

### 问题2: 端口占用

```bash
# 检查端口占用
netstat -tulpn | grep :8081

# 如果被占用，杀死进程或更换端口
```

### 问题3: 内存不足

```bash
# 增加JVM内存
export JAVA_OPTS="-Xmx2g -Xms1g"
mvn spring-boot:run
```

### 问题4: 依赖缺失

检查pom.xml中是否包含二维码生成依赖：

```xml
<dependency>
    <groupId>com.google.zxing</groupId>
    <artifactId>core</artifactId>
    <version>3.5.1</version>
</dependency>
<dependency>
    <groupId>com.google.zxing</groupId>
    <artifactId>javase</artifactId>
    <version>3.5.1</version>
</dependency>
```

## 📊 修复验证清单

### ✅ 基础验证
- [ ] qrcodes目录已创建
- [ ] 后端服务正常运行
- [ ] 测试API返回成功
- [ ] 批量生成API执行成功

### ✅ 文件验证
- [ ] 二维码PNG文件已生成
- [ ] 文件大小合理（10-50KB）
- [ ] 文件可通过浏览器访问
- [ ] 文件名格式正确

### ✅ 功能验证
- [ ] 二维码扫描返回正确URL
- [ ] 前端查询页面显示二维码
- [ ] 数据库qr_code_url字段正确
- [ ] 查询统计正常更新

### ✅ 性能验证
- [ ] 生成速度合理（<10秒/10个）
- [ ] 内存使用正常
- [ ] 无错误日志
- [ ] 并发访问正常

## 🎉 修复完成标志

当以下条件全部满足时，修复完成：

1. ✅ **文件存在**: `uploads/qrcodes/` 目录包含所有溯源码的二维码文件
2. ✅ **数据一致**: 数据库中的 `qr_code_url` 字段与实际文件一致
3. ✅ **功能正常**: 前端查询页面能正确显示二维码
4. ✅ **扫码有效**: 二维码扫描能跳转到正确的查询页面
5. ✅ **性能良好**: 二维码加载速度快，无明显延迟

## 📞 技术支持

如果修复过程中遇到问题，请检查：

1. **日志文件**: `logs/application.log`
2. **控制台输出**: Spring Boot启动日志
3. **浏览器控制台**: 前端错误信息
4. **网络连接**: 确保前后端通信正常

---

**修复脚本版本**: v1.0  
**适用环境**: SFAP开发环境  
**最后更新**: 2025-07-14
