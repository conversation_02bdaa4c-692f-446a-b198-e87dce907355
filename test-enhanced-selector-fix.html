1<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>增强选择器修复验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e4e7ed;
            border-radius: 6px;
        }
        .test-title {
            color: #409eff;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
        }
        .success {
            color: #67c23a;
        }
        .error {
            color: #f56c6c;
        }
        .loading {
            color: #409eff;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f0f9ff;
            border-left: 4px solid #409eff;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        button {
            background: #409eff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #66b1ff;
        }
        .fix-item {
            padding: 10px;
            margin: 10px 0;
            border-left: 4px solid #67c23a;
            background: #f0f9ff;
        }
        .issue-item {
            padding: 10px;
            margin: 10px 0;
            border-left: 4px solid #f56c6c;
            background: #fef0f0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>SFAP农品汇平台 - 增强选择器修复验证</h1>
        
        <div class="test-section">
            <div class="test-title">修复内容总结</div>
            
            <div class="fix-item">
                <h4>✅ 地区选择器显示问题修复</h4>
                <ul>
                    <li>将复杂的级联选择器改为简单的下拉选择器</li>
                    <li>移除了不可靠的名称匹配逻辑</li>
                    <li>按数据可用性分组显示地区</li>
                    <li>支持地区搜索功能</li>
                </ul>
            </div>
            
            <div class="fix-item">
                <h4>✅ API路由问题修复</h4>
                <ul>
                    <li>确认crawl-data.js使用正确的aiService实例</li>
                    <li>所有API请求指向AI服务端口5000</li>
                    <li>移除了未使用的导入避免ESLint错误</li>
                </ul>
            </div>
            
            <div class="fix-item">
                <h4>✅ 数据流优化</h4>
                <ul>
                    <li>简化了地区选择的数据结构</li>
                    <li>优化了组件间的数据传递</li>
                    <li>增强了错误处理和用户提示</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">API连接测试</div>
            <button onclick="testAllAPIs()">测试所有API</button>
            <div id="api-test-results"></div>
        </div>

        <div class="test-section">
            <div class="test-title">数据格式验证</div>
            <button onclick="testDataFormats()">验证数据格式</button>
            <div id="format-test-results"></div>
        </div>

        <div class="test-section">
            <div class="test-title">前端组件测试指南</div>
            <div>
                <h4>测试步骤：</h4>
                <ol>
                    <li><strong>访问前端页面</strong>: <a href="http://localhost:8080/#/ai/prediction" target="_blank">http://localhost:8080/#/ai/prediction</a></li>
                    <li><strong>验证分类选择器</strong>:
                        <ul>
                            <li>应显示18个真实分类</li>
                            <li>分类按"有数据"和"暂无数据"分组</li>
                            <li>每个分类显示产品数量和价格记录数</li>
                            <li>支持搜索功能</li>
                        </ul>
                    </li>
                    <li><strong>验证地区选择器</strong>:
                        <ul>
                            <li>应显示35个真实地区</li>
                            <li>地区按"有数据"和"暂无数据"分组</li>
                            <li>每个地区显示价格记录数</li>
                            <li>支持搜索功能</li>
                        </ul>
                    </li>
                    <li><strong>验证数据可用性指示器</strong>:
                        <ul>
                            <li>选择分类和地区后应显示数据质量评分</li>
                            <li>显示历史记录数、数据范围、最新更新时间</li>
                            <li>提供预测建议</li>
                        </ul>
                    </li>
                    <li><strong>验证预测功能</strong>:
                        <ul>
                            <li>选择有数据的组合进行预测</li>
                            <li>验证RNN和ARIMA预测都能正常工作</li>
                            <li>检查预测结果显示</li>
                        </ul>
                    </li>
                </ol>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">故障排除指南</div>
            <div>
                <h4>常见问题及解决方案：</h4>
                
                <div class="issue-item">
                    <h5>问题1: 分类或地区选择器为空</h5>
                    <p><strong>原因</strong>: AI服务未启动或数据库连接失败</p>
                    <p><strong>解决</strong>: 
                        <br>1. 确认AI服务运行在端口5000
                        <br>2. 检查数据库连接
                        <br>3. 查看浏览器控制台错误信息
                    </p>
                </div>
                
                <div class="issue-item">
                    <h5>问题2: 请求发送到错误端口(8081)</h5>
                    <p><strong>原因</strong>: 浏览器缓存或组件未正确使用新的API服务</p>
                    <p><strong>解决</strong>: 
                        <br>1. 清除浏览器缓存
                        <br>2. 硬刷新页面 (Ctrl+F5)
                        <br>3. 检查Network面板确认请求发送到5000端口
                    </p>
                </div>
                
                <div class="issue-item">
                    <h5>问题3: 数据可用性指示器不显示</h5>
                    <p><strong>原因</strong>: 组件间数据传递问题</p>
                    <p><strong>解决</strong>: 
                        <br>1. 确保选择了分类和地区
                        <br>2. 检查控制台是否有JavaScript错误
                        <br>3. 验证API返回的数据格式
                    </p>
                </div>
                
                <div class="issue-item">
                    <h5>问题4: 预测功能失败</h5>
                    <p><strong>原因</strong>: 选择的组合没有足够的历史数据</p>
                    <p><strong>解决</strong>: 
                        <br>1. 选择有数据的分类和地区组合
                        <br>2. 查看数据可用性指示器的建议
                        <br>3. 使用推荐的组合进行预测
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showResult(containerId, title, status, data) {
            const container = document.getElementById(containerId);
            const statusClass = status === 'success' ? 'success' : status === 'error' ? 'error' : 'loading';
            
            let html = `<h4 class="${statusClass}">${title}</h4>`;
            if (data) {
                html += `<div class="result">${JSON.stringify(data, null, 2)}</div>`;
            }
            
            container.innerHTML = html;
        }

        async function testAllAPIs() {
            showResult('api-test-results', '正在测试所有API...', 'loading');
            
            const results = {};
            
            // 测试健康检查
            try {
                const response = await fetch('http://localhost:5000/api/v1/health');
                results.health = response.ok ? '✅ 正常' : '❌ 异常';
            } catch (error) {
                results.health = '❌ 连接失败';
            }
            
            // 测试分类API
            try {
                const response = await fetch('http://localhost:5000/api/v1/crawl_data/categories');
                if (response.ok) {
                    const data = await response.json();
                    const count = data.data.categories.length;
                    results.categories = `✅ ${count}个分类`;
                } else {
                    results.categories = '❌ 请求失败';
                }
            } catch (error) {
                results.categories = '❌ 连接失败';
            }
            
            // 测试地区API
            try {
                const response = await fetch('http://localhost:5000/api/v1/crawl_data/regions');
                if (response.ok) {
                    const data = await response.json();
                    const count = data.data.regions.length;
                    results.regions = `✅ ${count}个地区`;
                } else {
                    results.regions = '❌ 请求失败';
                }
            } catch (error) {
                results.regions = '❌ 连接失败';
            }
            
            // 测试产品搜索API
            try {
                const response = await fetch('http://localhost:5000/api/v1/crawl_data/products?keyword=苹果&limit=5');
                if (response.ok) {
                    const data = await response.json();
                    const count = data.data.products.length;
                    results.products = `✅ 搜索到${count}个产品`;
                } else {
                    results.products = '❌ 请求失败';
                }
            } catch (error) {
                results.products = '❌ 连接失败';
            }
            
            showResult('api-test-results', 'API测试完成', 'success', results);
        }

        async function testDataFormats() {
            showResult('format-test-results', '正在验证数据格式...', 'loading');
            
            try {
                // 测试分类数据格式
                const categoriesResponse = await fetch('http://localhost:5000/api/v1/crawl_data/categories');
                const categoriesData = await categoriesResponse.json();
                
                // 测试地区数据格式
                const regionsResponse = await fetch('http://localhost:5000/api/v1/crawl_data/regions');
                const regionsData = await regionsResponse.json();
                
                const formatValidation = {
                    categories: {
                        hasCorrectStructure: !!(categoriesData.data && categoriesData.data.categories),
                        count: categoriesData.data ? categoriesData.data.categories.length : 0,
                        sampleFields: categoriesData.data && categoriesData.data.categories.length > 0 ? 
                            Object.keys(categoriesData.data.categories[0]) : [],
                        requiredFields: ['id', 'name', 'product_count', 'price_count']
                    },
                    regions: {
                        hasCorrectStructure: !!(regionsData.data && regionsData.data.regions),
                        count: regionsData.data ? regionsData.data.regions.length : 0,
                        sampleFields: regionsData.data && regionsData.data.regions.length > 0 ? 
                            Object.keys(regionsData.data.regions[0]) : [],
                        requiredFields: ['id', 'name', 'level', 'price_count']
                    }
                };
                
                // 验证必需字段
                if (formatValidation.categories.hasCorrectStructure) {
                    const missingCatFields = formatValidation.categories.requiredFields.filter(
                        field => !formatValidation.categories.sampleFields.includes(field)
                    );
                    formatValidation.categories.missingFields = missingCatFields;
                }
                
                if (formatValidation.regions.hasCorrectStructure) {
                    const missingRegFields = formatValidation.regions.requiredFields.filter(
                        field => !formatValidation.regions.sampleFields.includes(field)
                    );
                    formatValidation.regions.missingFields = missingRegFields;
                }
                
                showResult('format-test-results', '✅ 数据格式验证完成', 'success', formatValidation);
                
            } catch (error) {
                showResult('format-test-results', `❌ 数据格式验证失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动测试API
        window.onload = function() {
            testAllAPIs();
        };
    </script>
</body>
</html>
