// routes/auth.js - 用户认证路由
const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const rateLimit = require('express-rate-limit');
const { body, validationResult } = require('express-validator');
const User = require('../models/User');
const logger = require('../utils/logger');
const sendEmail = require('../utils/email');
const generateCode = require('../utils/generateCode');

const router = express.Router();

// 认证相关的请求限流 (更严格)
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 5, // 限制每个IP 15分钟内最多5次认证请求
  message: {
    code: 429,
    message: '认证请求过于频繁，请稍后再试',
    error: {
      type: 'AuthRateLimitError',
      details: '为了安全考虑，请稍后再试'
    }
  }
});

// 输入验证规则
const loginValidation = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('请输入有效的邮箱地址'),
  body('password')
    .isLength({ min: 6 })
    .withMessage('密码至少6位字符')
];

const registerValidation = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('请输入有效的邮箱地址'),
  body('password')
    .isLength({ min: 6 })
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('密码必须包含大小写字母和数字，至少6位'),
  body('name')
    .isLength({ min: 2, max: 20 })
    .matches(/^[\u4e00-\u9fa5a-zA-Z\s]+$/)
    .withMessage('姓名长度2-20字符，只能包含中文、英文和空格'),
  body('phone')
    .optional()
    .isMobilePhone('zh-CN')
    .withMessage('请输入有效的手机号码')
];

// 用户注册
router.post('/register', authLimiter, registerValidation, async (req, res) => {
  try {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(422).json({
        code: 422,
        message: '输入验证失败',
        error: {
          type: 'ValidationError',
          details: errors.array()
        },
        timestamp: new Date().toISOString(),
        requestId: req.requestId
      });
    }

    const { email, password, name, phone } = req.body;

    // 检查邮箱是否已存在
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(409).json({
        code: 10003,
        message: '邮箱已被注册',
        error: {
          type: 'ConflictError',
          details: '该邮箱地址已被其他用户使用'
        },
        timestamp: new Date().toISOString(),
        requestId: req.requestId
      });
    }

    // 密码加密
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // 创建用户
    const user = new User({
      email,
      password: hashedPassword,
      name,
      phone,
      role: 'user',
      status: 'active',
      emailVerified: false,
      createdAt: new Date(),
      lastLoginAt: null
    });

    await user.save();

    // 生成邮箱验证码
    const verificationCode = generateCode(6);
    user.emailVerificationCode = verificationCode;
    user.emailVerificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24小时后过期
    await user.save();

    // 发送验证邮件
    try {
      await sendEmail({
        to: email,
        subject: 'SFAP智慧农业平台 - 邮箱验证',
        template: 'email-verification',
        data: {
          name,
          verificationCode,
          expiresIn: '24小时'
        }
      });
    } catch (emailError) {
      logger.error('发送验证邮件失败:', emailError);
      // 邮件发送失败不影响注册成功
    }

    // 生成JWT Token
    const token = jwt.sign(
      { 
        userId: user._id, 
        email: user.email, 
        role: user.role 
      },
      process.env.JWT_SECRET,
      { expiresIn: '7d' }
    );

    logger.info(`用户注册成功: ${email}`);

    res.status(201).json({
      code: 201,
      message: '注册成功',
      data: {
        token,
        user: {
          id: user._id,
          email: user.email,
          name: user.name,
          role: user.role,
          avatar: user.avatar,
          emailVerified: user.emailVerified
        },
        expiresIn: 7 * 24 * 60 * 60 // 7天，单位秒
      },
      timestamp: new Date().toISOString(),
      requestId: req.requestId
    });

  } catch (error) {
    logger.error('用户注册失败:', error);
    res.status(500).json({
      code: 500,
      message: '注册失败，请稍后重试',
      error: {
        type: 'InternalServerError',
        details: process.env.NODE_ENV === 'development' ? error.message : '服务器内部错误'
      },
      timestamp: new Date().toISOString(),
      requestId: req.requestId
    });
  }
});

// 用户登录
router.post('/login', authLimiter, loginValidation, async (req, res) => {
  try {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(422).json({
        code: 422,
        message: '输入验证失败',
        error: {
          type: 'ValidationError',
          details: errors.array()
        },
        timestamp: new Date().toISOString(),
        requestId: req.requestId
      });
    }

    const { email, password } = req.body;

    // 查找用户
    const user = await User.findOne({ email }).select('+password');
    if (!user) {
      return res.status(401).json({
        code: 10001,
        message: '邮箱或密码错误',
        error: {
          type: 'AuthenticationError',
          details: '用户不存在或密码错误'
        },
        timestamp: new Date().toISOString(),
        requestId: req.requestId
      });
    }

    // 检查用户状态
    if (user.status !== 'active') {
      return res.status(403).json({
        code: 10006,
        message: '账户已被禁用',
        error: {
          type: 'ForbiddenError',
          details: '账户状态异常，请联系管理员'
        },
        timestamp: new Date().toISOString(),
        requestId: req.requestId
      });
    }

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      return res.status(401).json({
        code: 10002,
        message: '邮箱或密码错误',
        error: {
          type: 'AuthenticationError',
          details: '密码验证失败'
        },
        timestamp: new Date().toISOString(),
        requestId: req.requestId
      });
    }

    // 更新最后登录时间
    user.lastLoginAt = new Date();
    await user.save();

    // 生成JWT Token
    const token = jwt.sign(
      { 
        userId: user._id, 
        email: user.email, 
        role: user.role 
      },
      process.env.JWT_SECRET,
      { expiresIn: '7d' }
    );

    logger.info(`用户登录成功: ${email}`);

    res.json({
      code: 200,
      message: '登录成功',
      data: {
        token,
        user: {
          id: user._id,
          email: user.email,
          name: user.name,
          role: user.role,
          avatar: user.avatar,
          emailVerified: user.emailVerified,
          lastLoginAt: user.lastLoginAt
        },
        expiresIn: 7 * 24 * 60 * 60 // 7天，单位秒
      },
      timestamp: new Date().toISOString(),
      requestId: req.requestId
    });

  } catch (error) {
    logger.error('用户登录失败:', error);
    res.status(500).json({
      code: 500,
      message: '登录失败，请稍后重试',
      error: {
        type: 'InternalServerError',
        details: process.env.NODE_ENV === 'development' ? error.message : '服务器内部错误'
      },
      timestamp: new Date().toISOString(),
      requestId: req.requestId
    });
  }
});

// Token刷新
router.post('/refresh', async (req, res) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        code: 10004,
        message: 'Token缺失或格式错误',
        error: {
          type: 'AuthenticationError',
          details: '请提供有效的认证Token'
        },
        timestamp: new Date().toISOString(),
        requestId: req.requestId
      });
    }

    const token = authHeader.substring(7);

    // 验证Token (允许过期)
    let decoded;
    try {
      decoded = jwt.verify(token, process.env.JWT_SECRET, { ignoreExpiration: true });
    } catch (error) {
      return res.status(401).json({
        code: 10005,
        message: 'Token无效',
        error: {
          type: 'AuthenticationError',
          details: 'Token格式错误或已损坏'
        },
        timestamp: new Date().toISOString(),
        requestId: req.requestId
      });
    }

    // 检查Token是否在可刷新期限内 (过期后7天内可刷新)
    const tokenExp = decoded.exp * 1000;
    const now = Date.now();
    const refreshWindow = 7 * 24 * 60 * 60 * 1000; // 7天

    if (now - tokenExp > refreshWindow) {
      return res.status(401).json({
        code: 10007,
        message: 'Token已过期太久，请重新登录',
        error: {
          type: 'AuthenticationError',
          details: 'Token超出刷新期限'
        },
        timestamp: new Date().toISOString(),
        requestId: req.requestId
      });
    }

    // 验证用户是否仍然存在且状态正常
    const user = await User.findById(decoded.userId);
    if (!user || user.status !== 'active') {
      return res.status(401).json({
        code: 10001,
        message: '用户不存在或已被禁用',
        error: {
          type: 'AuthenticationError',
          details: '用户状态异常'
        },
        timestamp: new Date().toISOString(),
        requestId: req.requestId
      });
    }

    // 生成新Token
    const newToken = jwt.sign(
      { 
        userId: user._id, 
        email: user.email, 
        role: user.role 
      },
      process.env.JWT_SECRET,
      { expiresIn: '7d' }
    );

    logger.info(`Token刷新成功: ${user.email}`);

    res.json({
      code: 200,
      message: 'Token刷新成功',
      data: {
        token: newToken,
        user: {
          id: user._id,
          email: user.email,
          name: user.name,
          role: user.role,
          avatar: user.avatar,
          emailVerified: user.emailVerified
        },
        expiresIn: 7 * 24 * 60 * 60 // 7天，单位秒
      },
      timestamp: new Date().toISOString(),
      requestId: req.requestId
    });

  } catch (error) {
    logger.error('Token刷新失败:', error);
    res.status(500).json({
      code: 500,
      message: 'Token刷新失败',
      error: {
        type: 'InternalServerError',
        details: process.env.NODE_ENV === 'development' ? error.message : '服务器内部错误'
      },
      timestamp: new Date().toISOString(),
      requestId: req.requestId
    });
  }
});

// 用户登出
router.post('/logout', async (req, res) => {
  try {
    // 在实际应用中，可以将Token加入黑名单
    // 这里简化处理，只返回成功响应
    
    logger.info('用户登出成功');

    res.json({
      code: 200,
      message: '登出成功',
      data: null,
      timestamp: new Date().toISOString(),
      requestId: req.requestId
    });

  } catch (error) {
    logger.error('用户登出失败:', error);
    res.status(500).json({
      code: 500,
      message: '登出失败',
      error: {
        type: 'InternalServerError',
        details: process.env.NODE_ENV === 'development' ? error.message : '服务器内部错误'
      },
      timestamp: new Date().toISOString(),
      requestId: req.requestId
    });
  }
});

// 邮箱验证
router.post('/verify-email', async (req, res) => {
  try {
    const { email, code } = req.body;

    if (!email || !code) {
      return res.status(400).json({
        code: 400,
        message: '邮箱和验证码不能为空',
        error: {
          type: 'ValidationError',
          details: '缺少必要参数'
        },
        timestamp: new Date().toISOString(),
        requestId: req.requestId
      });
    }

    const user = await User.findOne({ email });
    if (!user) {
      return res.status(404).json({
        code: 10001,
        message: '用户不存在',
        error: {
          type: 'NotFoundError',
          details: '找不到对应的用户'
        },
        timestamp: new Date().toISOString(),
        requestId: req.requestId
      });
    }

    // 检查验证码
    if (user.emailVerificationCode !== code) {
      return res.status(400).json({
        code: 10008,
        message: '验证码错误',
        error: {
          type: 'ValidationError',
          details: '验证码不正确'
        },
        timestamp: new Date().toISOString(),
        requestId: req.requestId
      });
    }

    // 检查验证码是否过期
    if (user.emailVerificationExpires < new Date()) {
      return res.status(400).json({
        code: 10009,
        message: '验证码已过期',
        error: {
          type: 'ValidationError',
          details: '验证码已过期，请重新获取'
        },
        timestamp: new Date().toISOString(),
        requestId: req.requestId
      });
    }

    // 更新用户邮箱验证状态
    user.emailVerified = true;
    user.emailVerificationCode = undefined;
    user.emailVerificationExpires = undefined;
    await user.save();

    logger.info(`邮箱验证成功: ${email}`);

    res.json({
      code: 200,
      message: '邮箱验证成功',
      data: {
        emailVerified: true
      },
      timestamp: new Date().toISOString(),
      requestId: req.requestId
    });

  } catch (error) {
    logger.error('邮箱验证失败:', error);
    res.status(500).json({
      code: 500,
      message: '邮箱验证失败',
      error: {
        type: 'InternalServerError',
        details: process.env.NODE_ENV === 'development' ? error.message : '服务器内部错误'
      },
      timestamp: new Date().toISOString(),
      requestId: req.requestId
    });
  }
});

module.exports = router;
