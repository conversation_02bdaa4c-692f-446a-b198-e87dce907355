// app.js - Express服务器主文件
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const mongoose = require('mongoose');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// 导入路由模块
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const deviceRoutes = require('./routes/devices');
const sensorDataRoutes = require('./routes/sensorData');
const alertRoutes = require('./routes/alerts');
const fileRoutes = require('./routes/files');

// 导入中间件
const authMiddleware = require('./middleware/auth');
const errorHandler = require('./middleware/errorHandler');
const logger = require('./utils/logger');

// 创建Express应用
const app = express();

// 环境变量配置
require('dotenv').config();

const PORT = process.env.PORT || 3000;
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/sfap';
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

// 数据库连接
mongoose.connect(MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
.then(() => {
  logger.info('MongoDB连接成功');
})
.catch((error) => {
  logger.error('MongoDB连接失败:', error);
  process.exit(1);
});

// 基础中间件配置
app.use(helmet()); // 安全头设置
app.use(compression()); // 响应压缩
app.use(morgan('combined', { stream: { write: message => logger.info(message.trim()) } })); // 日志记录

// CORS配置
app.use(cors({
  origin: process.env.NODE_ENV === 'production' 
    ? ['https://your-domain.com', 'https://admin.your-domain.com']
    : ['http://localhost:8080', 'http://localhost:3000'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// 请求解析中间件
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 静态文件服务
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// 请求限流
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 限制每个IP 15分钟内最多100个请求
  message: {
    code: 429,
    message: '请求过于频繁，请稍后再试',
    error: {
      type: 'RateLimitError',
      details: '超出请求频率限制'
    }
  }
});
app.use('/api/', limiter);

// 请求ID中间件
app.use((req, res, next) => {
  req.requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  res.setHeader('X-Request-ID', req.requestId);
  next();
});

// API路由配置
app.use('/api/v1/auth', authRoutes);
app.use('/api/v1/users', authMiddleware, userRoutes);
app.use('/api/v1/devices', authMiddleware, deviceRoutes);
app.use('/api/v1/sensor-data', sensorDataRoutes); // 部分接口需要设备认证
app.use('/api/v1/alerts', authMiddleware, alertRoutes);
app.use('/api/v1/files', authMiddleware, fileRoutes);

// 健康检查接口
app.get('/health', (req, res) => {
  res.json({
    code: 200,
    message: 'success',
    data: {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      version: process.env.npm_package_version || '1.0.0'
    },
    requestId: req.requestId
  });
});

// API文档路由
app.get('/api/docs', (req, res) => {
  res.json({
    code: 200,
    message: 'SFAP API Documentation',
    data: {
      version: 'v1',
      endpoints: {
        auth: '/api/v1/auth',
        users: '/api/v1/users',
        devices: '/api/v1/devices',
        sensorData: '/api/v1/sensor-data',
        alerts: '/api/v1/alerts',
        files: '/api/v1/files'
      },
      documentation: 'https://docs.sfap.com/api'
    }
  });
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    code: 404,
    message: '接口不存在',
    error: {
      type: 'NotFoundError',
      details: `路径 ${req.originalUrl} 不存在`
    },
    timestamp: new Date().toISOString(),
    requestId: req.requestId
  });
});

// 全局错误处理
app.use(errorHandler);

// 优雅关闭处理
process.on('SIGTERM', () => {
  logger.info('收到SIGTERM信号，开始优雅关闭...');
  
  server.close(() => {
    logger.info('HTTP服务器已关闭');
    
    mongoose.connection.close(false, () => {
      logger.info('MongoDB连接已关闭');
      process.exit(0);
    });
  });
});

process.on('SIGINT', () => {
  logger.info('收到SIGINT信号，开始优雅关闭...');
  
  server.close(() => {
    logger.info('HTTP服务器已关闭');
    
    mongoose.connection.close(false, () => {
      logger.info('MongoDB连接已关闭');
      process.exit(0);
    });
  });
});

// 未捕获异常处理
process.on('uncaughtException', (error) => {
  logger.error('未捕获的异常:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('未处理的Promise拒绝:', reason);
  process.exit(1);
});

// 启动服务器
const server = app.listen(PORT, () => {
  logger.info(`SFAP API服务器启动成功`);
  logger.info(`端口: ${PORT}`);
  logger.info(`环境: ${process.env.NODE_ENV || 'development'}`);
  logger.info(`API文档: http://localhost:${PORT}/api/docs`);
  logger.info(`健康检查: http://localhost:${PORT}/health`);
});

module.exports = app;
