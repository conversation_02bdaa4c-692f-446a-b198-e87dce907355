<mxfile host="app.diagrams.net" modified="2024-07-30T10:00:00.000Z" agent="5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" etag="new-etag-12345" version="24.4.0" type="device">
  <diagram id="er-diagram-from-arch" name="智慧农业平台ER图">
    <mxGraphModel dx="2000" dy="1200" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1654" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />

        <!-- 标题 -->
        <mxCell id="title" value="智慧农业辅助平台 ER图 (基于架构设计)" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=24;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="627" y="20" width="600" height="40" as="geometry" />
        </mxCell>

        <!-- 实体定义 -->

        <!-- 用户核心 -->
        <mxCell id="users" value="&lt;b&gt;Users (用户表)&lt;/b&gt;&lt;hr&gt;user_id (PK)&lt;br&gt;username&lt;br&gt;password_hash&lt;br&gt;role (e.g., user, admin, farmer)&lt;br&gt;email&lt;br&gt;phone_number&lt;br&gt;avatar_url&lt;br&gt;created_at&lt;br&gt;updated_at" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="850" y="450" width="250" height="220" as="geometry" />
        </mxCell>
        <mxCell id="addresses" value="&lt;b&gt;Addresses (地址表)&lt;/b&gt;&lt;hr&gt;address_id (PK)&lt;br&gt;user_id (FK)&lt;br&gt;recipient_name&lt;br&gt;recipient_phone&lt;br&gt;province&lt;br&gt;city&lt;br&gt;district&lt;br&gt;detailed_address&lt;br&gt;is_default" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="1200" y="450" width="250" height="220" as="geometry" />
        </mxCell>

        <!-- 电商核心 -->
        <mxCell id="products" value="&lt;b&gt;Products (商品表)&lt;/b&gt;&lt;hr&gt;product_id (PK)&lt;br&gt;name&lt;br&gt;description&lt;br&gt;price&lt;br&gt;stock&lt;br&gt;category_id (FK)&lt;br&gt;seller_id (FK to Users)&lt;br&gt;image_url&lt;br&gt;created_at&lt;br&gt;updated_at" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="150" width="250" height="240" as="geometry" />
        </mxCell>
        <mxCell id="categories" value="&lt;b&gt;Categories (分类表)&lt;/b&gt;&lt;hr&gt;category_id (PK)&lt;br&gt;name&lt;br&gt;parent_id (Self-ref FK)&lt;br&gt;description" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="100" y="150" width="250" height="120" as="geometry" />
        </mxCell>
        <mxCell id="orders" value="&lt;b&gt;Orders (订单表)&lt;/b&gt;&lt;hr&gt;order_id (PK)&lt;br&gt;user_id (FK)&lt;br&gt;address_id (FK)&lt;br&gt;total_amount&lt;br&gt;status (e.g., pending, paid, shipped)&lt;br&gt;payment_id&lt;br&gt;created_at&lt;br&gt;updated_at" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="850" y="150" width="250" height="200" as="geometry" />
        </mxCell>
        <mxCell id="order_items" value="&lt;b&gt;OrderItems (订单项)&lt;/b&gt;&lt;hr&gt;order_item_id (PK)&lt;br&gt;order_id (FK)&lt;br&gt;product_id (FK)&lt;br&gt;quantity&lt;br&gt;price_per_unit" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="450" width="250" height="140" as="geometry" />
        </mxCell>
        <mxCell id="reviews" value="&lt;b&gt;Reviews (评价表)&lt;/b&gt;&lt;hr&gt;review_id (PK)&lt;br&gt;product_id (FK)&lt;br&gt;user_id (FK)&lt;br&gt;rating (1-5)&lt;br&gt;comment&lt;br&gt;created_at" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="750" width="250" height="160" as="geometry" />
        </mxCell>
        <mxCell id="cart_items" value="&lt;b&gt;CartItems (购物车项)&lt;/b&gt;&lt;hr&gt;cart_item_id (PK)&lt;br&gt;user_id (FK)&lt;br&gt;product_id (FK)&lt;br&gt;quantity&lt;br&gt;added_at" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="850" y="750" width="250" height="140" as="geometry" />
        </mxCell>

        <!-- 农业百科 -->
        <mxCell id="encyclopedia_articles" value="&lt;b&gt;EncyclopediaArticles (百科文章)&lt;/b&gt;&lt;hr&gt;article_id (PK)&lt;br&gt;title&lt;br&gt;content&lt;br&gt;author_id (FK to Users)&lt;br&gt;category_id (FK)&lt;br&gt;created_at" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="100" y="450" width="250" height="160" as="geometry" />
        </mxCell>

        <!-- 数据分析 -->
        <mxCell id="price_data" value="&lt;b&gt;PriceData (价格数据)&lt;/b&gt;&lt;hr&gt;price_data_id (PK)&lt;br&gt;product_name&lt;br&gt;price&lt;br&gt;date&lt;br&gt;location&lt;br&gt;source" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="1200" y="150" width="250" height="160" as="geometry" />
        </mxCell>

        <!-- 智能服务 -->
        <mxCell id="ai_chats" value="&lt;b&gt;AIChats (AI对话记录)&lt;/b&gt;&lt;hr&gt;chat_id (PK)&lt;br&gt;user_id (FK)&lt;br&gt;session_id&lt;br&gt;prompt&lt;br&gt;response&lt;br&gt;timestamp" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="1200" y="750" width="250" height="160" as="geometry" />
        </mxCell>

        <!-- 关系连接 -->
        <!-- Categories -> Products (One to Many) -->
        <mxCell id="rel_cat_prod" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classic;startArrow=none;endFill=1;startFill=0;" edge="1" parent="1" source="categories" target="products">
            <mxGeometry relative="1" as="geometry">
                <mxPoint as="sourcePoint" x="350" y="210"/>
                <mxPoint as="targetPoint" x="450" y="210"/>
            </mxGeometry>
        </mxCell>

        <!-- Users -> Products (Seller) (One to Many) -->
        <mxCell id="rel_user_prod_seller" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classic;startArrow=none;endFill=1;startFill=0;" edge="1" parent="1" source="users" target="products">
            <mxGeometry relative="1" as="geometry">
                <mxPoint as="sourcePoint" x="850" y="560"/>
                <mxPoint as="targetPoint" x="700" y="270"/>
                <Array as="points">
                    <mxPoint x="780" y="560"/>
                    <mxPoint x="780" y="270"/>
                </Array>
            </mxGeometry>
        </mxCell>

        <!-- Users -> Orders (One to Many) -->
        <mxCell id="rel_user_ord" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classic;startArrow=none;endFill=1;startFill=0;" edge="1" parent="1" source="users" target="orders">
            <mxGeometry relative="1" as="geometry">
                <mxPoint as="sourcePoint" x="975" y="450"/>
                <mxPoint as="targetPoint" x="975" y="350"/>
            </mxGeometry>
        </mxCell>

        <!-- Users -> Addresses (One to Many) -->
        <mxCell id="rel_user_addr" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classic;startArrow=none;endFill=1;startFill=0;" edge="1" parent="1" source="users" target="addresses">
            <mxGeometry relative="1" as="geometry">
                <mxPoint as="sourcePoint" x="1100" y="560"/>
                <mxPoint as="targetPoint" x="1200" y="560"/>
            </mxGeometry>
        </mxCell>

        <!-- Orders -> OrderItems (One to Many) -->
        <mxCell id="rel_ord_item" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classic;startArrow=none;endFill=1;startFill=0;" edge="1" parent="1" source="orders" target="order_items">
            <mxGeometry relative="1" as="geometry">
                <mxPoint as="sourcePoint" x="850" y="250"/>
                <mxPoint as="targetPoint" x="700" y="520"/>
                <Array as="points">
                    <mxPoint x="780" y="250"/>
                    <mxPoint x="780" y="520"/>
                </Array>
            </mxGeometry>
        </mxCell>

        <!-- Products -> OrderItems (One to Many) -->
        <mxCell id="rel_prod_item" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classic;startArrow=none;endFill=1;startFill=0;" edge="1" parent="1" source="products" target="order_items">
            <mxGeometry relative="1" as="geometry">
                <mxPoint as="sourcePoint" x="575" y="390"/>
                <mxPoint as="targetPoint" x="575" y="450"/>
            </mxGeometry>
        </mxCell>

        <!-- Users -> Reviews (One to Many) -->
        <mxCell id="rel_user_rev" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classic;startArrow=none;endFill=1;startFill=0;" edge="1" parent="1" source="users" target="reviews">
            <mxGeometry relative="1" as="geometry">
                <mxPoint as="sourcePoint" x="850" y="560"/>
                <mxPoint as="targetPoint" x="700" y="830"/>
                <Array as="points">
                    <mxPoint x="780" y="560"/>
                    <mxPoint x="780" y="830"/>
                </Array>
            </mxGeometry>
        </mxCell>

        <!-- Products -> Reviews (One to Many) -->
        <mxCell id="rel_prod_rev" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classic;startArrow=none;endFill=1;startFill=0;" edge="1" parent="1" source="products" target="reviews">
            <mxGeometry relative="1" as="geometry">
                <mxPoint as="sourcePoint" x="575" y="390"/>
                <mxPoint as="targetPoint" x="575" y="750"/>
            </mxGeometry>
        </mxCell>

        <!-- Users -> CartItems (One to Many) -->
        <mxCell id="rel_user_cart" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classic;startArrow=none;endFill=1;startFill=0;" edge="1" parent="1" source="users" target="cart_items">
            <mxGeometry relative="1" as="geometry">
                <mxPoint as="sourcePoint" x="975" y="670"/>
                <mxPoint as="targetPoint" x="975" y="750"/>
            </mxGeometry>
        </mxCell>

        <!-- Products -> CartItems (One to Many) -->
        <mxCell id="rel_prod_cart" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classic;startArrow=none;endFill=1;startFill=0;" edge="1" parent="1" source="products" target="cart_items">
            <mxGeometry relative="1" as="geometry">
                <mxPoint as="sourcePoint" x="700" y="270"/>
                <mxPoint as="targetPoint" x="850" y="820"/>
                <Array as="points">
                    <mxPoint x="780" y="270"/>
                    <mxPoint x="780" y="820"/>
                </Array>
            </mxGeometry>
        </mxCell>

        <!-- Users -> EncyclopediaArticles (Author) (One to Many) -->
        <mxCell id="rel_user_enc" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classic;startArrow=none;endFill=1;startFill=0;" edge="1" parent="1" source="users" target="encyclopedia_articles">
            <mxGeometry relative="1" as="geometry">
                <mxPoint as="sourcePoint" x="850" y="530"/>
                <mxPoint as="targetPoint" x="350" y="530"/>
            </mxGeometry>
        </mxCell>

        <!-- Categories -> EncyclopediaArticles (One to Many) -->
        <mxCell id="rel_cat_enc" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classic;startArrow=none;endFill=1;startFill=0;" edge="1" parent="1" source="categories" target="encyclopedia_articles">
            <mxGeometry relative="1" as="geometry">
                <mxPoint as="sourcePoint" x="225" y="270"/>
                <mxPoint as="targetPoint" x="225" y="450"/>
            </mxGeometry>
        </mxCell>

        <!-- Users -> AIChats (One to Many) -->
        <mxCell id="rel_user_chat" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classic;startArrow=none;endFill=1;startFill=0;" edge="1" parent="1" source="users" target="ai_chats">
            <mxGeometry relative="1" as="geometry">
                <mxPoint as="sourcePoint" x="1100" y="560"/>
                <mxPoint as="targetPoint" x="1200" y="830"/>
                 <Array as="points">
                    <mxPoint x="1150" y="560"/>
                    <mxPoint x="1150" y="830"/>
                </Array>
            </mxGeometry>
        </mxCell>

      </root>
    </mxGraphModel>
  </diagram>
</mxfile>