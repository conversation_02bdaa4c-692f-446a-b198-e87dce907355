---
description: 
globs: 
alwaysApply: true
---
📁 项目结构规范
前端（Vue2 + Element UI）
所有前端源码存放于 /src/ 目录下。

按功能模块组织目录：

api/：封装 axios 接口请求。

components/：基础组件，按用途拆分子目录。

views/：页面组件，路由层级与组件一一对应。

services/：服务封装层，例如 AI、天气、价格预测。

utils/：工具函数。

store/：Vuex 状态管理，支持模块化。

router/：路由配置。

assets/：静态资源（图片、样式等）。

styles/：全局样式与主题变量。

资源路径遵循相对路径引用，禁止绝对路径。

后端（Spring Boot + MyBatis-Plus）
后端源码放置于 backend/src/main/java/com/agriculture/。

按职责分包：

controller/：控制层（REST 接口）。

service/：业务接口；实现类放置于 impl/。

mapper/：数据库操作接口。

entity/：数据库实体类。

dto/：请求参数对象。

vo/：返回数据封装对象。

exception/：自定义异常。

config/：配置类（含跨域、安全、Swagger等）。

mall/：商城模块。

配置文件：resources/application.yml + 分环境配置（dev/prod）。

静态资源：放置于 resources/static/。

Mapper XML 文件：resources/mapper/ 目录下。

🔠 命名规范
Java 后端
类名：PascalCase，如 UserController。

方法名：camelCase，如 getProductList()。

变量名：camelCase，如 userId。

常量名：ALL_CAPS，如 DEFAULT_PAGE_SIZE。

包名：全小写，如 com.agriculture.service。

接口路径：RESTful 风格，推荐使用 /api/v1/xxx，使用复数资源名、短横线连接，例如 /api/v1/product-list。

DTO/VO：使用统一后缀命名如 LoginDTO、ProductVO。

Vue 前端
组件名：PascalCase（如 ProductList.vue）或 kebab-case（如 product-list.vue）。

路由路径：小写短横线风格 /user-profile。

Vuex 模块：文件名 camelCase，例如 user.js。

Mutation 常量：全大写下划线命名，如 SET_USER_DATA。

Action/函数：camelCase，如 fetchUser()。

🌐 接口设计与响应结构
所有接口均使用 RESTful 风格。

控制器使用 @RestController 注解。

HTTP 方法语义清晰：

GET /api/v1/products：查询

POST /api/v1/products：新增

PUT /api/v1/products/{id}：修改

DELETE /api/v1/products/{id}：删除

请求参数：

GET：路径参数或 query string。

POST/PUT：使用 @RequestBody JSON 请求体。

统一返回结构：

json
复制
编辑
{
  "code": 0,
  "msg": "success",
  "data": { ... }
}
分页结构：

json
复制
编辑
{
  "code": 0,
  "msg": "success",
  "data": {
    "total": 100,
    "items": [ ... ]
  }
}
错误处理：业务错误 code != 0，HTTP 状态仍返回 200，系统异常记录堆栈信息，前端统一处理。

⚙ Spring Boot 实践
使用 @Service、@RestController、@Mapper、@Transactional 进行分层与事务控制。

application.yml 配置端口（8081）、数据库、日志、跨域等。

环境隔离使用 spring.profiles.active。

跨域配置通过 CorsConfig 全局开放前端地址：

java
复制
编辑
@CrossOrigin(origins = "http://**************:8200")
启动类使用 SpringApplication.run(...) 启动。

不使用 JWT；认证方式为基础登录接口与会话控制。

🧨 异常处理与日志输出
全局异常统一处理：

使用 @ControllerAdvice + @ExceptionHandler。

统一返回 { code, msg }。

自定义异常类：BusinessException。

日志工具：使用 SLF4J + Logback。

日志级别：

error：系统异常

warn：业务可控异常

info：流程日志

debug：调试信息（生产环境关闭）

日志格式：

java
复制
编辑
logger.error("订单创建失败，orderId={}, userId={}", orderId, userId);
🖼 Vue2 项目规范
所有 Vue 文件均使用单文件组件（.vue）。

<template>、<script>、<style> 均不可省略。

props 与事件：使用 props 下发数据，$emit 上报事件。

组件职责单一，小型、复用为主。

路由懒加载：

js
复制
编辑
component: () => import('@/views/ProductDetail.vue')
Vuex 状态管理
模块化存储状态，文件划分明确：store/modules/user.js。

Mutation 命名统一常量化。

使用 mapState, mapGetters，避免直接访问 state。

Axios 封装
统一封装 axios 至 utils/request.js。

设置 baseURL 为 http://**************:8081。

添加拦截器统一处理：

请求头添加语言/权限信息

响应解包 data.code 判断逻辑

错误提示统一：401 提示登录，403 权限不足，500 弹出错误框。

环境变量
.env.development / .env.production 中配置：

ini
复制
编辑
VUE_APP_BASE_API=http://**************:8081/api
VUE_APP_AI_API_KEY=xxxx
🤖 AI 模块 & 预测模型接口规范
AI 接口路径：POST /api/ai/chat

请求参数示例：

json
复制
编辑
{ "question": "今日黄瓜价格如何？", "context": {} }
预测接口路径：POST /api/price/predict

请求参数示例：

json
复制
编辑
{ "productId": 123, "region": "Anhui", "history": [1.2, 1.4, 1.3] }
返回结构一致：

json
复制
编辑
{ "code": 0, "msg": "success", "data": { "forecast": [ ... ] } }
超时控制：接口响应较慢需处理加载状态。

API_KEY：存于 .env，不得硬编码。

调用频率：前端添加节流，后端添加限流。


出错回退策略：提示“服务不可用”，支持重试与日志记录