---
description:
globs:
alwaysApply: false
---
# 后端服务 AI 规则与偏好 (优化版)

这份文档旨在为 AI 助手提供关于本项目后端技术栈的全面、深入指南，包括 Java 后端服务、Python 数据服务以及共享的数据库和存储解决方案。遵循这些规则将帮助 AI 更准确地理解项目结构、编码约定和技术选型，并在此基础上生成高质量、符合规范的代码，有效协助开发任务。

---

## 1. 通用项目开发规范与指导原则

* **项目目标**: 构建高可用、可伸缩、易于维护的后端服务，为前端和数据分析提供稳定高效的 API 接口。
* **代码质量**:
    * 追求**清洁代码 (Clean Code)** 原则：高可读性、模块化设计、命名清晰、函数/方法职责单一。
    * **高内聚低耦合**: 鼓励模块内部功能紧密相关，模块之间依赖松散。
    * **可测试性**: 代码应易于进行单元测试和集成测试。
* **服务间通信**: Java 后端与 Python 数据服务之间主要通过 **RESTful API** 进行通信。确保 API 设计遵循 REST 原则（无状态、统一接口、资源导向），并考虑版本控制（例如 `/api/v1/resource`）。
* **错误处理**:
    * **统一且有意义**: 提供统一的错误响应格式（例如 JSON），包含错误码、错误信息和可选的详细信息。
    * **异常捕获与抛出**: 异常应在恰当的层面被捕获并处理。不要吞噬异常，也不要无谓地抛出。
    * **自定义异常**: 对于业务特有的错误，定义清晰的自定义异常。
* **日志记录**:
    * 确保关键操作、业务流程中的重要步骤、异常发生时都有清晰、有用的日志记录。
    * 日志级别应正确使用（DEBUG, INFO, WARN, ERROR），避免在生产环境输出过多DEBUG日志。
    * 敏感信息（如密码、API Key）严禁出现在日志中。
* **安全性**: 遵循安全编码实践，防范常见的Web漏洞（如SQL注入、XSS、CSRF、不安全的直接对象引用等）。
* **文档**:
    * 除了 API 文档（Swagger），代码内部也应有清晰的注释，特别是对复杂逻辑、关键算法或非显而易见的实现进行解释。
    * 公共方法和类应有 Javadoc 或 Python Docstrings。
* **代码评审**: 假设代码将进行严格的代码评审，因此代码应易于理解和审查。
* **版本控制**: 假设使用 Git，提交信息应清晰、简洁、有意义，遵循一定的提交规范（如 Conventional Commits）。

---

## 2. Java 后端服务 (Spring Boot)

* **框架**: **Spring Boot 2.7.0**。优先采用 Spring Boot 的约定优于配置原则，充分利用其自动化配置和starter依赖，构建 RESTful API。
* **项目结构**:
    * 推荐按领域/模块（package by feature）组织代码，而不是按层（package by layer）。
    * 服务分层清晰：Controller（处理HTTP请求）、Service（业务逻辑）、Repository（数据访问）。
* **依赖注入**: 优先使用**构造器注入 (Constructor Injection)**，它使得依赖关系更明确，更易于测试。避免使用字段注入或 Setter 注入。
* **数据库交互**:
    * **MySQL 8.0**: 主数据库。
    * **MyBatis Plus 3.5.2**: 推荐用于复杂的 SQL 查询、多表关联、自定义映射或对 SQL 有高度控制需求的场景。映射文件（XML）应清晰、可读。
    * **Spring Data JPA + Hibernate**: 推荐用于简单的 CRUD 操作、实体关系管理以及领域驱动设计（DDD）中实体和仓库的实现。在 ORM 映射中，优先使用 JPA 注解，并确保实体与数据库表结构良好对应。
    * **选择优先级**: 当简单 CRUD 操作时，JPA 优先；当涉及复杂联表查询或需要手写 SQL 优化时，考虑 MyBatis Plus。
    * **事务管理**: 使用 Spring 的 `@Transactional` 注解进行声明式事务管理，确保事务边界清晰，遵循 ACID 原则。
* **安全认证**:
    * 使用 **Spring Security** 框架进行身份验证和授权管理，配置应精简且安全。
    * 认证机制基于 **JWT (0.11.5)**。在设计认证流程时，需包含 JWT 的生成、签名、验证、解析以及在请求头中的传递。
    * 权限控制应细粒度，使用 `@PreAuthorize` 或基于角色的授权。
* **API 文档**:
    * 所有对外暴露的 RESTful API 接口都必须使用 **Swagger 2.9.2** 进行详细文档化。
    * 确保控制器方法使用 `@ApiOperation`, `@ApiImplicitParams`, `@ApiResponses` 等注解，使模型、参数、响应码和示例清晰明了。
* **数据校验**: 使用 `javax.validation` 或 `jakarta.validation`（如 `@Valid`, `@NotNull`, `@Size` 等）对传入的请求体进行数据校验。
* **异常处理**: 实现**全局异常处理器** (`@ControllerAdvice` 或 `@RestControllerAdvice`)，捕获并统一处理各类异常，返回标准化的错误响应。
* **常用工具库**:
    * **Hutool 5.8.0**: 优先使用 Hutool 提供的工具类进行日常开发任务，如字符串处理、日期时间操作、文件 IO、加密解密、集合操作等。
    * **Lombok 1.18.30**: 使用 Lombok 注解（如 `@Data`, `@NoArgsConstructor`, `@AllArgsConstructor`, `@Builder` 等）简化 POJO 类的代码，减少冗余。
* **JSON 处理**: 使用 **FastJSON 1.2.83** 进行 Java 对象与 JSON 字符串之间的序列化和反序列化操作。注意其序列化配置和可能的安全风险（旧版本）。
* **AI 集成**:
    * 通过 **DashScope SDK 2.18.2 (阿里云百炼)** 实现与阿里云百炼大模型的集成。
    * 在需要调用 AI 能力的业务场景中，封装好 SDK 调用逻辑，确保请求参数和响应处理的规范性，并考虑异步调用和结果缓存。
* **连接池**: 默认使用 **HikariCP**，无需额外配置，但要清楚其存在和高性能特性。

---

## 3. Python 数据服务

* **Web 框架**: **Flask 2.0.1+**。用于构建轻量级的数据服务 API 接口（如数据预处理、模型预测、数据爬取触发等），保持接口简洁、职责单一。
* **代码结构**:
    * 模块化设计，根据功能或领域划分不同的文件和目录（例如 `routes.py`, `data_processing.py`, `models.py`, `crawlers/`）。
    * 使用 Python 的包结构来组织大型应用。
* **虚拟环境**: 开发和部署时都应使用虚拟环境（如 `venv` 或 `conda`）管理项目依赖。
* **文档字符串与类型提示**:
    * 所有函数、类和复杂方法都应包含清晰的 **Docstrings**（遵循 Google 或 NumPy 风格），解释其功能、参数、返回值和可能抛出的异常。
    * 积极使用 **类型提示 (Type Hinting)** (`typing` 模块)，提高代码可读性和可维护性，便于静态分析和IDE提示。
* **异常处理**: 采用 Pythonic 的 `try...except...finally` 结构处理异常，避免裸露的 `except`。明确捕获特定类型的异常。
* **日志记录**: 使用 Python 内置的 `logging` 模块进行日志管理。配置不同的日志处理器和格式。
* **测试**:
    * 为核心功能和复杂逻辑编写单元测试（使用 `unittest` 或更推荐的 `pytest`）。
    * 对 API 接口进行集成测试。
* **环境变量**: 敏感信息（如数据库凭据、API 密钥）和配置参数应通过环境变量加载，而非硬编码。
* **爬虫框架**: **Scrapy 2.11.0+**。用于构建网络爬虫，进行数据采集。
    * 遵循 Scrapy 项目结构和组件（Spider, Pipeline, Item, Middleware）。
    * 确保爬虫具有良好的容错性，遵守 Robots 协议，并考虑反爬机制。
* **数据处理**:
    * **Pandas 2.0.0+**: 主要用于数据清洗、转换、分析和结构化处理。优先使用 Pandas DataFrame 和 Series 进行高效数据操作，避免低效率的循环。
    * **NumPy 1.26.0+**: 用于数值计算和数组操作，特别是处理大量同类型数据时，利用其向量化特性。
* **机器学习**:
    * **Scikit-learn 1.3.0+**: 用于传统的机器学习模型训练、评估、预处理（如特征工程、数据标准化、交叉验证）和常见算法实现。
    * **PyTorch 2.0.0+**: 用于深度学习模型的开发、训练和推理。在构建神经网络时，优先使用 PyTorch 的张量操作、`nn.Module` 结构和自动微分能力。模型训练过程应考虑 GPU 加速。
* **统计分析**: **Statsmodels 0.14.0+**: 用于进行统计建模和推断，如回归分析、时间序列分析等。
* **图像处理**: **Pillow 10.0.0+**: 用于图像的读取、写入、裁剪、缩放、格式转换等基本操作。
* **数据库连接**: 使用 **mysql-connector-python 8.0.0+** 连接 MySQL 数据库。确保连接池管理和 SQL 操作的安全性（例如参数化查询防止 SQL 注入）。
* **网络请求**:
    * **Requests 2.31.0+**: 进行通用的 HTTP 请求，例如调用外部 API 或获取网页内容。考虑会话（Session）管理、超时设置和错误重试机制。
    * **BeautifulSoup4 4.12.0+**: 用于解析 HTML 和 XML 文档，从网页中提取数据。结合 `lxml` 解析器以提高性能。

---

## 4. 数据库与存储

* **主数据库**: **MySQL 8.0**。所有持久化数据存储于此。
    * **数据库设计**: 确保数据库设计遵循范式（如第三范式），并为常用查询创建合适的索引。
    * **性能优化**: 关注 SQL 查询性能，避免 N+1 查询问题。
    * **事务管理**: 在所有需要原子性操作的场景中，正确使用数据库事务。
    * **安全性**: 避免直接拼接 SQL，使用参数化查询；管理好数据库连接凭据。
* **连接池**: Java 后端使用 **HikariCP**，Python 数据服务使用 `mysql-connector-python` 提供的连接管理。确保连接池参数配置合理，以优化性能和资源利用。
* **文件存储**: 主要使用 **本地文件系统** 进行文件存储。
    * **路径管理**: 文件存储路径应可配置，避免硬编码。
    * **安全性**: 考虑文件上传的安全校验（文件类型、大小、病毒扫描），防止恶意文件上传。
    * **权限管理**: 合理设置文件和目录的读写权限。
    * **大文件处理**: 对于大文件，考虑分块上传/下载和流式处理。

---

通过严格遵循这些详细的开发规范和技术栈偏好，AI 将能更精准地理解您的项目上下文，从而生成更符合团队风格、更高质量、更少错误的代码，并能更好地在开发过程中提供智能协助。