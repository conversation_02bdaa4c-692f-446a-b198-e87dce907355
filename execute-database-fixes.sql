-- 执行数据库修复脚本
-- 确保所有必要的表和字段都存在

USE agriculture_mall;

-- 1. 检查并添加product表的缺失字段
SELECT '=== 修复product表字段 ===' as message;

-- 添加popularity_score字段
ALTER TABLE product 
ADD COLUMN IF NOT EXISTS popularity_score DECIMAL(10,2) DEFAULT 0.0 COMMENT '商品热度评分';

-- 添加view_count字段
ALTER TABLE product 
ADD COLUMN IF NOT EXISTS view_count INT DEFAULT 0 COMMENT '浏览次数';

-- 添加sales_count字段
ALTER TABLE product 
ADD COLUMN IF NOT EXISTS sales_count INT DEFAULT 0 COMMENT '销售数量';

-- 添加rating字段
ALTER TABLE product 
ADD COLUMN IF NOT EXISTS rating DECIMAL(3,2) DEFAULT 0.0 COMMENT '商品评分';

-- 2. 更新现有商品的热度评分
UPDATE product 
SET popularity_score = COALESCE(view_count, 0) * 0.3 + COALESCE(sales_count, 0) * 0.7
WHERE deleted = 0;

-- 3. 确保news表存在并有正确的结构
SELECT '=== 检查并创建news表 ===' as message;

CREATE TABLE IF NOT EXISTS news (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '新闻ID',
    title VARCHAR(200) NOT NULL COMMENT '新闻标题',
    content TEXT COMMENT '新闻内容',
    summary VARCHAR(500) COMMENT '新闻摘要',
    author VARCHAR(100) COMMENT '作者',
    source VARCHAR(100) COMMENT '来源',
    category VARCHAR(50) COMMENT '分类',
    tags VARCHAR(200) COMMENT '标签',
    image_url VARCHAR(500) COMMENT '封面图片URL',
    view_count INT DEFAULT 0 COMMENT '浏览次数',
    like_count INT DEFAULT 0 COMMENT '点赞次数',
    is_hot TINYINT DEFAULT 0 COMMENT '是否热门：0-否，1-是',
    is_top TINYINT DEFAULT 0 COMMENT '是否置顶：0-否，1-是',
    status TINYINT DEFAULT 1 COMMENT '状态：0-草稿，1-发布，2-下线',
    publish_time TIMESTAMP NULL COMMENT '发布时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '是否删除：0-否，1-是',
    INDEX idx_category (category),
    INDEX idx_status (status),
    INDEX idx_is_hot (is_hot),
    INDEX idx_is_top (is_top),
    INDEX idx_publish_time (publish_time),
    INDEX idx_deleted (deleted)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='新闻资讯表';

-- 4. 插入示例新闻数据（如果表为空）
INSERT IGNORE INTO news (title, content, summary, author, source, category, is_hot, status, publish_time) VALUES
('智慧农业助力乡村振兴', '智慧农业技术正在改变传统农业生产方式，通过物联网、大数据、人工智能等技术手段，实现农业生产的精准化、智能化管理。这不仅提高了农业生产效率，还为农民增收致富开辟了新途径。', '智慧农业技术助力现代农业发展', '农业专家', 'SFAP平台', '农业科技', 1, 1, NOW()),
('有机农产品市场前景广阔', '随着消费者健康意识的提高，有机农产品需求持续增长。市场调研显示，有机农产品的销售额年增长率超过20%，预计未来几年将继续保持强劲增长势头。', '有机农产品市场分析报告', '市场分析师', 'SFAP平台', '市场动态', 1, 1, NOW()),
('农产品价格走势分析', '本月主要农产品价格呈现稳中有升的态势。其中，蔬菜类产品受季节性因素影响，价格有所上涨；粮食类产品价格保持稳定；水果类产品因丰收影响，价格略有下降。', '农产品价格监测报告', '价格分析师', 'SFAP平台', '价格行情', 0, 1, NOW()),
('绿色种植技术推广', '推广绿色种植技术，提高农产品质量和产量。通过科学施肥、生物防治、轮作休耕等技术措施，实现农业可持续发展，保护生态环境。', '绿色种植技术介绍', '技术专家', 'SFAP平台', '种植技术', 0, 1, NOW()),
('农业政策解读', '最新农业补贴政策解读，助力农民增收。政府出台多项惠农政策，包括种植补贴、农机购置补贴、农业保险等，为农业发展提供有力支持。', '农业政策分析', '政策专家', 'SFAP平台', '政策法规', 0, 1, NOW()),
('农产品电商发展趋势', '农产品电商平台快速发展，为农民提供了新的销售渠道。通过线上销售，农产品可以直接面向消费者，减少中间环节，提高农民收益。', '农产品电商分析', '电商专家', 'SFAP平台', '电商发展', 1, 1, NOW()),
('农业保险助力风险防控', '农业保险为农业生产提供风险保障，帮助农民应对自然灾害、病虫害等风险。完善的农业保险体系是现代农业发展的重要保障。', '农业保险政策解读', '保险专家', 'SFAP平台', '风险管理', 0, 1, NOW()),
('现代农业园区建设', '现代农业园区集成先进技术和管理模式，成为农业现代化的重要载体。园区通过规模化经营、标准化生产，提高农业竞争力。', '现代农业园区发展', '园区专家', 'SFAP平台', '园区建设', 0, 1, NOW());

-- 5. 检查并创建search_keyword表
SELECT '=== 检查并创建search_keyword表 ===' as message;

CREATE TABLE IF NOT EXISTS search_keyword (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    keyword VARCHAR(100) NOT NULL COMMENT '搜索关键词',
    search_count INT DEFAULT 0 COMMENT '搜索次数',
    result_count INT DEFAULT 0 COMMENT '搜索结果数量',
    click_rate DECIMAL(5,4) DEFAULT 0.0000 COMMENT '点击率',
    category_id BIGINT NULL COMMENT '关联分类ID',
    is_hot TINYINT DEFAULT 0 COMMENT '是否热门关键词：0-否，1-是',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    last_search_at TIMESTAMP NULL COMMENT '最后搜索时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_keyword (keyword),
    INDEX idx_search_count (search_count),
    INDEX idx_is_hot (is_hot),
    INDEX idx_status (status),
    INDEX idx_category_id (category_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='搜索关键词表';

-- 6. 插入热门搜索关键词
INSERT IGNORE INTO search_keyword (keyword, search_count, is_hot, status) VALUES
('有机蔬菜', 150, 1, 1),
('新鲜水果', 120, 1, 1),
('五常大米', 100, 1, 1),
('绿色食品', 90, 1, 1),
('农家菜', 80, 1, 1),
('特色农产品', 75, 1, 1),
('当季水果', 70, 1, 1),
('无公害蔬菜', 65, 1, 1),
('生态农产品', 60, 1, 1),
('地理标志产品', 55, 1, 1);

-- 7. 验证修复结果
SELECT '=== 验证修复结果 ===' as message;

-- 检查product表字段
SELECT COLUMN_NAME, DATA_TYPE, COLUMN_DEFAULT, COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'agriculture_mall' 
  AND TABLE_NAME = 'product' 
  AND COLUMN_NAME IN ('popularity_score', 'view_count', 'sales_count', 'rating');

-- 检查news表数据
SELECT COUNT(*) as news_count FROM news WHERE status = 1;

-- 检查search_keyword表数据
SELECT COUNT(*) as keyword_count FROM search_keyword WHERE status = 1;

SELECT '=== 数据库修复完成 ===' as message;
