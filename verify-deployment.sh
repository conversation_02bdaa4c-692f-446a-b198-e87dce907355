#!/bin/bash

# 智慧农业平台部署验证脚本
# 服务器IP: **************

echo "=== 智慧农业平台部署验证开始 ==="

SERVER_IP="**************"
BACKEND_PORT="8081"
FRONTEND_PORT="8200"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查函数
check_service() {
    local service_name=$1
    local url=$2
    local expected_status=$3
    
    echo -n "检查 $service_name ... "
    
    response=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout 10 "$url" 2>/dev/null)
    
    if [ "$response" = "$expected_status" ]; then
        echo -e "${GREEN}✅ 正常${NC} (HTTP $response)"
        return 0
    else
        echo -e "${RED}❌ 异常${NC} (HTTP $response)"
        return 1
    fi
}

# 检查端口
check_port() {
    local port=$1
    local service_name=$2
    
    echo -n "检查端口 $port ($service_name) ... "
    
    if nc -z $SERVER_IP $port 2>/dev/null; then
        echo -e "${GREEN}✅ 开放${NC}"
        return 0
    else
        echo -e "${RED}❌ 关闭${NC}"
        return 1
    fi
}

# 检查API响应内容
check_api_content() {
    local url=$1
    local expected_content=$2
    local service_name=$3
    
    echo -n "检查 $service_name API内容 ... "
    
    response=$(curl -s "$url" 2>/dev/null)
    
    if echo "$response" | grep -q "$expected_content"; then
        echo -e "${GREEN}✅ 内容正确${NC}"
        return 0
    else
        echo -e "${RED}❌ 内容异常${NC}"
        echo "响应内容: $response"
        return 1
    fi
}

echo ""
echo "1. 端口连通性检查"
echo "===================="
check_port $BACKEND_PORT "后端服务"
check_port $FRONTEND_PORT "前端服务"

echo ""
echo "2. 后端API检查"
echo "===================="
check_service "根路径" "http://$SERVER_IP:$BACKEND_PORT/" "200"
check_service "健康检查" "http://$SERVER_IP:$BACKEND_PORT/health" "200"
check_service "API信息" "http://$SERVER_IP:$BACKEND_PORT/api" "200"

echo ""
echo "3. API内容验证"
echo "===================="
check_api_content "http://$SERVER_IP:$BACKEND_PORT/" "智慧农业辅助平台" "根路径"
check_api_content "http://$SERVER_IP:$BACKEND_PORT/health" "UP" "健康检查"
check_api_content "http://$SERVER_IP:$BACKEND_PORT/api" "智慧农业辅助平台API" "API信息"

echo ""
echo "4. Swagger文档检查"
echo "===================="
check_service "Swagger UI" "http://$SERVER_IP:$BACKEND_PORT/swagger-ui.html" "200"

echo ""
echo "5. 业务API检查"
echo "===================="
check_service "用户API" "http://$SERVER_IP:$BACKEND_PORT/api/users/test" "404"  # 404是正常的，说明路由工作
check_service "商品API" "http://$SERVER_IP:$BACKEND_PORT/api/products" "200"
check_service "首页API" "http://$SERVER_IP:$BACKEND_PORT/api/home" "200"

echo ""
echo "6. 跨域检查"
echo "===================="
echo -n "检查CORS配置 ... "
cors_response=$(curl -s -H "Origin: http://$SERVER_IP:$FRONTEND_PORT" \
                     -H "Access-Control-Request-Method: GET" \
                     -H "Access-Control-Request-Headers: Content-Type" \
                     -X OPTIONS \
                     "http://$SERVER_IP:$BACKEND_PORT/api/home" \
                     -w "%{http_code}" -o /dev/null 2>/dev/null)

if [ "$cors_response" = "200" ] || [ "$cors_response" = "204" ]; then
    echo -e "${GREEN}✅ CORS配置正常${NC}"
else
    echo -e "${YELLOW}⚠️ CORS可能需要调整${NC} (HTTP $cors_response)"
fi

echo ""
echo "7. 文件上传目录检查"
echo "===================="
if [ -d "/www/wwwroot/agriculture/uploads" ]; then
    echo -e "${GREEN}✅ 上传目录存在${NC}"
    
    # 检查权限
    if [ -w "/www/wwwroot/agriculture/uploads" ]; then
        echo -e "${GREEN}✅ 上传目录可写${NC}"
    else
        echo -e "${RED}❌ 上传目录不可写${NC}"
    fi
else
    echo -e "${RED}❌ 上传目录不存在${NC}"
fi

echo ""
echo "8. 日志文件检查"
echo "===================="
if [ -f "/www/wwwroot/agriculture/logs/agriculture-mall.log" ]; then
    echo -e "${GREEN}✅ 日志文件存在${NC}"
    
    # 检查最近的日志
    echo "最近的日志内容:"
    tail -5 "/www/wwwroot/agriculture/logs/agriculture-mall.log" 2>/dev/null || echo "无法读取日志文件"
else
    echo -e "${YELLOW}⚠️ 日志文件不存在${NC}"
fi

echo ""
echo "9. 数据库连接检查"
echo "===================="
echo -n "检查数据库连接 ... "

# 通过API检查数据库连接
db_check=$(curl -s "http://$SERVER_IP:$BACKEND_PORT/health" 2>/dev/null)
if echo "$db_check" | grep -q "UP"; then
    echo -e "${GREEN}✅ 数据库连接正常${NC}"
else
    echo -e "${RED}❌ 数据库连接异常${NC}"
fi

echo ""
echo "10. 性能测试"
echo "===================="
echo -n "API响应时间测试 ... "

start_time=$(date +%s%N)
curl -s "http://$SERVER_IP:$BACKEND_PORT/api/home" > /dev/null 2>&1
end_time=$(date +%s%N)

response_time=$(( (end_time - start_time) / 1000000 ))

if [ $response_time -lt 1000 ]; then
    echo -e "${GREEN}✅ 响应时间: ${response_time}ms${NC}"
elif [ $response_time -lt 3000 ]; then
    echo -e "${YELLOW}⚠️ 响应时间: ${response_time}ms (稍慢)${NC}"
else
    echo -e "${RED}❌ 响应时间: ${response_time}ms (过慢)${NC}"
fi

echo ""
echo "=== 验证完成 ==="
echo ""
echo "📋 验证总结:"
echo "- 后端服务地址: http://$SERVER_IP:$BACKEND_PORT"
echo "- 前端访问地址: http://$SERVER_IP:$FRONTEND_PORT"
echo "- API文档地址: http://$SERVER_IP:$BACKEND_PORT/swagger-ui.html"
echo ""
echo "🔧 如果发现问题，请检查:"
echo "1. 宝塔面板中Java项目是否正常运行"
echo "2. 数据库服务是否启动"
echo "3. 防火墙端口是否开放"
echo "4. 日志文件中的错误信息"
echo ""
echo "📝 查看实时日志命令:"
echo "tail -f /www/wwwroot/agriculture/logs/agriculture-mall.log"
