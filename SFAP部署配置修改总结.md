# SFAP农品汇平台部署配置修改总结

## 📋 修改概览

本次配置修改主要是为了适配阿里云服务器部署，解决端口冲突问题，并优化生产环境配置。

## 🔧 配置修改详情

### 1. 端口配置调整

#### 原始端口分配：
- 前端：8080
- 后端Spring Boot：8081
- AI预测服务：5000
- 新闻爬取服务：5000 ❌ **端口冲突**
- 价格爬取服务：8000（API配置，但未启用Web服务）

#### 新端口分配：
- 前端：8200 ✅ **已修改**
- 后端Spring Boot：8081 ✅ **保持不变**
- AI预测服务：5000 ✅ **保持不变**
- 新闻爬取服务：5001 ✅ **已修改，解决冲突**
- 价格爬取服务：8002 ✅ **已修改（预留端口）**

### 2. 具体文件修改

#### 2.1 前端配置修改

**文件：`vue.config.js`**
```javascript
// 修改前
devServer: {
  port: 8080,
  host: 'localhost',

// 修改后
devServer: {
  port: 8200,
  host: 'localhost',
```

**影响：**
- 开发环境前端服务端口从8080改为8200
- 需要更新浏览器访问地址

#### 2.2 后端配置修改

**文件：`backend/main/src/main/resources/application.yml`**

**开发环境CORS配置：**
```yaml
# 修改前
app:
  domain: http://localhost:8080
  cors:
    allowed-origins: http://localhost:8080,http://127.0.0.1:8080,http://localhost:8082,http://127.0.0.1:8082

# 修改后
app:
  domain: http://localhost:8200
  cors:
    allowed-origins: http://localhost:8200,http://127.0.0.1:8200,http://localhost:8082,http://127.0.0.1:8082
```

**生产环境CORS配置：**
```yaml
# 修改前
app:
  domain: http://**************:8080
  cors:
    allowed-origins: http://**************,http://**************:8080,http://**************:8200

# 修改后
app:
  domain: http://**************:8200
  cors:
    allowed-origins: http://**************,http://**************:8200,http://**************:8081
```

**新增AI服务配置：**
```yaml
# 新增配置
ai:
  service:
    url: http://localhost:5000
    timeout: 30000
    retry-count: 3
```

#### 2.3 新闻爬取服务修改

**文件：`backend/python/app.py`**
```python
# 修改前
app.run(host='0.0.0.0', port=5000)

# 修改后
app.run(host='0.0.0.0', port=5001)  # 修改端口为5001避免与AI服务冲突
```

#### 2.4 价格爬取服务配置修改

**文件：`backend/datacrawl/config/settings.yaml`**
```yaml
# 修改前
api:
  enabled: false
  host: "0.0.0.0"
  port: 8000
  debug: false

# 修改后
api:
  enabled: false
  host: "0.0.0.0"
  port: 8002
  debug: false
```

**文件：`backend/datacrawl/.env.example`**
```bash
# 修改前
API_PORT=8000

# 修改后
API_PORT=8002
```

**注意**：价格爬取服务目前是纯命令行爬虫工具，API功能未启用，但为避免将来端口冲突，预先修改配置。

#### 2.5 AI服务配置修改

**文件：`src/config/ai-service.js`**
```javascript
// 修改前
production: {
  AI_SERVICE_URL: 'https://ai.sfap.com',
  DEBUG: false,
  LOG_LEVEL: 'error'
},

// 修改后
production: {
  AI_SERVICE_URL: 'http://**************:5000',
  DEBUG: false,
  LOG_LEVEL: 'error'
},
```

## 🌐 网络架构图

```
Internet
    ↓
Nginx (80/443)
    ↓
┌─────────────────────────────────────────┐
│              阿里云服务器                │
│         **************                  │
├─────────────────────────────────────────┤
│ 前端服务 (8200)                         │
│ ├── Vue.js 应用                         │
│ └── 静态资源                            │
├─────────────────────────────────────────┤
│ 后端服务 (8081)                         │
│ ├── Spring Boot API                     │
│ ├── 文件上传处理                        │
│ └── 业务逻辑                            │
├─────────────────────────────────────────┤
│ AI预测服务 (5000)                       │
│ ├── RNN模型预测                         │
│ ├── ARIMA模型预测                       │
│ └── 数据处理                            │
├─────────────────────────────────────────┤
│ 新闻爬取服务 (5001)                     │
│ ├── 农业新闻爬取                        │
│ ├── 新闻数据处理                        │
│ └── 定时任务                            │
├─────────────────────────────────────────┤
│ 价格爬取服务 (命令行)                   │
│ ├── 惠农网价格数据爬取                  │
│ ├── 价格数据处理                        │
│ └── 定时任务                            │
│ (预留API端口: 8002)                     │
├─────────────────────────────────────────┤
│ 数据存储                                │
│ ├── MySQL (3306)                       │
│ └── Redis (6379)                       │
└─────────────────────────────────────────┘
```

## 📁 文件上传路径配置

### 开发环境：
```
E:/计算机设计大赛2/V4.0/新建文件夹/SFAP/uploads/
├── avatars/          # 用户头像
├── images/           # 商品图片
├── qrcodes/          # 二维码文件
├── seller/           # 销售者相关文件
└── exports/          # 导出文件
```

### 生产环境：
```
/www/wwwroot/agriculture/uploads/
├── avatars/          # 用户头像
├── images/           # 商品图片
├── qrcodes/          # 二维码文件
├── seller/           # 销售者相关文件
└── exports/          # 导出文件
```

## 🔒 安全配置要点

### 1. 防火墙端口开放
```bash
# 必需开放的端口
80/tcp    # HTTP
443/tcp   # HTTPS
8200/tcp  # 前端服务
8081/tcp  # 后端API
5000/tcp  # AI预测服务
5001/tcp  # 新闻爬取服务
8002/tcp  # 价格爬取服务（预留）
3306/tcp  # MySQL (仅内网)
6379/tcp  # Redis (仅内网)
```

### 2. 数据库安全
- 创建专用数据库用户，避免使用root
- 设置强密码
- 限制远程访问
- 定期备份

### 3. 文件权限
```bash
# 应用目录权限
chown -R www:www /www/wwwroot/sfap-*
chmod -R 755 /www/wwwroot/sfap-*

# 上传目录权限
chown -R www:www /www/wwwroot/agriculture/uploads
chmod -R 755 /www/wwwroot/agriculture/uploads
```

## 🚀 启动顺序

1. **基础服务**：MySQL、Redis
2. **后端服务**：Spring Boot (8081)
3. **AI预测服务**：Flask AI服务 (5000)
4. **新闻爬取服务**：Flask新闻爬取服务 (5001)
5. **价格爬取服务**：定时任务（crontab）
6. **前端服务**：Nginx代理 (8200)
7. **Web服务器**：Nginx主服务 (80)

## 🔍 验证检查

### 1. 端口检查
```bash
netstat -tlnp | grep -E ':(80|8081|8200|5000|5001|8002|3306|6379)'
```

### 2. 服务状态检查
```bash
# 检查Java进程
ps aux | grep java

# 检查Python进程
ps aux | grep python

# 检查Nginx状态
systemctl status nginx
```

### 3. 功能测试
- 前端访问：http://**************
- API测试：http://**************:8081/api/health
- AI预测服务：http://**************:5000/api/v1/health
- 新闻爬取服务：http://**************:5001/api/crawler/status
- 价格爬取服务：检查日志文件和定时任务执行情况

## ⚠️ 注意事项

### 1. 环境变量
确保生产环境设置正确的环境变量：
```bash
export NODE_ENV=production
export SPRING_PROFILES_ACTIVE=prod
```

### 2. 数据库密码
生产环境必须修改默认数据库密码：
```yaml
spring:
  datasource:
    password: your_secure_password  # 替换为安全密码
```

### 3. 日志管理
- 配置日志轮转，避免日志文件过大
- 设置合适的日志级别
- 定期清理旧日志文件

### 4. 监控告警
- 设置服务监控
- 配置磁盘空间告警
- 监控内存和CPU使用率

## 📞 故障排查

### 常见问题：

1. **端口冲突**
   - 检查端口占用：`lsof -i :端口号`
   - 杀死占用进程：`kill -9 PID`

2. **权限问题**
   - 检查文件权限
   - 确认用户组设置

3. **跨域问题**
   - 验证CORS配置
   - 检查Nginx代理设置

4. **数据库连接失败**
   - 检查数据库服务状态
   - 验证连接参数
   - 检查防火墙设置

## 📝 部署检查清单

- [ ] 所有配置文件已修改
- [ ] 端口冲突已解决
- [ ] 数据库连接配置正确
- [ ] 文件上传路径配置正确
- [ ] CORS配置已更新
- [ ] 防火墙端口已开放
- [ ] SSL证书已配置（如需要）
- [ ] 监控告警已设置
- [ ] 备份策略已制定
- [ ] 文档已更新

---

**配置修改完成时间**: 2025-01-25  
**修改人员**: AI助手  
**版本**: v1.0  
**状态**: ✅ 已完成
