/**
 * API测试脚本
 * 用于验证前后端API通信是否正常
 */

const axios = require('axios');

// 配置不同环境的API地址
const environments = {
  development: {
    name: '开发环境',
    baseURL: 'http://localhost:8081',
    frontendURL: 'http://localhost:8080'
  },
  production: {
    name: '生产环境',
    baseURL: 'http://**************:8081',
    frontendURL: 'http://**************'
  }
};

// 测试API列表
const testAPIs = [
  {
    name: '管理员仪表板统计',
    method: 'GET',
    url: '/api/admin/dashboard/stats',
    requiresAuth: true
  },
  {
    name: '销售概览数据',
    method: 'GET',
    url: '/api/seller-center/analytics/overview',
    requiresAuth: true
  },
  {
    name: '商品列表',
    method: 'GET',
    url: '/api/seller-center/products?page=0&size=10',
    requiresAuth: true
  },
  {
    name: '订单统计',
    method: 'GET',
    url: '/api/seller-center/orders/stats',
    requiresAuth: true
  },
  {
    name: '系统趋势',
    method: 'GET',
    url: '/api/admin/dashboard/trends?days=30',
    requiresAuth: true
  }
];

// 创建axios实例
function createAxiosInstance(env) {
  return axios.create({
    baseURL: env.baseURL,
    timeout: 10000,
    headers: {
      'Content-Type': 'application/json',
      'Origin': env.frontendURL,
      'Referer': env.frontendURL
    }
  });
}

// 测试单个API
async function testAPI(api, axiosInstance, env) {
  try {
    console.log(`\n测试 ${api.name}...`);
    console.log(`请求: ${api.method} ${env.baseURL}${api.url}`);
    
    const config = {
      method: api.method.toLowerCase(),
      url: api.url
    };
    
    // 如果需要认证，添加测试用的头部
    if (api.requiresAuth) {
      config.headers = {
        'X-User-Id': '1', // 测试用户ID
        'Authorization': 'Bearer test-token' // 测试token
      };
    }
    
    const response = await axiosInstance(config);
    
    console.log(`✅ 成功: ${response.status} ${response.statusText}`);
    console.log(`响应数据:`, JSON.stringify(response.data, null, 2));
    
    return { success: true, status: response.status, data: response.data };
  } catch (error) {
    if (error.response) {
      console.log(`❌ 失败: ${error.response.status} ${error.response.statusText}`);
      console.log(`错误响应:`, JSON.stringify(error.response.data, null, 2));
      return { success: false, status: error.response.status, error: error.response.data };
    } else if (error.request) {
      console.log(`❌ 网络错误: 无法连接到服务器`);
      console.log(`错误详情:`, error.message);
      return { success: false, error: 'Network Error', message: error.message };
    } else {
      console.log(`❌ 请求配置错误:`, error.message);
      return { success: false, error: 'Request Error', message: error.message };
    }
  }
}

// 测试跨域配置
async function testCORS(env) {
  try {
    console.log(`\n测试跨域配置...`);
    console.log(`发送OPTIONS预检请求到: ${env.baseURL}/api/admin/dashboard/stats`);
    
    const axiosInstance = createAxiosInstance(env);
    const response = await axiosInstance({
      method: 'options',
      url: '/api/admin/dashboard/stats',
      headers: {
        'Access-Control-Request-Method': 'GET',
        'Access-Control-Request-Headers': 'authorization,x-user-id'
      }
    });
    
    console.log(`✅ CORS预检成功: ${response.status}`);
    console.log(`CORS头部:`, {
      'Access-Control-Allow-Origin': response.headers['access-control-allow-origin'],
      'Access-Control-Allow-Methods': response.headers['access-control-allow-methods'],
      'Access-Control-Allow-Headers': response.headers['access-control-allow-headers'],
      'Access-Control-Allow-Credentials': response.headers['access-control-allow-credentials']
    });
    
    return { success: true, headers: response.headers };
  } catch (error) {
    console.log(`❌ CORS预检失败:`, error.message);
    return { success: false, error: error.message };
  }
}

// 运行所有测试
async function runTests() {
  const envName = process.argv[2] || 'development';
  const env = environments[envName];
  
  if (!env) {
    console.log(`❌ 未知环境: ${envName}`);
    console.log(`可用环境: ${Object.keys(environments).join(', ')}`);
    process.exit(1);
  }
  
  console.log(`\n🚀 开始测试 ${env.name}`);
  console.log(`后端地址: ${env.baseURL}`);
  console.log(`前端地址: ${env.frontendURL}`);
  console.log(`=`.repeat(50));
  
  const axiosInstance = createAxiosInstance(env);
  const results = [];
  
  // 测试CORS
  const corsResult = await testCORS(env);
  results.push({ name: 'CORS预检', ...corsResult });
  
  // 测试所有API
  for (const api of testAPIs) {
    const result = await testAPI(api, axiosInstance, env);
    results.push({ name: api.name, ...result });
  }
  
  // 输出测试总结
  console.log(`\n📊 测试总结`);
  console.log(`=`.repeat(50));
  
  const successful = results.filter(r => r.success).length;
  const total = results.length;
  
  console.log(`总测试数: ${total}`);
  console.log(`成功: ${successful}`);
  console.log(`失败: ${total - successful}`);
  console.log(`成功率: ${(successful / total * 100).toFixed(1)}%`);
  
  console.log(`\n详细结果:`);
  results.forEach(result => {
    const status = result.success ? '✅' : '❌';
    console.log(`${status} ${result.name}`);
    if (!result.success && result.error) {
      console.log(`   错误: ${result.error}`);
    }
  });
  
  // 如果有失败的测试，退出码为1
  if (successful < total) {
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  runTests().catch(error => {
    console.error('测试运行失败:', error);
    process.exit(1);
  });
}

module.exports = { runTests, testAPI, testCORS };
