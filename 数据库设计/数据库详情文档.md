# 农业商城系统数据库详情文档

## 概述

本文档详细描述了农业商城系统的数据库结构，包含所有表的字段定义、索引、约束和业务说明。数据库采用 MySQL 8.0，字符集为 utf8mb4。

## 数据库信息

- **数据库名称**: `agriculture_mall`
- **字符集**: `utf8mb4`
- **排序规则**: `utf8mb4_unicode_ci`
- **存储引擎**: InnoDB

## 表结构详情

### 1. 用户相关表

#### 1.1 用户表 (user)

用户基础信息表，存储系统中所有用户的基本信息。

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|---------|
| id | bigint | PRIMARY KEY, AUTO_INCREMENT | - | 用户ID |
| username | varchar(50) | NOT NULL, UNIQUE | - | 用户名 |
| password | varchar(255) | NOT NULL | - | 密码(加密存储) |
| email | varchar(100) | UNIQUE | - | 邮箱 |
| phone | varchar(20) | - | - | 手机号 |
| nickname | varchar(50) | - | - | 昵称 |
| avatar | varchar(255) | - | - | 头像URL |
| gender | tinyint | - | 0 | 性别(0:未知 1:男 2:女) |
| birthday | date | - | - | 生日 |
| status | tinyint | NOT NULL | 1 | 状态(0:禁用 1:正常) |
| is_seller | tinyint(1) | NOT NULL | 0 | 是否为销售者(0:否 1:是) |
| created_at | datetime | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | datetime | NOT NULL | CURRENT_TIMESTAMP ON UPDATE | 更新时间 |
| deleted | tinyint | NOT NULL | 0 | 逻辑删除(0:未删除 1:已删除) |

**索引**:
- PRIMARY KEY: `id`
- UNIQUE KEY: `username`, `email`
- KEY: `idx_phone`, `idx_status`

#### 1.2 收货地址表 (address)

用户收货地址信息表。

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|---------|
| id | bigint | PRIMARY KEY, AUTO_INCREMENT | - | 地址ID |
| user_id | bigint | NOT NULL | - | 用户ID |
| name | varchar(50) | NOT NULL | - | 收货人姓名 |
| phone | varchar(20) | NOT NULL | - | 联系电话 |
| province | varchar(50) | NOT NULL | - | 省份 |
| city | varchar(50) | NOT NULL | - | 城市 |
| district | varchar(50) | NOT NULL | - | 区县 |
| detail | varchar(255) | NOT NULL | - | 详细地址 |
| is_default | tinyint(1) | NOT NULL | 0 | 是否默认地址(0:否 1:是) |
| created_at | datetime | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | datetime | NOT NULL | CURRENT_TIMESTAMP ON UPDATE | 更新时间 |
| deleted | tinyint(1) | NOT NULL | 0 | 是否删除(0:未删除 1:已删除) |

**索引**:
- PRIMARY KEY: `id`
- KEY: `idx_user_id`

### 2. 商品相关表

#### 2.1 商品分类表 (category)

商品分类层级结构表，支持多级分类。

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|---------|
| id | bigint | PRIMARY KEY, AUTO_INCREMENT | - | 分类ID |
| name | varchar(100) | NOT NULL | - | 分类名称 |
| parent_id | bigint | - | 0 | 父分类ID(0表示顶级分类) |
| level | tinyint | NOT NULL | 1 | 分类层级 |
| icon | varchar(255) | - | - | 分类图标 |
| image | varchar(255) | - | - | 分类图片 |
| description | text | - | - | 分类描述 |
| sort_order | int | - | 0 | 排序 |
| is_hot | tinyint | - | 0 | 是否热门(0:否 1:是) |
| status | tinyint | NOT NULL | 1 | 状态(0:禁用 1:启用) |
| product_count | int | - | 0 | 该分类下的商品数量 |
| created_at | datetime | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | datetime | NOT NULL | CURRENT_TIMESTAMP ON UPDATE | 更新时间 |
| deleted | tinyint | - | 0 | 逻辑删除(0:未删除 1:已删除) |

**索引**:
- PRIMARY KEY: `id`
- KEY: `idx_parent_id`, `idx_level`, `idx_sort_order`

#### 2.2 商品表 (product)

商品基础信息表。

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|---------|
| id | bigint | PRIMARY KEY, AUTO_INCREMENT | - | 商品ID |
| name | varchar(255) | NOT NULL | - | 商品名称 |
| description | text | - | - | 商品描述 |
| image | varchar(500) | - | - | 商品主图 |
| price | decimal(10,2) | NOT NULL | - | 商品价格 |
| original_price | decimal(10,2) | - | - | 原价 |
| stock | int | NOT NULL | 0 | 库存数量 |
| sales_count | int | - | 0 | 销售数量 |
| rating | decimal(3,2) | - | 0.00 | 平均评分 |
| review_count | int | - | 0 | 评价数量 |
| category_id | bigint | NOT NULL | - | 分类ID |
| seller_id | bigint | NOT NULL | 1 | 卖家ID |
| brand | varchar(100) | - | - | 品牌 |
| origin | varchar(100) | - | - | 产地 |
| unit | varchar(20) | - | - | 单位(斤/公斤/箱等) |
| shelf_life | varchar(50) | - | - | 保质期 |
| storage_method | varchar(100) | - | - | 储存方式 |
| tags | varchar(500) | - | - | 标签(JSON格式) |
| specifications | text | - | - | 规格参数(JSON格式) |
| status | tinyint | NOT NULL | 1 | 状态(0:下架 1:上架) |
| is_featured | tinyint | - | 0 | 是否精选(0:否 1:是) |
| is_hot | tinyint | - | 0 | 是否热门(0:否 1:是) |
| created_at | datetime | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | datetime | NOT NULL | CURRENT_TIMESTAMP ON UPDATE | 更新时间 |
| deleted | tinyint | - | 0 | 逻辑删除(0:未删除 1:已删除) |

**索引**:
- PRIMARY KEY: `id`
- KEY: `idx_category_id`, `idx_seller_id`, `idx_status`, `idx_price`

### 3. 订单相关表

#### 3.1 购物车表 (cart_item)

用户购物车商品表。

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|---------|
| id | bigint | PRIMARY KEY, AUTO_INCREMENT | - | 购物车项ID |
| user_id | bigint | NOT NULL | - | 用户ID |
| product_id | bigint | NOT NULL | - | 商品ID |
| quantity | int | NOT NULL | 1 | 数量 |
| price | decimal(10,2) | - | - | 商品价格(下单时的价格) |
| selected | tinyint | - | 1 | 是否选中(0:否 1:是) |
| created_at | datetime | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | datetime | NOT NULL | CURRENT_TIMESTAMP ON UPDATE | 更新时间 |
| deleted | tinyint | - | 0 | 逻辑删除(0:未删除 1:已删除) |

**索引**:
- PRIMARY KEY: `id`
- UNIQUE KEY: `uk_user_product` (user_id, product_id)
- KEY: `idx_user_id`, `idx_product_id`, `idx_deleted`

#### 3.2 订单主表 (order)

订单基础信息表。

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|---------|
| id | bigint | PRIMARY KEY, AUTO_INCREMENT | - | 订单ID |
| order_no | varchar(32) | NOT NULL, UNIQUE | - | 订单号 |
| user_id | bigint | NOT NULL | - | 用户ID |
| seller_id | bigint | NOT NULL | - | 卖家ID |
| total_amount | decimal(10,2) | NOT NULL | - | 订单总金额 |
| discount_amount | decimal(10,2) | - | 0.00 | 优惠金额 |
| shipping_fee | decimal(10,2) | - | 0.00 | 运费 |
| actual_amount | decimal(10,2) | NOT NULL | - | 实付金额 |
| payment_method | varchar(20) | - | - | 支付方式 |
| payment_status | tinyint | NOT NULL | 0 | 支付状态(0:未支付 1:已支付 2:已退款) |
| order_status | tinyint | NOT NULL | 0 | 订单状态(0:待支付 1:待发货 2:待收货 3:已完成 4:已取消) |
| shipping_address | text | - | - | 收货地址(JSON格式) |
| remark | varchar(500) | - | - | 订单备注 |
| payment_time | datetime | - | - | 支付时间 |
| shipping_time | datetime | - | - | 发货时间 |
| receive_time | datetime | - | - | 收货时间 |
| cancel_time | datetime | - | - | 取消时间 |
| cancel_reason | varchar(255) | - | - | 取消原因 |
| created_at | datetime | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | datetime | NOT NULL | CURRENT_TIMESTAMP ON UPDATE | 更新时间 |

**索引**:
- PRIMARY KEY: `id`
- UNIQUE KEY: `uk_order_no`
- KEY: `idx_user_id`, `idx_seller_id`, `idx_order_status`, `idx_created_at`

#### 3.3 订单商品表 (order_item)

订单中的商品详情表。

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|---------|
| id | bigint | PRIMARY KEY, AUTO_INCREMENT | - | 订单商品ID |
| order_id | bigint | NOT NULL | - | 订单ID |
| product_id | bigint | NOT NULL | - | 商品ID |
| product_name | varchar(255) | NOT NULL | - | 商品名称 |
| product_image | varchar(500) | - | - | 商品图片 |
| product_price | decimal(10,2) | NOT NULL | - | 商品单价 |
| quantity | int | NOT NULL | - | 购买数量 |
| total_price | decimal(10,2) | NOT NULL | - | 小计金额 |
| specifications | varchar(500) | - | - | 商品规格 |
| created_at | datetime | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |

**索引**:
- PRIMARY KEY: `id`
- KEY: `idx_order_id`, `idx_product_id`

### 4. 销售者相关表

#### 4.1 销售者申请表 (seller_application)

销售者资质申请表。

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|---------|
| id | bigint | PRIMARY KEY, AUTO_INCREMENT | - | 申请ID |
| user_id | bigint | NOT NULL | - | 用户ID |
| applicant_type | varchar(20) | NOT NULL | - | 申请人类型 |
| name | varchar(100) | NOT NULL | - | 申请人姓名 |
| id_number | varchar(20) | NOT NULL | - | 身份证号 |
| phone | varchar(20) | NOT NULL | - | 联系电话 |
| email | varchar(100) | - | - | 邮箱 |
| address | varchar(255) | NOT NULL | - | 地址 |
| farm_name | varchar(100) | - | - | 农场名称 |
| contact_info | text | - | - | 联系信息(JSON格式) |
| description | text | NOT NULL | - | 申请描述 |
| qualification_doc_urls | text | - | - | 资质文档URLs(JSON格式) |
| certificate_urls | text | - | - | 证书URLs |
| status | varchar(20) | NOT NULL | 'PENDING' | 申请状态 |
| review_comment | text | - | - | 审核意见 |
| reviewed_by | bigint | - | - | 审核人ID |
| reviewed_at | datetime | - | - | 审核时间 |
| created_at | datetime | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | datetime | NOT NULL | CURRENT_TIMESTAMP ON UPDATE | 更新时间 |

**索引**:
- PRIMARY KEY: `id`
- KEY: `idx_user_id`, `idx_status`

### 5. 内容相关表

#### 5.1 农业新闻表 (agriculture_news)

农业相关新闻资讯表。

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|---------|
| id | int | PRIMARY KEY, AUTO_INCREMENT | - | 新闻ID |
| title | varchar(255) | NOT NULL | - | 新闻标题 |
| content | text | - | - | 新闻正文内容 |
| summary | varchar(500) | - | - | 新闻摘要 |
| source | varchar(100) | - | - | 新闻来源 |
| publish_time | datetime | - | - | 发布时间 |
| url | varchar(255) | NOT NULL, UNIQUE | - | 原文链接 |
| category | varchar(50) | - | - | 新闻分类 |
| views | int | - | 0 | 阅读量 |
| crawl_time | datetime | - | CURRENT_TIMESTAMP | 爬取时间 |
| update_time | datetime | - | CURRENT_TIMESTAMP ON UPDATE | 更新时间 |

**索引**:
- PRIMARY KEY: `id`
- UNIQUE KEY: `idx_url`
- KEY: `idx_publish_time`, `idx_category`

#### 5.2 农业百科表 (encyclopedia)

农业知识百科表。

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|---------|
| id | bigint | PRIMARY KEY, AUTO_INCREMENT | - | 百科ID |
| title | varchar(100) | NOT NULL | - | 标题 |
| summary | varchar(255) | - | - | 摘要 |
| content | text | NOT NULL | - | 内容 |
| cover_image | varchar(255) | - | - | 封面图片URL |
| category_id | bigint | NOT NULL | - | 分类ID |
| category_name | varchar(50) | - | - | 分类名称 |
| author_id | bigint | NOT NULL | - | 作者ID |
| author_name | varchar(50) | - | - | 作者名称 |
| views_count | int | - | 0 | 浏览量 |
| likes_count | int | - | 0 | 点赞数 |
| favorites_count | int | - | 0 | 收藏数 |
| is_featured | tinyint(1) | - | 0 | 是否精选(0:否 1:是) |
| is_hot | tinyint(1) | - | 0 | 是否热门(0:否 1:是) |
| keywords | varchar(255) | - | - | 关键词，多个关键词用逗号分隔 |
| publish_date | datetime | - | CURRENT_TIMESTAMP | 发布时间 |
| created_at | datetime | - | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | datetime | - | CURRENT_TIMESTAMP ON UPDATE | 更新时间 |
| status | tinyint | - | 1 | 状态(0:草稿 1:已发布 2:已下架) |
| deleted | tinyint(1) | - | 0 | 是否删除(0:未删除 1:已删除) |

**索引**:
- PRIMARY KEY: `id`
- KEY: `idx_category_id`, `idx_author_id`, `idx_publish_date`, `idx_status`

### 6. 溯源相关表

#### 6.1 溯源事件表 (traceability_event)

产品溯源事件记录表。

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|---------|
| id | bigint | PRIMARY KEY, AUTO_INCREMENT | - | 事件ID |
| product_id | bigint | NOT NULL | - | 产品ID |
| event_type | varchar(50) | NOT NULL | - | 事件类型 |
| event_name | varchar(100) | NOT NULL | - | 事件名称 |
| event_description | text | - | - | 事件描述 |
| event_time | datetime | NOT NULL | - | 事件时间 |
| location | varchar(255) | - | - | 事件地点 |
| operator | varchar(100) | - | - | 操作人员 |
| data | text | - | - | 事件数据(JSON格式) |
| created_at | datetime | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | datetime | NOT NULL | CURRENT_TIMESTAMP ON UPDATE | 更新时间 |

**索引**:
- PRIMARY KEY: `id`
- KEY: `idx_product_id`, `idx_event_type`, `idx_event_time`

### 7. 其他功能表

#### 7.1 收藏表 (favorite)

用户商品收藏表。

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|---------|
| id | bigint | PRIMARY KEY, AUTO_INCREMENT | - | 收藏ID |
| user_id | bigint | NOT NULL | - | 用户ID |
| product_id | bigint | NOT NULL | - | 商品ID |
| created_at | datetime | NOT NULL | - | 创建时间 |
| updated_at | datetime | NOT NULL | - | 更新时间 |
| deleted | int | NOT NULL | 0 | 逻辑删除(0:未删除 1:已删除) |

**索引**:
- PRIMARY KEY: `id`

#### 7.2 消息通知表 (notification)

系统消息通知表。

| 字段名 | 类型 | 约束 | 默认值 | 说明 |
|--------|------|------|--------|---------|
| id | bigint | PRIMARY KEY, AUTO_INCREMENT | - | 通知ID |
| user_id | bigint | NOT NULL | - | 用户ID |
| title | varchar(100) | NOT NULL | - | 通知标题 |
| content | text | NOT NULL | - | 通知内容 |
| type | tinyint | NOT NULL | - | 通知类型(1:系统通知 2:订单通知 3:商品通知) |
| is_read | tinyint | NOT NULL | 0 | 是否已读(0:未读 1:已读) |
| created_at | datetime | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | datetime | NOT NULL | CURRENT_TIMESTAMP ON UPDATE | 更新时间 |
| deleted | tinyint | NOT NULL | 0 | 是否删除(0:未删除 1:已删除) |

**索引**:
- PRIMARY KEY: `id`
- KEY: `idx_user_id`

## 数据库设计原则

### 1. 命名规范
- 表名使用小写字母和下划线
- 字段名使用小写字母和下划线
- 主键统一命名为 `id`
- 外键字段以 `_id` 结尾
- 布尔类型字段以 `is_` 开头

### 2. 数据类型选择
- 主键使用 `bigint` 类型
- 金额字段使用 `decimal(10,2)` 类型
- 时间字段使用 `datetime` 类型
- 状态字段使用 `tinyint` 类型
- 文本内容使用 `text` 类型
- JSON数据使用 `text` 类型存储

### 3. 索引设计
- 每个表都有主键索引
- 外键字段建立普通索引
- 经常查询的字段建立索引
- 唯一性约束使用唯一索引

### 4. 逻辑删除
- 重要数据表使用逻辑删除
- 删除标记字段统一命名为 `deleted`
- 删除标记使用 `tinyint` 类型，0表示未删除，1表示已删除

### 5. 时间戳
- 每个表都包含 `created_at` 和 `updated_at` 字段
- 创建时间默认为 `CURRENT_TIMESTAMP`
- 更新时间默认为 `CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`

## 业务规则说明

### 1. 用户体系
- 用户注册后默认为普通用户
- 通过销售者申请审核后可成为销售者
- 销售者可以发布商品和管理订单

### 2. 商品管理
- 商品支持多级分类
- 商品价格支持原价和现价
- 商品状态控制上架下架

### 3. 订单流程
- 订单状态：待支付 → 待发货 → 待收货 → 已完成
- 支持订单取消和退款
- 订单金额包含商品金额、优惠金额和运费

### 4. 溯源体系
- 每个商品可以有多个溯源事件
- 溯源事件按时间顺序记录
- 支持查询完整的溯源链路

## 维护说明

### 1. 数据备份
- 建议每日进行数据库备份
- 重要操作前进行数据备份

### 2. 性能优化
- 定期分析慢查询日志
- 根据业务需求调整索引
- 考虑数据归档策略

### 3. 版本管理
- 数据库结构变更需要版本控制
- 提供数据迁移脚本
- 记录变更日志

---

**文档版本**: 1.0  
**创建时间**: 2024年  
**维护人员**: 开发团队  
**更新说明**: 初始版本，包含所有核心业务表结构