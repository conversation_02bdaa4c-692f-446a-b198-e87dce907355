# 溯源模块数据库设计文档

## 1. 实体关系分析

### 1.1 核心实体
1. **溯源记录(Traceability Record)**: 农产品溯源的主记录，包含基本信息
2. **溯源事件(Traceability Event)**: 农产品生产过程中的各个环节记录
3. **检验认证(Certificate)**: 农产品的质量检验和认证信息
4. **物流记录(Logistics)**: 农产品的运输和配送信息
5. **溯源码(Trace Code)**: 农产品的唯一溯源标识码

### 1.2 实体关系
- 一个**溯源记录**对应一个**产品**（一对一）
- 一个**溯源记录**包含多个**溯源事件**（一对多）
- 一个**溯源记录**拥有多个**检验认证**（一对多）
- 一个**溯源记录**关联多个**物流记录**（一对多）
- 一个**溯源记录**对应一个**溯源码**（一对一）
- 一个**用户(生产者)**可以创建多个**溯源记录**（一对多）

## 2. ER图设计

```mermaid
erDiagram
    traceability_records ||--o{ traceability_events : contains
    traceability_records ||--o{ trace_certificates : has
    traceability_records ||--o{ trace_logistics : associated_with
    traceability_records ||--|| trace_codes : identified_by
    users ||--o{ traceability_records : creates
    products ||--|| traceability_records : has_traceability
    
    traceability_records {
        bigint id PK
        bigint product_id FK
        varchar trace_code UK
        varchar product_name
        varchar farm_name
        bigint producer_id FK
        date creation_date
        date harvest_date
        date packaging_date
        tinyint status
        datetime created_at
        datetime updated_at
    }
    
    traceability_events {
        bigint id PK
        bigint trace_record_id FK
        varchar event_type
        datetime event_date
        text description
        varchar location
        varchar responsible_person
        json attachments
        datetime created_at
        datetime updated_at
    }
    
    trace_certificates {
        bigint id PK
        bigint trace_record_id FK
        varchar certificate_type
        varchar certificate_no
        varchar issuing_authority
        date issue_date
        date valid_until
        varchar certificate_url
        text description
        datetime created_at
        datetime updated_at
    }
    
    trace_logistics {
        bigint id PK
        bigint trace_record_id FK
        varchar carrier_name
        varchar transport_type
        datetime departure_time
        datetime arrival_time
        varchar origin
        varchar destination
        decimal temperature
        decimal humidity
        tinyint status
        datetime created_at
        datetime updated_at
    }
    
    trace_codes {
        varchar code PK
        bigint trace_record_id FK
        varchar qr_code_url
        datetime generated_time
        tinyint status
        int scan_count
        datetime last_scan_time
    }
    
    users {
        bigint id PK
        varchar username UK
        varchar password
        varchar email UK
        varchar phone UK
        varchar roles
        tinyint seller_status
        varchar farm_name
        datetime created_at
        datetime updated_at
    }
    
    products {
        bigint id PK
        varchar name
        text description
        bigint category_id FK
        decimal price
        int stock
        varchar origin
        bigint seller_id FK
        tinyint status
        datetime created_at
        datetime updated_at
    }
    
    seller_applications {
        bigint id PK
        bigint user_id FK
        varchar farm_name
        varchar contact_info
        json qualification_doc_urls
        datetime apply_date
        tinyint status
        text audit_comment
        bigint auditor_id FK
        datetime audit_date
    }
```

## 3. 表结构设计

### 3.1 traceability_records（溯源主记录表）

| 字段名 | 数据类型 | 约束 | 描述 |
| --- | --- | --- | --- |
| id | BIGINT | PK, AUTO_INCREMENT | 主键ID |
| product_id | BIGINT | FK, NOT NULL | 关联的产品ID |
| trace_code | VARCHAR(128) | UNIQUE, NOT NULL | 溯源码，唯一标识 |
| product_name | VARCHAR(255) | NOT NULL | 产品名称（冗余字段，方便查询） |
| farm_name | VARCHAR(255) | NOT NULL | 农场/生产基地名称 |
| producer_id | BIGINT | FK, NOT NULL | 生产者ID，关联用户表 |
| creation_date | DATE | | 开始生产日期 |
| harvest_date | DATE | | 采收/收获日期 |
| packaging_date | DATE | | 包装日期 |
| status | TINYINT | DEFAULT 0 | 状态：0-草稿，1-待审核，2-已发布，3-已下架，4-异常 |
| created_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

索引：
- PRIMARY KEY (id)
- UNIQUE KEY idx_trace_code (trace_code)
- KEY idx_product_id (product_id)
- KEY idx_producer_id (producer_id)

### 3.2 traceability_events（溯源事件表）

| 字段名 | 数据类型 | 约束 | 描述 |
| --- | --- | --- | --- |
| id | BIGINT | PK, AUTO_INCREMENT | 主键ID |
| trace_record_id | BIGINT | FK, NOT NULL | 关联的溯源记录ID |
| event_type | VARCHAR(50) | NOT NULL | 事件类型：PLANTING(种植)，FERTILIZING(施肥)，PEST_CONTROL(病虫害防治)，HARVESTING(采摘)，PACKAGING(包装)，STORAGE(仓储)，LOGISTICS(物流) |
| event_date | DATETIME | NOT NULL | 事件发生时间 |
| description | TEXT | | 事件描述 |
| location | VARCHAR(255) | | 事件发生地点 |
| responsible_person | VARCHAR(100) | | 责任人 |
| attachments | JSON | | 附件（图片/视频URL）：{"images": ["url1", "url2"], "videos": ["url1"]} |
| created_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

索引：
- PRIMARY KEY (id)
- KEY idx_trace_record_id (trace_record_id)
- KEY idx_event_date (event_date)

### 3.3 trace_certificates（认证信息表）

| 字段名 | 数据类型 | 约束 | 描述 |
| --- | --- | --- | --- |
| id | BIGINT | PK, AUTO_INCREMENT | 主键ID |
| trace_record_id | BIGINT | FK, NOT NULL | 关联的溯源记录ID |
| certificate_type | VARCHAR(100) | NOT NULL | 证书类型：ORGANIC(有机认证)，GREEN(绿色食品)，QUALITY(质量检测)，ORIGIN(原产地认证) |
| certificate_no | VARCHAR(100) | | 证书编号 |
| issuing_authority | VARCHAR(255) | | 颁发机构 |
| issue_date | DATE | | 颁发日期 |
| valid_until | DATE | | 有效期至 |
| certificate_url | VARCHAR(255) | | 证书图片URL |
| description | TEXT | | 证书描述 |
| created_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

索引：
- PRIMARY KEY (id)
- KEY idx_trace_record_id (trace_record_id)
- KEY idx_certificate_no (certificate_no)

### 3.4 trace_logistics（物流信息表）

| 字段名 | 数据类型 | 约束 | 描述 |
| --- | --- | --- | --- |
| id | BIGINT | PK, AUTO_INCREMENT | 主键ID |
| trace_record_id | BIGINT | FK, NOT NULL | 关联的溯源记录ID |
| carrier_name | VARCHAR(255) | | 承运商名称 |
| transport_type | VARCHAR(50) | | 运输方式：ROAD(公路)，RAILWAY(铁路)，AIR(空运)，SHIP(水运) |
| departure_time | DATETIME | | 出发时间 |
| arrival_time | DATETIME | | 到达时间 |
| origin | VARCHAR(255) | | 始发地 |
| destination | VARCHAR(255) | | 目的地 |
| temperature | DECIMAL(5,2) | | 运输温度（如冷链） |
| humidity | DECIMAL(5,2) | | 运输湿度 |
| status | TINYINT | DEFAULT 0 | 物流状态：0-待发货，1-运输中，2-已到达，3-已签收 |
| created_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

索引：
- PRIMARY KEY (id)
- KEY idx_trace_record_id (trace_record_id)
- KEY idx_status (status)

### 3.5 trace_codes（溯源码表）

| 字段名 | 数据类型 | 约束 | 描述 |
| --- | --- | --- | --- |
| code | VARCHAR(128) | PK | 溯源码（主键） |
| trace_record_id | BIGINT | FK, UNIQUE, NOT NULL | 关联的溯源记录ID |
| qr_code_url | VARCHAR(255) | | 二维码图片URL |
| generated_time | DATETIME | DEFAULT CURRENT_TIMESTAMP | 生成时间 |
| status | TINYINT | DEFAULT 1 | 状态：0-无效，1-有效 |
| scan_count | INT | DEFAULT 0 | 扫描次数统计 |
| last_scan_time | DATETIME | | 最后扫描时间 |

索引：
- PRIMARY KEY (code)
- UNIQUE KEY idx_trace_record_id (trace_record_id)

### 3.6 seller_applications（销售者申请表）

| 字段名 | 数据类型 | 约束 | 描述 |
| --- | --- | --- | --- |
| id | BIGINT | PK, AUTO_INCREMENT | 主键ID |
| user_id | BIGINT | FK, NOT NULL | 申请用户ID |
| farm_name | VARCHAR(255) | NOT NULL | 农场/生产基地名称 |
| contact_info | VARCHAR(255) | | 联系信息 |
| qualification_doc_urls | JSON | | 资质证明文档URLs |
| apply_date | DATETIME | DEFAULT CURRENT_TIMESTAMP | 申请日期 |
| status | TINYINT | DEFAULT 0 | 状态：0-待审核，1-已通过，2-已拒绝 |
| audit_comment | TEXT | | 审核意见 |
| auditor_id | BIGINT | FK | 审核人ID |
| audit_date | DATETIME | | 审核日期 |

索引：
- PRIMARY KEY (id)
- KEY idx_user_id (user_id)
- KEY idx_status (status)

## 4. 数据库创建脚本

```sql
-- 溯源主记录表
CREATE TABLE traceability_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    product_id BIGINT NOT NULL,
    trace_code VARCHAR(128) NOT NULL UNIQUE,
    product_name VARCHAR(255) NOT NULL,
    farm_name VARCHAR(255) NOT NULL,
    producer_id BIGINT NOT NULL,
    producer_name VARCHAR(255) DEFAULT NULL COMMENT '生产者名称',
    batch_number VARCHAR(100) DEFAULT NULL COMMENT '批次号',
    specification VARCHAR(255) DEFAULT NULL COMMENT '产品规格',
    quality_grade VARCHAR(50) DEFAULT NULL COMMENT '质量等级',
    creation_date DATE,
    harvest_date DATE,
    packaging_date DATE,
    status TINYINT DEFAULT 0 COMMENT '0-草稿，1-待审核，2-已发布，3-已下架，4-异常',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    CONSTRAINT fk_trace_product FOREIGN KEY (product_id) REFERENCES products (id),
    CONSTRAINT fk_trace_producer FOREIGN KEY (producer_id) REFERENCES users (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE INDEX idx_product_id ON traceability_records (product_id);
CREATE INDEX idx_producer_id ON traceability_records (producer_id);

-- 溯源事件表
CREATE TABLE traceability_events (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    trace_record_id BIGINT NOT NULL,
    event_sequence INT DEFAULT NULL COMMENT '事件序号',
    event_type VARCHAR(50) NOT NULL COMMENT 'PLANTING,FERTILIZING,PEST_CONTROL,HARVESTING,PACKAGING,STORAGE,LOGISTICS',
    event_date DATETIME NOT NULL,
    description TEXT,
    location VARCHAR(255),
    responsible_person VARCHAR(100),
    attachments JSON COMMENT '{"images": ["url1", "url2"], "videos": ["url1"]}',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    CONSTRAINT fk_event_trace_record FOREIGN KEY (trace_record_id) REFERENCES traceability_records (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE INDEX idx_trace_record_id ON traceability_events (trace_record_id);
CREATE INDEX idx_event_date ON traceability_events (event_date);

-- 认证信息表
CREATE TABLE trace_certificates (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    trace_record_id BIGINT NOT NULL,
    certificate_type VARCHAR(100) NOT NULL COMMENT 'ORGANIC,GREEN,QUALITY,ORIGIN',
    certificate_no VARCHAR(100),
    issuing_authority VARCHAR(255),
    issue_date DATE,
    valid_until DATE,
    certificate_url VARCHAR(255),
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    CONSTRAINT fk_certificate_trace_record FOREIGN KEY (trace_record_id) REFERENCES traceability_records (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE INDEX idx_trace_record_id ON trace_certificates (trace_record_id);
CREATE INDEX idx_certificate_no ON trace_certificates (certificate_no);

-- 物流信息表
CREATE TABLE trace_logistics (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    trace_record_id BIGINT NOT NULL,
    carrier_name VARCHAR(255),
    transport_type VARCHAR(50) COMMENT 'ROAD,RAILWAY,AIR,SHIP',
    departure_time DATETIME,
    arrival_time DATETIME,
    origin VARCHAR(255),
    destination VARCHAR(255),
    temperature DECIMAL(5,2),
    humidity DECIMAL(5,2),
    status TINYINT DEFAULT 0 COMMENT '0-待发货，1-运输中，2-已到达，3-已签收',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    CONSTRAINT fk_logistics_trace_record FOREIGN KEY (trace_record_id) REFERENCES traceability_records (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE INDEX idx_trace_record_id ON trace_logistics (trace_record_id);
CREATE INDEX idx_status ON trace_logistics (status);

-- 溯源码表
CREATE TABLE trace_codes (
    code VARCHAR(128) PRIMARY KEY,
    trace_record_id BIGINT NOT NULL UNIQUE,
    qr_code_url VARCHAR(255),
    generated_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    status TINYINT DEFAULT 1 COMMENT '0-无效，1-有效',
    scan_count INT DEFAULT 0,
    last_scan_time DATETIME,
    CONSTRAINT fk_trace_code_record FOREIGN KEY (trace_record_id) REFERENCES traceability_records (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 销售者申请表
CREATE TABLE seller_applications (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    farm_name VARCHAR(255) NOT NULL,
    contact_info VARCHAR(255),
    qualification_doc_urls JSON,
    apply_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    status TINYINT DEFAULT 0 COMMENT '0-待审核，1-已通过，2-已拒绝',
    audit_comment TEXT,
    auditor_id BIGINT,
    audit_date DATETIME,
    CONSTRAINT fk_application_user FOREIGN KEY (user_id) REFERENCES users (id),
    CONSTRAINT fk_application_auditor FOREIGN KEY (auditor_id) REFERENCES users (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE INDEX idx_user_id ON seller_applications (user_id);
CREATE INDEX idx_status ON seller_applications (status);

-- 修改users表，添加角色和销售者状态
ALTER TABLE users
ADD COLUMN roles VARCHAR(255) DEFAULT 'ROLE_USER' AFTER phone,
ADD COLUMN seller_status TINYINT DEFAULT 0 COMMENT '0-未申请，1-待审核，2-已通过，3-已拒绝' AFTER roles,
ADD COLUMN farm_name VARCHAR(255) AFTER seller_status;
```

## 5. 索引设计方案

### 5.1 主索引
- 各表的主键索引 (PRIMARY KEY)
- traceability_records 表的 trace_code 字段唯一索引 (UNIQUE KEY)
- trace_codes 表的 trace_record_id 字段唯一索引 (UNIQUE KEY)

### 5.2 外键索引
- traceability_records 表的 product_id, producer_id 外键索引
- traceability_events 表的 trace_record_id 外键索引
- trace_certificates 表的 trace_record_id 外键索引
- trace_logistics 表的 trace_record_id 外键索引
- seller_applications 表的 user_id, auditor_id 外键索引

### 5.3 业务索引
- traceability_events 表的 event_date 索引，优化按时间查询
- trace_certificates 表的 certificate_no 索引，便于证书验证
- trace_logistics 表的 status 索引，便于物流状态查询
- seller_applications 表的 status 索引，优化申请状态筛选

## 6. 测试数据

```sql
-- 插入测试销售者申请数据
INSERT INTO seller_applications (user_id, farm_name, contact_info, qualification_doc_urls, apply_date, status, audit_comment, auditor_id, audit_date)
VALUES 
(1, '阳光农场', '张三 13800138000', '{"docs":["http://example.com/doc1.jpg","http://example.com/doc2.jpg"]}', '2025-01-01 10:00:00', 1, '资料齐全，审核通过', 99, '2025-01-02 15:30:00'),
(2, '绿叶果蔬园', '李四 13900139000', '{"docs":["http://example.com/doc3.jpg"]}', '2025-01-05 09:15:00', 0, NULL, NULL, NULL);

-- 插入测试溯源记录数据
INSERT INTO traceability_records (product_id, trace_code, product_name, farm_name, producer_id, creation_date, harvest_date, packaging_date, status)
VALUES 
(101, 'TR2025010100001', '有机红富士苹果', '阳光农场', 1, '2025-03-01', '2025-09-15', '2025-09-16', 2),
(102, 'TR2025010100002', '绿色有机蔬菜', '绿叶果蔬园', 2, '2025-04-01', '2025-04-30', '2025-05-01', 1);

-- 插入测试溯源事件数据
INSERT INTO traceability_events (trace_record_id, event_type, event_date, description, location, responsible_person, attachments)
VALUES 
(1, 'PLANTING', '2025-03-01 08:00:00', '开始种植红富士苹果', '阳光农场A区', '张三', '{"images":["http://example.com/img1.jpg"]}'),
(1, 'FERTILIZING', '2025-04-15 09:30:00', '施有机肥50kg', '阳光农场A区', '李明', '{"images":["http://example.com/img2.jpg"]}'),
(1, 'PEST_CONTROL', '2025-06-01 07:45:00', '使用生物农药防治蚜虫', '阳光农场A区', '王五', '{"images":["http://example.com/img3.jpg"]}'),
(1, 'HARVESTING', '2025-09-15 08:30:00', '采摘红富士苹果500kg', '阳光农场A区', '张三', '{"images":["http://example.com/img4.jpg","http://example.com/img5.jpg"],"videos":["http://example.com/vid1.mp4"]}'),
(1, 'PACKAGING', '2025-09-16 10:00:00', '分级包装', '阳光农场包装车间', '李明', '{"images":["http://example.com/img6.jpg"]}'),
(2, 'PLANTING', '2025-04-01 07:30:00', '种植有机蔬菜', '绿叶果蔬园B区', '李四', '{"images":["http://example.com/img7.jpg"]}'),
(2, 'HARVESTING', '2025-04-30 08:00:00', '收获有机蔬菜300kg', '绿叶果蔬园B区', '赵六', '{"images":["http://example.com/img8.jpg"]}');

-- 插入测试认证信息数据
INSERT INTO trace_certificates (trace_record_id, certificate_type, certificate_no, issuing_authority, issue_date, valid_until, certificate_url, description)
VALUES 
(1, 'ORGANIC', 'ORG2025001', '国家有机产品认证中心', '2025-01-15', '2026-01-14', 'http://example.com/cert1.jpg', '有机产品认证'),
(1, 'QUALITY', 'QUA2025003', '农产品质量检测中心', '2025-09-17', '2025-12-17', 'http://example.com/cert2.jpg', '质量安全检测合格');

-- 插入测试物流信息数据
INSERT INTO trace_logistics (trace_record_id, carrier_name, transport_type, departure_time, arrival_time, origin, destination, temperature, humidity, status)
VALUES 
(1, '快速物流公司', 'ROAD', '2025-09-17 08:00:00', '2025-09-18 15:30:00', '山东烟台', '北京市朝阳区', 5.2, 65.3, 3);

-- 插入测试溯源码数据
INSERT INTO trace_codes (code, trace_record_id, qr_code_url, status, scan_count, last_scan_time)
VALUES 
('TR2025010100001', 1, 'http://example.com/qr1.png', 1, 120, '2025-10-01 12:30:45'),
('TR2025010100002', 2, 'http://example.com/qr2.png', 1, 45, '2025-09-01 18:25:12');
```