-- 销售者申请表
CREATE TABLE `seller_application` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '申请ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `applicant_type` varchar(20) NOT NULL COMMENT '申请者类型: personal(个人), business(企业)',
  `name` varchar(100) NOT NULL COMMENT '姓名或企业名称',
  `id_number` varchar(50) NOT NULL COMMENT '身份证号或营业执照号',
  `phone` varchar(20) NOT NULL COMMENT '联系电话',
  `email` varchar(100) NOT NULL COMMENT '联系邮箱',
  `address` varchar(255) NOT NULL COMMENT '地址',
  `description` text NOT NULL COMMENT '销售描述',
  `certificate_urls` text COMMENT '证书URL列表，以逗号分隔',
  `status` varchar(20) NOT NULL COMMENT '状态: pending(待审核), approved(已通过), rejected(已拒绝)',
  `reject_reason` text COMMENT '拒绝原因',
  `approved_by` bigint(20) DEFAULT NULL COMMENT '审批人ID',
  `approved_time` datetime DEFAULT NULL COMMENT '审批时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='销售者申请表';

-- 修改用户表，添加是否为销售者字段
ALTER TABLE `user` ADD COLUMN `is_seller` tinyint(1) DEFAULT '0' COMMENT '是否为销售者' AFTER `enabled`; 