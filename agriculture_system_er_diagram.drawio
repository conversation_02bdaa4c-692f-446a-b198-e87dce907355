<mxfile host="app.diagrams.net" modified="2024-03-29T08:30:00.000Z" agent="5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36" etag="SFAP-ER-2024" version="14.7.7" type="device">
  <diagram id="agriculture-system-er" name="农智汇平台数据库ER图">
    <mxGraphModel dx="1422" dy="762" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1400" pageHeight="900" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- 标题 -->
        <mxCell id="title" value="农智汇平台数据库实体关系图" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=24;fontStyle=1;fontColor=#2c3e50;" vertex="1" parent="1">
          <mxGeometry x="450" y="20" width="500" height="40" as="geometry" />
        </mxCell>
        
        <!-- 用户实体 -->
        <mxCell id="user_entity" value="用户&#xa;(User)" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;fontStyle=1;fillColor=#3498db;strokeColor=#2980b9;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="100" y="100" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 用户属性 -->
        <mxCell id="user_id" value="用户ID" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#f39c12;strokeColor=#e67e22;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="20" y="80" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="username" value="用户名" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="20" y="120" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="password" value="密码" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="20" y="160" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="nickname" value="昵称" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="240" y="80" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="email" value="邮箱" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="240" y="120" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="phone" value="手机号" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="240" y="160" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="role" value="角色" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="130" y="40" width="60" height="30" as="geometry" />
        </mxCell>
        
        <!-- 商品分类实体 -->
        <mxCell id="category_entity" value="商品分类&#xa;(Category)" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;fontStyle=1;fillColor=#e74c3c;strokeColor=#c0392b;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="400" y="100" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 商品分类属性 -->
        <mxCell id="category_id" value="分类ID" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#f39c12;strokeColor=#e67e22;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="320" y="80" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="category_name" value="分类名称" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="320" y="120" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="parent_id" value="父分类ID" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="320" y="160" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="level" value="层级" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="540" y="80" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="sort" value="排序" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="540" y="120" width="60" height="30" as="geometry" />
        </mxCell>
        
        <!-- 商品实体 -->
        <mxCell id="product_entity" value="商品&#xa;(Product)" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;fontStyle=1;fillColor=#27ae60;strokeColor=#229954;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="700" y="100" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 商品属性 -->
        <mxCell id="product_id" value="商品ID" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#f39c12;strokeColor=#e67e22;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="620" y="60" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="product_name" value="商品名称" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="620" y="100" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="description" value="描述" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="620" y="140" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="price" value="价格" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="840" y="60" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="stock" value="库存" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="840" y="100" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="rating" value="评分" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="840" y="140" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="seller_id" value="卖家ID" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="730" y="40" width="60" height="30" as="geometry" />
        </mxCell>
        
        <!-- 订单实体 -->
        <mxCell id="order_entity" value="订单&#xa;(Order)" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;fontStyle=1;fillColor=#9b59b6;strokeColor=#8e44ad;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="100" y="300" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 订单属性 -->
        <mxCell id="order_id" value="订单ID" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#f39c12;strokeColor=#e67e22;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="20" y="280" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="order_no" value="订单号" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="20" y="320" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="total_amount" value="总金额" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="20" y="360" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="order_status" value="订单状态" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="240" y="280" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="recipient_name" value="收货人" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="240" y="320" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="recipient_phone" value="收货电话" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="240" y="360" width="60" height="30" as="geometry" />
        </mxCell>
        
        <!-- 订单项实体 -->
        <mxCell id="order_item_entity" value="订单项&#xa;(OrderItem)" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;fontStyle=1;fillColor=#f39c12;strokeColor=#e67e22;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="400" y="300" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 订单项属性 -->
        <mxCell id="order_item_id" value="订单项ID" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#f39c12;strokeColor=#e67e22;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="320" y="280" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="quantity" value="数量" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="320" y="320" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="price_at_purchase" value="购买价格" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="320" y="360" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="item_total" value="小计" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="540" y="320" width="60" height="30" as="geometry" />
        </mxCell>
        
        <!-- 购物车实体 -->
        <mxCell id="cart_item_entity" value="购物车&#xa;(CartItem)" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;fontStyle=1;fillColor=#1abc9c;strokeColor=#16a085;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="700" y="300" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 购物车属性 -->
        <mxCell id="cart_item_id" value="购物车ID" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#f39c12;strokeColor=#e67e22;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="620" y="280" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="cart_quantity" value="数量" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="620" y="320" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="selected" value="是否选中" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="620" y="360" width="60" height="30" as="geometry" />
        </mxCell>
        
        <!-- 收藏实体 -->
        <mxCell id="favorite_entity" value="收藏&#xa;(Favorite)" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;fontStyle=1;fillColor=#e67e22;strokeColor=#d35400;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="1000" y="100" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 收藏属性 -->
        <mxCell id="favorite_id" value="收藏ID" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#f39c12;strokeColor=#e67e22;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="920" y="80" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="favorite_time" value="收藏时间" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="920" y="120" width="60" height="30" as="geometry" />
        </mxCell>
        
        <!-- 评价实体 -->
        <mxCell id="review_entity" value="评价&#xa;(Review)" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;fontStyle=1;fillColor=#8e44ad;strokeColor=#7d3c98;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="1000" y="300" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 评价属性 -->
        <mxCell id="review_id" value="评价ID" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#f39c12;strokeColor=#e67e22;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="920" y="260" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="review_rating" value="评分" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="920" y="300" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="review_content" value="评价内容" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="920" y="340" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="review_images" value="评价图片" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="1140" y="280" width="60" height="30" as="geometry" />
        </mxCell>
        
        <!-- 地址实体 -->
        <mxCell id="address_entity" value="收货地址&#xa;(Address)" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;fontStyle=1;fillColor=#34495e;strokeColor=#2c3e50;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="100" y="500" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 地址属性 -->
        <mxCell id="address_id" value="地址ID" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#f39c12;strokeColor=#e67e22;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="20" y="480" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="address_name" value="收货人" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="20" y="520" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="address_phone" value="联系电话" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="20" y="560" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="province" value="省份" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="240" y="480" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="city" value="城市" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="240" y="520" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="district" value="区县" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="240" y="560" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="detail_address" value="详细地址" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="130" y="600" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="is_default" value="默认地址" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="130" y="440" width="60" height="30" as="geometry" />
        </mxCell>

        <!-- 农业百科实体 -->
        <mxCell id="encyclopedia_entity" value="农业百科&#xa;(Encyclopedia)" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;fontStyle=1;fillColor=#16a085;strokeColor=#138d75;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="400" y="500" width="120" height="60" as="geometry" />
        </mxCell>

        <!-- 农业百科属性 -->
        <mxCell id="encyclopedia_id" value="百科ID" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#f39c12;strokeColor=#e67e22;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="320" y="480" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="encyclopedia_title" value="标题" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="320" y="520" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="encyclopedia_content" value="内容" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="320" y="560" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="cover_image" value="封面图片" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="540" y="480" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="views_count" value="浏览量" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="540" y="520" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="likes_count" value="点赞数" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="540" y="560" width="60" height="30" as="geometry" />
        </mxCell>

        <!-- 百科分类实体 -->
        <mxCell id="encyclopedia_category_entity" value="百科分类&#xa;(EncyclopediaCategory)" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;fontStyle=1;fillColor=#d35400;strokeColor=#ba4a00;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="700" y="500" width="120" height="60" as="geometry" />
        </mxCell>

        <!-- 百科分类属性 -->
        <mxCell id="enc_category_id" value="分类ID" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#f39c12;strokeColor=#e67e22;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="620" y="480" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="enc_category_name" value="分类名称" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="620" y="520" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="enc_parent_id" value="父分类ID" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="620" y="560" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="enc_count" value="文章数量" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="840" y="520" width="60" height="30" as="geometry" />
        </mxCell>

        <!-- AI对话实体 -->
        <mxCell id="ai_conversation_entity" value="AI对话&#xa;(AiConversation)" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;fontStyle=1;fillColor=#5d4e75;strokeColor=#4a3c5a;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="100" y="700" width="120" height="60" as="geometry" />
        </mxCell>

        <!-- AI对话属性 -->
        <mxCell id="conversation_id" value="对话ID" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#f39c12;strokeColor=#e67e22;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="20" y="680" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="session_id" value="会话ID" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="20" y="720" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="user_input" value="用户输入" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="20" y="760" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="ai_response" value="AI回复" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="240" y="680" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="conversation_time" value="对话时间" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="240" y="720" width="60" height="30" as="geometry" />
        </mxCell>

        <!-- 价格预测实体 -->
        <mxCell id="price_forecast_entity" value="价格预测&#xa;(PriceForecast)" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;fontStyle=1;fillColor=#dc7633;strokeColor=#ca6f1e;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="400" y="700" width="120" height="60" as="geometry" />
        </mxCell>

        <!-- 价格预测属性 -->
        <mxCell id="forecast_id" value="预测ID" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#f39c12;strokeColor=#e67e22;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="320" y="680" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="model_type" value="模型类型" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="320" y="720" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="forecast_category" value="产品类别" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="320" y="760" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="region" value="地区" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="540" y="680" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="forecast_date" value="预测日期" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="540" y="720" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="forecast_price" value="预测价格" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="540" y="760" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="confidence" value="置信度" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="430" y="800" width="60" height="30" as="geometry" />
        </mxCell>

        <!-- 关系菱形 -->
        <!-- 用户下订单关系 -->
        <mxCell id="user_order_rel" value="下订单" style="rhombus;whiteSpace=wrap;html=1;fontSize=12;fillColor=#f1c40f;strokeColor=#f39c12;fontColor=#2c3e50;" vertex="1" parent="1">
          <mxGeometry x="130" y="200" width="60" height="40" as="geometry" />
        </mxCell>

        <!-- 商品分类包含商品关系 -->
        <mxCell id="category_product_rel" value="包含" style="rhombus;whiteSpace=wrap;html=1;fontSize=12;fillColor=#f1c40f;strokeColor=#f39c12;fontColor=#2c3e50;" vertex="1" parent="1">
          <mxGeometry x="550" y="110" width="60" height="40" as="geometry" />
        </mxCell>

        <!-- 订单包含订单项关系 -->
        <mxCell id="order_item_rel" value="包含" style="rhombus;whiteSpace=wrap;html=1;fontSize=12;fillColor=#f1c40f;strokeColor=#f39c12;fontColor=#2c3e50;" vertex="1" parent="1">
          <mxGeometry x="280" y="310" width="60" height="40" as="geometry" />
        </mxCell>

        <!-- 订单项关联商品关系 -->
        <mxCell id="item_product_rel" value="关联" style="rhombus;whiteSpace=wrap;html=1;fontSize=12;fillColor=#f1c40f;strokeColor=#f39c12;fontColor=#2c3e50;" vertex="1" parent="1">
          <mxGeometry x="580" y="200" width="60" height="40" as="geometry" />
        </mxCell>

        <!-- 用户购物车关系 -->
        <mxCell id="user_cart_rel" value="拥有" style="rhombus;whiteSpace=wrap;html=1;fontSize=12;fillColor=#f1c40f;strokeColor=#f39c12;fontColor=#2c3e50;" vertex="1" parent="1">
          <mxGeometry x="400" y="200" width="60" height="40" as="geometry" />
        </mxCell>

        <!-- 购物车包含商品关系 -->
        <mxCell id="cart_product_rel" value="包含" style="rhombus;whiteSpace=wrap;html=1;fontSize=12;fillColor=#f1c40f;strokeColor=#f39c12;fontColor=#2c3e50;" vertex="1" parent="1">
          <mxGeometry x="730" y="200" width="60" height="40" as="geometry" />
        </mxCell>

        <!-- 用户收藏商品关系 -->
        <mxCell id="user_favorite_rel" value="收藏" style="rhombus;whiteSpace=wrap;html=1;fontSize=12;fillColor=#f1c40f;strokeColor=#f39c12;fontColor=#2c3e50;" vertex="1" parent="1">
          <mxGeometry x="580" y="80" width="60" height="40" as="geometry" />
        </mxCell>

        <!-- 用户拥有地址关系 -->
        <mxCell id="user_address_rel" value="拥有" style="rhombus;whiteSpace=wrap;html=1;fontSize=12;fillColor=#f1c40f;strokeColor=#f39c12;fontColor=#2c3e50;" vertex="1" parent="1">
          <mxGeometry x="130" y="400" width="60" height="40" as="geometry" />
        </mxCell>

        <!-- 百科分类包含百科关系 -->
        <mxCell id="enc_category_encyclopedia_rel" value="包含" style="rhombus;whiteSpace=wrap;html=1;fontSize=12;fillColor=#f1c40f;strokeColor=#f39c12;fontColor=#2c3e50;" vertex="1" parent="1">
          <mxGeometry x="580" y="510" width="60" height="40" as="geometry" />
        </mxCell>

        <!-- 用户AI对话关系 -->
        <mxCell id="user_ai_rel" value="对话" style="rhombus;whiteSpace=wrap;html=1;fontSize=12;fillColor=#f1c40f;strokeColor=#f39c12;fontColor=#2c3e50;" vertex="1" parent="1">
          <mxGeometry x="130" y="600" width="60" height="40" as="geometry" />
        </mxCell>

        <!-- 实体关系连接线 -->
        <!-- 用户-订单关系连接 -->
        <mxCell id="user_order_line1" value="" style="endArrow=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="user_entity" target="user_order_rel">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="410" as="sourcePoint" />
            <mxPoint x="610" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="user_order_line2" value="" style="endArrow=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="user_order_rel" target="order_entity">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="410" as="sourcePoint" />
            <mxPoint x="610" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 商品分类-商品关系连接 -->
        <mxCell id="category_product_line1" value="" style="endArrow=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="category_entity" target="category_product_rel">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="410" as="sourcePoint" />
            <mxPoint x="610" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="category_product_line2" value="" style="endArrow=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="category_product_rel" target="product_entity">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="410" as="sourcePoint" />
            <mxPoint x="610" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 订单-订单项关系连接 -->
        <mxCell id="order_item_line1" value="" style="endArrow=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="order_entity" target="order_item_rel">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="410" as="sourcePoint" />
            <mxPoint x="610" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="order_item_line2" value="" style="endArrow=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="order_item_rel" target="order_item_entity">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="410" as="sourcePoint" />
            <mxPoint x="610" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 订单项-商品关系连接 -->
        <mxCell id="item_product_line1" value="" style="endArrow=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="order_item_entity" target="item_product_rel">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="410" as="sourcePoint" />
            <mxPoint x="610" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="item_product_line2" value="" style="endArrow=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="item_product_rel" target="product_entity">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="410" as="sourcePoint" />
            <mxPoint x="610" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 用户-购物车关系连接 -->
        <mxCell id="user_cart_line1" value="" style="endArrow=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="user_entity" target="user_cart_rel">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="410" as="sourcePoint" />
            <mxPoint x="610" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="user_cart_line2" value="" style="endArrow=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="user_cart_rel" target="cart_item_entity">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="410" as="sourcePoint" />
            <mxPoint x="610" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 购物车-商品关系连接 -->
        <mxCell id="cart_product_line1" value="" style="endArrow=none;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="cart_item_entity" target="cart_product_rel">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="410" as="sourcePoint" />
            <mxPoint x="610" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="cart_product_line2" value="" style="endArrow=none;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="cart_product_rel" target="product_entity">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="410" as="sourcePoint" />
            <mxPoint x="610" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 用户-收藏关系连接 -->
        <mxCell id="user_favorite_line1" value="" style="endArrow=none;html=1;exitX=1;exitY=0;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="user_entity" target="user_favorite_rel">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="410" as="sourcePoint" />
            <mxPoint x="610" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="user_favorite_line2" value="" style="endArrow=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="user_favorite_rel" target="favorite_entity">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="410" as="sourcePoint" />
            <mxPoint x="610" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 收藏-商品关系连接 -->
        <mxCell id="favorite_product_line" value="" style="endArrow=none;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="favorite_entity" target="product_entity">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="410" as="sourcePoint" />
            <mxPoint x="610" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 用户-地址关系连接 -->
        <mxCell id="user_address_line1" value="" style="endArrow=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="order_entity" target="user_address_rel">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="410" as="sourcePoint" />
            <mxPoint x="610" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="user_address_line2" value="" style="endArrow=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="user_address_rel" target="address_entity">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="410" as="sourcePoint" />
            <mxPoint x="610" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 百科分类-百科关系连接 -->
        <mxCell id="enc_category_line1" value="" style="endArrow=none;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="encyclopedia_category_entity" target="enc_category_encyclopedia_rel">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="410" as="sourcePoint" />
            <mxPoint x="610" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="enc_category_line2" value="" style="endArrow=none;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="enc_category_encyclopedia_rel" target="encyclopedia_entity">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="410" as="sourcePoint" />
            <mxPoint x="610" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 用户-AI对话关系连接 -->
        <mxCell id="user_ai_line1" value="" style="endArrow=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="address_entity" target="user_ai_rel">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="410" as="sourcePoint" />
            <mxPoint x="610" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="user_ai_line2" value="" style="endArrow=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="user_ai_rel" target="ai_conversation_entity">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="410" as="sourcePoint" />
            <mxPoint x="610" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 基数标注 -->
        <!-- 用户-订单 (1:N) -->
        <mxCell id="user_order_card1" value="1" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;fontColor=#e74c3c;" vertex="1" parent="1">
          <mxGeometry x="150" y="170" width="20" height="20" as="geometry" />
        </mxCell>
        <mxCell id="user_order_card2" value="N" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;fontColor=#e74c3c;" vertex="1" parent="1">
          <mxGeometry x="150" y="270" width="20" height="20" as="geometry" />
        </mxCell>

        <!-- 商品分类-商品 (1:N) -->
        <mxCell id="category_product_card1" value="1" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;fontColor=#e74c3c;" vertex="1" parent="1">
          <mxGeometry x="530" y="120" width="20" height="20" as="geometry" />
        </mxCell>
        <mxCell id="category_product_card2" value="N" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;fontColor=#e74c3c;" vertex="1" parent="1">
          <mxGeometry x="670" y="120" width="20" height="20" as="geometry" />
        </mxCell>

        <!-- 订单-订单项 (1:N) -->
        <mxCell id="order_item_card1" value="1" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;fontColor=#e74c3c;" vertex="1" parent="1">
          <mxGeometry x="230" y="320" width="20" height="20" as="geometry" />
        </mxCell>
        <mxCell id="order_item_card2" value="N" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;fontColor=#e74c3c;" vertex="1" parent="1">
          <mxGeometry x="370" y="320" width="20" height="20" as="geometry" />
        </mxCell>

        <!-- 用户-购物车 (1:N) -->
        <mxCell id="user_cart_card1" value="1" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;fontColor=#e74c3c;" vertex="1" parent="1">
          <mxGeometry x="230" y="210" width="20" height="20" as="geometry" />
        </mxCell>
        <mxCell id="user_cart_card2" value="N" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;fontColor=#e74c3c;" vertex="1" parent="1">
          <mxGeometry x="670" y="210" width="20" height="20" as="geometry" />
        </mxCell>

        <!-- 用户-收藏 (M:N) -->
        <mxCell id="user_favorite_card1" value="M" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;fontColor=#e74c3c;" vertex="1" parent="1">
          <mxGeometry x="230" y="90" width="20" height="20" as="geometry" />
        </mxCell>
        <mxCell id="user_favorite_card2" value="N" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;fontColor=#e74c3c;" vertex="1" parent="1">
          <mxGeometry x="970" y="90" width="20" height="20" as="geometry" />
        </mxCell>

        <!-- 百科分类-百科 (1:N) -->
        <mxCell id="enc_category_card1" value="1" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;fontColor=#e74c3c;" vertex="1" parent="1">
          <mxGeometry x="670" y="520" width="20" height="20" as="geometry" />
        </mxCell>
        <mxCell id="enc_category_card2" value="N" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;fontColor=#e74c3c;" vertex="1" parent="1">
          <mxGeometry x="530" y="520" width="20" height="20" as="geometry" />
        </mxCell>

        <!-- 图例说明 -->
        <mxCell id="legend_title" value="图例说明" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#2c3e50;" vertex="1" parent="1">
          <mxGeometry x="1200" y="100" width="100" height="30" as="geometry" />
        </mxCell>

        <mxCell id="legend_entity" value="实体" style="rounded=0;whiteSpace=wrap;html=1;fontSize=12;fillColor=#3498db;strokeColor=#2980b9;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="1200" y="140" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="legend_entity_text" value="实体（矩形）" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="1270" y="140" width="80" height="30" as="geometry" />
        </mxCell>

        <mxCell id="legend_attribute" value="属性" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#95a5a6;strokeColor=#7f8c8d;" vertex="1" parent="1">
          <mxGeometry x="1200" y="180" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="legend_attribute_text" value="属性（椭圆）" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="1270" y="180" width="80" height="30" as="geometry" />
        </mxCell>

        <mxCell id="legend_key" value="主键" style="ellipse;whiteSpace=wrap;html=1;fontSize=12;fillColor=#f39c12;strokeColor=#e67e22;fontColor=white;" vertex="1" parent="1">
          <mxGeometry x="1200" y="220" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="legend_key_text" value="主键属性（橙色椭圆）" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="1270" y="220" width="120" height="30" as="geometry" />
        </mxCell>

        <mxCell id="legend_relationship" value="关系" style="rhombus;whiteSpace=wrap;html=1;fontSize=12;fillColor=#f1c40f;strokeColor=#f39c12;fontColor=#2c3e50;" vertex="1" parent="1">
          <mxGeometry x="1200" y="260" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="legend_relationship_text" value="关系（菱形）" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="1270" y="260" width="80" height="30" as="geometry" />
        </mxCell>

        <!-- 系统说明 -->
        <mxCell id="system_description" value="农智汇平台主要功能模块：&#xa;• 用户管理：用户注册、登录、个人信息管理&#xa;• 电商系统：商品展示、购物车、订单管理、支付&#xa;• 农业百科：分类浏览、内容管理、收藏功能&#xa;• AI智能助手：智能对话、农业咨询&#xa;• 数据分析：价格预测、市场分析&#xa;• 地址管理：收货地址维护&#xa;• 评价系统：商品评价、用户反馈" style="text;html=1;strokeColor=#34495e;fillColor=#ecf0f1;align=left;verticalAlign=top;whiteSpace=wrap;rounded=1;fontSize=12;fontColor=#2c3e50;" vertex="1" parent="1">
          <mxGeometry x="1200" y="320" width="280" height="200" as="geometry" />
        </mxCell>

      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
