/**
 * SFAP溯源中心修复验证脚本
 * 用于测试修复后的功能是否正常工作
 */

// 测试API端点
const testApiEndpoints = async () => {
  const baseUrl = 'http://localhost:8081';
  const endpoints = [
    '/api/traceability/public/stats',
    '/api/traceability/seller/1/stats', 
    '/api/traceability/admin/stats'
  ];

  console.log('🔍 测试API端点...');
  
  for (const endpoint of endpoints) {
    try {
      const response = await fetch(`${baseUrl}${endpoint}`);
      const data = await response.json();
      
      if (response.ok && data.success) {
        console.log(`✅ ${endpoint} - 正常`);
        console.log(`   数据: ${JSON.stringify(data.data, null, 2)}`);
      } else {
        console.log(`❌ ${endpoint} - 错误: ${data.message || response.statusText}`);
      }
    } catch (error) {
      console.log(`❌ ${endpoint} - 网络错误: ${error.message}`);
    }
  }
};

// 测试字体文件
const testFontFiles = async () => {
  const baseUrl = 'http://localhost:8080';
  const fontFiles = [
    '/static/fonts/element-icons.woff',
    '/static/fonts/element-icons.ttf'
  ];

  console.log('\n🔍 测试字体文件...');
  
  for (const fontFile of fontFiles) {
    try {
      const response = await fetch(`${baseUrl}${fontFile}`);
      
      if (response.ok) {
        console.log(`✅ ${fontFile} - 可访问 (${response.headers.get('content-type')})`);
      } else {
        console.log(`❌ ${fontFile} - 无法访问: ${response.status} ${response.statusText}`);
      }
    } catch (error) {
      console.log(`❌ ${fontFile} - 网络错误: ${error.message}`);
    }
  }
};

// 测试角色权限
const testRolePermissions = () => {
  console.log('\n🔍 测试角色权限逻辑...');
  
  // 模拟不同角色的用户
  const testUsers = [
    { username: 'test_user', role: 'user', userType: 'normal' },
    { username: 'seller_user', role: 'seller', userType: 'seller' },
    { username: 'admin_user', role: 'admin', userType: 'admin' }
  ];

  // 模拟权限检查函数
  const hasRolePermission = (userRole, requiredRole) => {
    const roleHierarchy = {
      'admin': 3,
      'seller': 2,
      'user': 1,
      'guest': 0
    };
    return roleHierarchy[userRole] >= roleHierarchy[requiredRole];
  };

  const testCases = [
    { requiredRoles: ['user'], description: '普通用户功能' },
    { requiredRoles: ['seller'], description: '销售者功能' },
    { requiredRoles: ['admin'], description: '管理员功能' }
  ];

  testUsers.forEach(user => {
    console.log(`\n👤 用户: ${user.username} (${user.role})`);
    
    testCases.forEach(testCase => {
      const hasAccess = testCase.requiredRoles.includes(user.role) || 
                       testCase.requiredRoles.some(role => hasRolePermission(user.role, role));
      
      console.log(`   ${hasAccess ? '✅' : '❌'} ${testCase.description}: ${hasAccess ? '有权限' : '无权限'}`);
    });
  });
};

// 主测试函数
const runTests = async () => {
  console.log('🚀 开始SFAP溯源中心修复验证\n');
  
  try {
    await testApiEndpoints();
    await testFontFiles();
    testRolePermissions();
    
    console.log('\n✨ 测试完成！');
    console.log('\n📋 检查清单:');
    console.log('□ 后端API端点正常响应');
    console.log('□ 字体文件可以正常访问');
    console.log('□ 角色权限逻辑正确');
    console.log('□ 前端页面无控制台错误');
    console.log('□ Element UI图标正常显示');
    
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
  }
};

// 如果在Node.js环境中运行
if (typeof window === 'undefined') {
  // Node.js环境，需要安装node-fetch
  console.log('请在浏览器控制台中运行此脚本，或安装node-fetch后在Node.js中运行');
  console.log('浏览器运行方法:');
  console.log('1. 打开 http://localhost:8080');
  console.log('2. 按F12打开开发者工具');
  console.log('3. 在控制台中粘贴并运行此脚本');
} else {
  // 浏览器环境，直接运行
  runTests();
}

// 导出测试函数供手动调用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { testApiEndpoints, testFontFiles, testRolePermissions, runTests };
}
