#!/bin/bash

# 二维码URL修复脚本
# 用于修复生产环境中错误的localhost:8080二维码URL

echo "🔧 SFAP二维码URL修复脚本"
echo "=================================="

# 服务器配置
SERVER_HOST="**************"
SERVER_PORT="8081"
BASE_URL="http://${SERVER_HOST}:${SERVER_PORT}"

echo "📋 修复计划:"
echo "1. 停止后端服务"
echo "2. 重新部署修复后的代码"
echo "3. 启动后端服务"
echo "4. 调用修复API"
echo "5. 验证修复结果"
echo ""

# 检查是否在生产服务器上
if [ ! -d "/www/wwwroot/test.com/backend" ]; then
    echo "❌ 错误: 请在生产服务器上运行此脚本"
    exit 1
fi

echo "🛑 步骤1: 停止后端服务"
echo "=================================="
cd /www/wwwroot/test.com/backend

# 查找并停止Java进程
JAVA_PID=$(ps aux | grep java | grep agriculture | awk '{print $2}')
if [ ! -z "$JAVA_PID" ]; then
    echo "找到Java进程: $JAVA_PID"
    kill -9 $JAVA_PID
    echo "✅ 后端服务已停止"
else
    echo "⚠️  未找到运行中的后端服务"
fi

echo ""
echo "🚀 步骤2: 启动后端服务"
echo "=================================="

# 启动后端服务
nohup java -jar -Dspring.profiles.active=prod agriculture-mall-*.jar > app.log 2>&1 &
NEW_PID=$!

echo "后端服务启动中... PID: $NEW_PID"
echo "等待服务启动完成..."

# 等待服务启动
for i in {1..30}; do
    if curl -s "${BASE_URL}/api/health" > /dev/null 2>&1; then
        echo "✅ 后端服务启动成功"
        break
    fi
    
    if [ $i -eq 30 ]; then
        echo "❌ 后端服务启动超时"
        exit 1
    fi
    
    echo "等待中... ($i/30)"
    sleep 2
done

echo ""
echo "🔧 步骤3: 调用修复API"
echo "=================================="

# 调用修复API
echo "正在修复所有二维码URL..."

RESPONSE=$(curl -s -X POST "${BASE_URL}/api/qrcode/fix-all-urls" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer admin-token")

echo "API响应: $RESPONSE"

# 检查响应
if echo "$RESPONSE" | grep -q '"success":true'; then
    echo "✅ 二维码URL修复成功"
else
    echo "❌ 二维码URL修复失败"
    echo "响应内容: $RESPONSE"
fi

echo ""
echo "🧪 步骤4: 验证修复结果"
echo "=================================="

# 测试几个溯源码
TEST_CODES=("SFAPA2401130900500214BB0" "SFAPA24011009002008E8BC9" "SFAPA24011208005001FD1A8")

echo "测试溯源码查询..."
for code in "${TEST_CODES[@]}"; do
    echo "测试溯源码: $code"
    
    QUERY_RESPONSE=$(curl -s -X POST "${BASE_URL}/api/traceability/query" \
        -H "Content-Type: application/json" \
        -d "{\"traceCode\":\"$code\",\"source\":\"test\",\"location\":\"测试\",\"ipAddress\":\"127.0.0.1\",\"deviceInfo\":\"测试设备\"}")
    
    if echo "$QUERY_RESPONSE" | grep -q '"success":true'; then
        echo "  ✅ 查询成功"
    else
        echo "  ❌ 查询失败"
    fi
done

echo ""
echo "📊 步骤5: 生成测试报告"
echo "=================================="

# 生成测试报告
cat > qr_fix_report.txt << EOF
SFAP二维码URL修复报告
生成时间: $(date)
服务器: ${SERVER_HOST}

修复内容:
- 将所有二维码中的 http://localhost:8080 替换为 http://**************:8200
- 重新生成所有二维码文件
- 更新数据库中的二维码URL字段

修复范围:
- traceability_record.qr_code_url
- trace_codes.qr_code_url  
- products.qr_code_url

测试结果:
$(for code in "${TEST_CODES[@]}"; do
    QUERY_RESPONSE=$(curl -s -X POST "${BASE_URL}/api/traceability/query" \
        -H "Content-Type: application/json" \
        -d "{\"traceCode\":\"$code\",\"source\":\"test\",\"location\":\"测试\",\"ipAddress\":\"127.0.0.1\",\"deviceInfo\":\"测试设备\"}")
    
    if echo "$QUERY_RESPONSE" | grep -q '"success":true'; then
        echo "- $code: ✅ 正常"
    else
        echo "- $code: ❌ 异常"
    fi
done)

建议:
1. 重新生成并打印新的二维码标签
2. 测试手机扫码功能
3. 监控后续查询日志

EOF

echo "✅ 修复报告已生成: qr_fix_report.txt"

echo ""
echo "🎉 修复完成!"
echo "=================================="
echo "请执行以下验证步骤:"
echo "1. 用手机扫描新生成的二维码"
echo "2. 确认跳转地址为: http://**************:8200/trace/xxx"
echo "3. 确认溯源查询功能正常"
echo ""
echo "如果仍有问题，请检查:"
echo "- 后端服务日志: tail -f /www/wwwroot/test.com/backend/app.log"
echo "- NGINX配置是否正确"
echo "- 前端服务是否正常运行"
echo ""
echo "修复报告: $(pwd)/qr_fix_report.txt"
