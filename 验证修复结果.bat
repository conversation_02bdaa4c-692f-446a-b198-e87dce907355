@echo off
echo ========================================
echo SFAP溯源模块修复验证脚本 v2.0
echo ========================================
echo.

echo 修复内容：
echo - TraceabilityRecord实体类字段补全和类型修正
echo - TraceabilityDetailVO状态字段类型修正
echo - TraceabilityServiceImpl状态比较逻辑修正
echo - 前端TraceabilityManagement状态处理修正
echo.

echo 1. 检查后端编译状态...
cd backend\main
echo 正在编译后端代码...
call mvn clean compile -q
if %errorlevel% equ 0 (
    echo [✓] 后端编译成功 - 所有Java编译错误已修复
) else (
    echo [✗] 后端编译失败 - 仍有编译错误需要修复
    echo 请检查编译输出获取详细错误信息
    pause
    exit /b 1
)
echo.

echo 2. 检查前端编译状态...
cd ..\..\
echo 正在检查前端代码...
call npm run lint --silent
if %errorlevel% equ 0 (
    echo [✓] 前端ESLint检查通过
) else (
    echo [!] 前端ESLint检查发现问题，继续编译测试...
)

echo 正在测试前端编译...
call npm run build --silent
if %errorlevel% equ 0 (
    echo [✓] 前端编译成功 - isSeller导入错误已修复
) else (
    echo [✗] 前端编译失败 - 仍有编译错误
)
echo.

echo 3. 测试API接口连通性...
echo 正在测试后端服务连接...
curl -s -o nul -w "HTTP状态码: %%{http_code}" http://localhost:8081/api/test
if %errorlevel% equ 0 (
    echo.
    echo [✓] 后端服务连接正常
) else (
    echo.
    echo [!] 后端服务未启动或连接失败
    echo 请确保后端服务在localhost:8081运行
)
echo.

echo 4. 测试销售者溯源记录API...
echo 正在测试创建溯源记录接口...
curl -X POST "http://localhost:8081/api/traceability/seller/records" ^
  -H "Content-Type: application/json" ^
  -H "X-User-Id: 18" ^
  -d "{\"productId\": 1, \"productName\": \"测试产品\", \"productionDate\": \"2025-07-10\", \"expiryDate\": \"2025-07-20\", \"batchNumber\": \"TEST001\", \"status\": \"draft\"}" ^
  -w "HTTP状态码: %%{http_code}\n" ^
  -s
echo.

echo 5. 测试获取溯源记录列表...
curl -X GET "http://localhost:8081/api/traceability/seller/records?page=1&size=10" ^
  -H "Content-Type: application/json" ^
  -H "X-User-Id: 18" ^
  -w "HTTP状态码: %%{http_code}\n" ^
  -s
echo.

echo ========================================
echo 验证完成！
echo ========================================
echo.
echo 修复内容总结：
echo [✓] TraceabilityRecord实体类字段补全和类型修正
echo [✓] TraceabilityDetailVO状态字段类型修正（Integer → String）
echo [✓] TraceabilityServiceImpl状态比较逻辑修正（数字 → 字符串）
echo [✓] 前端TraceabilityManagement状态处理修正
echo [✓] 控制器编译错误修复
echo [✓] 服务实现编译错误修复
echo [✓] 前端路由权限验证增强
echo [✓] 前端isSeller函数导入修复
echo [✓] 溯源码生成格式统一
echo.
echo 状态字段统一规范：
echo - draft: 草稿
echo - pending: 待审核
echo - published: 已发布
echo - rejected: 已驳回
echo.
echo 如果所有测试都通过，说明修复成功！
echo 现在可以启动前端服务测试完整功能。
echo.
pause
