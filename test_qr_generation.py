#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试二维码生成和产品表同步更新功能
"""

import requests
import json
import mysql.connector
from datetime import datetime

# 配置
API_BASE_URL = "http://localhost:8081"
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'fan13965711955',
    'database': 'agriculture_mall'
}

def test_qr_generation_sync():
    """测试二维码生成和产品表同步更新"""
    print("=== 测试二维码生成和产品表同步更新 ===")
    
    # 连接数据库
    conn = mysql.connector.connect(**DB_CONFIG)
    cursor = conn.cursor(dictionary=True)
    
    try:
        # 1. 查找一个有溯源码但没有二维码的产品
        query = """
        SELECT p.id, p.name, p.trace_code, p.qr_code_url as product_qr_url,
               tr.id as record_id, tr.qr_code_url as record_qr_url
        FROM product p
        JOIN traceability_record tr ON p.id = tr.product_id
        WHERE p.trace_code IS NOT NULL 
        AND p.qr_code_url IS NULL 
        AND tr.deleted = 0
        LIMIT 1
        """
        cursor.execute(query)
        test_product = cursor.fetchone()
        
        if not test_product:
            print("❌ 没有找到适合测试的产品（有溯源码但没有二维码）")
            return False
        
        print(f"📦 测试产品: {test_product['name']} (ID: {test_product['id']})")
        print(f"🔍 溯源码: {test_product['trace_code']}")
        print(f"📱 产品二维码URL: {test_product['product_qr_url']}")
        print(f"📱 记录二维码URL: {test_product['record_qr_url']}")
        
        # 2. 调用二维码生成API
        trace_code = test_product['trace_code']
        api_url = f"{API_BASE_URL}/api/qrcode/generate/{trace_code}"
        
        print(f"\n🚀 调用二维码生成API: {api_url}")
        
        # 这里需要添加认证头，假设使用Bearer token
        headers = {
            'Content-Type': 'application/json',
            # 'Authorization': 'Bearer YOUR_TOKEN_HERE'  # 需要根据实际情况添加
        }
        
        try:
            response = requests.post(api_url, headers=headers, timeout=30)
            print(f"📡 API响应状态: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ API响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
                
                # 3. 验证数据库更新
                print("\n🔍 验证数据库更新...")
                
                # 检查产品表
                cursor.execute("SELECT qr_code_url FROM product WHERE id = %s", (test_product['id'],))
                updated_product = cursor.fetchone()
                
                # 检查溯源记录表
                cursor.execute("SELECT qr_code_url FROM traceability_record WHERE id = %s", (test_product['record_id'],))
                updated_record = cursor.fetchone()
                
                print(f"📱 产品表二维码URL: {updated_product['qr_code_url']}")
                print(f"📱 记录表二维码URL: {updated_record['qr_code_url']}")
                
                # 验证同步
                if updated_product['qr_code_url'] and updated_record['qr_code_url']:
                    if updated_product['qr_code_url'] == updated_record['qr_code_url']:
                        print("✅ 二维码URL同步成功！")
                        return True
                    else:
                        print("❌ 二维码URL不一致！")
                        return False
                else:
                    print("❌ 二维码URL更新失败！")
                    return False
                    
            else:
                print(f"❌ API调用失败: {response.text}")
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"❌ API调用异常: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False
    finally:
        cursor.close()
        conn.close()

def check_data_consistency():
    """检查数据一致性"""
    print("\n=== 检查数据一致性 ===")
    
    conn = mysql.connector.connect(**DB_CONFIG)
    cursor = conn.cursor(dictionary=True)
    
    try:
        # 检查产品表和溯源记录表的二维码URL不一致的情况
        query = """
        SELECT 
            p.id as product_id,
            p.name as product_name,
            p.trace_code,
            p.qr_code_url as product_qr_url,
            tr.id as record_id,
            tr.qr_code_url as record_qr_url
        FROM product p
        JOIN traceability_record tr ON p.id = tr.product_id
        WHERE p.trace_code IS NOT NULL 
        AND tr.deleted = 0
        AND (
            (p.qr_code_url IS NULL AND tr.qr_code_url IS NOT NULL) OR
            (p.qr_code_url IS NOT NULL AND tr.qr_code_url IS NULL) OR
            (p.qr_code_url != tr.qr_code_url)
        )
        """
        cursor.execute(query)
        inconsistent_records = cursor.fetchall()
        
        if inconsistent_records:
            print(f"⚠️ 发现 {len(inconsistent_records)} 个数据不一致的记录:")
            for record in inconsistent_records:
                print(f"  - 产品: {record['product_name']} (ID: {record['product_id']})")
                print(f"    溯源码: {record['trace_code']}")
                print(f"    产品二维码: {record['product_qr_url']}")
                print(f"    记录二维码: {record['record_qr_url']}")
                print()
        else:
            print("✅ 所有数据一致性检查通过！")
            
        return len(inconsistent_records) == 0
        
    except Exception as e:
        print(f"❌ 数据一致性检查异常: {e}")
        return False
    finally:
        cursor.close()
        conn.close()

def main():
    """主函数"""
    print("🧪 SFAP平台二维码生成同步测试")
    print("=" * 50)
    
    # 检查初始数据一致性
    initial_consistency = check_data_consistency()
    
    # 测试二维码生成和同步
    test_result = test_qr_generation_sync()
    
    # 再次检查数据一致性
    final_consistency = check_data_consistency()
    
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    print(f"  初始数据一致性: {'✅ 通过' if initial_consistency else '❌ 失败'}")
    print(f"  二维码生成测试: {'✅ 通过' if test_result else '❌ 失败'}")
    print(f"  最终数据一致性: {'✅ 通过' if final_consistency else '❌ 失败'}")
    
    if test_result and final_consistency:
        print("\n🎉 所有测试通过！二维码生成和同步功能正常工作。")
    else:
        print("\n⚠️ 部分测试失败，请检查相关配置和代码。")

if __name__ == "__main__":
    main()
