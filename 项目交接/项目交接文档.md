# 智慧农业平台（SFAP）项目交接文档

## 项目概述

智慧农业平台（Smart Farmer Assistance Platform, SFAP）是一个基于Spring Boot + Vue.js的现代化农业服务平台，采用前后端分离架构，为农民、消费者和农业专家提供全方位的智能化农业服务。

## 项目结构说明

### 根目录结构
```
SFAP/
├── backend/                    # 后端项目目录
│   ├── main/                   # Spring Boot主项目
│   └── python/                 # Python爬虫服务
├── src/                        # 前端Vue.js源码
├── dist/                       # 前端构建产物
├── public/                     # 静态资源
├── 任务/                       # 开发任务文档
├── 后端开发/                   # 后端开发文档
├── 数据库设计/                 # 数据库设计文档
├── 架构文档/                   # 系统架构文档
└── 任务完成说明文档.md          # 项目完成情况总览
```

## 核心模块位置映射

### 1. 农品汇商城模块

**后端位置：**
- Controller: `backend/main/src/main/java/com/agriculture/mall/controller/`
  - `ProductController.java` - 商品管理API
  - `CategoryController.java` - 分类管理API
  - `CartController.java` - 购物车API
  - `OrderController.java` - 订单管理API
  - `ReviewController.java` - 评价系统API
  - `FavoriteController.java` - 收藏功能API
  - `UserController.java` - 用户管理API

- Service: `backend/main/src/main/java/com/agriculture/mall/service/`
  - `ProductService.java` & `impl/ProductServiceImpl.java`
  - `CategoryService.java` & `impl/CategoryServiceImpl.java`
  - `CartService.java` & `impl/CartServiceImpl.java`
  - `OrderService.java` & `impl/OrderServiceImpl.java`
  - `ReviewService.java` & `impl/ReviewServiceImpl.java`
  - `FavoriteService.java` & `impl/FavoriteServiceImpl.java`
  - `UserService.java` & `impl/UserServiceImpl.java`

- Mapper: `backend/main/src/main/java/com/agriculture/mall/mapper/`
  - `ProductMapper.java` & `resources/mapper/ProductMapper.xml`
  - `CategoryMapper.java` & `resources/mapper/CategoryMapper.xml`
  - `CartItemMapper.java` & `resources/mapper/CartItemMapper.xml`
  - `OrderMapper.java` & `resources/mapper/OrderMapper.xml`
  - `ReviewMapper.java` & `resources/mapper/ReviewMapper.xml`
  - `FavoriteMapper.java` & `resources/mapper/FavoriteMapper.xml`

- Entity: `backend/main/src/main/java/com/agriculture/mall/entity/`
  - `Product.java`, `Category.java`, `CartItem.java`, `Order.java`, `Review.java`, `Favorite.java`

**前端位置：**
- 页面组件: `src/views/`
  - `Home.vue` - 首页
  - `Shop.vue` - 商品列表页
  - `ProductDetail.vue` - 商品详情页
  - `Cart.vue` - 购物车页面
  - `Order.vue` - 订单页面
  - `Profile.vue` - 用户中心

- 通用组件: `src/components/`
  - `Header.vue` - 页面头部
  - `ProductCard.vue` - 商品卡片
  - `CategoryNav.vue` - 分类导航
  - `ReviewList.vue` - 评价列表
  - `OrderStatus.vue` - 订单状态

### 2. 推荐系统模块

**后端位置：**
- 推荐算法: `backend/main/src/main/java/com/agriculture/mall/recommendation/`
  - `ContentBasedRecommendation.java` - 基于内容的推荐
  - `ItemBasedCF.java` - 基于物品的协同过滤
  - `UserBasedCF.java` - 基于用户的协同过滤

- 推荐服务: `backend/main/src/main/java/com/agriculture/mall/service/`
  - `ProductRecommendationService.java` & `impl/ProductRecommendationServiceImpl.java`
  - `UserBehaviorService.java` & `impl/UserBehaviorServiceImpl.java`

- 推荐控制器: `backend/main/src/main/java/com/agriculture/mall/controller/`
  - `ProductRecommendationController.java`

**前端位置：**
- 推荐组件: `src/components/`
  - `RecommendedProducts.vue` - 推荐商品展示
  - `PersonalizedRecommendation.vue` - 个性化推荐

### 3. 溯源系统模块

**后端位置：**
- 溯源API: `backend/main/src/main/java/com/agriculture/traceability/`
  - `TraceabilityController.java`
  - `TraceabilityService.java`
  - `TraceabilityRecord.java`

**前端位置：**
- 溯源页面: `src/views/`
  - `Traceability.vue` - 溯源查询页面

- 溯源组件: `src/components/traceability/`
  - `TraceabilityCenter.vue` - 溯源中心
  - `TraceabilitySteps.vue` - 溯源步骤
  - `TraceabilityCharts.vue` - 溯源图表
  - `TraceabilityReports.vue` - 溯源报告
  - `TraceabilitySearch.vue` - 溯源查询
  - `TraceabilityShare.vue` - 溯源分享
  - `ProductInfo.vue` - 产品信息
  - `LoadingState.vue` - 加载状态

### 4. 农业百科模块

**后端位置：**
- 百科API: `backend/main/src/main/java/com/agriculture/encyclopedia/`
  - `EncyclopediaController.java`
  - `EncyclopediaService.java` & `EncyclopediaServiceImpl.java`
  - `EncyclopediaCommentController.java`
  - `EncyclopediaCommentService.java` & `EncyclopediaCommentServiceImpl.java`

**前端位置：**
- 百科页面: `src/views/`
  - `Encyclopedia.vue` - 农业百科主页
  - `EncyclopediaDetail.vue` - 百科文章详情页

### 5. 天气预警与价格预测模块

**后端位置：**
- 天气API: `backend/main/src/main/java/com/agriculture/weather/`
  - `WeatherAlertController.java`
  - `WeatherAlertService.java` & `WeatherAlertServiceImpl.java`

- 价格预测API: `backend/main/src/main/java/com/agriculture/price/`
  - `PriceForecastController.java`
  - `PriceForecastService.java` & `PriceForecastServiceImpl.java`
  - `PriceIndexController.java`

**前端位置：**
- 天气页面: `src/views/`
  - `Weather.vue` - 天气信息页面

- 价格页面: `src/views/`
  - `Price.vue` - 价格信息页面
  - `PriceIndices.vue` - 价格指数页面

- 相关组件: `src/components/`
  - `WeatherDetail.vue` - 天气详情
  - `WeatherNotification.vue` - 天气通知
  - `PriceTrendChart.vue` - 价格趋势图表

### 6. 新闻资讯模块

**后端位置：**
- 新闻API: `backend/main/src/main/java/com/agriculture/news/`
  - `NewsController.java`
  - `NewsService.java` & `NewsServiceImpl.java`
  - `NewsMapper.java`
  - `News.java`

**前端位置：**
- 新闻页面: `src/views/`
  - `News.vue` - 新闻列表页面
  - `NewsDetail.vue` - 新闻详情页面

### 7. AI智能助手模块

**后端位置：**
- AI API: `backend/main/src/main/java/com/agriculture/ai/`
  - `AIController.java`
  - `AIService.java`
  - `AIConversation.java`

**前端位置：**
- AI助手: `src/views/`
  - `AIAssistant.vue` - AI助手页面和组件

## 技术栈详情

### 后端技术栈
- **框架**: Spring Boot 2.x
- **数据库**: MySQL 8.0
- **ORM**: MyBatis
- **缓存**: Redis
- **安全**: Spring Security + JWT
- **构建工具**: Maven
- **JDK版本**: Java 8+

### 前端技术栈
- **框架**: Vue.js 2.6.11
- **构建工具**: Vue CLI 4.5.0
- **UI库**: Element UI 2.15.14
- **状态管理**: Vuex 3.4.0
- **路由**: Vue Router 3.2.0
- **HTTP客户端**: Axios 1.8.4
- **图表库**: ECharts 4.9.0
- **动画库**: Animate.css 4.1.1
- **样式预处理**: Sass/SCSS

## 数据库设计

### 核心数据表
- **商品相关**: `products`, `categories`, `product_images`
- **用户相关**: `users`, `user_addresses`, `user_behavior`
- **订单相关**: `orders`, `order_items`, `cart_items`
- **评价相关**: `reviews`, `review_images`
- **推荐相关**: `user_behavior`, `product_recommendations`
- **溯源相关**: `traceability_records`, `traceability_events`
- **百科相关**: `encyclopedia`, `encyclopedia_categories`, `encyclopedia_comments`
- **新闻相关**: `news`, `news_categories`
- **价格相关**: `price_forecasts`, `price_indices`

## API接口规范

### 接口前缀
- 商城模块: `/api/mall/`
- 推荐系统: `/api/recommendations/`
- 溯源系统: `/api/traceability/`
- 百科模块: `/api/encyclopedia/`
- 天气模块: `/api/weather/`
- 价格模块: `/api/price/`
- 新闻模块: `/api/news/`
- AI助手: `/api/ai/`

### 响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## 开发环境配置

### 后端启动
```bash
cd backend/main
mvn spring-boot:run
```
默认端口: 8081

### 前端启动
```bash
npm install
npm run serve
```
默认端口: 8080

### 数据库配置
- 数据库名: `agriculture_platform`
- 用户名: `root`
- 密码: 根据本地配置
- 端口: 3306

## 项目完成度

### 已完成模块 ✅
1. **农品汇商城模块** - 100%
2. **推荐系统模块** - 100%
3. **溯源系统模块** - 100%
4. **农业百科模块** - 100%
5. **天气预警模块** - 100%
6. **价格预测模块** - 100%
7. **新闻资讯模块** - 100%
8. **AI智能助手模块** - 100%

### 代码质量状态
- **编译状态**: ✅ 无编译错误
- **代码规范**: ✅ 遵循ESLint规范
- **接口测试**: ✅ 主要API已测试
- **前端集成**: ✅ 前后端已联调

## 常见开发任务

### 添加新的API接口
1. 在对应的Controller中添加新方法
2. 在Service接口和实现类中添加业务逻辑
3. 如需数据库操作，在Mapper中添加方法
4. 更新前端API调用

### 添加新的前端页面
1. 在`src/views/`中创建Vue组件
2. 在`src/router/index.js`中添加路由配置
3. 如需要，在导航菜单中添加入口

### 修改数据库结构
1. 更新对应的Entity类
2. 修改Mapper.xml文件
3. 更新相关的Service和Controller
4. 执行数据库迁移脚本

## 注意事项

1. **端口配置**: 前端固定8080端口，后端固定8081端口，请勿修改
2. **代码规范**: 严格遵循ESLint配置，每次修改后检查是否有语法错误
3. **组件导入**: 前端开发时确保正确导入所有依赖组件
4. **API文档**: 每个模块开发完成后需要更新对应的API文档
5. **错误处理**: 前后端都要有完善的错误处理机制

## 联系与支持

如有问题，请参考：
- `任务完成说明文档.md` - 详细的功能完成情况
- `后端开发/` - 后端开发相关文档
- `数据库设计/` - 数据库设计文档
- `架构文档/` - 系统架构设计文档

---

**文档更新时间**: 2024年12月
**项目版本**: V4.0
**维护状态**: 活跃开发中