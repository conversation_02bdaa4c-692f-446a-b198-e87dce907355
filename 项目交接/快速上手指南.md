# 智慧农业平台快速上手指南

> 🚀 帮助开发者快速理解项目结构，掌握开发流程，高效完成开发任务

## 📖 文档导航

在开始开发前，建议按顺序阅读以下文档：

1. **📋 [任务完成说明文档.md](./任务完成说明文档.md)** - 了解项目整体完成情况
2. **🔄 [项目交接文档.md](./项目交接文档.md)** - 掌握项目基本信息和技术栈
3. **📍 [模块位置索引.md](./模块位置索引.md)** - 快速定位各模块文件位置
4. **📏 [开发规范与最佳实践.md](./开发规范与最佳实践.md)** - 遵循项目编码规范
5. **🚀 [快速上手指南.md](./快速上手指南.md)** - 本文档，开发实战指南

## ⚡ 5分钟快速启动

### 1. 环境检查
确保以下环境已安装：
- ✅ Java 8+
- ✅ Node.js 14+
- ✅ MySQL 8.0
- ✅ Redis (可选)
- ✅ Maven 3.6+

### 2. 项目启动

#### 后端启动 (端口8081)
```bash
cd backend/main
mvn spring-boot:run
```

#### 前端启动 (端口8080)
```bash
npm install
npm run serve
```

### 3. 验证启动
- 🌐 前端访问: http://localhost:8080
- 🔧 后端API: http://localhost:8081/api
- 📊 数据库连接正常

## 🎯 常见开发任务

### 任务1: 添加新的API接口

#### 场景：为商品模块添加批量删除接口

**步骤1: 创建Controller方法**
```java
// 文件位置: backend/main/src/main/java/com/agriculture/mall/controller/ProductController.java

@DeleteMapping("/batch")
public Result<Void> batchDeleteProducts(@RequestBody List<Long> ids) {
    try {
        productService.batchDeleteProducts(ids);
        return Result.success();
    } catch (Exception e) {
        log.error("批量删除商品失败, ids: {}", ids, e);
        return Result.error("批量删除商品失败");
    }
}
```

**步骤2: 添加Service方法**
```java
// 文件位置: backend/main/src/main/java/com/agriculture/mall/service/ProductService.java
void batchDeleteProducts(List<Long> ids);

// 文件位置: backend/main/src/main/java/com/agriculture/mall/service/impl/ProductServiceImpl.java
@Override
@Transactional
public void batchDeleteProducts(List<Long> ids) {
    if (ids == null || ids.isEmpty()) {
        throw new BusinessException("删除的商品ID不能为空");
    }
    
    for (Long id : ids) {
        productMapper.deleteById(id);
    }
}
```

**步骤3: 前端API调用**
```javascript
// 文件位置: src/api/product.js
export function batchDeleteProducts(ids) {
  return request({
    url: '/api/mall/products/batch',
    method: 'delete',
    data: ids
  })
}

// 在组件中使用
import { batchDeleteProducts } from '@/api/product'

methods: {
  async handleBatchDelete() {
    try {
      await batchDeleteProducts(this.selectedIds)
      this.$message.success('删除成功')
      this.fetchProductList()
    } catch (error) {
      this.$message.error('删除失败')
    }
  }
}
```

### 任务2: 创建新的页面组件

#### 场景：创建商品统计页面

**步骤1: 创建页面组件**
```vue
<!-- 文件位置: src/views/ProductStats.vue -->
<template>
  <div class="product-stats">
    <el-card>
      <div slot="header">
        <span>商品统计</span>
      </div>
      
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ totalProducts }}</div>
            <div class="stat-label">总商品数</div>
          </div>
        </el-col>
        <!-- 更多统计项 -->
      </el-row>
      
      <div class="chart-container">
        <div id="productChart" style="height: 400px;"></div>
      </div>
    </el-card>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { getProductStats } from '@/api/product'

export default {
  name: 'ProductStats',
  data() {
    return {
      totalProducts: 0,
      chartData: []
    }
  },
  async created() {
    await this.fetchStats()
    this.initChart()
  },
  methods: {
    async fetchStats() {
      try {
        const response = await getProductStats()
        this.totalProducts = response.data.total
        this.chartData = response.data.chartData
      } catch (error) {
        this.$message.error('获取统计数据失败')
      }
    },
    
    initChart() {
      const chart = echarts.init(document.getElementById('productChart'))
      const option = {
        title: { text: '商品分类统计' },
        xAxis: { type: 'category', data: this.chartData.map(item => item.name) },
        yAxis: { type: 'value' },
        series: [{
          data: this.chartData.map(item => item.value),
          type: 'bar'
        }]
      }
      chart.setOption(option)
    }
  }
}
</script>

<style scoped lang="scss">
.product-stats {
  .stat-item {
    text-align: center;
    padding: 20px;
    
    .stat-value {
      font-size: 32px;
      font-weight: bold;
      color: #409EFF;
    }
    
    .stat-label {
      font-size: 14px;
      color: #666;
      margin-top: 8px;
    }
  }
  
  .chart-container {
    margin-top: 20px;
  }
}
</style>
```

**步骤2: 添加路由配置**
```javascript
// 文件位置: src/router/index.js
{
  path: '/product-stats',
  name: 'ProductStats',
  component: () => import('@/views/ProductStats.vue'),
  meta: {
    title: '商品统计',
    requiresAuth: true
  }
}
```

**步骤3: 添加导航菜单**
```vue
<!-- 在 src/components/Header.vue 或相应的导航组件中添加 -->
<el-menu-item index="/product-stats">
  <i class="el-icon-data-analysis"></i>
  <span>商品统计</span>
</el-menu-item>
```

### 任务3: 修复Bug

#### 场景：修复商品搜索功能的分页问题

**问题定位**
1. 检查前端搜索组件: `src/views/Shop.vue`
2. 检查后端搜索接口: `ProductController.searchProducts()`
3. 检查数据库查询: `ProductMapper.xml`

**修复步骤**
```javascript
// 前端修复 - src/views/Shop.vue
methods: {
  async handleSearch() {
    // 修复：搜索时重置页码
    this.currentPage = 1
    await this.fetchProducts()
  },
  
  async fetchProducts() {
    try {
      const params = {
        keyword: this.searchKeyword,
        categoryId: this.selectedCategory,
        page: this.currentPage,
        size: this.pageSize
      }
      
      const response = await getProductList(params)
      this.productList = response.data.list
      this.total = response.data.total
    } catch (error) {
      this.$message.error('获取商品列表失败')
    }
  }
}
```

```java
// 后端修复 - ProductServiceImpl.java
@Override
public PageResult<ProductVO> searchProducts(ProductSearchDTO searchDTO) {
    // 修复：确保分页参数正确
    if (searchDTO.getPage() <= 0) {
        searchDTO.setPage(1);
    }
    if (searchDTO.getSize() <= 0) {
        searchDTO.setSize(10);
    }
    
    // 计算偏移量
    int offset = (searchDTO.getPage() - 1) * searchDTO.getSize();
    searchDTO.setOffset(offset);
    
    List<Product> products = productMapper.searchProducts(searchDTO);
    int total = productMapper.countSearchProducts(searchDTO);
    
    return new PageResult<>(products.stream()
        .map(this::convertToVO)
        .collect(Collectors.toList()), total);
}
```

## 🔧 开发工具配置

### IDE配置建议

#### IntelliJ IDEA
1. **安装插件**:
   - Lombok
   - MyBatis Log Plugin
   - Vue.js
   - ESLint

2. **代码格式化**:
   - 导入项目的代码格式化配置
   - 启用保存时自动格式化

#### VS Code
1. **安装扩展**:
   - Vetur (Vue开发)
   - ESLint
   - Prettier
   - Auto Rename Tag

### 数据库工具
- **推荐**: Navicat, DataGrip, 或 MySQL Workbench
- **连接信息**: 参考 `application.yml` 配置

### API测试工具
- **推荐**: Postman, Insomnia, 或 IDEA HTTP Client
- **接口文档**: 访问 http://localhost:8081/swagger-ui.html (如果配置了Swagger)

## 🐛 常见问题解决

### 问题1: 后端启动失败

**症状**: 端口被占用或数据库连接失败

**解决方案**:
```bash
# 检查端口占用
netstat -ano | findstr :8081

# 杀死占用进程
taskkill /PID <进程ID> /F

# 检查数据库连接
mysql -u root -p
```

### 问题2: 前端编译错误

**症状**: ESLint错误或依赖缺失

**解决方案**:
```bash
# 重新安装依赖
rm -rf node_modules package-lock.json
npm install

# 修复ESLint错误
npm run lint --fix
```

### 问题3: 跨域问题

**症状**: 前端无法访问后端API

**解决方案**:
```javascript
// vue.config.js
module.exports = {
  devServer: {
    proxy: {
      '/api': {
        target: 'http://localhost:8081',
        changeOrigin: true
      }
    }
  }
}
```

## 📚 学习资源

### 技术文档
- **Spring Boot**: https://spring.io/projects/spring-boot
- **Vue.js**: https://vuejs.org/
- **Element UI**: https://element.eleme.io/
- **MyBatis**: https://mybatis.org/mybatis-3/

### 项目相关
- **数据库设计**: 查看 `数据库设计/` 目录
- **API接口**: 查看 `后端开发/` 目录
- **架构设计**: 查看 `架构文档/` 目录

## 🎯 开发技巧

### 1. 快速定位文件
使用IDE的全局搜索功能：
- **Ctrl+Shift+F**: 全局搜索代码
- **Ctrl+Shift+N**: 搜索文件名
- **Ctrl+N**: 搜索类名

### 2. 调试技巧

#### 后端调试
```java
// 使用日志调试
log.debug("查询参数: {}", params);
log.info("查询结果数量: {}", results.size());

// 使用断点调试
// 在IDE中设置断点，使用Debug模式启动
```

#### 前端调试
```javascript
// 使用console调试
console.log('API响应:', response)
console.table(this.productList)

// 使用Vue DevTools
// 安装浏览器扩展进行组件调试
```

### 3. 性能优化技巧

#### 数据库查询优化
```sql
-- 使用EXPLAIN分析查询
EXPLAIN SELECT * FROM products WHERE category_id = 1;

-- 添加合适的索引
CREATE INDEX idx_product_category ON products(category_id);
```

#### 前端性能优化
```javascript
// 使用计算属性缓存
computed: {
  filteredProducts() {
    return this.products.filter(product => 
      product.name.includes(this.searchKeyword)
    )
  }
}

// 使用防抖处理搜索
import { debounce } from 'lodash'

methods: {
  handleSearch: debounce(function() {
    this.fetchProducts()
  }, 300)
}
```

## 🚀 进阶开发

### 1. 添加新模块

当需要添加全新的业务模块时：

1. **创建包结构**
```
com.agriculture.newmodule/
├── controller/
├── service/
│   └── impl/
├── mapper/
├── entity/
└── dto/
```

2. **创建前端模块**
```
src/
├── views/newmodule/
├── components/newmodule/
└── api/newmodule.js
```

3. **更新路由和导航**

### 2. 集成第三方服务

#### 集成支付服务
```java
@Service
public class PaymentService {
    
    public PaymentResult processPayment(PaymentRequest request) {
        // 调用第三方支付API
        // 处理支付结果
        // 更新订单状态
    }
}
```

#### 集成消息推送
```javascript
// 前端WebSocket连接
const socket = new WebSocket('ws://localhost:8081/websocket')

socket.onmessage = function(event) {
  const message = JSON.parse(event.data)
  // 处理推送消息
}
```

## 📋 开发检查清单

### 开发前检查
- [ ] 阅读相关文档
- [ ] 理解业务需求
- [ ] 确认技术方案
- [ ] 准备开发环境

### 开发中检查
- [ ] 遵循编码规范
- [ ] 编写必要注释
- [ ] 处理异常情况
- [ ] 进行单元测试

### 开发后检查
- [ ] 功能测试通过
- [ ] 代码审查完成
- [ ] 文档已更新
- [ ] 性能测试通过

---

## 🎉 开始你的开发之旅！

现在你已经掌握了项目的基本信息和开发流程，可以开始高效的开发工作了！

**记住**:
- 📖 遇到问题先查文档
- 🔍 善用搜索和调试工具
- 📏 严格遵循编码规范
- 🧪 重视测试和代码质量
- 📝 及时更新文档

**祝你开发愉快！** 🚀

---

*如有疑问，请参考项目文档或联系项目维护者*