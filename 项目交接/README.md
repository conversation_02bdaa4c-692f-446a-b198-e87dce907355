# 智慧助农平台 (Smart Farmer Assistance Platform)

<div align="center">
  <img src="./src/assets/images/logo/logo-small.png" alt="智慧助农平台" width="200">
  
  [![Vue](https://img.shields.io/badge/Vue-2.6.11-4FC08D?style=flat-square&logo=vue.js)](https://vuejs.org/)
  [![Spring Boot](https://img.shields.io/badge/Spring%20Boot-2.7.0-6DB33F?style=flat-square&logo=spring-boot)](https://spring.io/projects/spring-boot)
  [![Python](https://img.shields.io/badge/Python-3.8+-3776AB?style=flat-square&logo=python)](https://www.python.org/)
  [![MySQL](https://img.shields.io/badge/MySQL-8.0-4479A1?style=flat-square&logo=mysql)](https://www.mysql.com/)
  [![License](https://img.shields.io/badge/License-MIT-blue?style=flat-square)](LICENSE)
</div>

## 📖 项目简介

智慧助农平台是一个综合性的农业服务平台，旨在通过现代化的信息技术手段，为农民提供全方位的农业生产、销售和管理服务。平台集成了农产品电商、价格预测、天气服务、农业百科、AI助手等多个功能模块，致力于提升农业生产效率，促进农产品流通，助力乡村振兴。

## 📚 项目交接文档导航

**🎯 新接手开发者（如Cursor）必读文档**：

### 📋 核心交接文档
1. **[Cursor交接清单.md](./Cursor交接清单.md)** - 🎯 **Cursor专用交接清单和快速上手指南**
2. **[项目状态总览.md](./项目状态总览.md)** - 项目整体状态和关键信息一览
3. **[项目交接文档.md](./项目交接文档.md)** - 项目基本信息、技术栈和开发环境
4. **[模块位置索引.md](./模块位置索引.md)** - 所有模块文件的精确位置索引
5. **[快速上手指南.md](./快速上手指南.md)** - 5分钟快速启动和常见开发任务
6. **[开发规范与最佳实践.md](./开发规范与最佳实践.md)** - 编码规范和开发流程
7. **[任务完成说明文档.md](./任务完成说明文档.md)** - 项目完成情况详细说明

### 🚀 建议阅读顺序（Cursor专用）
1. **[Cursor交接清单](./Cursor交接清单.md)** - 🎯 **Cursor必读！完整交接指南**
2. **首先阅读本README** - 了解项目整体概况
3. **[项目状态总览](./项目状态总览.md)** - 快速了解项目完成状态
4. **[快速上手指南](./快速上手指南.md)** - 快速启动项目
5. **[模块位置索引](./模块位置索引.md)** - 了解代码结构
6. **[项目交接文档](./项目交接文档.md)** - 深入了解技术细节
7. **[开发规范](./开发规范与最佳实践.md)** - 掌握开发标准

### 📊 项目完成状态
- **总体完成度**: ✅ 100%
- **所有8大核心模块**: ✅ 已完成
- **前后端代码**: ✅ 完整实现
- **数据库设计**: ✅ 完整建表
- **API接口**: ✅ 全部实现
- **交接文档**: ✅ 完整齐全

## ✨ 核心功能

### 🛒 农品汇电商模块
- **农产品展示与搜索**：多维度分类展示，智能搜索推荐
- **产品详情管理**：图文详情、产地信息、溯源链接
- **价格走势分析**：历史价格追踪、市场趋势预测
- **用户评价系统**：多维度评价、图片评价支持
- **供应商管理**：农户认证、产品上架管理
- **智能推荐**：基于用户行为的个性化推荐

### 🔍 农产品溯源系统
- **全链路追溯**：从种植到销售的完整记录
- **二维码查询**：扫码即可查看产品溯源信息
- **质量认证**：有机认证、绿色食品认证展示
- **生产记录**：种植过程、施肥记录、采收信息

### 📊 数据分析与预测
- **价格预测模型**：基于机器学习的价格走势预测
- **市场分析报告**：农产品市场趋势分析
- **销售数据统计**：多维度销售数据可视化
- **用户行为分析**：用户偏好和购买模式分析

### 🌤️ 智能农业服务
- **天气预报服务**：精准的农业气象服务
- **农业百科**：作物种植技术、病虫害防治
- **AI智能助手**：农业问题智能问答
- **农事提醒**：基于天气和季节的农事建议

### 👤 用户管理系统
- **多角色权限**：普通用户、农户、管理员
- **个人中心**：订单管理、收藏夹、地址管理
- **消息通知**：订单状态、价格提醒、农事通知
- **积分系统**：购买积分、评价奖励

## 🏗️ 技术架构

### 前端技术栈
- **框架**: Vue.js 2.6.11
- **UI组件库**: Element UI 2.15.14
- **状态管理**: Vuex 3.4.0
- **路由管理**: Vue Router 3.2.0
- **HTTP客户端**: Axios 1.8.4
- **图表库**: ECharts 4.9.0
- **动画库**: Animate.css 4.1.1
- **构建工具**: Vue CLI 4.5.0
- **样式预处理**: Sass
- **代码规范**: ESLint + Prettier

### 后端技术栈
- **框架**: Spring Boot 2.7.0
- **数据库**: MySQL 8.0
- **ORM框架**: MyBatis-Plus 3.5.2 + Spring Data JPA
- **安全框架**: Spring Security + JWT
- **API文档**: Swagger 2.9.2
- **工具库**: Hutool 5.8.0
- **Java版本**: JDK 11
- **构建工具**: Maven

### Python数据服务
- **Web框架**: Flask 2.0.1
- **数据处理**: Pandas 2.0.0 + NumPy 1.26.0
- **机器学习**: Scikit-learn 1.3.0 + PyTorch 2.0.0
- **数据分析**: Statsmodels 0.14.0
- **爬虫框架**: Scrapy 2.11.0
- **图像处理**: Pillow 10.0.0
- **数据库连接**: MySQL Connector Python 8.0.0

### 基础设施
- **数据库**: MySQL 8.0
- **Web服务器**: Nginx
- **反向代理**: Nginx配置
- **文件存储**: 本地文件系统
- **API网关**: Spring Boot内置

## 📁 项目结构

```
SFAP/
├── frontend/                    # 前端Vue.js应用
│   ├── src/
│   │   ├── api/                # API接口封装
│   │   ├── assets/             # 静态资源
│   │   ├── components/         # 公共组件
│   │   ├── views/              # 页面组件
│   │   ├── router/             # 路由配置
│   │   ├── store/              # Vuex状态管理
│   │   ├── utils/              # 工具函数
│   │   └── styles/             # 样式文件
│   ├── public/                 # 公共资源
│   ├── package.json            # 前端依赖配置
│   └── vue.config.js           # Vue CLI配置
├── backend/
│   ├── main/                   # Spring Boot主服务
│   │   ├── src/main/java/com/agriculture/
│   │   │   ├── controller/     # 控制层
│   │   │   ├── service/        # 业务层
│   │   │   ├── mapper/         # 数据访问层
│   │   │   ├── entity/         # 实体类
│   │   │   ├── dto/            # 数据传输对象
│   │   │   ├── vo/             # 视图对象
│   │   │   ├── config/         # 配置类
│   │   │   └── exception/      # 异常处理
│   │   ├── src/main/resources/
│   │   │   ├── mapper/         # MyBatis XML映射
│   │   │   ├── db/             # 数据库脚本
│   │   │   └── application.yml # 应用配置
│   │   └── pom.xml             # Maven依赖配置
│   └── python/                 # Python数据分析服务
│       ├── app.py              # Flask应用入口
│       ├── crawler/            # 数据爬虫模块
│       ├── requirements.txt    # Python依赖
│       └── db_manager.py       # 数据库管理
├── docs/                       # 项目文档
│   ├── 架构文档/               # 系统架构设计
│   ├── 数据库设计/             # 数据库设计文档
│   ├── 后端开发/               # 后端API文档
│   └── 任务/                   # 项目任务规划
├── scripts/                    # 部署脚本
├── nginx.conf                  # Nginx配置
└── README.md                   # 项目说明文档
```

## 🚀 快速开始

### 环境要求
- **Node.js**: >= 14.0.0
- **Java**: JDK 11+
- **Python**: >= 3.8
- **MySQL**: >= 8.0
- **Maven**: >= 3.6.0
- **Git**: 最新版本

### 1. 克隆项目
```bash
git clone <repository-url>
cd SFAP
```

### 2. 数据库初始化
```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE agriculture_mall CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 导入初始化脚本
mysql -u root -p agriculture_mall < backend/main/src/main/resources/db/complete_init.sql
```

### 3. 后端服务启动
```bash
# 进入后端目录
cd backend/main

# 修改数据库配置
# 编辑 src/main/resources/application.yml
# 更新数据库连接信息

# 编译并启动
mvn clean install
mvn spring-boot:run

# 服务将在 http://localhost:8081 启动
```

### 4. Python数据服务启动
```bash
# 进入Python服务目录
cd backend/python

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt

# 启动服务
python app.py

# 服务将在 http://localhost:5000 启动
```

### 5. 前端应用启动
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run serve

# 应用将在 http://localhost:8080 启动
```

### 6. 环境变量配置
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
# 配置API密钥、数据库连接等信息
```

## 🔧 开发指南

### 前端开发
```bash
# 开发模式启动
npm run serve

# 代码检查
npm run lint

# 构建生产版本
npm run build
```

### 后端开发
```bash
# 启动开发模式
mvn spring-boot:run -Dspring-boot.run.profiles=dev

# 运行测试
mvn test

# 打包应用
mvn clean package
```

### Python服务开发
```bash
# 启动开发模式
FLASK_ENV=development python app.py

# 运行爬虫
python crawler/main.py
```

## 📊 API文档

### 接口基础信息
- **Base URL**: `http://localhost:8081/api/v1`
- **认证方式**: JWT Token
- **数据格式**: JSON
- **字符编码**: UTF-8

### 主要接口模块

#### 用户认证
- `POST /auth/login` - 用户登录
- `POST /auth/register` - 用户注册
- `POST /auth/logout` - 用户登出
- `GET /auth/profile` - 获取用户信息

#### 农产品管理
- `GET /products` - 获取产品列表
- `GET /products/{id}` - 获取产品详情
- `POST /products` - 创建产品
- `PUT /products/{id}` - 更新产品
- `DELETE /products/{id}` - 删除产品

#### 订单管理
- `GET /orders` - 获取订单列表
- `POST /orders` - 创建订单
- `GET /orders/{id}` - 获取订单详情
- `PUT /orders/{id}/status` - 更新订单状态

#### 价格分析
- `GET /prices/history/{productId}` - 获取价格历史
- `GET /prices/forecast/{productId}` - 获取价格预测
- `GET /prices/indices` - 获取价格指数

### Swagger文档
启动后端服务后，访问 `http://localhost:8081/swagger-ui.html` 查看完整API文档。

## 🗄️ 数据库设计

### 核心数据表

#### 用户相关
- `users` - 用户基础信息
- `user_profiles` - 用户详细资料
- `addresses` - 用户地址信息

#### 产品相关
- `products` - 农产品信息
- `categories` - 产品分类
- `product_images` - 产品图片
- `product_prices` - 价格历史

#### 交易相关
- `orders` - 订单信息
- `order_items` - 订单明细
- `cart_items` - 购物车
- `reviews` - 用户评价

#### 内容相关
- `news` - 农业资讯
- `encyclopedia` - 农业百科
- `banners` - 轮播图

详细的数据库设计文档请参考 `docs/数据库设计/` 目录。

## 🎨 UI设计规范

### 设计原则
- **一致性**: 保持视觉和交互的一致性
- **简洁性**: 减少视觉噪音，突出核心功能
- **响应性**: 适配各种设备尺寸
- **可访问性**: 确保所有用户都能便捷使用

### 色彩系统
- **主色调**: #409EFF (智慧蓝)
- **成功色**: #67C23A (生机绿)
- **警告色**: #E6A23C (提醒橙)
- **危险色**: #F56C6C (警示红)
- **信息色**: #909399 (中性灰)

### 组件库
基于Element UI进行定制化开发，保持农业主题的视觉风格。

## 🔒 安全措施

### 认证授权
- JWT Token认证机制
- 基于角色的权限控制(RBAC)
- 接口访问频率限制
- 敏感操作二次验证

### 数据安全
- 数据库连接加密
- 敏感信息脱敏处理
- SQL注入防护
- XSS攻击防护

### 系统安全
- HTTPS传输加密
- CORS跨域配置
- 文件上传安全检查
- 日志审计机制

## 📈 性能优化

### 前端优化
- 路由懒加载
- 图片懒加载
- 组件按需引入
- 静态资源CDN加速
- Gzip压缩

### 后端优化
- 数据库连接池
- Redis缓存机制
- 分页查询优化
- 数据库索引优化
- 异步任务处理

### 系统优化
- Nginx反向代理
- 负载均衡配置
- 数据库读写分离
- 微服务架构设计

## 🚀 部署指南

### 开发环境部署
```bash
# 1. 启动数据库服务
sudo systemctl start mysql

# 2. 启动后端服务
cd backend/main
mvn spring-boot:run

# 3. 启动Python服务
cd backend/python
python app.py

# 4. 启动前端服务
npm run serve
```

### 生产环境部署
```bash
# 1. 构建前端应用
npm run build

# 2. 打包后端应用
mvn clean package

# 3. 配置Nginx
sudo cp nginx.conf /etc/nginx/sites-available/agriculture
sudo ln -s /etc/nginx/sites-available/agriculture /etc/nginx/sites-enabled/
sudo systemctl reload nginx

# 4. 启动应用服务
java -jar target/agriculture-mall-1.0.0.jar
```

### Docker部署
```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d
```

## 🧪 测试

### 单元测试
```bash
# 后端测试
mvn test

# 前端测试
npm run test:unit
```

### 集成测试
```bash
# API测试
npm run test:api

# E2E测试
npm run test:e2e
```

### 性能测试
```bash
# 压力测试
ab -n 1000 -c 10 http://localhost:8081/api/v1/products
```

## 📝 更新日志

### v1.0.0 (2024-01-15)
- ✨ 初始版本发布
- 🛒 农品汇电商模块
- 🔍 农产品溯源系统
- 📊 数据分析与预测
- 🌤️ 智能农业服务
- 👤 用户管理系统

## 🤝 贡献指南

### 开发流程
1. Fork 项目到个人仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

### 代码规范
- 遵循ESLint配置的代码规范
- 使用Prettier进行代码格式化
- 编写清晰的提交信息
- 添加必要的单元测试

### 问题反馈
- 使用GitHub Issues报告问题
- 提供详细的问题描述和复现步骤
- 标注问题的优先级和类型

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 👥 开发团队

- **项目负责人**: [团队负责人]
- **前端开发**: [前端开发者]
- **后端开发**: [后端开发者]
- **数据分析**: [数据分析师]
- **UI设计**: [UI设计师]

## 📞 联系我们

- **项目地址**: [GitHub Repository]
- **问题反馈**: [GitHub Issues]
- **邮箱**: <EMAIL>
- **官网**: https://agriculture-platform.example.com

## 🙏 致谢

感谢所有为本项目做出贡献的开发者和用户，特别感谢以下开源项目：

- [Vue.js](https://vuejs.org/) - 渐进式JavaScript框架
- [Element UI](https://element.eleme.io/) - 基于Vue的组件库
- [Spring Boot](https://spring.io/projects/spring-boot) - Java应用开发框架
- [MyBatis-Plus](https://baomidou.com/) - MyBatis增强工具
- [ECharts](https://echarts.apache.org/) - 数据可视化图表库

---

<div align="center">
  <p>🌾 助力农业现代化，共建智慧农业生态 🌾</p>
  <p>Made with ❤️ by Agriculture Platform Team</p>
</div>