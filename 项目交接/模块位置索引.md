# 智慧农业平台模块位置索引

> 本文档提供项目中所有模块的精确文件位置，便于快速定位和开发

## 🏪 农品汇商城模块

### 后端文件位置

#### Controllers (控制器层)
```
backend/main/src/main/java/com/agriculture/mall/controller/
├── ProductController.java          # 商品管理API
├── CategoryController.java         # 分类管理API  
├── CartController.java            # 购物车API
├── OrderController.java           # 订单管理API
├── ReviewController.java          # 评价系统API
├── FavoriteController.java        # 收藏功能API
├── UserController.java            # 用户管理API
└── AddressController.java         # 地址管理API
```

#### Services (服务层)
```
backend/main/src/main/java/com/agriculture/mall/service/
├── ProductService.java
├── CategoryService.java
├── CartService.java
├── OrderService.java
├── ReviewService.java
├── FavoriteService.java
├── UserService.java
└── impl/
    ├── ProductServiceImpl.java
    ├── CategoryServiceImpl.java
    ├── CartServiceImpl.java
    ├── OrderServiceImpl.java
    ├── ReviewServiceImpl.java
    ├── FavoriteServiceImpl.java
    └── UserServiceImpl.java
```

#### Mappers (数据访问层)
```
backend/main/src/main/java/com/agriculture/mall/mapper/
├── ProductMapper.java
├── CategoryMapper.java
├── CartItemMapper.java
├── OrderMapper.java
├── OrderItemMapper.java
├── ReviewMapper.java
├── FavoriteMapper.java
└── UserMapper.java

backend/main/src/main/resources/mapper/
├── ProductMapper.xml
├── CategoryMapper.xml
├── CartItemMapper.xml
├── OrderMapper.xml
├── OrderItemMapper.xml
├── ReviewMapper.xml
├── FavoriteMapper.xml
└── UserMapper.xml
```

#### Entities (实体类)
```
backend/main/src/main/java/com/agriculture/mall/entity/
├── Product.java
├── Category.java
├── CartItem.java
├── Order.java
├── OrderItem.java
├── Review.java
├── Favorite.java
├── User.java
└── Address.java
```

### 前端文件位置

#### 页面组件
```
src/views/
├── Home.vue                       # 首页
├── Shop.vue                       # 商品列表页
├── ProductDetail.vue              # 商品详情页
├── Cart.vue                       # 购物车页面
├── Order.vue                      # 订单页面
├── Profile.vue                    # 用户中心
└── Login.vue                      # 登录页面
```

#### 通用组件
```
src/components/
├── Header.vue                     # 页面头部
├── ProductCard.vue                # 商品卡片
├── CategoryNav.vue                # 分类导航
├── ReviewList.vue                 # 评价列表
├── OrderStatus.vue                # 订单状态
├── AddressForm.vue                # 地址表单
└── SellerApplication.vue          # 商家申请
```

## 🤖 推荐系统模块

### 后端文件位置

#### 推荐算法
```
backend/main/src/main/java/com/agriculture/mall/recommendation/
├── ContentBasedRecommendation.java    # 基于内容的推荐
├── ItemBasedCF.java                   # 基于物品的协同过滤
└── UserBasedCF.java                   # 基于用户的协同过滤
```

#### 推荐服务
```
backend/main/src/main/java/com/agriculture/mall/service/
├── ProductRecommendationService.java
├── UserBehaviorService.java
└── impl/
    ├── ProductRecommendationServiceImpl.java
    └── UserBehaviorServiceImpl.java
```

#### 推荐控制器
```
backend/main/src/main/java/com/agriculture/mall/controller/
└── ProductRecommendationController.java
```

#### 推荐相关实体
```
backend/main/src/main/java/com/agriculture/mall/entity/
├── UserBehavior.java
└── ProductRecommendation.java

backend/main/src/main/java/com/agriculture/mall/mapper/
├── UserBehaviorMapper.java
└── ProductRecommendationMapper.java
```

### 前端文件位置
```
src/components/
├── RecommendedProducts.vue        # 推荐商品展示
└── PersonalizedRecommendation.vue # 个性化推荐
```

## 🔍 溯源系统模块

### 后端文件位置
```
backend/main/src/main/java/com/agriculture/traceability/
├── controller/
│   └── TraceabilityController.java
├── service/
│   ├── TraceabilityService.java
│   └── impl/
│       └── TraceabilityServiceImpl.java
├── entity/
│   ├── TraceabilityRecord.java
│   └── TraceabilityEvent.java
└── mapper/
    └── TraceabilityMapper.java
```

### 前端文件位置

#### 溯源页面
```
src/views/
└── Traceability.vue               # 溯源查询页面
```

#### 溯源组件
```
src/components/traceability/
├── TraceabilityCenter.vue         # 溯源中心
├── TraceabilitySteps.vue          # 溯源步骤
├── TraceabilityCharts.vue         # 溯源图表
├── TraceabilityReports.vue        # 溯源报告
├── TraceabilitySearch.vue         # 溯源查询
├── TraceabilityShare.vue          # 溯源分享
├── ProductInfo.vue                # 产品信息
└── LoadingState.vue               # 加载状态
```

## 📚 农业百科模块

### 后端文件位置
```
backend/main/src/main/java/com/agriculture/encyclopedia/
├── controller/
│   ├── EncyclopediaController.java
│   └── EncyclopediaCommentController.java
├── service/
│   ├── EncyclopediaService.java
│   ├── EncyclopediaCommentService.java
│   └── impl/
│       ├── EncyclopediaServiceImpl.java
│       └── EncyclopediaCommentServiceImpl.java
├── entity/
│   ├── Encyclopedia.java
│   ├── EncyclopediaCategory.java
│   └── EncyclopediaComment.java
└── mapper/
    ├── EncyclopediaMapper.java
    └── EncyclopediaCommentMapper.java
```

### 前端文件位置
```
src/views/
├── Encyclopedia.vue                # 农业百科主页
└── EncyclopediaDetail.vue         # 百科文章详情页
```

## 🌤️ 天气预警模块

### 后端文件位置
```
backend/main/src/main/java/com/agriculture/weather/
├── controller/
│   └── WeatherAlertController.java
├── service/
│   ├── WeatherAlertService.java
│   └── impl/
│       └── WeatherAlertServiceImpl.java
├── entity/
│   └── WeatherAlert.java
└── model/
    └── WeatherData.java
```

### 前端文件位置
```
src/views/
└── Weather.vue                    # 天气信息页面

src/components/
├── WeatherDetail.vue              # 天气详情
└── WeatherNotification.vue        # 天气通知
```

## 💰 价格预测模块

### 后端文件位置
```
backend/main/src/main/java/com/agriculture/price/
├── controller/
│   ├── PriceForecastController.java
│   └── PriceIndexController.java
├── service/
│   ├── PriceForecastService.java
│   └── impl/
│       └── PriceForecastServiceImpl.java
├── entity/
│   ├── PriceForecast.java
│   └── PriceIndex.java
└── model/
    └── PriceForecastModel.java
```

### 前端文件位置
```
src/views/
├── Price.vue                      # 价格信息页面
└── PriceIndices.vue               # 价格指数页面

src/components/
└── PriceTrendChart.vue            # 价格趋势图表
```

## 📰 新闻资讯模块

### 后端文件位置
```
backend/main/src/main/java/com/agriculture/news/
├── controller/
│   └── NewsController.java
├── service/
│   ├── NewsService.java
│   └── impl/
│       └── NewsServiceImpl.java
├── entity/
│   ├── News.java
│   └── NewsCategory.java
└── mapper/
    └── NewsMapper.java
```

### 前端文件位置
```
src/views/
├── News.vue                       # 新闻列表页面
└── NewsDetail.vue                 # 新闻详情页面
```

## 🤖 AI智能助手模块

### 后端文件位置
```
backend/main/src/main/java/com/agriculture/ai/
├── controller/
│   └── AIController.java
├── service/
│   ├── AIService.java
│   └── impl/
│       └── AIServiceImpl.java
└── entity/
    └── AIConversation.java
```

### 前端文件位置
```
src/views/
└── AIAssistant.vue                # AI助手页面
```

## 🔧 通用模块

### 后端通用组件
```
backend/main/src/main/java/com/agriculture/
├── common/                        # 通用工具类
│   ├── Result.java               # 统一响应结果
│   ├── PageResult.java           # 分页结果
│   └── Constants.java            # 常量定义
├── config/                       # 配置类
│   ├── WebConfig.java           # Web配置
│   ├── SecurityConfig.java      # 安全配置
│   └── RedisConfig.java         # Redis配置
├── exception/                    # 异常处理
│   ├── GlobalExceptionHandler.java
│   └── BusinessException.java
├── filter/                       # 过滤器
│   └── JwtAuthenticationFilter.java
├── security/                     # 安全相关
│   ├── JwtTokenUtil.java
│   └── UserDetailsServiceImpl.java
└── util/                         # 工具类
    ├── DateUtil.java
    ├── StringUtil.java
    └── FileUtil.java
```

### 前端通用组件
```
src/
├── api/                          # API接口定义
│   ├── product.js
│   ├── user.js
│   ├── order.js
│   └── common.js
├── assets/                       # 静态资源
│   ├── images/
│   └── styles/
├── router/                       # 路由配置
│   └── index.js
├── store/                        # Vuex状态管理
│   ├── index.js
│   └── modules/
│       ├── user.js
│       ├── cart.js
│       └── product.js
├── utils/                        # 工具函数
│   ├── request.js               # Axios封装
│   ├── auth.js                  # 认证工具
│   └── common.js                # 通用工具
└── styles/                       # 全局样式
    ├── global.scss
    └── variables.scss
```

## 📄 配置文件位置

### 后端配置
```
backend/main/src/main/resources/
├── application.yml               # 主配置文件
├── application-dev.yml           # 开发环境配置
├── application-prod.yml          # 生产环境配置
├── mapper/                       # MyBatis映射文件
└── static/                       # 静态资源
```

### 前端配置
```
SFAP/
├── package.json                  # 依赖配置
├── vue.config.js                 # Vue CLI配置
├── .eslintrc.js                  # ESLint配置
├── .prettierrc                   # Prettier配置
├── .env                          # 环境变量
├── .env.development              # 开发环境变量
└── .env.production               # 生产环境变量
```

## 🗄️ 数据库相关

### SQL脚本位置
```
SFAP/
├── product_sql_inserts.sql       # 商品数据插入脚本
├── product_stats.sql             # 商品统计脚本
└── 数据库设计/                   # 数据库设计文档目录
    ├── 农品汇模块数据库设计.md
    ├── 溯源模块数据库设计.md
    └── 推荐系统数据库设计.md
```

## 📖 文档位置

### 项目文档
```
SFAP/
├── README.md                     # 项目说明
├── 任务完成说明文档.md            # 完成情况总览
├── 项目交接文档.md                # 项目交接说明
├── 模块位置索引.md                # 本文档
├── 任务/                         # 开发任务文档
├── 后端开发/                     # 后端开发文档
├── 数据库设计/                   # 数据库设计文档
└── 架构文档/                     # 系统架构文档
```

---

**使用说明**: 
- 使用 Ctrl+F 快速搜索模块名称或文件名
- 所有路径均为相对于项目根目录的路径
- 如需添加新功能，请参考对应模块的文件结构
- 修改文件后请及时更新本索引文档