# 智慧农业平台开发规范与最佳实践

> 本文档定义了项目的编码标准、开发流程和最佳实践，确保代码质量和团队协作效率

## 🎯 核心开发原则

### 1. 代码质量第一
- **零编译错误**: 任何代码提交前必须确保无编译错误
- **ESLint规范**: 严格遵循ESLint配置，不允许有语法警告
- **代码审查**: 重要功能修改需要代码审查
- **测试覆盖**: 核心业务逻辑需要单元测试

### 2. 架构一致性
- **分层架构**: 严格遵循Controller → Service → Mapper的三层架构
- **模块化设计**: 功能模块间保持低耦合高内聚
- **接口规范**: 统一的API接口设计和响应格式
- **异常处理**: 完善的异常处理和错误日志

### 3. 性能优化
- **数据库优化**: 合理使用索引，避免N+1查询
- **缓存策略**: 热点数据使用Redis缓存
- **前端优化**: 组件懒加载，代码分割
- **资源压缩**: 静态资源压缩和CDN使用

## 🔧 技术栈规范

### 后端开发规范 (Spring Boot)

#### 1. 项目结构规范
```
com.agriculture.{module}/
├── controller/          # 控制器层
├── service/            # 服务层
│   └── impl/          # 服务实现
├── mapper/            # 数据访问层
├── entity/            # 实体类
├── dto/               # 数据传输对象
├── vo/                # 视图对象
└── config/            # 配置类
```

#### 2. 命名规范
- **类名**: PascalCase (如 `ProductController`)
- **方法名**: camelCase (如 `getProductById`)
- **常量**: UPPER_SNAKE_CASE (如 `MAX_PAGE_SIZE`)
- **包名**: 全小写，用点分隔 (如 `com.agriculture.mall`)

#### 3. Controller层规范
```java
@RestController
@RequestMapping("/api/mall/products")
@Slf4j
public class ProductController {
    
    @Autowired
    private ProductService productService;
    
    @GetMapping("/{id}")
    public Result<ProductVO> getProduct(@PathVariable Long id) {
        try {
            ProductVO product = productService.getProductById(id);
            return Result.success(product);
        } catch (Exception e) {
            log.error("获取商品失败, id: {}", id, e);
            return Result.error("获取商品失败");
        }
    }
}
```

#### 4. Service层规范
```java
@Service
@Transactional
@Slf4j
public class ProductServiceImpl implements ProductService {
    
    @Autowired
    private ProductMapper productMapper;
    
    @Override
    public ProductVO getProductById(Long id) {
        if (id == null || id <= 0) {
            throw new BusinessException("商品ID不能为空");
        }
        
        Product product = productMapper.selectById(id);
        if (product == null) {
            throw new BusinessException("商品不存在");
        }
        
        return convertToVO(product);
    }
}
```

#### 5. 数据库操作规范
- **使用MyBatis**: 所有数据库操作通过MyBatis进行
- **参数校验**: 所有输入参数必须校验
- **事务管理**: 涉及多表操作使用@Transactional
- **SQL优化**: 避免SELECT *，使用具体字段

### 前端开发规范 (Vue.js)

#### 1. 组件结构规范
```vue
<template>
  <div class="component-name">
    <!-- 模板内容 -->
  </div>
</template>

<script>
export default {
  name: 'ComponentName',
  components: {
    // 子组件
  },
  props: {
    // 属性定义
  },
  data() {
    return {
      // 数据定义
    }
  },
  computed: {
    // 计算属性
  },
  watch: {
    // 监听器
  },
  created() {
    // 生命周期钩子
  },
  methods: {
    // 方法定义
  }
}
</script>

<style scoped lang="scss">
.component-name {
  // 样式定义
}
</style>
```

#### 2. 命名规范
- **组件名**: PascalCase (如 `ProductCard.vue`)
- **文件名**: PascalCase (如 `ProductDetail.vue`)
- **变量名**: camelCase (如 `productList`)
- **CSS类名**: kebab-case (如 `.product-card`)

#### 3. 组件开发规范
```vue
<template>
  <div class="product-card">
    <el-card :body-style="{ padding: '0px' }">
      <img :src="product.image" class="image">
      <div style="padding: 14px;">
        <span>{{ product.name }}</span>
        <div class="bottom clearfix">
          <time class="time">{{ product.createTime }}</time>
          <el-button type="text" class="button" @click="handleClick">查看详情</el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'ProductCard',
  props: {
    product: {
      type: Object,
      required: true,
      default: () => ({})
    }
  },
  methods: {
    handleClick() {
      this.$emit('click', this.product)
    }
  }
}
</script>
```

#### 4. API调用规范
```javascript
// api/product.js
import request from '@/utils/request'

export function getProductList(params) {
  return request({
    url: '/api/mall/products',
    method: 'get',
    params
  })
}

export function getProductDetail(id) {
  return request({
    url: `/api/mall/products/${id}`,
    method: 'get'
  })
}

// 在组件中使用
import { getProductList } from '@/api/product'

export default {
  data() {
    return {
      productList: [],
      loading: false
    }
  },
  async created() {
    await this.fetchProductList()
  },
  methods: {
    async fetchProductList() {
      try {
        this.loading = true
        const response = await getProductList()
        this.productList = response.data
      } catch (error) {
        this.$message.error('获取商品列表失败')
        console.error('获取商品列表失败:', error)
      } finally {
        this.loading = false
      }
    }
  }
}
```

## 📋 开发流程规范

### 1. 新功能开发流程

#### 步骤1: 需求分析
- 明确功能需求和业务逻辑
- 设计API接口和数据结构
- 确定前端页面和组件设计

#### 步骤2: 后端开发
1. **创建实体类** (Entity)
2. **创建Mapper接口和XML** (数据访问层)
3. **创建Service接口和实现** (业务逻辑层)
4. **创建Controller** (控制器层)
5. **编写单元测试**

#### 步骤3: 前端开发
1. **创建API接口文件**
2. **创建页面组件**
3. **创建通用组件**
4. **配置路由**
5. **集成测试**

#### 步骤4: 联调测试
1. **前后端接口联调**
2. **功能测试**
3. **性能测试**
4. **用户体验测试**

### 2. Bug修复流程

1. **问题定位**: 确定问题所在的模块和文件
2. **影响分析**: 评估修复对其他功能的影响
3. **修复实现**: 编写修复代码
4. **测试验证**: 验证修复效果
5. **回归测试**: 确保不影响其他功能

### 3. 代码审查流程

#### 审查要点
- **功能正确性**: 代码是否实现了预期功能
- **代码质量**: 是否遵循编码规范
- **性能考虑**: 是否存在性能问题
- **安全性**: 是否存在安全漏洞
- **可维护性**: 代码是否易于理解和维护

## 🔒 安全规范

### 1. 后端安全
- **输入验证**: 所有用户输入必须验证
- **SQL注入防护**: 使用参数化查询
- **XSS防护**: 输出数据进行HTML转义
- **认证授权**: 使用JWT进行身份认证
- **敏感信息**: 不在日志中记录敏感信息

### 2. 前端安全
- **输入验证**: 前端也要进行输入验证
- **XSS防护**: 避免使用v-html插入用户内容
- **CSRF防护**: 使用CSRF Token
- **敏感信息**: 不在前端存储敏感信息

## 📊 性能优化规范

### 1. 后端性能优化

#### 数据库优化
```sql
-- 好的做法：使用索引
CREATE INDEX idx_product_category ON products(category_id);

-- 好的做法：避免SELECT *
SELECT id, name, price FROM products WHERE category_id = ?;

-- 避免：N+1查询
-- 使用JOIN或批量查询替代
```

#### 缓存策略
```java
@Service
public class ProductServiceImpl implements ProductService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    public ProductVO getProductById(Long id) {
        String key = "product:" + id;
        ProductVO cached = (ProductVO) redisTemplate.opsForValue().get(key);
        
        if (cached != null) {
            return cached;
        }
        
        ProductVO product = // 从数据库查询
        redisTemplate.opsForValue().set(key, product, 30, TimeUnit.MINUTES);
        
        return product;
    }
}
```

### 2. 前端性能优化

#### 组件懒加载
```javascript
// router/index.js
const routes = [
  {
    path: '/product',
    name: 'Product',
    component: () => import('@/views/Product.vue')
  }
]
```

#### 图片懒加载
```vue
<template>
  <img v-lazy="product.image" :alt="product.name">
</template>
```

## 🧪 测试规范

### 1. 后端测试

#### 单元测试
```java
@SpringBootTest
class ProductServiceTest {
    
    @Autowired
    private ProductService productService;
    
    @Test
    void testGetProductById() {
        // Given
        Long productId = 1L;
        
        // When
        ProductVO result = productService.getProductById(productId);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(productId);
    }
}
```

#### 集成测试
```java
@SpringBootTest
@AutoConfigureTestDatabase
class ProductControllerIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Test
    void testGetProduct() {
        ResponseEntity<Result> response = restTemplate.getForEntity(
            "/api/mall/products/1", Result.class);
        
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
    }
}
```

### 2. 前端测试

#### 组件测试
```javascript
import { shallowMount } from '@vue/test-utils'
import ProductCard from '@/components/ProductCard.vue'

describe('ProductCard.vue', () => {
  it('renders product name', () => {
    const product = { name: 'Test Product' }
    const wrapper = shallowMount(ProductCard, {
      propsData: { product }
    })
    expect(wrapper.text()).toMatch('Test Product')
  })
})
```

## 📝 文档规范

### 1. 代码注释

#### Java注释
```java
/**
 * 商品服务实现类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Service
public class ProductServiceImpl implements ProductService {
    
    /**
     * 根据ID获取商品信息
     * 
     * @param id 商品ID
     * @return 商品视图对象
     * @throws BusinessException 当商品不存在时抛出
     */
    @Override
    public ProductVO getProductById(Long id) {
        // 实现逻辑
    }
}
```

#### JavaScript注释
```javascript
/**
 * 商品卡片组件
 * 用于展示商品基本信息
 */
export default {
  name: 'ProductCard',
  props: {
    /**
     * 商品信息对象
     * @type {Object}
     */
    product: {
      type: Object,
      required: true
    }
  },
  methods: {
    /**
     * 处理点击事件
     * 向父组件发送点击事件
     */
    handleClick() {
      this.$emit('click', this.product)
    }
  }
}
```

### 2. API文档

#### 接口文档格式
```markdown
## 获取商品详情

### 请求信息
- **URL**: `/api/mall/products/{id}`
- **方法**: GET
- **认证**: 需要

### 路径参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id     | Long | 是   | 商品ID |

### 响应示例
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "name": "有机苹果",
    "price": 29.90,
    "description": "新鲜有机苹果"
  }
}
```

### 错误码
| 错误码 | 说明 |
|--------|------|
| 404    | 商品不存在 |
| 500    | 服务器内部错误 |
```

## 🚀 部署规范

### 1. 环境配置

#### 开发环境
- **后端端口**: 8081
- **前端端口**: 8080
- **数据库**: 本地MySQL
- **缓存**: 本地Redis

#### 生产环境
- **负载均衡**: Nginx
- **应用服务**: Docker容器
- **数据库**: MySQL集群
- **缓存**: Redis集群

### 2. 构建部署

#### 后端构建
```bash
# 构建JAR包
mvn clean package -Dmaven.test.skip=true

# Docker构建
docker build -t agriculture-backend .
```

#### 前端构建
```bash
# 构建生产版本
npm run build

# Docker构建
docker build -t agriculture-frontend .
```

## 📋 检查清单

### 开发完成检查
- [ ] 代码无编译错误
- [ ] 通过ESLint检查
- [ ] 单元测试通过
- [ ] 集成测试通过
- [ ] API文档已更新
- [ ] 代码已审查
- [ ] 性能测试通过
- [ ] 安全检查通过

### 发布前检查
- [ ] 功能测试完成
- [ ] 性能测试通过
- [ ] 安全扫描通过
- [ ] 文档已更新
- [ ] 数据库迁移脚本准备
- [ ] 回滚方案准备
- [ ] 监控告警配置

---

**遵循本规范，确保项目代码质量和开发效率！**

*最后更新: 2024年12月*