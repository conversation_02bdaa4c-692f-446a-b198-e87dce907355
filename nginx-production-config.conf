# 农品汇生产环境Nginx配置
# 解决静态资源404错误和优化性能

# 前端服务配置 (端口8200)
server {
    listen 8200;
    server_name **************;
    
    # 前端静态文件根目录
    root /var/www/html/agriculture-frontend/dist;
    index index.html;
    
    # 前端路由配置 (Vue Router History模式)
    location / {
        try_files $uri $uri/ /index.html;
        
        # 缓存HTML文件
        location ~* \.html$ {
            expires 1h;
            add_header Cache-Control "public, must-revalidate";
        }
        
        # 缓存静态资源
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            access_log off;
        }
    }
    
    # API代理到后端
    location /api/ {
        proxy_pass http://127.0.0.1:8081;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # 错误页面
    error_page 404 /index.html;
    error_page 500 502 503 504 /50x.html;
    
    # 日志配置
    access_log /var/log/nginx/agriculture-frontend.access.log;
    error_log /var/log/nginx/agriculture-frontend.error.log;
}

# 后端服务配置 (端口8081)
server {
    listen 8081;
    server_name **************;
    
    # 后端应用代理
    location / {
        proxy_pass http://127.0.0.1:8080;  # 假设Spring Boot运行在8080端口
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # 支持WebSocket (如果需要)
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    
    # 静态资源配置 - 关键修复点
    location /static/ {
        alias /var/www/html/agriculture-backend/static/;
        
        # 图片文件缓存
        location ~* \.(jpg|jpeg|png|gif|ico|svg)$ {
            expires 30d;
            add_header Cache-Control "public, immutable";
            access_log off;
            
            # 图片不存在时返回默认图片
            try_files $uri /static/images/default-placeholder.jpg;
        }
        
        # 其他静态文件
        location ~* \.(css|js|pdf|doc|docx|xls|xlsx)$ {
            expires 7d;
            add_header Cache-Control "public";
            access_log off;
        }
        
        # 跨域配置
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type';
    }
    
    # 上传文件配置
    location /uploads/ {
        alias /var/www/html/agriculture-backend/uploads/;
        
        # 图片文件
        location ~* \.(jpg|jpeg|png|gif|ico|svg)$ {
            expires 30d;
            add_header Cache-Control "public, immutable";
            access_log off;
        }
        
        # 文档文件
        location ~* \.(pdf|doc|docx|xls|xlsx)$ {
            expires 7d;
            add_header Cache-Control "public";
        }
    }
    
    # 健康检查
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    # 日志配置
    access_log /var/log/nginx/agriculture-backend.access.log;
    error_log /var/log/nginx/agriculture-backend.error.log;
}

# 全局配置
http {
    # 基础配置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    
    # 文件上传大小限制
    client_max_body_size 50M;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';
    
    # 默认MIME类型
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
}

# 使用说明：
# 1. 将此配置保存为 /etc/nginx/sites-available/agriculture
# 2. 创建软链接：sudo ln -s /etc/nginx/sites-available/agriculture /etc/nginx/sites-enabled/
# 3. 测试配置：sudo nginx -t
# 4. 重载配置：sudo systemctl reload nginx
# 
# 目录结构要求：
# /var/www/html/agriculture-frontend/dist/     # 前端构建文件
# /var/www/html/agriculture-backend/static/    # 后端静态资源
# /var/www/html/agriculture-backend/uploads/   # 上传文件
#
# 确保目录权限：
# sudo chown -R www-data:www-data /var/www/html/agriculture-*
# sudo chmod -R 755 /var/www/html/agriculture-*
