# SFAP平台溯源模块技术实现方案

## 🏗️ 技术架构概览

### 整体架构
```
前端层 (Vue 2 + Element UI)
    ├── 用户查询界面
    ├── 销售者管理界面  
    └── 管理员审核界面
            ↕️ HTTP/HTTPS
后端层 (Spring Boot + MyBatis Plus)
    ├── Controller 层 (API接口)
    ├── Service 层 (业务逻辑)
    ├── Mapper 层 (数据访问)
    └── 工具层 (二维码生成、文件处理)
            ↕️ JDBC
数据层 (MySQL 8.0)
    ├── 溯源核心表 (7个表)
    ├── 业务关联表 (product, user)
    └── 系统配置表
```

## 💻 前端技术实现

### 1. 技术栈选择
- **框架**: Vue 2.6+ (与现有项目保持一致)
- **UI组件**: Element UI 2.15+
- **样式**: SCSS + CSS Variables
- **状态管理**: Vuex 3.x
- **路由**: Vue Router 3.x
- **HTTP客户端**: Axios
- **扫码库**: jsQR (轻量级二维码识别)
- **图表库**: ECharts 5.x (数据可视化)

### 2. 组件架构设计

#### 2.1 页面组件结构
```
src/views/traceability/
├── TraceabilityQuery.vue          # 溯源查询页面
├── TraceabilityDetail.vue         # 溯源详情页面
├── seller/
│   ├── TraceabilityList.vue       # 销售者溯源列表
│   ├── TraceabilityForm.vue       # 溯源记录编辑
│   ├── EventManagement.vue        # 生产事件管理
│   ├── CertificateManagement.vue  # 认证管理
│   └── LogisticsManagement.vue    # 物流管理
└── admin/
    ├── AuditList.vue              # 审核列表
    ├── AuditDetail.vue            # 审核详情
    └── Statistics.vue             # 统计分析
```

#### 2.2 公共组件
```
src/components/traceability/
├── QRCodeScanner.vue              # 二维码扫描组件
├── TraceabilityTimeline.vue       # 时间轴组件
├── ProductInfoCard.vue            # 产品信息卡片
├── CertificateViewer.vue          # 证书查看器
├── LogisticsTracker.vue           # 物流轨迹
├── ImageUploader.vue              # 图片上传组件
└── DataChart.vue                  # 数据图表组件
```

### 3. 关键功能实现

#### 3.1 二维码扫描功能
```javascript
// QRCodeScanner.vue
import jsQR from 'jsqr'

export default {
  data() {
    return {
      scanning: false,
      stream: null
    }
  },
  methods: {
    async startScan() {
      try {
        this.stream = await navigator.mediaDevices.getUserMedia({
          video: { facingMode: 'environment' }
        })
        this.scanning = true
        this.scanQRCode()
      } catch (error) {
        this.$message.error('无法访问摄像头')
      }
    },
    
    scanQRCode() {
      const video = this.$refs.video
      const canvas = this.$refs.canvas
      const context = canvas.getContext('2d')
      
      const scan = () => {
        if (video.readyState === video.HAVE_ENOUGH_DATA) {
          canvas.width = video.videoWidth
          canvas.height = video.videoHeight
          context.drawImage(video, 0, 0, canvas.width, canvas.height)
          
          const imageData = context.getImageData(0, 0, canvas.width, canvas.height)
          const code = jsQR(imageData.data, imageData.width, imageData.height)
          
          if (code) {
            this.$emit('scan-success', code.data)
            this.stopScan()
            return
          }
        }
        
        if (this.scanning) {
          requestAnimationFrame(scan)
        }
      }
      
      scan()
    }
  }
}
```

#### 3.2 时间轴组件
```javascript
// TraceabilityTimeline.vue
<template>
  <el-timeline>
    <el-timeline-item
      v-for="event in events"
      :key="event.id"
      :timestamp="formatDate(event.event_date)"
      :type="getEventType(event.event_type)"
    >
      <el-card>
        <h4>{{ event.event_type }}</h4>
        <p>{{ event.description }}</p>
        <div v-if="event.attachments" class="attachments">
          <el-image
            v-for="img in parseAttachments(event.attachments)"
            :key="img"
            :src="img"
            :preview-src-list="[img]"
            class="attachment-image"
          />
        </div>
        <div class="event-meta">
          <span>地点: {{ event.location }}</span>
          <span>负责人: {{ event.responsible_person }}</span>
        </div>
      </el-card>
    </el-timeline-item>
  </el-timeline>
</template>
```

#### 3.3 响应式设计
```scss
// 移动端适配
@media screen and (max-width: 768px) {
  .traceability-container {
    padding: 10px;
    
    .product-card {
      margin-bottom: 15px;
      
      .card-header {
        font-size: 16px;
        padding: 10px;
      }
      
      .card-content {
        padding: 15px 10px;
      }
    }
    
    .timeline-item {
      .timeline-content {
        font-size: 14px;
        line-height: 1.5;
      }
    }
  }
}

// 平板端适配
@media screen and (min-width: 768px) and (max-width: 1024px) {
  .traceability-container {
    padding: 20px;
    
    .grid-layout {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
    }
  }
}
```

## 🔧 后端技术实现

### 1. 技术栈选择
- **框架**: Spring Boot 2.7+
- **ORM**: MyBatis Plus 3.5+
- **数据库**: MySQL 8.0
- **缓存**: Redis 6.x
- **文件存储**: 本地存储 + 阿里云OSS
- **二维码生成**: ZXing 3.5+
- **JSON处理**: Jackson 2.13+
- **验证**: Hibernate Validator

### 2. 项目结构设计
```
src/main/java/com/agriculture/
├── controller/
│   └── TraceabilityController.java
├── service/
│   ├── TraceabilityService.java
│   ├── TraceabilityEventService.java
│   ├── TraceabilityCertificateService.java
│   └── TraceabilityLogisticsService.java
├── mapper/
│   ├── TraceabilityRecordMapper.java
│   ├── TraceabilityEventMapper.java
│   ├── TraceabilityCertificateMapper.java
│   └── TraceabilityLogisticsMapper.java
├── entity/
│   ├── TraceabilityRecord.java
│   ├── TraceabilityEvent.java
│   ├── TraceabilityCertificate.java
│   └── TraceabilityLogistics.java
├── dto/
│   ├── TraceabilityQueryDTO.java
│   ├── TraceabilityCreateDTO.java
│   └── TraceabilityUpdateDTO.java
└── utils/
    ├── QRCodeGenerator.java
    ├── TraceCodeGenerator.java
    └── FileUploadUtil.java
```

### 3. 核心功能实现

#### 3.1 溯源码生成算法
```java
@Component
public class TraceCodeGenerator {
    
    private static final String PREFIX = "SFAP";
    private static final String CHARSET = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    
    @Autowired
    private TraceCodeSequenceMapper sequenceMapper;
    
    public String generateTraceCode(Long productId) {
        // 获取序列号
        Long sequence = sequenceMapper.getNextSequence();
        
        // 生成时间戳（年月日时分）
        String timestamp = LocalDateTime.now()
            .format(DateTimeFormatter.ofPattern("yyMMddHHmm"));
        
        // 产品ID的后4位
        String productSuffix = String.format("%04d", productId % 10000);
        
        // 随机字符
        String randomStr = generateRandomString(4);
        
        // 组合溯源码: SFAP + 时间戳 + 产品后缀 + 序列号 + 随机字符
        String traceCode = PREFIX + timestamp + productSuffix + 
                          String.format("%04d", sequence % 10000) + randomStr;
        
        return traceCode;
    }
    
    private String generateRandomString(int length) {
        Random random = new Random();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append(CHARSET.charAt(random.nextInt(CHARSET.length())));
        }
        return sb.toString();
    }
}
```

#### 3.2 二维码生成服务
```java
@Service
public class QRCodeService {
    
    private static final int QR_CODE_SIZE = 300;
    private static final String QR_CODE_FORMAT = "PNG";
    
    public String generateQRCode(String traceCode) {
        try {
            // 构建查询URL
            String queryUrl = "https://sfap.example.com/trace/" + traceCode;
            
            // 生成二维码
            BitMatrix bitMatrix = new MultiFormatWriter().encode(
                queryUrl, BarcodeFormat.QR_CODE, QR_CODE_SIZE, QR_CODE_SIZE);
            
            // 转换为图片
            BufferedImage image = MatrixToImageWriter.toBufferedImage(bitMatrix);
            
            // 保存到文件系统
            String fileName = "qr_" + traceCode + ".png";
            String filePath = uploadPath + "/qrcodes/" + fileName;
            
            File file = new File(filePath);
            file.getParentFile().mkdirs();
            ImageIO.write(image, QR_CODE_FORMAT, file);
            
            // 返回访问URL
            return "/uploads/qrcodes/" + fileName;
            
        } catch (Exception e) {
            log.error("生成二维码失败: {}", e.getMessage());
            throw new RuntimeException("二维码生成失败");
        }
    }
}
```

#### 3.3 查询性能优化
```java
@Service
public class TraceabilityQueryService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    private static final String CACHE_PREFIX = "trace:";
    private static final int CACHE_EXPIRE = 3600; // 1小时
    
    public TraceabilityDetailVO getTraceabilityDetail(String traceCode) {
        // 先从缓存获取
        String cacheKey = CACHE_PREFIX + traceCode;
        TraceabilityDetailVO cached = (TraceabilityDetailVO) 
            redisTemplate.opsForValue().get(cacheKey);
        
        if (cached != null) {
            return cached;
        }
        
        // 从数据库查询
        TraceabilityDetailVO detail = queryFromDatabase(traceCode);
        
        if (detail != null) {
            // 存入缓存
            redisTemplate.opsForValue().set(cacheKey, detail, CACHE_EXPIRE, TimeUnit.SECONDS);
        }
        
        return detail;
    }
    
    private TraceabilityDetailVO queryFromDatabase(String traceCode) {
        // 复杂的多表关联查询
        return traceabilityMapper.getCompleteTraceabilityInfo(traceCode);
    }
}
```

## 🗄️ 数据库优化方案

### 1. 索引优化
```sql
-- 溯源记录表索引
CREATE INDEX idx_trace_code ON traceability_record(trace_code);
CREATE INDEX idx_product_id ON traceability_record(product_id);
CREATE INDEX idx_producer_id ON traceability_record(producer_id);
CREATE INDEX idx_status_created ON traceability_record(status, created_at);

-- 溯源事件表索引
CREATE INDEX idx_trace_record_id ON traceability_event(trace_record_id);
CREATE INDEX idx_event_type_date ON traceability_event(event_type, event_date);

-- 查询记录表索引
CREATE INDEX idx_trace_code_time ON traceability_query(trace_code, query_time);
CREATE INDEX idx_query_time ON traceability_query(query_time);
```

### 2. 分区策略
```sql
-- 查询记录表按月分区
ALTER TABLE traceability_query PARTITION BY RANGE (YEAR(query_time) * 100 + MONTH(query_time)) (
    PARTITION p202507 VALUES LESS THAN (202508),
    PARTITION p202508 VALUES LESS THAN (202509),
    PARTITION p202509 VALUES LESS THAN (202510),
    PARTITION p202510 VALUES LESS THAN (202511),
    PARTITION p202511 VALUES LESS THAN (202512),
    PARTITION p202512 VALUES LESS THAN (202601),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

### 3. 数据清理策略
```java
@Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
public void cleanOldQueryRecords() {
    // 删除3个月前的查询记录
    LocalDateTime cutoffDate = LocalDateTime.now().minusMonths(3);
    traceabilityQueryMapper.deleteOldRecords(cutoffDate);
    
    log.info("清理了{}条历史查询记录", deletedCount);
}
```

## 📱 移动端优化

### 1. PWA支持
```javascript
// manifest.json
{
  "name": "SFAP溯源查询",
  "short_name": "SFAP溯源",
  "description": "农产品溯源信息查询",
  "start_url": "/trace",
  "display": "standalone",
  "background_color": "#ffffff",
  "theme_color": "#409EFF",
  "icons": [
    {
      "src": "/icons/icon-192x192.png",
      "sizes": "192x192",
      "type": "image/png"
    }
  ]
}
```

### 2. 离线缓存策略
```javascript
// service-worker.js
const CACHE_NAME = 'sfap-trace-v1';
const urlsToCache = [
  '/trace',
  '/static/css/app.css',
  '/static/js/app.js'
];

self.addEventListener('fetch', event => {
  if (event.request.url.includes('/api/traceability/query/')) {
    event.respondWith(
      caches.open(CACHE_NAME).then(cache => {
        return cache.match(event.request).then(response => {
          if (response) {
            // 返回缓存的响应
            fetch(event.request).then(fetchResponse => {
              cache.put(event.request, fetchResponse.clone());
            });
            return response;
          }
          // 网络请求
          return fetch(event.request).then(fetchResponse => {
            cache.put(event.request, fetchResponse.clone());
            return fetchResponse;
          });
        });
      })
    );
  }
});
```

## 🔒 安全实现方案

### 1. API安全
```java
@RestController
@RequestMapping("/api/traceability")
@Validated
public class TraceabilityController {
    
    @PostMapping("/records")
    @PreAuthorize("hasRole('SELLER')")
    public ResponseEntity<?> createRecord(
        @Valid @RequestBody TraceabilityCreateDTO dto,
        Authentication auth) {
        
        // 验证用户只能操作自己的产品
        if (!isOwnerOfProduct(dto.getProductId(), auth.getName())) {
            throw new AccessDeniedException("无权操作此产品");
        }
        
        return ResponseEntity.ok(traceabilityService.createRecord(dto));
    }
}
```

### 2. 数据验证
```java
@Component
public class TraceabilityValidator {
    
    public void validateEventSequence(List<TraceabilityEvent> events) {
        // 验证事件时间顺序
        events.sort(Comparator.comparing(TraceabilityEvent::getEventDate));
        
        for (int i = 1; i < events.size(); i++) {
            if (events.get(i).getEventDate().isBefore(events.get(i-1).getEventDate())) {
                throw new ValidationException("事件时间顺序不正确");
            }
        }
        
        // 验证必要事件是否存在
        boolean hasPlanting = events.stream()
            .anyMatch(e -> "播种".equals(e.getEventType()));
        boolean hasHarvest = events.stream()
            .anyMatch(e -> "采收".equals(e.getEventType()));
            
        if (!hasPlanting || !hasHarvest) {
            throw new ValidationException("缺少必要的生产事件");
        }
    }
}
```

---

**技术方案版本**: v1.0  
**最后更新**: 2025-07-14  
**适用项目**: SFAP平台溯源模块
