# SFAP平台溯源模块数据库分析报告

## 📊 数据库连接信息
- **数据库**: agriculture_mall
- **主机**: localhost
- **用户**: root
- **表总数**: 67个表（包含视图）

## 🗃️ 溯源相关表结构分析

### 1. traceability_record（溯源主记录表）
**作用**: 存储农产品溯源的核心信息，是整个溯源系统的主表

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | bigint | 主键ID | PRI, AUTO_INCREMENT |
| product_id | bigint | 关联产品ID | UNI, NOT NULL |
| trace_code | varchar(128) | 唯一溯源码 | UNI, NOT NULL |
| product_name | varchar(255) | 产品名称 | NOT NULL |
| farm_name | varchar(255) | 生产基地名称 | NULL |
| producer_id | bigint | 生产者ID | MUL, NOT NULL |
| producer_name | varchar(255) | 生产者名称 | NULL |
| batch_number | varchar(100) | 批次号 | NULL |
| specification | varchar(255) | 产品规格 | NULL |
| quality_grade | varchar(50) | 质量等级 | NULL |
| creation_date | date | 开始生产日期 | NULL |
| harvest_date | date | 采摘/收获日期 | NULL |
| packaging_date | date | 包装日期 | NULL |
| qr_code_url | varchar(255) | 二维码图片URL | NULL |
| status | tinyint | 状态(0:草稿,1:待审核,2:已发布,3:已下架,4:异常) | DEFAULT 0 |
| created_at | datetime | 创建时间 | DEFAULT CURRENT_TIMESTAMP |
| updated_at | datetime | 更新时间 | ON UPDATE CURRENT_TIMESTAMP |
| deleted | tinyint(1) | 逻辑删除标识 | DEFAULT 0 |

**当前数据**: 0条记录

### 2. traceability_event（溯源事件表）
**作用**: 记录农产品生产过程中的关键事件

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | bigint | 主键ID | PRI, AUTO_INCREMENT |
| trace_record_id | bigint | 关联溯源记录ID | MUL, NOT NULL |
| event_sequence | int | 事件序号 | NULL |
| event_type | varchar(50) | 事件类型 | MUL, NOT NULL |
| event_date | datetime | 事件发生时间 | MUL, NOT NULL |
| description | text | 事件描述 | NULL |
| location | varchar(255) | 事件发生地点 | NULL |
| responsible_person | varchar(100) | 责任人 | NULL |
| attachments | text | 附件信息 | NULL |
| created_at | datetime | 创建时间 | DEFAULT CURRENT_TIMESTAMP |
| updated_at | datetime | 更新时间 | ON UPDATE CURRENT_TIMESTAMP |
| deleted | tinyint(1) | 逻辑删除标识 | DEFAULT 0 |

**当前数据**: 0条记录

### 3. trace_certificates（认证信息表）
**作用**: 存储农产品相关的认证证书信息

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | bigint | 主键ID | PRI, AUTO_INCREMENT |
| trace_record_id | bigint | 关联溯源记录ID | MUL, NOT NULL |
| certificate_type | varchar(100) | 认证类型 | MUL, NOT NULL |
| certificate_no | varchar(100) | 证书编号 | MUL |
| issuing_authority | varchar(255) | 颁发机构 | NULL |
| issue_date | date | 颁发日期 | NULL |
| valid_until | date | 有效期至 | NULL |
| certificate_url | varchar(255) | 证书文件URL | NULL |
| description | text | 证书描述 | NULL |
| created_at | datetime | 创建时间 | DEFAULT CURRENT_TIMESTAMP |
| updated_at | datetime | 更新时间 | ON UPDATE CURRENT_TIMESTAMP |

**当前数据**: 0条记录

### 4. trace_logistics（物流信息表）
**作用**: 记录农产品的物流运输信息

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | bigint | 主键ID | PRI, AUTO_INCREMENT |
| trace_record_id | bigint | 关联溯源记录ID | MUL, NOT NULL |
| carrier_name | varchar(255) | 承运商名称 | NULL |
| transport_type | varchar(50) | 运输方式 | NULL |
| departure_time | datetime | 出发时间 | NULL |
| arrival_time | datetime | 到达时间 | NULL |
| origin | varchar(255) | 起始地 | NULL |
| destination | varchar(255) | 目的地 | NULL |
| temperature | decimal(5,2) | 运输温度 | NULL |
| humidity | decimal(5,2) | 运输湿度 | NULL |
| status | tinyint | 物流状态 | MUL, DEFAULT 0 |
| created_at | datetime | 创建时间 | DEFAULT CURRENT_TIMESTAMP |
| updated_at | datetime | 更新时间 | ON UPDATE CURRENT_TIMESTAMP |

**当前数据**: 0条记录

### 5. trace_codes（溯源码表）
**作用**: 管理溯源码的生成、状态和统计信息

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| code | varchar(128) | 溯源码 | PRI |
| trace_record_id | bigint | 关联溯源记录ID | UNI, NOT NULL |
| qr_code_url | varchar(255) | 二维码图片URL | NULL |
| generated_time | datetime | 生成时间 | DEFAULT CURRENT_TIMESTAMP |
| status | tinyint | 状态 | DEFAULT 1 |
| scan_count | int | 总扫描次数 | DEFAULT 0 |
| daily_scan_count | int | 日扫描次数 | DEFAULT 0 |
| weekly_scan_count | int | 周扫描次数 | DEFAULT 0 |
| monthly_scan_count | int | 月扫描次数 | DEFAULT 0 |
| last_scan_time | datetime | 最后扫描时间 | NULL |
| first_scan_time | datetime | 首次扫描时间 | NULL |

**当前数据**: 0条记录

### 6. traceability_query（查询记录表）
**作用**: 记录用户查询溯源信息的行为数据

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | bigint | 主键ID | PRI, AUTO_INCREMENT |
| trace_code | varchar(128) | 查询的溯源码 | MUL, NOT NULL |
| trace_record_id | bigint | 关联溯源记录ID | MUL, NOT NULL |
| ip_address | varchar(50) | 查询者IP地址 | NULL |
| device_info | varchar(255) | 设备信息 | NULL |
| location | varchar(255) | 查询地点 | NULL |
| user_id | bigint | 用户ID（如已登录） | NULL |
| query_time | datetime | 查询时间 | MUL, DEFAULT CURRENT_TIMESTAMP |
| created_at | datetime | 创建时间 | DEFAULT CURRENT_TIMESTAMP |
| deleted | tinyint(1) | 逻辑删除标识 | DEFAULT 0 |

**当前数据**: 0条记录

## 🔗 表关系分析

### 主要外键关系
```
traceability_record (主表)
├── product (product_id → product.id)
├── user (producer_id → user.id)
└── 从表关联:
    ├── traceability_event (trace_record_id)
    ├── trace_certificates (trace_record_id)
    ├── trace_logistics (trace_record_id)
    ├── trace_codes (trace_record_id)
    └── traceability_query (trace_record_id)
```

### 数据完整性
- ✅ 主键约束完整
- ✅ 外键关系清晰
- ✅ 唯一性约束合理
- ✅ 索引设计优化

## 📈 现有数据统计

### 基础数据
- **产品总数**: 57个（主要为蔬菜类）
- **用户总数**: 管理员4个，销售者4个
- **溯源记录**: 0条（需要初始化）

### 产品分布（前10个）
| ID | 产品名称 | 分类ID | 销售者ID | 价格 | 库存 |
|----|----------|--------|----------|------|------|
| 1001 | 有机菠菜 | 11 | 1 | 8.50 | 500 |
| 1002 | 精品小白菜 | 11 | 1 | 6.80 | 800 |
| 1003 | 农家生菜 | 11 | 1 | 7.20 | 600 |
| 1004 | 有机韭菜 | 11 | 1 | 9.80 | 400 |
| 1005 | 新鲜芹菜 | 11 | 1 | 5.60 | 750 |

## ⚠️ 发现的问题

### 1. 数据缺失
- 所有溯源表均为空，需要生成测试数据
- 缺少溯源码生成机制的初始化

### 2. 字段优化建议
- `trace_code_sequence` 表需要查看具体用途
- 考虑添加溯源记录的审核时间字段
- 物流表可能需要添加运单号字段

### 3. 性能优化
- 查询记录表需要定期清理历史数据
- 扫描统计字段需要定时任务更新

## 🎯 下一步行动

1. **生成测试数据** - 为现有产品创建溯源记录
2. **完善表结构** - 根据业务需求微调字段
3. **建立索引** - 优化查询性能
4. **数据初始化** - 创建基础配置数据

---

**分析完成时间**: 2025-07-14  
**数据库版本**: MySQL 8.0  
**表结构状态**: ✅ 完整可用
