# SFAP平台溯源模块角色权限设计

## 🎭 角色体系概览

### 角色层次结构
```
管理员 (admin)
├── 系统管理权限
├── 审核管理权限
├── 数据分析权限
└── 用户管理权限

销售者 (seller)
├── 溯源记录管理权限
├── 产品关联权限
├── 数据查看权限
└── 基础操作权限

普通用户 (user)
├── 溯源查询权限
├── 信息浏览权限
└── 基础交互权限

游客 (guest)
└── 有限查询权限
```

## 👤 普通用户权限设计

### 基础权限
| 功能模块 | 权限名称 | 权限代码 | 说明 |
|----------|----------|----------|------|
| 溯源查询 | 查询溯源信息 | trace:query | 通过溯源码查询产品信息 |
| 信息浏览 | 浏览溯源详情 | trace:view | 查看完整的溯源信息 |
| 历史记录 | 查看查询历史 | trace:history | 查看个人查询历史记录 |
| 收藏功能 | 收藏产品 | trace:favorite | 收藏感兴趣的产品 |
| 分享功能 | 分享溯源信息 | trace:share | 分享溯源信息到社交媒体 |
| 反馈评价 | 提交反馈 | trace:feedback | 对产品质量进行评价反馈 |

### 功能限制
- ❌ 不能创建或修改溯源记录
- ❌ 不能查看其他用户的私人信息
- ❌ 不能访问管理功能
- ✅ 可以查询所有已发布的溯源信息
- ✅ 可以使用扫码功能
- ✅ 可以查看统计数据（公开部分）

### 数据访问范围
```sql
-- 普通用户只能查询已发布的溯源记录
SELECT * FROM traceability_record 
WHERE status = 2 AND deleted = 0;

-- 可以查看所有公开的认证信息
SELECT * FROM trace_certificates 
WHERE trace_record_id IN (
    SELECT id FROM traceability_record 
    WHERE status = 2 AND deleted = 0
);
```

## 🏪 销售者权限设计

### 核心权限
| 功能模块 | 权限名称 | 权限代码 | 说明 |
|----------|----------|----------|------|
| 溯源管理 | 创建溯源记录 | trace:create | 为自己的产品创建溯源记录 |
| 溯源管理 | 编辑溯源记录 | trace:update | 修改自己的溯源记录 |
| 溯源管理 | 删除溯源记录 | trace:delete | 删除自己的溯源记录 |
| 事件管理 | 管理生产事件 | trace:event:manage | 添加、编辑生产事件 |
| 认证管理 | 管理认证信息 | trace:cert:manage | 上传、管理认证证书 |
| 物流管理 | 管理物流信息 | trace:logistics:manage | 录入、更新物流信息 |
| 文件管理 | 上传附件 | trace:file:upload | 上传图片和文档附件 |
| 数据查看 | 查看统计数据 | trace:stats:view | 查看自己产品的统计数据 |

### 权限验证逻辑
```java
@Component
public class TraceabilityPermissionChecker {
    
    /**
     * 检查销售者是否有权限操作指定的溯源记录
     */
    public boolean canManageTraceRecord(Long sellerId, Long traceRecordId) {
        TraceabilityRecord record = traceabilityMapper.selectById(traceRecordId);
        if (record == null) {
            return false;
        }
        
        // 检查是否是记录的创建者
        if (!sellerId.equals(record.getProducerId())) {
            return false;
        }
        
        // 检查记录状态，已发布的记录不能修改关键信息
        if (record.getStatus() == 2) {
            return false; // 已发布状态不允许修改
        }
        
        return true;
    }
    
    /**
     * 检查销售者是否有权限操作指定的产品
     */
    public boolean canManageProduct(Long sellerId, Long productId) {
        Product product = productMapper.selectById(productId);
        return product != null && sellerId.equals(product.getSellerId());
    }
}
```

### 数据访问范围
```sql
-- 销售者只能查看和管理自己的溯源记录
SELECT * FROM traceability_record 
WHERE producer_id = #{sellerId} AND deleted = 0;

-- 只能管理自己产品的溯源事件
SELECT e.* FROM traceability_event e
JOIN traceability_record r ON e.trace_record_id = r.id
WHERE r.producer_id = #{sellerId} AND e.deleted = 0;
```

### 操作限制
- ✅ 可以为自己的产品创建溯源记录
- ✅ 可以编辑未发布的溯源记录
- ❌ 不能修改已发布的溯源记录的关键信息
- ❌ 不能查看其他销售者的私有数据
- ❌ 不能直接发布溯源记录（需要审核）

## 👨‍💼 管理员权限设计

### 全局权限
| 功能模块 | 权限名称 | 权限代码 | 说明 |
|----------|----------|----------|------|
| 审核管理 | 审核溯源记录 | trace:audit | 审核销售者提交的溯源记录 |
| 审核管理 | 批量审核 | trace:audit:batch | 批量处理审核任务 |
| 系统管理 | 管理所有记录 | trace:admin:manage | 查看和管理所有溯源记录 |
| 用户管理 | 管理用户权限 | trace:user:manage | 管理用户的溯源相关权限 |
| 数据分析 | 查看全局统计 | trace:stats:global | 查看系统级别的统计数据 |
| 系统配置 | 配置系统参数 | trace:config:manage | 管理溯源系统配置 |
| 数据导出 | 导出数据 | trace:export | 导出溯源数据报表 |
| 日志查看 | 查看操作日志 | trace:log:view | 查看系统操作日志 |

### 审核工作流权限
```java
@Service
public class AuditWorkflowService {
    
    /**
     * 审核溯源记录
     */
    @PreAuthorize("hasAuthority('trace:audit')")
    public void auditTraceRecord(Long recordId, AuditDecision decision, String comment) {
        TraceabilityRecord record = traceabilityMapper.selectById(recordId);
        
        switch (decision) {
            case APPROVE:
                // 审核通过，生成溯源码
                approveRecord(record);
                break;
            case REJECT:
                // 审核拒绝，返回修改
                rejectRecord(record, comment);
                break;
            case REQUEST_MODIFICATION:
                // 要求修改
                requestModification(record, comment);
                break;
        }
        
        // 记录审核日志
        recordAuditLog(recordId, decision, comment);
    }
    
    private void approveRecord(TraceabilityRecord record) {
        // 生成溯源码
        String traceCode = traceCodeGenerator.generateTraceCode(record.getProductId());
        record.setTraceCode(traceCode);
        record.setStatus(2); // 已发布
        
        // 生成二维码
        String qrCodeUrl = qrCodeService.generateQRCode(traceCode);
        record.setQrCodeUrl(qrCodeUrl);
        
        traceabilityMapper.updateById(record);
        
        // 发送通知给销售者
        notificationService.sendApprovalNotification(record.getProducerId(), record.getId());
    }
}
```

### 数据访问范围
```sql
-- 管理员可以查看所有数据
SELECT * FROM traceability_record WHERE deleted = 0;

-- 可以查看所有审核历史
SELECT * FROM audit_log WHERE module = 'traceability';

-- 可以查看全局统计数据
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN status = 2 THEN 1 END) as published_records,
    COUNT(CASE WHEN status = 1 THEN 1 END) as pending_records
FROM traceability_record WHERE deleted = 0;
```

## 🔐 权限控制实现

### 1. 注解式权限控制
```java
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@PreAuthorize("hasAuthority(#permission)")
public @interface RequirePermission {
    String value();
    String message() default "权限不足";
}

// 使用示例
@RequirePermission("trace:create")
public TraceabilityRecord createRecord(TraceabilityCreateDTO dto) {
    // 创建溯源记录的逻辑
}
```

### 2. 数据权限过滤器
```java
@Component
public class DataPermissionInterceptor implements Interceptor {
    
    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
        Object parameter = invocation.getArgs()[1];
        
        // 获取当前用户信息
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        UserDetails user = (UserDetails) auth.getPrincipal();
        
        // 根据用户角色添加数据过滤条件
        if (user.getAuthorities().stream().anyMatch(a -> a.getAuthority().equals("ROLE_SELLER"))) {
            // 销售者只能查看自己的数据
            addSellerDataFilter(mappedStatement, parameter, user.getUsername());
        }
        
        return invocation.proceed();
    }
}
```

### 3. 前端权限控制
```javascript
// 权限指令
Vue.directive('permission', {
  bind(el, binding) {
    const { value } = binding
    const permissions = store.getters.permissions
    
    if (value && value instanceof Array && value.length > 0) {
      const hasPermission = permissions.some(permission => {
        return value.includes(permission)
      })
      
      if (!hasPermission) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    }
  }
})

// 使用示例
<el-button v-permission="['trace:create']">创建溯源记录</el-button>
```

### 4. 路由权限控制
```javascript
// router/modules/traceability.js
export default {
  path: '/traceability',
  component: Layout,
  meta: { 
    title: '溯源管理',
    roles: ['seller', 'admin'] 
  },
  children: [
    {
      path: 'list',
      component: () => import('@/views/traceability/seller/TraceabilityList'),
      meta: { 
        title: '溯源记录',
        permissions: ['trace:view'] 
      }
    },
    {
      path: 'create',
      component: () => import('@/views/traceability/seller/TraceabilityForm'),
      meta: { 
        title: '创建溯源',
        permissions: ['trace:create'] 
      }
    },
    {
      path: 'audit',
      component: () => import('@/views/traceability/admin/AuditList'),
      meta: { 
        title: '审核管理',
        roles: ['admin'],
        permissions: ['trace:audit'] 
      }
    }
  ]
}
```

## 🛡️ 安全策略

### 1. 敏感操作二次验证
```java
@PostMapping("/records/{id}/publish")
@RequirePermission("trace:audit")
public ResponseEntity<?> publishRecord(
    @PathVariable Long id,
    @RequestParam String verificationCode) {
    
    // 验证二次验证码
    if (!verificationService.verify(verificationCode)) {
        throw new SecurityException("验证码错误");
    }
    
    // 执行发布操作
    return ResponseEntity.ok(auditService.publishRecord(id));
}
```

### 2. 操作日志记录
```java
@Aspect
@Component
public class AuditLogAspect {
    
    @AfterReturning(pointcut = "@annotation(requirePermission)", returning = "result")
    public void logOperation(JoinPoint joinPoint, RequirePermission requirePermission, Object result) {
        // 记录操作日志
        AuditLog log = new AuditLog();
        log.setUserId(getCurrentUserId());
        log.setOperation(joinPoint.getSignature().getName());
        log.setPermission(requirePermission.value());
        log.setTimestamp(LocalDateTime.now());
        log.setResult(result != null ? "SUCCESS" : "FAILURE");
        
        auditLogService.save(log);
    }
}
```

### 3. 权限缓存策略
```java
@Service
public class PermissionCacheService {
    
    @Cacheable(value = "user_permissions", key = "#userId")
    public Set<String> getUserPermissions(Long userId) {
        User user = userService.getById(userId);
        Set<String> permissions = new HashSet<>();
        
        // 根据用户角色获取权限
        switch (user.getRole()) {
            case "admin":
                permissions.addAll(getAdminPermissions());
                break;
            case "seller":
                permissions.addAll(getSellerPermissions());
                break;
            case "user":
                permissions.addAll(getUserPermissions());
                break;
        }
        
        return permissions;
    }
    
    @CacheEvict(value = "user_permissions", key = "#userId")
    public void clearUserPermissionCache(Long userId) {
        // 清除用户权限缓存
    }
}
```

## 📊 权限监控

### 1. 权限使用统计
```sql
-- 权限使用频率统计
SELECT 
    permission,
    COUNT(*) as usage_count,
    COUNT(DISTINCT user_id) as user_count
FROM audit_log 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY permission
ORDER BY usage_count DESC;
```

### 2. 异常权限检测
```java
@Scheduled(fixedRate = 300000) // 每5分钟检查一次
public void detectAbnormalPermissionUsage() {
    // 检测异常的权限使用模式
    List<AbnormalUsage> abnormalUsages = auditLogService.detectAbnormalUsage();
    
    for (AbnormalUsage usage : abnormalUsages) {
        // 发送告警
        alertService.sendSecurityAlert(usage);
        
        // 记录安全日志
        securityLogService.recordSecurityEvent(usage);
    }
}
```

---

**权限设计版本**: v1.0  
**最后更新**: 2025-07-14  
**安全等级**: 高
