<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>销售者产品500错误诊断</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        .test-section h3 {
            color: #34495e;
            margin-top: 0;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .code-block {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.danger {
            background: #dc3545;
        }
        .btn.danger:hover {
            background: #c82333;
        }
        #results {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 销售者产品500错误诊断工具</h1>
            <p>帮助诊断 POST /api/seller/products 500错误的根本原因</p>
        </div>

        <div class="test-section">
            <h3>📋 问题分析</h3>
            <div class="status warning">
                <strong>错误信息:</strong> POST http://**************:8081/api/seller/products 500 (Internal Server Error)
            </div>
            <p><strong>可能原因:</strong></p>
            <ul>
                <li>❌ 用户认证失败 (X-User-Id 请求头缺失或无效)</li>
                <li>❌ 销售者权限验证失败</li>
                <li>❌ 产品数据验证失败</li>
                <li>❌ 数据库操作异常</li>
                <li>❌ 溯源记录创建失败</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🧪 诊断测试</h3>
            <button class="btn" onclick="testServerHealth()">1. 测试服务器健康状态</button>
            <button class="btn" onclick="testUserAuth()">2. 测试用户认证</button>
            <button class="btn" onclick="testSellerPermission()">3. 测试销售者权限</button>
            <button class="btn" onclick="testProductCreation()">4. 测试产品创建</button>
            <button class="btn danger" onclick="clearResults()">清空结果</button>
        </div>

        <div class="test-section">
            <h3>🔧 修复建议</h3>
            <div id="fix-suggestions">
                <p>请先运行诊断测试，系统会根据测试结果提供具体的修复建议。</p>
            </div>
        </div>

        <div class="test-section">
            <h3>📊 测试结果</h3>
            <div id="results">
                <p>点击上方按钮开始诊断...</p>
            </div>
        </div>
    </div>

    <script>
        const SERVER_BASE = 'http://**************:8081';
        
        function addResult(title, content, type = 'info') {
            const results = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            
            const statusClass = type === 'success' ? 'success' : 
                               type === 'error' ? 'error' : 'warning';
            
            results.innerHTML += `
                <div class="status ${statusClass}">
                    <strong>[${timestamp}] ${title}</strong><br>
                    <div class="code-block">${content}</div>
                </div>
            `;
            results.scrollTop = results.scrollHeight;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '<p>结果已清空，点击按钮重新开始诊断...</p>';
            document.getElementById('fix-suggestions').innerHTML = '<p>请先运行诊断测试，系统会根据测试结果提供具体的修复建议。</p>';
        }

        async function testServerHealth() {
            addResult('服务器健康检查', '正在检查服务器状态...', 'warning');
            
            try {
                const response = await fetch(`${SERVER_BASE}/api/health`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.text();
                    addResult('✅ 服务器健康检查', `服务器状态正常\n响应: ${data}`, 'success');
                } else {
                    addResult('❌ 服务器健康检查', `服务器响应异常\n状态码: ${response.status}`, 'error');
                }
            } catch (error) {
                addResult('❌ 服务器健康检查', `连接失败: ${error.message}`, 'error');
            }
        }

        async function testUserAuth() {
            addResult('用户认证测试', '正在测试用户认证...', 'warning');
            
            // 模拟前端发送的请求头
            const headers = {
                'Content-Type': 'application/json',
                'X-User-Id': '7', // 使用测试用户ID
                'Authorization': 'Bearer test-token'
            };
            
            try {
                const response = await fetch(`${SERVER_BASE}/api/seller/products?page=1&size=1`, {
                    method: 'GET',
                    headers: headers
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    addResult('✅ 用户认证测试', `认证成功\n响应: ${JSON.stringify(data, null, 2)}`, 'success');
                } else if (response.status === 401) {
                    addResult('❌ 用户认证测试', `认证失败 (401)\n响应: ${JSON.stringify(data, null, 2)}`, 'error');
                } else if (response.status === 403) {
                    addResult('❌ 用户认证测试', `权限不足 (403)\n响应: ${JSON.stringify(data, null, 2)}`, 'error');
                } else {
                    addResult('❌ 用户认证测试', `其他错误 (${response.status})\n响应: ${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                addResult('❌ 用户认证测试', `请求失败: ${error.message}`, 'error');
            }
        }

        async function testSellerPermission() {
            addResult('销售者权限测试', '正在测试销售者权限...', 'warning');
            
            const headers = {
                'Content-Type': 'application/json',
                'X-User-Id': '7', // 使用测试销售者ID
                'Authorization': 'Bearer test-token'
            };
            
            try {
                const response = await fetch(`${SERVER_BASE}/api/seller/products/statistics`, {
                    method: 'GET',
                    headers: headers
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    addResult('✅ 销售者权限测试', `权限验证成功\n响应: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    addResult('❌ 销售者权限测试', `权限验证失败 (${response.status})\n响应: ${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                addResult('❌ 销售者权限测试', `请求失败: ${error.message}`, 'error');
            }
        }

        async function testProductCreation() {
            addResult('产品创建测试', '正在测试产品创建...', 'warning');
            
            const headers = {
                'Content-Type': 'application/json',
                'X-User-Id': '7', // 使用测试销售者ID
                'Authorization': 'Bearer test-token'
            };
            
            const testProduct = {
                name: '测试产品_' + Date.now(),
                description: '这是一个测试产品',
                price: 99.99,
                stock: 100,
                categoryId: 1,
                unit: '个',
                origin: '测试产地',
                specification: '测试规格'
            };
            
            try {
                const response = await fetch(`${SERVER_BASE}/api/seller/products`, {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify(testProduct)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    addResult('✅ 产品创建测试', `产品创建成功\n响应: ${JSON.stringify(data, null, 2)}`, 'success');
                    updateFixSuggestions('success');
                } else {
                    addResult('❌ 产品创建测试', `产品创建失败 (${response.status})\n响应: ${JSON.stringify(data, null, 2)}`, 'error');
                    updateFixSuggestions('error', response.status, data);
                }
            } catch (error) {
                addResult('❌ 产品创建测试', `请求失败: ${error.message}`, 'error');
                updateFixSuggestions('network_error', null, error);
            }
        }

        function updateFixSuggestions(result, status = null, data = null) {
            const suggestions = document.getElementById('fix-suggestions');
            
            if (result === 'success') {
                suggestions.innerHTML = `
                    <div class="status success">
                        <strong>✅ 问题已解决</strong><br>
                        产品创建功能正常工作。如果前端仍有问题，请检查：
                        <ul>
                            <li>前端请求头是否正确设置 X-User-Id</li>
                            <li>用户是否有销售者权限</li>
                            <li>前端发送的数据格式是否正确</li>
                        </ul>
                    </div>
                `;
            } else if (result === 'error') {
                let suggestion = '';
                
                if (status === 401) {
                    suggestion = `
                        <strong>🔧 认证问题修复建议:</strong>
                        <ol>
                            <li>检查前端是否正确设置 X-User-Id 请求头</li>
                            <li>确认用户已正确登录</li>
                            <li>检查 UserAuthInterceptor 是否正常工作</li>
                            <li>验证用户状态是否为激活状态</li>
                        </ol>
                    `;
                } else if (status === 403) {
                    suggestion = `
                        <strong>🔧 权限问题修复建议:</strong>
                        <ol>
                            <li>确认用户角色为 'seller' 或 'SELLER'</li>
                            <li>检查用户的 userType 字段</li>
                            <li>验证 AuthUtils.isSeller() 方法逻辑</li>
                            <li>检查销售者申请是否已通过审核</li>
                        </ol>
                    `;
                } else if (status === 500) {
                    suggestion = `
                        <strong>🔧 服务器错误修复建议:</strong>
                        <ol>
                            <li>检查后端日志: tail -f /www/wwwroot/test.com/backend/app.log</li>
                            <li>验证数据库连接是否正常</li>
                            <li>检查产品数据验证逻辑</li>
                            <li>确认溯源记录创建是否成功</li>
                            <li>检查是否有必填字段缺失</li>
                        </ol>
                        <div class="code-block">
                        # 查看详细错误日志
                        tail -f /www/wwwroot/test.com/backend/app.log | grep ERROR
                        
                        # 检查数据库连接
                        mysql -u root -p agriculture_mall
                        
                        # 重启后端服务
                        cd /www/wwwroot/test.com/backend
                        ./restart.sh
                        </div>
                    `;
                }
                
                suggestions.innerHTML = `
                    <div class="status error">
                        ${suggestion}
                    </div>
                `;
            } else if (result === 'network_error') {
                suggestions.innerHTML = `
                    <div class="status error">
                        <strong>🔧 网络连接问题修复建议:</strong>
                        <ol>
                            <li>检查后端服务是否正常运行</li>
                            <li>验证端口 8081 是否开放</li>
                            <li>检查防火墙设置</li>
                            <li>确认 NGINX 代理配置正确</li>
                        </ol>
                    </div>
                `;
            }
        }

        // 页面加载时自动运行服务器健康检查
        window.onload = function() {
            setTimeout(testServerHealth, 1000);
        };
    </script>
</body>
</html>
