#!/bin/bash

# 智慧农业平台文件上传和静态资源系统验证脚本
# 验证生产环境配置是否正确工作

echo "=========================================="
echo "智慧农业平台文件上传系统验证"
echo "=========================================="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
SERVER_IP="**************"
BACKEND_PORT="8081"
FRONTEND_PORT="8200"
BASE_URL="http://$SERVER_IP:$BACKEND_PORT"

# 验证计数器
total_tests=0
passed_tests=0
failed_tests=0

# 测试函数
test_api() {
    local test_name=$1
    local url=$2
    local expected_status=${3:-200}
    
    total_tests=$((total_tests + 1))
    echo -n "[$total_tests] $test_name ... "
    
    # 使用curl测试API
    response=$(curl -s -w "%{http_code}" -o /dev/null "$url" 2>/dev/null)
    
    if [ "$response" = "$expected_status" ]; then
        echo -e "${GREEN}✅ 通过 ($response)${NC}"
        passed_tests=$((passed_tests + 1))
        return 0
    else
        echo -e "${RED}❌ 失败 ($response)${NC}"
        failed_tests=$((failed_tests + 1))
        return 1
    fi
}

# 测试图片访问
test_image_access() {
    local test_name=$1
    local image_path=$2
    
    total_tests=$((total_tests + 1))
    echo -n "[$total_tests] $test_name ... "
    
    # 测试图片访问
    url="$BASE_URL$image_path"
    response=$(curl -s -w "%{http_code}" -o /dev/null "$url" 2>/dev/null)
    
    if [ "$response" = "200" ] || [ "$response" = "404" ]; then
        if [ "$response" = "200" ]; then
            echo -e "${GREEN}✅ 图片存在 ($response)${NC}"
        else
            echo -e "${YELLOW}⚠️ 图片不存在但路径可访问 ($response)${NC}"
        fi
        passed_tests=$((passed_tests + 1))
        return 0
    else
        echo -e "${RED}❌ 路径配置错误 ($response)${NC}"
        failed_tests=$((failed_tests + 1))
        return 1
    fi
}

echo -e "${BLUE}🔍 1. 后端服务状态检查${NC}"
echo "=================================="

# 测试后端基础服务
test_api "后端服务健康检查" "$BASE_URL/"
test_api "新闻API接口" "$BASE_URL/api/news?page=1&size=5"
test_api "商品API接口" "$BASE_URL/api/mall/products?page=1&size=5"
test_api "首页API接口" "$BASE_URL/api/home"

echo ""
echo -e "${BLUE}📁 2. 静态资源路径验证${NC}"
echo "=================================="

# 测试各类图片路径访问
test_image_access "商品图片路径" "/uploads/images/products/test.jpg"
test_image_access "用户头像路径" "/uploads/images/avatars/test.jpg"
test_image_access "新闻图片路径" "/uploads/images/news/test.jpg"
test_image_access "百科图片路径" "/uploads/images/encyclopedia/test.jpg"
test_image_access "轮播图片路径" "/uploads/images/banners/test.jpg"
test_image_access "分类图片路径" "/uploads/images/categories/test.jpg"

echo ""
echo -e "${BLUE}🖼️ 3. 实际图片访问测试${NC}"
echo "=================================="

# 获取实际的图片URL进行测试
echo "获取商品数据并测试图片访问..."

# 获取商品列表
products_response=$(curl -s "$BASE_URL/api/mall/products?page=1&size=3" 2>/dev/null)

if echo "$products_response" | grep -q '"code":200'; then
    echo -e "${GREEN}✅ 成功获取商品数据${NC}"
    
    # 提取图片URL并测试（这里简化处理，实际可能需要更复杂的JSON解析）
    echo "测试商品图片访问..."
    
    # 测试一些常见的商品图片
    common_images=(
        "/uploads/images/products/apple.jpg"
        "/uploads/images/products/rice.jpg"
        "/uploads/images/products/tomato.jpg"
    )
    
    for img in "${common_images[@]}"; do
        test_image_access "商品图片 $(basename $img)" "$img"
    done
else
    echo -e "${RED}❌ 无法获取商品数据${NC}"
    failed_tests=$((failed_tests + 1))
fi

echo ""
echo -e "${BLUE}📤 4. 文件上传API测试${NC}"
echo "=================================="

# 测试文件上传API端点
test_api "文件上传API端点" "$BASE_URL/api/upload/image" "405"  # 405 Method Not Allowed 表示端点存在但需要POST
test_api "商品图片上传API端点" "$BASE_URL/api/upload/product" "405"

echo ""
echo -e "${BLUE}🌐 5. 前端访问测试${NC}"
echo "=================================="

# 测试前端服务
FRONTEND_URL="http://$SERVER_IP:$FRONTEND_PORT"
test_api "前端服务访问" "$FRONTEND_URL"

echo ""
echo -e "${BLUE}🔗 6. 跨域配置测试${NC}"
echo "=================================="

# 测试CORS配置
total_tests=$((total_tests + 1))
echo -n "[$total_tests] CORS配置测试 ... "

cors_response=$(curl -s -H "Origin: http://$SERVER_IP:$FRONTEND_PORT" \
                     -H "Access-Control-Request-Method: GET" \
                     -H "Access-Control-Request-Headers: Content-Type" \
                     -X OPTIONS "$BASE_URL/api/news" 2>/dev/null)

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ CORS配置正常${NC}"
    passed_tests=$((passed_tests + 1))
else
    echo -e "${RED}❌ CORS配置异常${NC}"
    failed_tests=$((failed_tests + 1))
fi

echo ""
echo -e "${BLUE}📊 7. 验证结果汇总${NC}"
echo "=================================="

echo "总测试数: $total_tests"
echo -e "通过测试: ${GREEN}$passed_tests${NC}"
echo -e "失败测试: ${RED}$failed_tests${NC}"

success_rate=$(( passed_tests * 100 / total_tests ))
echo "成功率: $success_rate%"

echo ""
echo -e "${BLUE}🔧 8. 配置状态检查${NC}"
echo "=================================="

# 检查目录是否存在
if [ -d "/www/wwwroot/test.com/backend/uploads" ]; then
    echo -e "${GREEN}✅ 上传目录存在${NC}"
    
    # 检查子目录
    subdirs=("images/products" "images/avatars" "images/news" "images/encyclopedia")
    for subdir in "${subdirs[@]}"; do
        if [ -d "/www/wwwroot/test.com/backend/uploads/$subdir" ]; then
            echo -e "${GREEN}✅ $subdir 目录存在${NC}"
        else
            echo -e "${RED}❌ $subdir 目录不存在${NC}"
        fi
    done
else
    echo -e "${RED}❌ 主上传目录不存在${NC}"
fi

echo ""
echo -e "${BLUE}📝 9. 问题诊断和建议${NC}"
echo "=================================="

if [ $failed_tests -eq 0 ]; then
    echo -e "${GREEN}🎉 所有测试通过！文件上传系统配置正确！${NC}"
    echo ""
    echo "✅ 系统状态良好，可以正常使用以下功能："
    echo "• 文件上传和存储"
    echo "• 静态资源访问"
    echo "• 图片显示"
    echo "• 跨域请求"
elif [ $success_rate -ge 80 ]; then
    echo -e "${YELLOW}⚠️ 大部分功能正常，但有少量问题需要解决${NC}"
    echo ""
    echo "建议检查："
    echo "• 确保所有目录已创建"
    echo "• 检查文件权限设置"
    echo "• 验证Nginx配置"
elif [ $success_rate -ge 60 ]; then
    echo -e "${YELLOW}⚠️ 系统部分功能正常，需要进一步配置${NC}"
    echo ""
    echo "需要检查："
    echo "• 后端静态资源配置"
    echo "• 目录结构和权限"
    echo "• 前后端URL配置"
else
    echo -e "${RED}❌ 系统存在较多问题，需要全面检查配置${NC}"
    echo ""
    echo "紧急检查项："
    echo "• 后端服务是否正常启动"
    echo "• 网络连接是否正常"
    echo "• 基础配置是否正确"
fi

echo ""
echo -e "${BLUE}🚀 10. 下一步操作建议${NC}"
echo "=================================="

if [ $failed_tests -eq 0 ]; then
    echo "1. 系统已就绪，可以开始使用"
    echo "2. 建议进行用户验收测试"
    echo "3. 配置监控和备份策略"
else
    echo "1. 根据失败的测试项进行针对性修复"
    echo "2. 重新运行验证脚本确认修复效果"
    echo "3. 检查服务器日志获取详细错误信息"
fi

echo ""
echo "验证完成时间: $(date)"
exit $failed_tests
