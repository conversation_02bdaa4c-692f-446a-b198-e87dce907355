# 智能推荐模块API文档

## 模块概述

智能推荐模块是智慧农业平台的核心AI引擎，基于机器学习和大数据分析技术，为用户提供个性化的农业生产建议、商品推荐、种植方案等智能化服务。

## 模块描述

本模块实现了完整的智能推荐体系，包括商品推荐、种植方案推荐、农事活动推荐、设备推荐、市场分析推荐等功能。采用协同过滤、内容推荐、深度学习等多种推荐算法，结合用户行为数据和农业专业知识，提供精准的个性化推荐服务。

## 子任务完成情况

### ✅ 已完成任务

#### 1. 数据库层 (Mapper)
- [x] RecommendationMapper - 推荐数据访问层
- [x] UserBehaviorMapper - 用户行为数据访问层
- [x] RecommendationModelMapper - 推荐模型数据访问层
- [x] RecommendationLogMapper - 推荐日志数据访问层
- [x] UserPreferenceMapper - 用户偏好数据访问层
- [x] ItemFeatureMapper - 物品特征数据访问层
- [x] RecommendationFeedbackMapper - 推荐反馈数据访问层

#### 2. 业务逻辑层 (Service)
- [x] RecommendationService & RecommendationServiceImpl - 推荐业务逻辑
- [x] ProductRecommendationService & ProductRecommendationServiceImpl - 商品推荐业务逻辑
- [x] PlantingRecommendationService & PlantingRecommendationServiceImpl - 种植推荐业务逻辑
- [x] UserBehaviorService & UserBehaviorServiceImpl - 用户行为业务逻辑
- [x] RecommendationModelService & RecommendationModelServiceImpl - 推荐模型业务逻辑
- [x] PersonalizationService & PersonalizationServiceImpl - 个性化业务逻辑
- [x] ContentRecommendationService & ContentRecommendationServiceImpl - 内容推荐业务逻辑

#### 3. 控制器层 (Controller)
- [x] RecommendationController - 推荐API接口
- [x] ProductRecommendationController - 商品推荐API接口
- [x] PlantingRecommendationController - 种植推荐API接口
- [x] UserBehaviorController - 用户行为API接口
- [x] RecommendationModelController - 推荐模型API接口
- [x] PersonalizationController - 个性化API接口
- [x] ContentRecommendationController - 内容推荐API接口

## API接口列表

### 1. 通用推荐 API (RecommendationController)

**基础路径**: `/api/recommendation`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/` | 获取推荐列表 | userId, type, limit, page |
| POST | `/feedback` | 提交推荐反馈 | userId, itemId, action, rating |
| GET | `/hot` | 获取热门推荐 | type, limit |
| GET | `/trending` | 获取趋势推荐 | type, period, limit |
| POST | `/refresh` | 刷新用户推荐 | userId, type |
| GET | `/explain/{itemId}` | 获取推荐解释 | itemId, userId |
| GET | `/diversity` | 获取多样化推荐 | userId, categories, limit |
| POST | `/batch` | 批量获取推荐 | userIds, type, limit |
| GET | `/stats` | 获取推荐统计 | userId, period |

### 2. 商品推荐 API (ProductRecommendationController)

**基础路径**: `/api/recommendation/products`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/personal` | 个性化商品推荐 | userId, limit, categoryId |
| GET | `/similar/{productId}` | 相似商品推荐 | productId, limit |
| GET | `/collaborative` | 协同过滤推荐 | userId, limit |
| GET | `/content-based` | 基于内容的推荐 | userId, limit |
| GET | `/frequently-bought-together` | 经常一起购买的商品 | productId, limit |
| GET | `/seasonal` | 季节性商品推荐 | userId, season, limit |
| GET | `/price-range` | 价格区间推荐 | userId, minPrice, maxPrice, limit |
| GET | `/new-arrivals` | 新品推荐 | userId, limit |
| GET | `/bestsellers` | 畅销商品推荐 | categoryId, limit |
| POST | `/view-history` | 基于浏览历史推荐 | userId, limit |
| GET | `/cross-sell` | 交叉销售推荐 | cartItems, limit |
| GET | `/upsell` | 向上销售推荐 | productId, limit |

### 3. 种植推荐 API (PlantingRecommendationController)

**基础路径**: `/api/recommendation/planting`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/crops` | 推荐种植作物 | farmId, season, soilType, climate |
| GET | `/varieties` | 推荐作物品种 | cropId, farmId, conditions |
| GET | `/schedule` | 推荐种植时间表 | farmId, cropId, year |
| GET | `/rotation` | 推荐轮作方案 | farmId, currentCrops, period |
| GET | `/companion` | 推荐伴生种植 | cropId, farmId |
| GET | `/fertilizer` | 推荐施肥方案 | farmId, cropId, soilTest |
| GET | `/irrigation` | 推荐灌溉方案 | farmId, cropId, weather |
| GET | `/pest-control` | 推荐病虫害防治 | farmId, cropId, season |
| GET | `/equipment` | 推荐农业设备 | farmId, cropId, operation |
| POST | `/optimize` | 优化种植方案 | farmId, objectives, constraints |
| GET | `/yield-prediction` | 产量预测推荐 | farmId, cropId, plantingPlan |
| GET | `/risk-assessment` | 风险评估推荐 | farmId, plantingPlan |

### 4. 用户行为 API (UserBehaviorController)

**基础路径**: `/api/recommendation/behavior`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| POST | `/track` | 记录用户行为 | userId, action, itemId, context |
| GET | `/history` | 获取用户行为历史 | userId, actionType, startDate, endDate |
| GET | `/patterns` | 获取用户行为模式 | userId, period |
| POST | `/batch-track` | 批量记录用户行为 | behaviors |
| GET | `/preferences` | 获取用户偏好 | userId |
| PUT | `/preferences` | 更新用户偏好 | userId, preferences |
| GET | `/similarity` | 获取相似用户 | userId, limit |
| GET | `/segments` | 获取用户分群 | userId |
| POST | `/implicit-feedback` | 记录隐式反馈 | userId, itemId, action, duration |
| GET | `/engagement` | 获取用户参与度 | userId, period |

### 5. 推荐模型 API (RecommendationModelController)

**基础路径**: `/api/recommendation/models`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/` | 获取推荐模型列表 | type, status |
| GET | `/{id}` | 获取推荐模型详情 | id |
| POST | `/` | 创建推荐模型 | Model对象 |
| PUT | `/{id}` | 更新推荐模型 | id, Model对象 |
| DELETE | `/{id}` | 删除推荐模型 | id |
| POST | `/{id}/train` | 训练推荐模型 | id, trainingData |
| GET | `/{id}/performance` | 获取模型性能 | id |
| POST | `/{id}/evaluate` | 评估推荐模型 | id, testData |
| PUT | `/{id}/deploy` | 部署推荐模型 | id |
| PUT | `/{id}/rollback` | 回滚推荐模型 | id, version |
| GET | `/{id}/predictions` | 获取模型预测 | id, input |
| POST | `/{id}/ab-test` | A/B测试 | id, testConfig |

### 6. 个性化 API (PersonalizationController)

**基础路径**: `/api/recommendation/personalization`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/profile/{userId}` | 获取用户画像 | userId |
| PUT | `/profile/{userId}` | 更新用户画像 | userId, profile |
| GET | `/interests/{userId}` | 获取用户兴趣 | userId |
| PUT | `/interests/{userId}` | 更新用户兴趣 | userId, interests |
| GET | `/tags/{userId}` | 获取用户标签 | userId |
| POST | `/tags/{userId}` | 添加用户标签 | userId, tags |
| GET | `/context` | 获取上下文推荐 | userId, location, time, weather |
| POST | `/cold-start` | 冷启动推荐 | userId, basicInfo |
| GET | `/diversity-score` | 获取推荐多样性评分 | userId, recommendations |
| POST | `/real-time` | 实时个性化推荐 | userId, context, limit |

### 7. 内容推荐 API (ContentRecommendationController)

**基础路径**: `/api/recommendation/content`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/articles` | 推荐文章 | userId, category, limit |
| GET | `/videos` | 推荐视频 | userId, category, limit |
| GET | `/courses` | 推荐课程 | userId, level, topic, limit |
| GET | `/experts` | 推荐专家 | userId, field, limit |
| GET | `/events` | 推荐活动 | userId, location, type, limit |
| GET | `/news` | 推荐新闻 | userId, category, limit |
| GET | `/trending-topics` | 推荐热门话题 | userId, limit |
| GET | `/related/{contentId}` | 相关内容推荐 | contentId, limit |
| POST | `/read-later` | 稍后阅读推荐 | userId, contentIds |
| GET | `/personalized-feed` | 个性化内容流 | userId, page, size |

## 数据库表结构

本模块涉及以下数据库表：

### 推荐核心表
- `recommendation` - 推荐记录表
- `recommendation_model` - 推荐模型表
- `recommendation_log` - 推荐日志表
- `recommendation_feedback` - 推荐反馈表

### 用户行为表
- `user_behavior` - 用户行为表
- `user_preference` - 用户偏好表
- `user_profile` - 用户画像表
- `user_interest` - 用户兴趣表
- `user_tag` - 用户标签表

### 物品特征表
- `item_feature` - 物品特征表
- `item_similarity` - 物品相似度表
- `item_category` - 物品分类表
- `item_tag` - 物品标签表

### 推荐算法表
- `algorithm_config` - 算法配置表
- `model_performance` - 模型性能表
- `ab_test` - A/B测试表
- `recommendation_rule` - 推荐规则表

## 推荐算法

### 1. 协同过滤算法
- **用户协同过滤**: 基于用户相似度的推荐
- **物品协同过滤**: 基于物品相似度的推荐
- **矩阵分解**: SVD、NMF等矩阵分解算法

### 2. 内容推荐算法
- **TF-IDF**: 基于文本特征的相似度计算
- **余弦相似度**: 基于特征向量的相似度计算
- **深度学习**: 基于神经网络的特征学习

### 3. 混合推荐算法
- **加权混合**: 多种算法结果的加权组合
- **切换混合**: 根据情况切换不同算法
- **分层混合**: 分层次应用不同算法

### 4. 深度学习算法
- **深度神经网络**: DNN推荐模型
- **卷积神经网络**: CNN特征提取
- **循环神经网络**: RNN序列推荐
- **注意力机制**: Attention机制增强推荐

## 推荐策略

### 1. 多样性策略
- **类别多样性**: 确保推荐结果涵盖多个类别
- **特征多样性**: 基于物品特征的多样性
- **时间多样性**: 考虑时间因素的多样性

### 2. 新颖性策略
- **流行度惩罚**: 降低热门物品的推荐权重
- **时间衰减**: 考虑物品的时效性
- **个性化新颖性**: 基于用户历史的新颖性

### 3. 实时性策略
- **在线学习**: 实时更新推荐模型
- **增量更新**: 增量式模型更新
- **缓存策略**: 智能缓存推荐结果

## 性能优化

1. **分布式计算**: 使用Spark等分布式框架处理大规模数据
2. **缓存机制**: Redis缓存热门推荐结果
3. **异步处理**: 模型训练和推荐计算异步执行
4. **预计算**: 预先计算相似度矩阵和推荐结果
5. **负载均衡**: 推荐服务的负载均衡和扩展

## 评估指标

### 1. 准确性指标
- **精确率 (Precision)**: 推荐结果的准确性
- **召回率 (Recall)**: 推荐结果的覆盖率
- **F1分数**: 精确率和召回率的调和平均

### 2. 排序指标
- **NDCG**: 归一化折损累积增益
- **MAP**: 平均精度均值
- **MRR**: 平均倒数排名

### 3. 业务指标
- **点击率 (CTR)**: 推荐点击率
- **转化率**: 推荐转化率
- **用户满意度**: 用户反馈评分

## 注意事项

1. **冷启动问题**: 新用户和新物品的推荐策略
2. **数据稀疏性**: 处理用户-物品交互数据稀疏问题
3. **实时性要求**: 平衡推荐准确性和响应时间
4. **隐私保护**: 保护用户隐私和数据安全
5. **算法公平性**: 避免推荐算法的偏见和歧视

---

**文档版本**: v1.0  
**最后更新**: 2024-12-01  
**维护人员**: Agriculture Team