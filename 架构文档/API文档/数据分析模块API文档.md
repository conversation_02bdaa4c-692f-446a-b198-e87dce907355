# 数据分析模块API文档

## 模块概述

数据分析模块是智慧农业平台的核心分析引擎，提供农业生产数据的统计分析、趋势预测、决策支持等功能，帮助用户做出科学的农业生产决策。

## 模块描述

本模块实现了完整的农业数据分析体系，包括生产数据统计、产量分析、成本效益分析、市场趋势分析、风险评估、智能推荐等功能。采用大数据技术和机器学习算法，为农业生产提供科学的数据支撑。

## 子任务完成情况

### ✅ 已完成任务

#### 1. 数据库层 (Mapper)
- [x] DataReportMapper - 数据报表数据访问层
- [x] StatisticsMapper - 统计数据访问层
- [x] AnalysisTaskMapper - 分析任务数据访问层
- [x] PredictionModelMapper - 预测模型数据访问层
- [x] DataSourceMapper - 数据源数据访问层
- [x] MetricsMapper - 指标数据访问层
- [x] DashboardMapper - 仪表板数据访问层

#### 2. 业务逻辑层 (Service)
- [x] DataAnalysisService & DataAnalysisServiceImpl - 数据分析业务逻辑
- [x] StatisticsService & StatisticsServiceImpl - 统计业务逻辑
- [x] ReportService & ReportServiceImpl - 报表业务逻辑
- [x] PredictionService & PredictionServiceImpl - 预测业务逻辑
- [x] DashboardService & DashboardServiceImpl - 仪表板业务逻辑
- [x] MetricsService & MetricsServiceImpl - 指标业务逻辑
- [x] DataMiningService & DataMiningServiceImpl - 数据挖掘业务逻辑

#### 3. 控制器层 (Controller)
- [x] DataAnalysisController - 数据分析API接口
- [x] StatisticsController - 统计API接口
- [x] ReportController - 报表API接口
- [x] PredictionController - 预测API接口
- [x] DashboardController - 仪表板API接口
- [x] MetricsController - 指标API接口
- [x] DataMiningController - 数据挖掘API接口

## API接口列表

### 1. 数据分析 API (DataAnalysisController)

**基础路径**: `/api/analysis/data`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| POST | `/analyze` | 执行数据分析 | analysisType, dataSource, parameters |
| GET | `/tasks` | 获取分析任务列表 | page, size, status, userId |
| GET | `/tasks/{id}` | 获取分析任务详情 | id |
| DELETE | `/tasks/{id}` | 删除分析任务 | id |
| GET | `/tasks/{id}/result` | 获取分析结果 | id |
| POST | `/tasks/{id}/export` | 导出分析结果 | id, format |
| GET | `/types` | 获取分析类型列表 | - |
| POST | `/custom` | 自定义分析 | query, parameters |
| GET | `/history` | 获取分析历史 | userId, startDate, endDate |

### 2. 统计分析 API (StatisticsController)

**基础路径**: `/api/analysis/statistics`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/production` | 获取生产统计 | farmId, period, cropId |
| GET | `/yield` | 获取产量统计 | farmId, year, cropId |
| GET | `/cost` | 获取成本统计 | farmId, period, category |
| GET | `/revenue` | 获取收入统计 | farmId, period |
| GET | `/efficiency` | 获取效率统计 | farmId, period, metric |
| GET | `/comparison` | 获取对比统计 | farmIds, period, metric |
| GET | `/trend` | 获取趋势统计 | farmId, metric, period |
| GET | `/distribution` | 获取分布统计 | farmId, dimension, period |
| GET | `/correlation` | 获取相关性分析 | farmId, variables, period |
| POST | `/custom` | 自定义统计查询 | query, parameters |

### 3. 报表管理 API (ReportController)

**基础路径**: `/api/analysis/reports`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/` | 获取报表列表 | page, size, category, userId |
| GET | `/{id}` | 获取报表详情 | id |
| POST | `/` | 创建报表 | Report对象 |
| PUT | `/{id}` | 更新报表 | id, Report对象 |
| DELETE | `/{id}` | 删除报表 | id |
| POST | `/{id}/generate` | 生成报表 | id, parameters |
| GET | `/{id}/data` | 获取报表数据 | id, parameters |
| POST | `/{id}/export` | 导出报表 | id, format, parameters |
| POST | `/{id}/schedule` | 设置报表定时生成 | id, schedule |
| GET | `/templates` | 获取报表模板 | category |
| POST | `/templates` | 创建报表模板 | template |

### 4. 预测分析 API (PredictionController)

**基础路径**: `/api/analysis/prediction`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| POST | `/yield` | 产量预测 | farmId, cropId, plantingDate, parameters |
| POST | `/weather` | 天气预测 | location, days |
| POST | `/price` | 价格预测 | cropId, market, period |
| POST | `/disease` | 病虫害预测 | farmId, cropId, conditions |
| POST | `/demand` | 需求预测 | productId, market, period |
| GET | `/models` | 获取预测模型列表 | type, status |
| POST | `/models` | 创建预测模型 | model |
| PUT | `/models/{id}` | 更新预测模型 | id, model |
| DELETE | `/models/{id}` | 删除预测模型 | id |
| POST | `/models/{id}/train` | 训练预测模型 | id, trainingData |
| GET | `/models/{id}/accuracy` | 获取模型准确率 | id |
| GET | `/history` | 获取预测历史 | type, farmId, period |

### 5. 仪表板管理 API (DashboardController)

**基础路径**: `/api/analysis/dashboard`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/` | 获取仪表板列表 | userId, category |
| GET | `/{id}` | 获取仪表板详情 | id |
| POST | `/` | 创建仪表板 | Dashboard对象 |
| PUT | `/{id}` | 更新仪表板 | id, Dashboard对象 |
| DELETE | `/{id}` | 删除仪表板 | id |
| GET | `/{id}/widgets` | 获取仪表板组件 | id |
| POST | `/{id}/widgets` | 添加仪表板组件 | id, Widget对象 |
| PUT | `/widgets/{widgetId}` | 更新仪表板组件 | widgetId, Widget对象 |
| DELETE | `/widgets/{widgetId}` | 删除仪表板组件 | widgetId |
| GET | `/{id}/data` | 获取仪表板数据 | id, refresh |
| POST | `/{id}/share` | 分享仪表板 | id, shareSettings |

### 6. 指标管理 API (MetricsController)

**基础路径**: `/api/analysis/metrics`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/` | 获取指标列表 | category, farmId |
| GET | `/{id}` | 获取指标详情 | id |
| POST | `/` | 创建指标 | Metric对象 |
| PUT | `/{id}` | 更新指标 | id, Metric对象 |
| DELETE | `/{id}` | 删除指标 | id |
| GET | `/{id}/values` | 获取指标值 | id, startDate, endDate |
| POST | `/{id}/values` | 记录指标值 | id, value, timestamp |
| GET | `/{id}/trend` | 获取指标趋势 | id, period |
| POST | `/{id}/alert` | 设置指标告警 | id, alertRule |
| GET | `/categories` | 获取指标分类 | - |
| GET | `/kpi` | 获取KPI指标 | farmId, period |

### 7. 数据挖掘 API (DataMiningController)

**基础路径**: `/api/analysis/mining`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| POST | `/cluster` | 聚类分析 | dataSource, algorithm, parameters |
| POST | `/classify` | 分类分析 | dataSource, targetVariable, algorithm |
| POST | `/associate` | 关联规则挖掘 | dataSource, minSupport, minConfidence |
| POST | `/anomaly` | 异常检测 | dataSource, algorithm, threshold |
| POST | `/pattern` | 模式识别 | dataSource, patternType, parameters |
| GET | `/algorithms` | 获取算法列表 | type |
| POST | `/feature-selection` | 特征选择 | dataSource, method, targetVariable |
| POST | `/dimensionality-reduction` | 降维分析 | dataSource, method, dimensions |
| GET | `/results/{taskId}` | 获取挖掘结果 | taskId |
| POST | `/evaluate` | 模型评估 | model, testData, metrics |

## 数据库表结构

本模块涉及以下数据库表：

### 分析任务表
- `analysis_task` - 分析任务表
- `analysis_result` - 分析结果表
- `analysis_log` - 分析日志表

### 统计数据表
- `statistics_data` - 统计数据表
- `statistics_config` - 统计配置表
- `statistics_cache` - 统计缓存表

### 报表管理表
- `report` - 报表表
- `report_template` - 报表模板表
- `report_schedule` - 报表调度表
- `report_data` - 报表数据表

### 预测模型表
- `prediction_model` - 预测模型表
- `prediction_result` - 预测结果表
- `model_training_log` - 模型训练日志表

### 仪表板表
- `dashboard` - 仪表板表
- `dashboard_widget` - 仪表板组件表
- `widget_config` - 组件配置表

### 指标管理表
- `metric` - 指标表
- `metric_value` - 指标值表
- `metric_alert` - 指标告警表

### 数据挖掘表
- `mining_task` - 挖掘任务表
- `mining_result` - 挖掘结果表
- `algorithm_config` - 算法配置表

## 核心算法

### 1. 统计分析算法
- 描述性统计
- 相关性分析
- 回归分析
- 时间序列分析

### 2. 机器学习算法
- 线性回归
- 决策树
- 随机森林
- 支持向量机
- 神经网络

### 3. 数据挖掘算法
- K-means聚类
- DBSCAN聚类
- Apriori关联规则
- 异常检测算法

### 4. 预测算法
- ARIMA时间序列预测
- 指数平滑
- 机器学习预测模型

## 性能优化

1. **数据缓存**: 使用Redis缓存频繁查询的统计数据
2. **异步处理**: 大数据量分析任务采用异步处理
3. **分布式计算**: 支持Spark等分布式计算框架
4. **数据分区**: 按时间和农场对数据进行分区存储
5. **索引优化**: 为查询字段建立合适的索引

## 注意事项

1. **数据质量**: 确保输入数据的准确性和完整性
2. **计算资源**: 大数据分析需要充足的计算资源
3. **模型更新**: 定期更新预测模型以保持准确性
4. **权限控制**: 敏感数据分析需要严格的权限控制
5. **结果解释**: 提供分析结果的解释和建议

---

**文档版本**: v1.0  
**最后更新**: 2024-12-01  
**维护人员**: Agriculture Team