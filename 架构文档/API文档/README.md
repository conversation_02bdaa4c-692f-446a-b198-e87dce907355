# 智慧农业平台 API 文档总览

## 文档概述

本文档集合包含了智慧农业平台（SFAP - Smart Farm Agriculture Platform）所有模块的详细API文档。平台采用微服务架构设计，各模块相对独立，通过RESTful API进行交互。

## 平台架构

智慧农业平台采用分层架构设计：

```
┌─────────────────────────────────────────────────────────────┐
│                        前端层                                │
│  Web管理端 │ 移动端APP │ 小程序 │ 第三方集成                  │
├─────────────────────────────────────────────────────────────┤
│                        API网关层                             │
│  路由转发 │ 负载均衡 │ 认证授权 │ 限流熔断 │ 日志监控          │
├─────────────────────────────────────────────────────────────┤
│                        业务服务层                            │
│  用户管理 │ 商城模块 │ 农业管理 │ 数据分析 │ 智能推荐 │ 系统管理 │
├─────────────────────────────────────────────────────────────┤
│                        数据访问层                            │
│  MySQL │ Redis │ MongoDB │ Elasticsearch │ InfluxDB        │
├─────────────────────────────────────────────────────────────┤
│                        基础设施层                            │
│  Docker │ Kubernetes │ 消息队列 │ 文件存储 │ 监控告警        │
└─────────────────────────────────────────────────────────────┘
```

## 模块文档列表

### 1. [用户管理模块](./用户管理模块API文档.md)

**模块描述**: 负责用户注册、登录、权限管理、个人信息管理等功能

**核心功能**:
- 用户认证与授权
- 角色权限管理
- 用户资料管理
- 地址管理
- 安全设置

**主要API**:
- 认证管理 API (`/api/auth`)
- 用户管理 API (`/api/users`)
- 角色管理 API (`/api/roles`)
- 权限管理 API (`/api/permissions`)
- 用户地址 API (`/api/user-addresses`)
- 用户资料 API (`/api/user-profiles`)

### 2. [商城模块](./商城模块API文档.md)

**模块描述**: 提供完整的电商功能，包括商品管理、购物车、订单处理、评价系统等

**核心功能**:
- 商品展示与管理
- 购物车管理
- 订单处理流程
- 评价与收藏
- 分类管理

**主要API**:
- 商品管理 API (`/api/mall/products`)
- 分类管理 API (`/api/mall/categories`)
- 购物车管理 API (`/api/mall/cart`)
- 订单管理 API (`/api/mall/orders`)
- 评价管理 API (`/api/mall/reviews`)
- 收藏管理 API (`/api/mall/favorites`)

### 3. [农业管理模块](./农业管理模块API文档.md)

**模块描述**: 核心农业生产管理功能，包括农场管理、作物种植、设备监控等

**核心功能**:
- 农场信息管理
- 作物种植管理
- 农事活动记录
- 设备监控管理
- 病虫害防治
- 收获管理

**主要API**:
- 农场管理 API (`/api/agriculture/farms`)
- 作物管理 API (`/api/agriculture/crops`)
- 种植管理 API (`/api/agriculture/planting`)
- 农事活动 API (`/api/agriculture/activities`)
- 设备管理 API (`/api/agriculture/equipment`)
- 传感器管理 API (`/api/agriculture/sensors`)
- 病虫害管理 API (`/api/agriculture/pest-disease`)
- 收获管理 API (`/api/agriculture/harvest`)

### 4. [数据分析模块](./数据分析模块API文档.md)

**模块描述**: 提供农业生产数据的统计分析、趋势预测、决策支持等功能

**核心功能**:
- 生产数据统计
- 趋势分析预测
- 报表生成
- 仪表板展示
- 数据挖掘

**主要API**:
- 数据分析 API (`/api/analysis/data`)
- 统计分析 API (`/api/analysis/statistics`)
- 报表管理 API (`/api/analysis/reports`)
- 预测分析 API (`/api/analysis/prediction`)
- 仪表板管理 API (`/api/analysis/dashboard`)
- 指标管理 API (`/api/analysis/metrics`)
- 数据挖掘 API (`/api/analysis/mining`)

### 5. [智能推荐模块](./智能推荐模块API文档.md)

**模块描述**: 基于AI技术提供个性化推荐服务，包括商品推荐、种植建议等

**核心功能**:
- 商品个性化推荐
- 种植方案推荐
- 用户行为分析
- 推荐模型管理
- 内容推荐

**主要API**:
- 通用推荐 API (`/api/recommendation`)
- 商品推荐 API (`/api/recommendation/products`)
- 种植推荐 API (`/api/recommendation/planting`)
- 用户行为 API (`/api/recommendation/behavior`)
- 推荐模型 API (`/api/recommendation/models`)
- 个性化 API (`/api/recommendation/personalization`)
- 内容推荐 API (`/api/recommendation/content`)

### 6. [系统管理模块](./系统管理模块API文档.md)

**模块描述**: 提供系统配置、监控、日志、备份等系统级管理功能

**核心功能**:
- 系统配置管理
- 日志管理
- 监控告警
- 数据备份
- 系统维护
- 安全管理

**主要API**:
- 系统配置 API (`/api/system/config`)
- 日志管理 API (`/api/system/logs`)
- 监控管理 API (`/api/system/monitoring`)
- 告警管理 API (`/api/system/alerts`)
- 备份管理 API (`/api/system/backup`)
- 维护管理 API (`/api/system/maintenance`)
- 安全管理 API (`/api/system/security`)

## API 设计规范

### 1. RESTful 设计原则

- **资源导向**: URL表示资源，HTTP方法表示操作
- **统一接口**: 使用标准HTTP方法（GET、POST、PUT、DELETE）
- **无状态**: 每个请求包含处理所需的所有信息
- **分层系统**: 支持负载均衡、缓存等中间层

### 2. URL 命名规范

```
# 基础格式
/api/{module}/{resource}

# 示例
GET    /api/mall/products          # 获取商品列表
GET    /api/mall/products/{id}     # 获取指定商品
POST   /api/mall/products          # 创建商品
PUT    /api/mall/products/{id}     # 更新商品
DELETE /api/mall/products/{id}     # 删除商品
```

### 3. HTTP 状态码

| 状态码 | 含义 | 使用场景 |
|--------|------|----------|
| 200 | OK | 请求成功 |
| 201 | Created | 资源创建成功 |
| 204 | No Content | 删除成功 |
| 400 | Bad Request | 请求参数错误 |
| 401 | Unauthorized | 未认证 |
| 403 | Forbidden | 无权限 |
| 404 | Not Found | 资源不存在 |
| 500 | Internal Server Error | 服务器内部错误 |

### 4. 统一响应格式

```json
{
  "code": 200,
  "message": "success",
  "data": {
    // 响应数据
  },
  "timestamp": "2024-12-01T10:00:00Z"
}
```

### 5. 分页格式

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "content": [],
    "page": 1,
    "size": 20,
    "total": 100,
    "totalPages": 5
  }
}
```

## 认证与授权

### 1. JWT 认证

所有API请求（除公开接口外）都需要在请求头中携带JWT令牌：

```
Authorization: Bearer <jwt_token>
```

### 2. 权限控制

基于RBAC（Role-Based Access Control）模型：

- **用户（User）**: 系统使用者
- **角色（Role）**: 权限的集合
- **权限（Permission）**: 具体的操作权限
- **资源（Resource）**: 受保护的系统资源

### 3. API 权限标识

每个API接口都有对应的权限标识，格式为：`{module}:{resource}:{action}`

```
# 示例
mall:product:read    # 商品查看权限
mall:product:write   # 商品编辑权限
mall:order:manage    # 订单管理权限
```

## 错误处理

### 1. 错误响应格式

```json
{
  "code": 400,
  "message": "参数验证失败",
  "errors": [
    {
      "field": "email",
      "message": "邮箱格式不正确"
    }
  ],
  "timestamp": "2024-12-01T10:00:00Z"
}
```

### 2. 常见错误码

| 错误码 | 含义 | 描述 |
|--------|------|------|
| 1001 | 参数验证失败 | 请求参数不符合要求 |
| 1002 | 资源不存在 | 请求的资源不存在 |
| 1003 | 权限不足 | 用户无权限访问资源 |
| 1004 | 业务逻辑错误 | 业务规则验证失败 |
| 1005 | 系统异常 | 系统内部错误 |

## 版本控制

### 1. API 版本策略

采用URL路径版本控制：

```
/api/v1/mall/products
/api/v2/mall/products
```

### 2. 版本兼容性

- **向后兼容**: 新版本保持对旧版本的兼容
- **废弃通知**: 提前通知API废弃计划
- **平滑迁移**: 提供迁移指南和工具

## 性能优化

### 1. 缓存策略

- **Redis缓存**: 热点数据缓存
- **HTTP缓存**: 静态资源缓存
- **查询缓存**: 数据库查询结果缓存

### 2. 分页优化

- **游标分页**: 大数据量分页优化
- **索引优化**: 分页查询索引优化
- **缓存分页**: 分页结果缓存

### 3. 并发控制

- **限流**: API请求频率限制
- **熔断**: 服务故障自动熔断
- **降级**: 服务降级策略

## 监控与日志

### 1. API 监控

- **响应时间**: 监控API响应时间
- **成功率**: 监控API成功率
- **并发量**: 监控API并发请求量
- **错误率**: 监控API错误率

### 2. 日志记录

- **访问日志**: 记录所有API访问
- **错误日志**: 记录系统错误信息
- **业务日志**: 记录关键业务操作
- **安全日志**: 记录安全相关事件

## 测试与文档

### 1. API 测试

- **单元测试**: 接口逻辑单元测试
- **集成测试**: 模块间集成测试
- **性能测试**: API性能压力测试
- **安全测试**: API安全漏洞测试

### 2. 文档维护

- **Swagger文档**: 自动生成API文档
- **示例代码**: 提供调用示例
- **变更日志**: 记录API变更历史
- **最佳实践**: 提供使用最佳实践

## 部署与运维

### 1. 容器化部署

```yaml
# docker-compose.yml 示例
version: '3.8'
services:
  api-gateway:
    image: sfap/api-gateway:latest
    ports:
      - "8080:8080"
  
  user-service:
    image: sfap/user-service:latest
    
  mall-service:
    image: sfap/mall-service:latest
```

### 2. 服务发现

- **Consul**: 服务注册与发现
- **负载均衡**: 服务负载均衡
- **健康检查**: 服务健康状态检查

### 3. 配置管理

- **配置中心**: 统一配置管理
- **环境隔离**: 不同环境配置隔离
- **动态配置**: 运行时配置更新

## 安全最佳实践

### 1. 数据安全

- **数据加密**: 敏感数据加密存储
- **传输加密**: HTTPS传输加密
- **数据脱敏**: 日志数据脱敏

### 2. 访问控制

- **最小权限**: 最小权限原则
- **权限审计**: 定期权限审计
- **访问日志**: 完整访问日志记录

### 3. 安全防护

- **SQL注入防护**: 参数化查询
- **XSS防护**: 输入输出过滤
- **CSRF防护**: CSRF令牌验证

---

## 联系信息

**项目团队**: Agriculture Development Team  
**技术支持**: <EMAIL>  
**文档维护**: <EMAIL>  

**最后更新**: 2024-12-01  
**文档版本**: v1.0

---

> 本文档集合为智慧农业平台的完整API参考文档，包含了所有模块的详细接口说明。如有疑问或建议，请联系项目团队。