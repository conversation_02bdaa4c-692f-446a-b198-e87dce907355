# 农业管理模块API文档

## 模块概述

农业管理模块是智慧农业平台的核心业务模块，提供农场管理、作物种植、农事记录、设备监控等农业生产全流程管理功能。

## 模块描述

本模块实现了完整的农业生产管理体系，包括农场信息管理、作物生长周期管理、农事活动记录、农业设备监控、病虫害防治、产量统计等核心功能。支持多农场管理，提供数据分析和决策支持。

## 子任务完成情况

### ✅ 已完成任务

#### 1. 数据库层 (Mapper)
- [x] FarmMapper - 农场数据访问层
- [x] CropMapper - 作物数据访问层
- [x] PlantingRecordMapper - 种植记录数据访问层
- [x] FarmingActivityMapper - 农事活动数据访问层
- [x] EquipmentMapper - 设备数据访问层
- [x] SensorDataMapper - 传感器数据访问层
- [x] PestDiseaseMapper - 病虫害数据访问层
- [x] HarvestRecordMapper - 收获记录数据访问层

#### 2. 业务逻辑层 (Service)
- [x] FarmService & FarmServiceImpl - 农场业务逻辑
- [x] CropService & CropServiceImpl - 作物业务逻辑
- [x] PlantingService & PlantingServiceImpl - 种植业务逻辑
- [x] FarmingActivityService & FarmingActivityServiceImpl - 农事活动业务逻辑
- [x] EquipmentService & EquipmentServiceImpl - 设备业务逻辑
- [x] SensorService & SensorServiceImpl - 传感器业务逻辑
- [x] PestDiseaseService & PestDiseaseServiceImpl - 病虫害业务逻辑
- [x] HarvestService & HarvestServiceImpl - 收获业务逻辑

#### 3. 控制器层 (Controller)
- [x] FarmController - 农场API接口
- [x] CropController - 作物API接口
- [x] PlantingController - 种植API接口
- [x] FarmingActivityController - 农事活动API接口
- [x] EquipmentController - 设备API接口
- [x] SensorController - 传感器API接口
- [x] PestDiseaseController - 病虫害API接口
- [x] HarvestController - 收获API接口

## API接口列表

### 1. 农场管理 API (FarmController)

**基础路径**: `/api/agriculture/farms`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/` | 分页查询农场列表 | page, size, ownerId, region, status |
| GET | `/{id}` | 根据ID获取农场详情 | id |
| POST | `/` | 创建农场 | Farm对象 |
| PUT | `/{id}` | 更新农场信息 | id, Farm对象 |
| DELETE | `/{id}` | 删除农场 | id |
| GET | `/{id}/areas` | 获取农场区域列表 | id |
| POST | `/{id}/areas` | 添加农场区域 | id, FarmArea对象 |
| PUT | `/{id}/status` | 更新农场状态 | id, status |
| GET | `/{id}/stats` | 获取农场统计信息 | id |
| GET | `/{id}/weather` | 获取农场天气信息 | id |
| GET | `/user/{userId}` | 获取用户农场列表 | userId |

### 2. 作物管理 API (CropController)

**基础路径**: `/api/agriculture/crops`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/` | 获取所有作物类型 | - |
| GET | `/{id}` | 根据ID获取作物详情 | id |
| POST | `/` | 创建作物类型 | Crop对象 |
| PUT | `/{id}` | 更新作物信息 | id, Crop对象 |
| DELETE | `/{id}` | 删除作物类型 | id |
| GET | `/category/{category}` | 根据分类获取作物列表 | category |
| GET | `/{id}/growth-stages` | 获取作物生长阶段 | id |
| GET | `/{id}/care-guide` | 获取作物养护指南 | id |
| GET | `/search` | 搜索作物 | keyword, category |

### 3. 种植管理 API (PlantingController)

**基础路径**: `/api/agriculture/planting`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/` | 分页查询种植记录 | page, size, farmId, cropId, season |
| GET | `/{id}` | 根据ID获取种植记录详情 | id |
| POST | `/` | 创建种植记录 | PlantingRecord对象 |
| PUT | `/{id}` | 更新种植记录 | id, PlantingRecord对象 |
| DELETE | `/{id}` | 删除种植记录 | id |
| PUT | `/{id}/stage` | 更新种植阶段 | id, stage, stageDate |
| GET | `/{id}/timeline` | 获取种植时间线 | id |
| GET | `/farm/{farmId}` | 获取农场种植记录 | farmId, season |
| GET | `/{id}/progress` | 获取种植进度 | id |
| POST | `/{id}/complete` | 完成种植 | id, harvestDate, yield |

### 4. 农事活动管理 API (FarmingActivityController)

**基础路径**: `/api/agriculture/activities`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/` | 分页查询农事活动 | page, size, farmId, activityType, startDate, endDate |
| GET | `/{id}` | 根据ID获取活动详情 | id |
| POST | `/` | 创建农事活动 | FarmingActivity对象 |
| PUT | `/{id}` | 更新农事活动 | id, FarmingActivity对象 |
| DELETE | `/{id}` | 删除农事活动 | id |
| PUT | `/{id}/complete` | 完成农事活动 | id, completionNotes |
| GET | `/calendar` | 获取农事日历 | farmId, year, month |
| GET | `/pending` | 获取待办农事活动 | farmId |
| GET | `/types` | 获取农事活动类型 | - |
| POST | `/batch` | 批量创建农事活动 | activities |

### 5. 设备管理 API (EquipmentController)

**基础路径**: `/api/agriculture/equipment`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/` | 分页查询设备列表 | page, size, farmId, equipmentType, status |
| GET | `/{id}` | 根据ID获取设备详情 | id |
| POST | `/` | 添加设备 | Equipment对象 |
| PUT | `/{id}` | 更新设备信息 | id, Equipment对象 |
| DELETE | `/{id}` | 删除设备 | id |
| PUT | `/{id}/status` | 更新设备状态 | id, status |
| GET | `/{id}/maintenance` | 获取设备维护记录 | id |
| POST | `/{id}/maintenance` | 添加维护记录 | id, MaintenanceRecord对象 |
| GET | `/{id}/usage` | 获取设备使用记录 | id, startDate, endDate |
| POST | `/{id}/control` | 控制设备 | id, command, parameters |
| GET | `/farm/{farmId}` | 获取农场设备列表 | farmId |

### 6. 传感器管理 API (SensorController)

**基础路径**: `/api/agriculture/sensors`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/` | 分页查询传感器列表 | page, size, farmId, sensorType, status |
| GET | `/{id}` | 根据ID获取传感器详情 | id |
| POST | `/` | 添加传感器 | Sensor对象 |
| PUT | `/{id}` | 更新传感器信息 | id, Sensor对象 |
| DELETE | `/{id}` | 删除传感器 | id |
| GET | `/{id}/data` | 获取传感器数据 | id, startTime, endTime, interval |
| POST | `/{id}/data` | 上传传感器数据 | id, SensorData对象 |
| GET | `/{id}/latest` | 获取传感器最新数据 | id |
| PUT | `/{id}/calibrate` | 校准传感器 | id, calibrationData |
| GET | `/farm/{farmId}/data` | 获取农场传感器数据汇总 | farmId, dataType, period |
| GET | `/{id}/alerts` | 获取传感器告警 | id |

### 7. 病虫害管理 API (PestDiseaseController)

**基础路径**: `/api/agriculture/pest-disease`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/` | 分页查询病虫害记录 | page, size, farmId, cropId, type, severity |
| GET | `/{id}` | 根据ID获取病虫害详情 | id |
| POST | `/` | 记录病虫害 | PestDisease对象 |
| PUT | `/{id}` | 更新病虫害记录 | id, PestDisease对象 |
| DELETE | `/{id}` | 删除病虫害记录 | id |
| POST | `/{id}/treatment` | 添加治疗记录 | id, TreatmentRecord对象 |
| GET | `/{id}/treatments` | 获取治疗记录列表 | id |
| GET | `/knowledge` | 获取病虫害知识库 | keyword, cropId |
| POST | `/identify` | 病虫害识别 | image, cropId |
| GET | `/statistics` | 获取病虫害统计 | farmId, period |
| GET | `/alerts` | 获取病虫害预警 | farmId, region |

### 8. 收获管理 API (HarvestController)

**基础路径**: `/api/agriculture/harvest`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/` | 分页查询收获记录 | page, size, farmId, cropId, season |
| GET | `/{id}` | 根据ID获取收获记录详情 | id |
| POST | `/` | 创建收获记录 | HarvestRecord对象 |
| PUT | `/{id}` | 更新收获记录 | id, HarvestRecord对象 |
| DELETE | `/{id}` | 删除收获记录 | id |
| GET | `/{id}/quality` | 获取收获质量检测 | id |
| POST | `/{id}/quality` | 添加质量检测记录 | id, QualityRecord对象 |
| GET | `/statistics` | 获取收获统计 | farmId, year, cropId |
| GET | `/yield-analysis` | 获取产量分析 | farmId, period |
| POST | `/{id}/storage` | 记录储存信息 | id, StorageRecord对象 |
| GET | `/{id}/storage` | 获取储存记录 | id |

## 数据库表结构

本模块涉及以下数据库表：

### 农场管理表
- `farm` - 农场主表
- `farm_area` - 农场区域表
- `farm_user` - 农场用户关联表

### 作物管理表
- `crop` - 作物类型表
- `crop_variety` - 作物品种表
- `crop_growth_stage` - 作物生长阶段表
- `planting_record` - 种植记录表

### 农事活动表
- `farming_activity` - 农事活动表
- `activity_type` - 活动类型表
- `farming_calendar` - 农事日历表

### 设备管理表
- `equipment` - 设备表
- `equipment_type` - 设备类型表
- `equipment_maintenance` - 设备维护表
- `equipment_usage` - 设备使用记录表

### 传感器数据表
- `sensor` - 传感器表
- `sensor_data` - 传感器数据表
- `sensor_alert` - 传感器告警表

### 病虫害管理表
- `pest_disease` - 病虫害记录表
- `pest_disease_type` - 病虫害类型表
- `treatment_record` - 治疗记录表
- `pest_disease_knowledge` - 病虫害知识库表

### 收获管理表
- `harvest_record` - 收获记录表
- `quality_record` - 质量检测记录表
- `storage_record` - 储存记录表

## 特色功能

1. **智能监控**: 基于IoT传感器的实时环境监控
2. **数据分析**: 农业生产数据的统计分析和可视化
3. **决策支持**: 基于历史数据和AI算法的种植建议
4. **病虫害识别**: 基于图像识别的病虫害自动识别
5. **产量预测**: 基于机器学习的产量预测模型
6. **农事提醒**: 智能农事日历和任务提醒

## 注意事项

1. **数据准确性**: 确保传感器数据的准确性和及时性
2. **设备兼容性**: 支持多种农业设备和传感器的接入
3. **数据安全**: 农业生产数据的安全存储和传输
4. **离线支持**: 考虑农村网络环境，提供离线数据同步
5. **扩展性**: 支持大规模农场和多种作物类型

---

**文档版本**: v1.0  
**最后更新**: 2024-12-01  
**维护人员**: Agriculture Team