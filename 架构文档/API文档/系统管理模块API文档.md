# 系统管理模块API文档

## 模块概述

系统管理模块是智慧农业平台的核心管理模块，提供系统配置、日志管理、监控告警、数据备份、系统维护等功能，确保平台的稳定运行和高效管理。

## 模块描述

本模块实现了完整的系统管理体系，包括系统配置管理、日志收集与分析、性能监控、告警通知、数据备份恢复、系统维护、安全管理等核心功能。采用微服务架构，支持分布式部署和集群管理。

## 子任务完成情况

### ✅ 已完成任务

#### 1. 数据库层 (Mapper)
- [x] SystemConfigMapper - 系统配置数据访问层
- [x] SystemLogMapper - 系统日志数据访问层
- [x] MonitoringMapper - 监控数据访问层
- [x] AlertMapper - 告警数据访问层
- [x] BackupMapper - 备份数据访问层
- [x] MaintenanceMapper - 维护数据访问层
- [x] SecurityLogMapper - 安全日志数据访问层

#### 2. 业务逻辑层 (Service)
- [x] SystemConfigService & SystemConfigServiceImpl - 系统配置业务逻辑
- [x] LogService & LogServiceImpl - 日志业务逻辑
- [x] MonitoringService & MonitoringServiceImpl - 监控业务逻辑
- [x] AlertService & AlertServiceImpl - 告警业务逻辑
- [x] BackupService & BackupServiceImpl - 备份业务逻辑
- [x] MaintenanceService & MaintenanceServiceImpl - 维护业务逻辑
- [x] SecurityService & SecurityServiceImpl - 安全业务逻辑

#### 3. 控制器层 (Controller)
- [x] SystemConfigController - 系统配置API接口
- [x] LogController - 日志API接口
- [x] MonitoringController - 监控API接口
- [x] AlertController - 告警API接口
- [x] BackupController - 备份API接口
- [x] MaintenanceController - 维护API接口
- [x] SecurityController - 安全API接口

## API接口列表

### 1. 系统配置管理 API (SystemConfigController)

**基础路径**: `/api/system/config`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/` | 获取系统配置列表 | category, keyword |
| GET | `/{key}` | 根据键获取配置值 | key |
| PUT | `/{key}` | 更新配置值 | key, value |
| POST | `/` | 创建系统配置 | SystemConfig对象 |
| DELETE | `/{key}` | 删除系统配置 | key |
| GET | `/categories` | 获取配置分类 | - |
| POST | `/batch` | 批量更新配置 | configs |
| GET | `/export` | 导出系统配置 | category |
| POST | `/import` | 导入系统配置 | configFile |
| POST | `/reset` | 重置配置为默认值 | keys |
| GET | `/history/{key}` | 获取配置变更历史 | key |

### 2. 日志管理 API (LogController)

**基础路径**: `/api/system/logs`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/` | 分页查询系统日志 | page, size, level, module, startTime, endTime |
| GET | `/{id}` | 根据ID获取日志详情 | id |
| DELETE | `/{id}` | 删除日志 | id |
| POST | `/` | 记录系统日志 | SystemLog对象 |
| GET | `/levels` | 获取日志级别列表 | - |
| GET | `/modules` | 获取日志模块列表 | - |
| GET | `/statistics` | 获取日志统计信息 | period, module |
| POST | `/export` | 导出日志 | filters, format |
| DELETE | `/cleanup` | 清理过期日志 | beforeDate |
| GET | `/real-time` | 实时日志流 | level, module |
| POST | `/search` | 搜索日志 | keyword, filters |

### 3. 监控管理 API (MonitoringController)

**基础路径**: `/api/system/monitoring`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/metrics` | 获取系统指标 | category, period |
| GET | `/health` | 获取系统健康状态 | - |
| GET | `/performance` | 获取性能指标 | metric, period |
| GET | `/resources` | 获取资源使用情况 | type |
| GET | `/services` | 获取服务状态 | - |
| GET | `/databases` | 获取数据库状态 | - |
| GET | `/cache` | 获取缓存状态 | - |
| GET | `/queues` | 获取队列状态 | - |
| POST | `/custom-metric` | 记录自定义指标 | metric, value, timestamp |
| GET | `/dashboard` | 获取监控仪表板数据 | - |
| GET | `/trends` | 获取趋势分析 | metric, period |

### 4. 告警管理 API (AlertController)

**基础路径**: `/api/system/alerts`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/` | 分页查询告警列表 | page, size, level, status, startTime, endTime |
| GET | `/{id}` | 根据ID获取告警详情 | id |
| POST | `/` | 创建告警 | Alert对象 |
| PUT | `/{id}/status` | 更新告警状态 | id, status |
| POST | `/{id}/acknowledge` | 确认告警 | id, comment |
| DELETE | `/{id}` | 删除告警 | id |
| GET | `/rules` | 获取告警规则列表 | - |
| POST | `/rules` | 创建告警规则 | AlertRule对象 |
| PUT | `/rules/{id}` | 更新告警规则 | id, AlertRule对象 |
| DELETE | `/rules/{id}` | 删除告警规则 | id |
| GET | `/statistics` | 获取告警统计 | period |
| POST | `/test` | 测试告警规则 | ruleId |

### 5. 备份管理 API (BackupController)

**基础路径**: `/api/system/backup`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/` | 获取备份列表 | page, size, type, status |
| GET | `/{id}` | 根据ID获取备份详情 | id |
| POST | `/create` | 创建备份 | type, description |
| POST | `/{id}/restore` | 恢复备份 | id, options |
| DELETE | `/{id}` | 删除备份 | id |
| GET | `/{id}/download` | 下载备份文件 | id |
| POST | `/upload` | 上传备份文件 | backupFile |
| GET | `/schedules` | 获取备份计划列表 | - |
| POST | `/schedules` | 创建备份计划 | BackupSchedule对象 |
| PUT | `/schedules/{id}` | 更新备份计划 | id, BackupSchedule对象 |
| DELETE | `/schedules/{id}` | 删除备份计划 | id |
| POST | `/verify/{id}` | 验证备份完整性 | id |

### 6. 维护管理 API (MaintenanceController)

**基础路径**: `/api/system/maintenance`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/tasks` | 获取维护任务列表 | page, size, status, type |
| GET | `/tasks/{id}` | 根据ID获取维护任务详情 | id |
| POST | `/tasks` | 创建维护任务 | MaintenanceTask对象 |
| PUT | `/tasks/{id}` | 更新维护任务 | id, MaintenanceTask对象 |
| DELETE | `/tasks/{id}` | 删除维护任务 | id |
| POST | `/tasks/{id}/execute` | 执行维护任务 | id |
| GET | `/schedules` | 获取维护计划列表 | - |
| POST | `/schedules` | 创建维护计划 | MaintenanceSchedule对象 |
| GET | `/windows` | 获取维护窗口 | - |
| POST | `/windows` | 设置维护窗口 | MaintenanceWindow对象 |
| GET | `/status` | 获取系统维护状态 | - |
| POST | `/mode` | 设置维护模式 | enabled, message |

### 7. 安全管理 API (SecurityController)

**基础路径**: `/api/system/security`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/logs` | 获取安全日志 | page, size, type, level, startTime, endTime |
| POST | `/logs` | 记录安全事件 | SecurityLog对象 |
| GET | `/threats` | 获取安全威胁列表 | page, size, severity |
| POST | `/scan` | 执行安全扫描 | scanType, target |
| GET | `/scan/{id}/result` | 获取扫描结果 | id |
| GET | `/policies` | 获取安全策略列表 | - |
| POST | `/policies` | 创建安全策略 | SecurityPolicy对象 |
| PUT | `/policies/{id}` | 更新安全策略 | id, SecurityPolicy对象 |
| DELETE | `/policies/{id}` | 删除安全策略 | id |
| GET | `/compliance` | 获取合规检查结果 | - |
| POST | `/compliance/check` | 执行合规检查 | checkType |
| GET | `/certificates` | 获取证书列表 | - |
| POST | `/certificates` | 上传证书 | certificate |
| GET | `/firewall/rules` | 获取防火墙规则 | - |
| POST | `/firewall/rules` | 创建防火墙规则 | FirewallRule对象 |

## 数据库表结构

本模块涉及以下数据库表：

### 系统配置表
- `system_config` - 系统配置表
- `config_category` - 配置分类表
- `config_history` - 配置变更历史表

### 日志管理表
- `system_log` - 系统日志表
- `log_level` - 日志级别表
- `log_module` - 日志模块表
- `log_archive` - 日志归档表

### 监控管理表
- `monitoring_metric` - 监控指标表
- `performance_data` - 性能数据表
- `health_check` - 健康检查表
- `service_status` - 服务状态表

### 告警管理表
- `alert` - 告警表
- `alert_rule` - 告警规则表
- `alert_history` - 告警历史表
- `notification_config` - 通知配置表

### 备份管理表
- `backup_record` - 备份记录表
- `backup_schedule` - 备份计划表
- `restore_log` - 恢复日志表

### 维护管理表
- `maintenance_task` - 维护任务表
- `maintenance_schedule` - 维护计划表
- `maintenance_window` - 维护窗口表
- `maintenance_log` - 维护日志表

### 安全管理表
- `security_log` - 安全日志表
- `security_threat` - 安全威胁表
- `security_policy` - 安全策略表
- `security_scan` - 安全扫描表
- `firewall_rule` - 防火墙规则表

## 核心功能

### 1. 系统配置管理
- **配置分类**: 按功能模块分类管理配置
- **动态配置**: 支持运行时动态更新配置
- **配置验证**: 配置值的格式和范围验证
- **配置历史**: 记录配置变更历史
- **配置备份**: 配置的导入导出功能

### 2. 日志管理
- **日志收集**: 统一收集各模块日志
- **日志分级**: 支持多种日志级别
- **日志搜索**: 强大的日志搜索和过滤功能
- **日志归档**: 自动归档和清理过期日志
- **实时监控**: 实时日志流监控

### 3. 监控告警
- **性能监控**: CPU、内存、磁盘、网络监控
- **服务监控**: 应用服务健康状态监控
- **数据库监控**: 数据库性能和连接监控
- **自定义指标**: 支持业务自定义监控指标
- **智能告警**: 基于规则的智能告警机制

### 4. 数据备份
- **自动备份**: 定时自动备份数据
- **增量备份**: 支持增量和全量备份
- **备份验证**: 备份文件完整性验证
- **快速恢复**: 支持快速数据恢复
- **异地备份**: 支持异地备份存储

### 5. 安全管理
- **安全审计**: 完整的安全事件审计
- **威胁检测**: 实时安全威胁检测
- **访问控制**: 细粒度的访问权限控制
- **安全扫描**: 定期安全漏洞扫描
- **合规检查**: 安全合规性检查

## 监控指标

### 1. 系统指标
- **CPU使用率**: 系统CPU使用情况
- **内存使用率**: 系统内存使用情况
- **磁盘使用率**: 磁盘空间使用情况
- **网络流量**: 网络输入输出流量
- **负载均衡**: 系统负载情况

### 2. 应用指标
- **响应时间**: API接口响应时间
- **吞吐量**: 系统处理请求数量
- **错误率**: 系统错误发生率
- **并发用户数**: 同时在线用户数
- **数据库连接数**: 数据库连接池使用情况

### 3. 业务指标
- **用户活跃度**: 用户活跃情况统计
- **交易量**: 业务交易数量统计
- **数据增长**: 业务数据增长趋势
- **功能使用率**: 各功能模块使用情况

## 告警策略

### 1. 告警级别
- **紧急**: 系统严重故障，需要立即处理
- **重要**: 系统异常，需要及时处理
- **警告**: 系统预警，需要关注
- **信息**: 系统信息，仅作记录

### 2. 告警方式
- **邮件通知**: 发送告警邮件
- **短信通知**: 发送告警短信
- **微信通知**: 企业微信群通知
- **钉钉通知**: 钉钉群机器人通知
- **系统通知**: 系统内部消息通知

### 3. 告警规则
- **阈值告警**: 基于指标阈值的告警
- **趋势告警**: 基于数据趋势的告警
- **异常告警**: 基于异常检测的告警
- **组合告警**: 多条件组合告警

## 性能优化

1. **缓存策略**: 使用Redis缓存热点配置和监控数据
2. **异步处理**: 日志写入和告警通知异步处理
3. **数据分区**: 按时间对日志数据进行分区存储
4. **索引优化**: 为查询字段建立合适的索引
5. **批量操作**: 批量处理日志和监控数据

## 注意事项

1. **权限控制**: 系统管理功能需要严格的权限控制
2. **数据安全**: 敏感配置和日志数据需要加密存储
3. **性能影响**: 监控和日志收集不应影响业务性能
4. **存储管理**: 合理管理日志和监控数据的存储空间
5. **灾难恢复**: 制定完善的灾难恢复计划

---

**文档版本**: v1.0  
**最后更新**: 2024-12-01  
**维护人员**: Agriculture Team