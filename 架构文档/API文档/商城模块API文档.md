# 商城模块API文档

## 模块概述

商城模块是智慧农业平台的核心电商功能模块，提供完整的在线购物体验，包括商品展示、购物车管理、订单处理、评价系统和收藏功能。

## 模块描述

本模块实现了完整的B2C电商功能，支持农产品的在线销售、用户购物、订单管理等核心业务流程。采用微服务架构设计，各功能模块相对独立，便于维护和扩展。

## 子任务完成情况

### ✅ 已完成任务

#### 1. 数据库层 (Mapper)
- [x] ProductMapper - 商品数据访问层
- [x] CategoryMapper - 分类数据访问层
- [x] CartMapper - 购物车数据访问层
- [x] OrderMapper - 订单数据访问层
- [x] OrderItemMapper - 订单项数据访问层
- [x] ReviewMapper - 评价数据访问层
- [x] FavoriteMapper - 收藏数据访问层

#### 2. 业务逻辑层 (Service)
- [x] ProductService & ProductServiceImpl - 商品业务逻辑
- [x] CategoryService & CategoryServiceImpl - 分类业务逻辑
- [x] CartService & CartServiceImpl - 购物车业务逻辑
- [x] OrderService & OrderServiceImpl - 订单业务逻辑
- [x] ReviewService & ReviewServiceImpl - 评价业务逻辑
- [x] FavoriteService & FavoriteServiceImpl - 收藏业务逻辑

#### 3. 控制器层 (Controller)
- [x] ProductController - 商品API接口
- [x] CategoryController - 分类API接口
- [x] CartController - 购物车API接口
- [x] OrderController - 订单API接口
- [x] ReviewController - 评价API接口
- [x] FavoriteController - 收藏API接口

## API接口列表

### 1. 商品管理 API (ProductController)

**基础路径**: `/api/mall/products`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/` | 分页查询商品列表 | page, size, categoryId, keyword, minPrice, maxPrice, sortField, sortOrder |
| GET | `/{id}` | 根据ID获取商品详情 | id |
| POST | `/` | 创建商品 | Product对象 |
| PUT | `/{id}` | 更新商品 | id, Product对象 |
| DELETE | `/{id}` | 删除商品 | id |
| GET | `/hot` | 获取热门商品 | limit |
| GET | `/new` | 获取新品商品 | limit |
| GET | `/featured` | 获取精选商品 | limit |
| POST | `/{id}/view` | 增加商品浏览次数 | id |
| PUT | `/{id}/stock` | 更新商品库存 | id, stockChange |
| PUT | `/{id}/sales` | 更新商品销量 | id, salesChange |
| GET | `/search` | 搜索商品 | page, size, keyword, categoryId, minPrice, maxPrice, sortField, sortOrder |
| GET | `/search/suggestions` | 获取搜索建议 | keyword, limit |
| GET | `/stats/home` | 获取首页统计数据 | - |
| PUT | `/batch/status` | 批量更新商品状态 | productIds, status |
| GET | `/check-stock` | 检查商品库存 | productId, quantity |

### 2. 分类管理 API (CategoryController)

**基础路径**: `/api/mall/categories`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/` | 获取所有分类 | - |
| GET | `/main` | 获取主分类列表 | - |
| GET | `/sub/{parentId}` | 获取子分类列表 | parentId |
| GET | `/{id}` | 根据ID获取分类详情 | id |
| POST | `/` | 创建分类 | Category对象 |
| PUT | `/{id}` | 更新分类 | id, Category对象 |
| DELETE | `/{id}` | 删除分类 | id |
| GET | `/tree` | 获取分类树形结构 | - |
| PUT | `/{id}/product-count` | 更新分类商品数量 | id, countChange |
| PUT | `/batch/status` | 批量更新分类状态 | categoryIds, status |
| GET | `/{id}/has-children` | 检查分类是否有子分类 | id |
| GET | `/{id}/has-products` | 检查分类是否有商品 | id |
| GET | `/{id}/path` | 获取分类路径 | id |

### 3. 购物车管理 API (CartController)

**基础路径**: `/api/mall/cart`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/` | 获取用户购物车列表 | userId |
| POST | `/` | 添加商品到购物车 | userId, productId, quantity |
| PUT | `/{id}/quantity` | 更新购物车商品数量 | id, quantity |
| DELETE | `/{id}` | 删除购物车商品 | id |
| DELETE | `/batch` | 批量删除购物车商品 | cartIds |
| DELETE | `/clear` | 清空用户购物车 | userId |
| PUT | `/{id}/select` | 选中/取消选中购物车商品 | id, selected |
| PUT | `/select-all` | 全选/取消全选购物车商品 | userId, selected |
| GET | `/stats` | 获取购物车统计信息 | userId |
| GET | `/selected` | 获取用户选中的购物车商品 | userId |
| POST | `/check-stock` | 检查购物车商品库存 | userId |
| POST | `/sync-price` | 同步购物车商品价格 | userId |
| GET | `/count` | 获取用户购物车商品数量 | userId |

### 4. 订单管理 API (OrderController)

**基础路径**: `/api/mall/orders`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| POST | `/` | 创建订单 | userId, addressId, cartIds, remark |
| GET | `/` | 分页查询用户订单列表 | page, size, userId, status |
| GET | `/{id}` | 根据ID获取订单详情 | id |
| GET | `/order-no/{orderNo}` | 根据订单号获取订单 | orderNo |
| PUT | `/{id}/cancel` | 取消订单 | id, reason |
| PUT | `/{id}/confirm` | 确认收货 | id |
| PUT | `/{id}/pay` | 支付订单 | id, paymentMethod, transactionId |
| PUT | `/{id}/ship` | 发货 | id, shippingCompany, trackingNumber |
| GET | `/stats/user` | 获取用户订单状态统计 | userId |
| GET | `/stats/today` | 获取今日订单统计 | - |
| POST | `/handle-timeout` | 处理超时订单 | - |
| POST | `/calculate` | 计算订单金额 | cartIds, couponId |
| POST | `/pre-create` | 预创建订单 | userId, cartIds |
| GET | `/{id}/logistics` | 获取订单物流信息 | id |
| POST | `/{id}/refund` | 申请退款 | id, reason, description |
| GET | `/{id}/reviewable-items` | 获取可评价的订单项 | id |

### 5. 评价管理 API (ReviewController)

**基础路径**: `/api/mall/reviews`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/` | 分页查询商品评价列表 | page, size, productId, rating, hasImages, sortField, sortOrder |
| GET | `/stats` | 获取商品评价统计 | productId |
| POST | `/` | 创建评价 | Review对象 |
| GET | `/{id}` | 根据ID获取评价详情 | id |
| POST | `/{id}/like` | 点赞评价 | id, userId |
| DELETE | `/{id}/like` | 取消点赞评价 | id, userId |
| POST | `/{id}/reply` | 回复评价 | id, replyContent, replyUserId |
| DELETE | `/{id}` | 删除评价 | id |
| PUT | `/{id}/audit` | 审核评价 | id, auditStatus, auditRemark |
| GET | `/latest` | 获取最新评价 | limit |
| GET | `/user/{userId}` | 获取用户评价列表 | page, size, userId |
| GET | `/check-reviewable` | 检查用户是否可以评价订单项 | userId, orderItemId |
| GET | `/check-reviewed` | 检查用户是否已评价订单项 | userId, orderItemId |
| GET | `/product/{productId}/rating` | 获取商品好评率 | productId |
| DELETE | `/batch` | 批量删除评价 | reviewIds |
| GET | `/tags/stats` | 获取评价标签统计 | productId |

### 6. 收藏管理 API (FavoriteController)

**基础路径**: `/api/mall/favorites`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/` | 分页查询用户收藏列表 | page, size, userId |
| POST | `/` | 添加收藏 | userId, productId |
| DELETE | `/` | 取消收藏 | userId, productId |
| DELETE | `/batch` | 批量取消收藏 | userId, productIds |
| GET | `/check` | 检查用户是否收藏了商品 | userId, productId |
| POST | `/check-batch` | 批量检查用户是否收藏了商品列表 | userId, productIds |
| GET | `/count/user` | 获取用户收藏数量 | userId |
| GET | `/count/product` | 获取商品收藏数量 | productId |
| GET | `/hot` | 获取热门收藏商品 | limit |
| DELETE | `/clear` | 清空用户收藏 | userId |
| GET | `/product-ids` | 获取用户收藏的商品ID列表 | userId |
| POST | `/sync` | 同步收藏商品信息 | userId |
| GET | `/stats/trend` | 获取收藏趋势统计 | startDate, endDate |
| GET | `/stats/category` | 获取用户收藏分类统计 | userId |

## 完成标准

### 功能完成标准
- [x] 所有Mapper接口实现完成，包含完整的SQL查询逻辑
- [x] 所有Service接口及实现类完成，包含完整的业务逻辑
- [x] 所有Controller接口完成，提供完整的RESTful API
- [x] 代码符合项目规范，包含适当的注释和异常处理
- [x] 使用统一的响应格式和错误处理机制

### 技术标准
- [x] 使用MyBatis-Plus进行数据访问
- [x] 使用Spring Boot注解进行依赖注入
- [x] 使用Swagger注解进行API文档化
- [x] 实现分页查询功能
- [x] 实现缓存机制（Redis）
- [x] 实现事务管理
- [x] 实现参数验证

### 质量标准
- [x] 代码结构清晰，职责分离
- [x] 异常处理完善
- [x] 日志记录完整
- [x] 接口设计符合RESTful规范
- [x] 数据库操作高效，避免N+1查询问题

## 数据库表结构

本模块涉及以下数据库表：

### 核心业务表
- `product` - 商品主表
- `product_image` - 商品图片表
- `product_price_history` - 商品价格历史表
- `category` - 商品分类表
- `cart_item` - 购物车表
- `order` - 订单主表
- `order_item` - 订单商品表
- `product_review` - 商品评价表
- `product_favorite` - 商品收藏表

### 扩展功能表
- `user_behavior` - 用户行为表
- `product_recommendation` - 商品推荐表
- `search_keyword` - 搜索关键词表
- `user_search_history` - 用户搜索历史表

## 注意事项

1. **安全性**: 所有用户相关操作需要进行身份验证
2. **性能**: 商品列表查询使用分页，避免一次性加载大量数据
3. **缓存**: 热门商品、分类树等数据使用Redis缓存
4. **事务**: 订单创建、库存更新等操作使用事务保证数据一致性
5. **异步**: 推荐算法、统计计算等耗时操作考虑异步处理
6. **监控**: 关键业务操作需要添加监控和告警

## 后续扩展

1. **支付集成**: 集成第三方支付平台
2. **物流跟踪**: 集成物流公司API
3. **推荐算法**: 实现个性化推荐
4. **营销活动**: 优惠券、秒杀等营销功能
5. **数据分析**: 销售数据分析和报表
6. **移动端适配**: 提供移动端专用API

---

**文档版本**: v1.0  
**最后更新**: 2024-12-01  
**维护人员**: Agriculture Team