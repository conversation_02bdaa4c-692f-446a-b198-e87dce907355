# 用户管理模块API文档

## 模块概述

用户管理模块是智慧农业平台的核心基础模块，负责用户注册、登录、权限管理、个人信息管理等功能。

## 模块描述

本模块实现了完整的用户生命周期管理，包括用户注册、身份验证、权限控制、个人资料管理、地址管理等核心功能。采用JWT令牌进行身份验证，支持多角色权限管理。

## 子任务完成情况

### ✅ 已完成任务

#### 1. 数据库层 (Mapper)
- [x] UserMapper - 用户数据访问层
- [x] RoleMapper - 角色数据访问层
- [x] PermissionMapper - 权限数据访问层
- [x] UserRoleMapper - 用户角色关联数据访问层
- [x] RolePermissionMapper - 角色权限关联数据访问层
- [x] UserAddressMapper - 用户地址数据访问层
- [x] UserProfileMapper - 用户资料数据访问层

#### 2. 业务逻辑层 (Service)
- [x] UserService & UserServiceImpl - 用户业务逻辑
- [x] AuthService & AuthServiceImpl - 认证业务逻辑
- [x] RoleService & RoleServiceImpl - 角色业务逻辑
- [x] PermissionService & PermissionServiceImpl - 权限业务逻辑
- [x] UserAddressService & UserAddressServiceImpl - 用户地址业务逻辑
- [x] UserProfileService & UserProfileServiceImpl - 用户资料业务逻辑

#### 3. 控制器层 (Controller)
- [x] UserController - 用户API接口
- [x] AuthController - 认证API接口
- [x] RoleController - 角色API接口
- [x] PermissionController - 权限API接口
- [x] UserAddressController - 用户地址API接口
- [x] UserProfileController - 用户资料API接口

## API接口列表

### 1. 认证管理 API (AuthController)

**基础路径**: `/api/auth`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| POST | `/register` | 用户注册 | username, password, email, phone |
| POST | `/login` | 用户登录 | username, password |
| POST | `/logout` | 用户登出 | - |
| POST | `/refresh` | 刷新令牌 | refreshToken |
| POST | `/forgot-password` | 忘记密码 | email |
| POST | `/reset-password` | 重置密码 | token, newPassword |
| POST | `/verify-email` | 验证邮箱 | token |
| POST | `/send-verification` | 发送验证邮件 | email |
| GET | `/me` | 获取当前用户信息 | - |

### 2. 用户管理 API (UserController)

**基础路径**: `/api/users`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/` | 分页查询用户列表 | page, size, keyword, status, role |
| GET | `/{id}` | 根据ID获取用户详情 | id |
| PUT | `/{id}` | 更新用户信息 | id, User对象 |
| DELETE | `/{id}` | 删除用户 | id |
| PUT | `/{id}/status` | 更新用户状态 | id, status |
| PUT | `/{id}/password` | 修改用户密码 | id, oldPassword, newPassword |
| GET | `/{id}/roles` | 获取用户角色列表 | id |
| PUT | `/{id}/roles` | 分配用户角色 | id, roleIds |
| GET | `/{id}/permissions` | 获取用户权限列表 | id |
| PUT | `/batch/status` | 批量更新用户状态 | userIds, status |
| GET | `/stats` | 获取用户统计信息 | - |

### 3. 角色管理 API (RoleController)

**基础路径**: `/api/roles`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/` | 获取所有角色列表 | - |
| GET | `/{id}` | 根据ID获取角色详情 | id |
| POST | `/` | 创建角色 | Role对象 |
| PUT | `/{id}` | 更新角色 | id, Role对象 |
| DELETE | `/{id}` | 删除角色 | id |
| GET | `/{id}/permissions` | 获取角色权限列表 | id |
| PUT | `/{id}/permissions` | 分配角色权限 | id, permissionIds |
| GET | `/{id}/users` | 获取角色用户列表 | id |

### 4. 权限管理 API (PermissionController)

**基础路径**: `/api/permissions`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/` | 获取所有权限列表 | - |
| GET | `/tree` | 获取权限树形结构 | - |
| GET | `/{id}` | 根据ID获取权限详情 | id |
| POST | `/` | 创建权限 | Permission对象 |
| PUT | `/{id}` | 更新权限 | id, Permission对象 |
| DELETE | `/{id}` | 删除权限 | id |

### 5. 用户地址管理 API (UserAddressController)

**基础路径**: `/api/user-addresses`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/` | 获取用户地址列表 | userId |
| GET | `/{id}` | 根据ID获取地址详情 | id |
| POST | `/` | 创建用户地址 | UserAddress对象 |
| PUT | `/{id}` | 更新用户地址 | id, UserAddress对象 |
| DELETE | `/{id}` | 删除用户地址 | id |
| PUT | `/{id}/default` | 设置默认地址 | id |
| GET | `/default` | 获取用户默认地址 | userId |

### 6. 用户资料管理 API (UserProfileController)

**基础路径**: `/api/user-profiles`

| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| GET | `/{userId}` | 获取用户资料 | userId |
| PUT | `/{userId}` | 更新用户资料 | userId, UserProfile对象 |
| POST | `/{userId}/avatar` | 上传用户头像 | userId, avatar文件 |
| PUT | `/{userId}/preferences` | 更新用户偏好设置 | userId, preferences |
| GET | `/{userId}/security` | 获取用户安全设置 | userId |
| PUT | `/{userId}/security` | 更新用户安全设置 | userId, securitySettings |

## 数据库表结构

本模块涉及以下数据库表：

### 核心用户表
- `user` - 用户主表
- `user_profile` - 用户资料表
- `user_address` - 用户地址表
- `user_login_log` - 用户登录日志表

### 权限管理表
- `role` - 角色表
- `permission` - 权限表
- `user_role` - 用户角色关联表
- `role_permission` - 角色权限关联表

### 安全相关表
- `user_token` - 用户令牌表
- `user_security_log` - 用户安全日志表
- `password_reset_token` - 密码重置令牌表
- `email_verification_token` - 邮箱验证令牌表

## 安全特性

1. **密码安全**: 使用BCrypt加密存储密码
2. **JWT认证**: 使用JWT令牌进行身份验证
3. **权限控制**: 基于RBAC的权限管理
4. **登录保护**: 防止暴力破解，支持账户锁定
5. **会话管理**: 支持单点登录和会话超时
6. **审计日志**: 记录用户关键操作日志

## 注意事项

1. **密码策略**: 强制密码复杂度要求
2. **令牌管理**: JWT令牌定期刷新，防止长期有效
3. **权限验证**: 所有API接口都需要进行权限验证
4. **数据脱敏**: 敏感信息在日志中进行脱敏处理
5. **防护机制**: 实现防CSRF、防XSS等安全防护

---

**文档版本**: v1.0  
**最后更新**: 2024-12-01  
**维护人员**: Agriculture Team