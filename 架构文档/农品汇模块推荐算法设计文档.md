# 农品汇模块推荐算法设计文档

## 1. 推荐系统概述

### 1.1 系统目标
农品汇推荐系统旨在为用户提供个性化的农产品推荐，提高用户购买转化率和平台交易量。通过分析用户行为、商品特征、季节性因素等多维度数据，为用户推荐最符合其需求和偏好的农产品。

### 1.2 推荐场景
- **首页推荐**: 为用户推荐热门和个性化商品
- **商品详情页推荐**: 推荐相关或互补商品
- **购物车推荐**: 推荐搭配商品
- **搜索推荐**: 基于搜索关键词的相关推荐
- **分类页推荐**: 分类内的个性化排序
- **季节性推荐**: 基于时令的农产品推荐

### 1.3 技术架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   数据收集层     │───▶│   算法计算层     │───▶│   推荐服务层     │
│  (行为/商品)     │    │  (离线/在线)     │    │   (API接口)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   数据存储层     │    │   模型存储层     │    │   缓存存储层     │
│ (MySQL/Redis)   │    │ (Redis/File)    │    │    (Redis)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 2. 推荐算法设计

### 2.1 协同过滤算法

#### 2.1.1 用户协同过滤 (User-Based CF)
基于用户相似度进行推荐，适用于用户数量相对较少的场景。

**算法流程**:
1. 计算用户相似度矩阵
2. 找到目标用户的相似用户
3. 基于相似用户的偏好进行推荐

**相似度计算公式**:
```
sim(u,v) = cos(u,v) = (u·v) / (||u|| × ||v||)
```

**实现代码**:
```java
@Component
public class UserBasedCF {
    
    public Map<Long, Double> calculateUserSimilarity(Long userId, List<UserBehavior> behaviors) {
        Map<Long, Double> similarities = new HashMap<>();
        
        // 获取目标用户的行为向量
        Map<Long, Double> userVector = buildUserVector(userId, behaviors);
        
        // 获取所有其他用户
        Set<Long> allUsers = behaviors.stream()
            .map(UserBehavior::getUserId)
            .filter(id -> !id.equals(userId))
            .collect(Collectors.toSet());
        
        for (Long otherUserId : allUsers) {
            Map<Long, Double> otherVector = buildUserVector(otherUserId, behaviors);
            double similarity = cosineSimilarity(userVector, otherVector);
            if (similarity > 0.1) { // 相似度阈值
                similarities.put(otherUserId, similarity);
            }
        }
        
        return similarities.entrySet().stream()
            .sorted(Map.Entry.<Long, Double>comparingByValue().reversed())
            .limit(50) // 取前50个相似用户
            .collect(Collectors.toMap(
                Map.Entry::getKey,
                Map.Entry::getValue,
                (e1, e2) -> e1,
                LinkedHashMap::new
            ));
    }
    
    private Map<Long, Double> buildUserVector(Long userId, List<UserBehavior> behaviors) {
        return behaviors.stream()
            .filter(b -> b.getUserId().equals(userId))
            .collect(Collectors.groupingBy(
                UserBehavior::getProductId,
                Collectors.summingDouble(b -> getWeightByBehaviorType(b.getBehaviorType()))
            ));
    }
    
    private double getWeightByBehaviorType(Integer behaviorType) {
        switch (behaviorType) {
            case 1: return 1.0;  // 浏览
            case 2: return 2.0;  // 收藏
            case 3: return 3.0;  // 加购物车
            case 4: return 5.0;  // 购买
            default: return 1.0;
        }
    }
    
    private double cosineSimilarity(Map<Long, Double> vectorA, Map<Long, Double> vectorB) {
        Set<Long> commonItems = new HashSet<>(vectorA.keySet());
        commonItems.retainAll(vectorB.keySet());
        
        if (commonItems.isEmpty()) {
            return 0.0;
        }
        
        double dotProduct = commonItems.stream()
            .mapToDouble(item -> vectorA.get(item) * vectorB.get(item))
            .sum();
        
        double normA = Math.sqrt(vectorA.values().stream()
            .mapToDouble(v -> v * v)
            .sum());
        
        double normB = Math.sqrt(vectorB.values().stream()
            .mapToDouble(v -> v * v)
            .sum());
        
        return dotProduct / (normA * normB);
    }
}
```

#### 2.1.2 物品协同过滤 (Item-Based CF)
基于商品相似度进行推荐，适用于商品数量相对稳定的场景。

**算法流程**:
1. 计算商品相似度矩阵
2. 基于用户历史行为的商品
3. 推荐相似商品

**实现代码**:
```java
@Component
public class ItemBasedCF {
    
    public Map<Long, Double> calculateItemSimilarity(Long productId, List<UserBehavior> behaviors) {
        Map<Long, Double> similarities = new HashMap<>();
        
        // 获取目标商品的用户向量
        Map<Long, Double> productVector = buildProductVector(productId, behaviors);
        
        // 获取所有其他商品
        Set<Long> allProducts = behaviors.stream()
            .map(UserBehavior::getProductId)
            .filter(id -> !id.equals(productId))
            .collect(Collectors.toSet());
        
        for (Long otherProductId : allProducts) {
            Map<Long, Double> otherVector = buildProductVector(otherProductId, behaviors);
            double similarity = cosineSimilarity(productVector, otherVector);
            if (similarity > 0.1) {
                similarities.put(otherProductId, similarity);
            }
        }
        
        return similarities.entrySet().stream()
            .sorted(Map.Entry.<Long, Double>comparingByValue().reversed())
            .limit(20)
            .collect(Collectors.toMap(
                Map.Entry::getKey,
                Map.Entry::getValue,
                (e1, e2) -> e1,
                LinkedHashMap::new
            ));
    }
    
    private Map<Long, Double> buildProductVector(Long productId, List<UserBehavior> behaviors) {
        return behaviors.stream()
            .filter(b -> b.getProductId().equals(productId))
            .collect(Collectors.groupingBy(
                UserBehavior::getUserId,
                Collectors.summingDouble(b -> getWeightByBehaviorType(b.getBehaviorType()))
            ));
    }
}
```

### 2.2 内容推荐算法

#### 2.2.1 基于商品特征的推荐
根据商品的分类、价格、产地、品牌等特征进行推荐。

**特征向量构建**:
```java
@Component
public class ContentBasedRecommender {
    
    public List<ProductRecommendation> recommendByContent(Long userId, int limit) {
        // 获取用户历史偏好
        UserPreference preference = getUserPreference(userId);
        
        // 获取候选商品
        List<Product> candidates = getCandidateProducts(userId);
        
        // 计算商品与用户偏好的匹配度
        List<ProductScore> scores = candidates.stream()
            .map(product -> {
                double score = calculateContentScore(product, preference);
                return new ProductScore(product.getId(), score);
            })
            .sorted((a, b) -> Double.compare(b.getScore(), a.getScore()))
            .limit(limit)
            .collect(Collectors.toList());
        
        return scores.stream()
            .map(ps -> buildRecommendation(ps, RecommendationType.CONTENT_BASED))
            .collect(Collectors.toList());
    }
    
    private double calculateContentScore(Product product, UserPreference preference) {
        double score = 0.0;
        
        // 分类偏好权重
        if (preference.getPreferredCategories().contains(product.getCategoryId())) {
            score += 0.3;
        }
        
        // 价格区间偏好
        if (product.getPrice().compareTo(preference.getMinPrice()) >= 0 &&
            product.getPrice().compareTo(preference.getMaxPrice()) <= 0) {
            score += 0.2;
        }
        
        // 产地偏好
        if (preference.getPreferredOrigins().contains(product.getOrigin())) {
            score += 0.2;
        }
        
        // 品牌偏好
        if (preference.getPreferredBrands().contains(product.getBrand())) {
            score += 0.1;
        }
        
        // 商品质量分数
        score += product.getRating().doubleValue() / 5.0 * 0.2;
        
        return score;
    }
    
    private UserPreference getUserPreference(Long userId) {
        // 从用户行为中分析偏好
        List<UserBehavior> behaviors = userBehaviorMapper.selectList(
            new LambdaQueryWrapper<UserBehavior>()
                .eq(UserBehavior::getUserId, userId)
                .ge(UserBehavior::getCreatedAt, LocalDateTime.now().minusDays(30))
        );
        
        return analyzeUserPreference(behaviors);
    }
}
```

### 2.3 混合推荐算法

#### 2.3.1 加权混合策略
结合多种算法的推荐结果，通过加权平均得到最终推荐。

```java
@Component
public class HybridRecommender {
    
    @Autowired
    private UserBasedCF userBasedCF;
    
    @Autowired
    private ItemBasedCF itemBasedCF;
    
    @Autowired
    private ContentBasedRecommender contentBasedRecommender;
    
    @Autowired
    private PopularityRecommender popularityRecommender;
    
    public List<ProductRecommendation> hybridRecommend(Long userId, int limit) {
        Map<Long, Double> finalScores = new HashMap<>();
        
        // 协同过滤推荐 (权重: 0.4)
        List<ProductRecommendation> cfRecommendations = 
            getCFRecommendations(userId, limit * 2);
        cfRecommendations.forEach(rec -> 
            finalScores.merge(rec.getProductId(), rec.getScore() * 0.4, Double::sum));
        
        // 内容推荐 (权重: 0.3)
        List<ProductRecommendation> contentRecommendations = 
            contentBasedRecommender.recommendByContent(userId, limit * 2);
        contentRecommendations.forEach(rec -> 
            finalScores.merge(rec.getProductId(), rec.getScore() * 0.3, Double::sum));
        
        // 热门推荐 (权重: 0.2)
        List<ProductRecommendation> popularRecommendations = 
            popularityRecommender.recommendPopular(userId, limit * 2);
        popularRecommendations.forEach(rec -> 
            finalScores.merge(rec.getProductId(), rec.getScore() * 0.2, Double::sum));
        
        // 季节性推荐 (权重: 0.1)
        List<ProductRecommendation> seasonalRecommendations = 
            getSeasonalRecommendations(userId, limit * 2);
        seasonalRecommendations.forEach(rec -> 
            finalScores.merge(rec.getProductId(), rec.getScore() * 0.1, Double::sum));
        
        // 排序并返回结果
        return finalScores.entrySet().stream()
            .sorted(Map.Entry.<Long, Double>comparingByValue().reversed())
            .limit(limit)
            .map(entry -> buildFinalRecommendation(entry.getKey(), entry.getValue()))
            .collect(Collectors.toList());
    }
}
```

### 2.4 实时推荐算法

#### 2.4.1 基于会话的推荐
根据用户当前会话的行为进行实时推荐。

```java
@Component
public class RealTimeRecommender {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    public List<ProductRecommendation> recommendBySession(String sessionId, Long currentProductId) {
        // 获取会话行为
        List<Long> sessionProducts = getSessionProducts(sessionId);
        
        if (sessionProducts.isEmpty()) {
            return getDefaultRecommendations();
        }
        
        // 基于会话商品计算推荐
        Map<Long, Double> scores = new HashMap<>();
        
        for (Long productId : sessionProducts) {
            // 获取相似商品
            Map<Long, Double> similarProducts = itemBasedCF.calculateItemSimilarity(
                productId, getAllBehaviors());
            
            similarProducts.forEach((id, similarity) -> {
                if (!sessionProducts.contains(id) && !id.equals(currentProductId)) {
                    scores.merge(id, similarity, Double::sum);
                }
            });
        }
        
        return scores.entrySet().stream()
            .sorted(Map.Entry.<Long, Double>comparingByValue().reversed())
            .limit(10)
            .map(entry -> buildRecommendation(entry.getKey(), entry.getValue(), 
                RecommendationType.SESSION_BASED))
            .collect(Collectors.toList());
    }
    
    private List<Long> getSessionProducts(String sessionId) {
        String key = "session:products:" + sessionId;
        List<Object> products = redisTemplate.opsForList().range(key, 0, -1);
        return products.stream()
            .map(obj -> Long.valueOf(obj.toString()))
            .collect(Collectors.toList());
    }
    
    public void recordSessionBehavior(String sessionId, Long productId, Integer behaviorType) {
        String key = "session:products:" + sessionId;
        
        // 记录商品访问
        redisTemplate.opsForList().leftPush(key, productId);
        redisTemplate.expire(key, Duration.ofHours(2)); // 会话过期时间2小时
        
        // 限制会话商品数量
        redisTemplate.opsForList().trim(key, 0, 19); // 保留最近20个商品
    }
}
```

## 3. 推荐服务接口设计

### 3.1 个性化推荐接口

#### 3.1.1 获取用户推荐
```http
GET /api/recommendations/personal
```

**请求参数**:
```json
{
  "userId": 123,
  "scene": "homepage",
  "limit": 20,
  "excludeProductIds": [1, 2, 3]
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "recommendations": [
      {
        "productId": 1,
        "productName": "红富士苹果",
        "productImage": "https://example.com/apple.jpg",
        "price": 25.80,
        "originalPrice": 30.00,
        "rating": 4.8,
        "salesCount": 500,
        "recommendationType": 1,
        "recommendationReason": "基于您的购买历史推荐",
        "score": 0.85,
        "algorithmVersion": "v1.2"
      }
    ],
    "total": 20,
    "algorithmInfo": {
      "mainAlgorithm": "hybrid",
      "weights": {
        "collaborative": 0.4,
        "content": 0.3,
        "popularity": 0.2,
        "seasonal": 0.1
      }
    }
  }
}
```

#### 3.1.2 相关商品推荐
```http
GET /api/recommendations/related/{productId}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "recommendations": [
      {
        "productId": 2,
        "productName": "青苹果",
        "productImage": "https://example.com/green-apple.jpg",
        "price": 22.00,
        "similarity": 0.78,
        "recommendationReason": "购买此商品的用户还购买了"
      }
    ]
  }
}
```

#### 3.1.3 实时推荐接口
```http
GET /api/recommendations/realtime
```

**请求参数**:
```json
{
  "sessionId": "session_123456",
  "currentProductId": 1,
  "limit": 10
}
```

### 3.2 推荐服务实现

```java
@RestController
@RequestMapping("/api/recommendations")
@Slf4j
public class RecommendationController {
    
    @Autowired
    private RecommendationService recommendationService;
    
    @GetMapping("/personal")
    public Result<RecommendationResponse> getPersonalRecommendations(
            @RequestParam Long userId,
            @RequestParam(defaultValue = "homepage") String scene,
            @RequestParam(defaultValue = "20") int limit,
            @RequestParam(required = false) List<Long> excludeProductIds) {
        
        RecommendationRequest request = RecommendationRequest.builder()
            .userId(userId)
            .scene(scene)
            .limit(limit)
            .excludeProductIds(excludeProductIds)
            .build();
        
        RecommendationResponse response = recommendationService.getPersonalRecommendations(request);
        return Result.success(response);
    }
    
    @GetMapping("/related/{productId}")
    public Result<List<ProductRecommendation>> getRelatedProducts(
            @PathVariable Long productId,
            @RequestParam(defaultValue = "10") int limit) {
        
        List<ProductRecommendation> recommendations = 
            recommendationService.getRelatedProducts(productId, limit);
        return Result.success(recommendations);
    }
    
    @GetMapping("/realtime")
    public Result<List<ProductRecommendation>> getRealTimeRecommendations(
            @RequestParam String sessionId,
            @RequestParam(required = false) Long currentProductId,
            @RequestParam(defaultValue = "10") int limit) {
        
        List<ProductRecommendation> recommendations = 
            recommendationService.getRealTimeRecommendations(sessionId, currentProductId, limit);
        return Result.success(recommendations);
    }
    
    @PostMapping("/feedback")
    public Result<Void> recordFeedback(@RequestBody RecommendationFeedback feedback) {
        recommendationService.recordFeedback(feedback);
        return Result.success();
    }
    
    @PostMapping("/behavior")
    public Result<Void> recordBehavior(@RequestBody UserBehaviorDTO behaviorDTO) {
        recommendationService.recordUserBehavior(behaviorDTO);
        return Result.success();
    }
}
```

### 3.3 推荐服务核心实现

```java
@Service
@Transactional
public class RecommendationServiceImpl implements RecommendationService {
    
    @Autowired
    private HybridRecommender hybridRecommender;
    
    @Autowired
    private RealTimeRecommender realTimeRecommender;
    
    @Autowired
    private ProductRecommendationMapper recommendationMapper;
    
    @Autowired
    private UserBehaviorMapper behaviorMapper;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Override
    public RecommendationResponse getPersonalRecommendations(RecommendationRequest request) {
        Long userId = request.getUserId();
        String cacheKey = "recommendations:" + userId + ":" + request.getScene();
        
        // 尝试从缓存获取
        RecommendationResponse cached = (RecommendationResponse) 
            redisTemplate.opsForValue().get(cacheKey);
        if (cached != null) {
            return cached;
        }
        
        // 生成推荐
        List<ProductRecommendation> recommendations;
        
        if (isNewUser(userId)) {
            // 新用户推荐热门商品
            recommendations = getPopularRecommendations(request.getLimit());
        } else {
            // 老用户个性化推荐
            recommendations = hybridRecommender.hybridRecommend(userId, request.getLimit());
        }
        
        // 过滤排除的商品
        if (request.getExcludeProductIds() != null) {
            recommendations = recommendations.stream()
                .filter(rec -> !request.getExcludeProductIds().contains(rec.getProductId()))
                .collect(Collectors.toList());
        }
        
        // 保存推荐结果到数据库
        saveRecommendations(userId, recommendations);
        
        // 构建响应
        RecommendationResponse response = RecommendationResponse.builder()
            .recommendations(recommendations)
            .total(recommendations.size())
            .algorithmInfo(buildAlgorithmInfo())
            .build();
        
        // 缓存结果
        redisTemplate.opsForValue().set(cacheKey, response, Duration.ofHours(2));
        
        return response;
    }
    
    @Override
    public List<ProductRecommendation> getRelatedProducts(Long productId, int limit) {
        String cacheKey = "related:" + productId;
        
        @SuppressWarnings("unchecked")
        List<ProductRecommendation> cached = (List<ProductRecommendation>) 
            redisTemplate.opsForValue().get(cacheKey);
        if (cached != null) {
            return cached.stream().limit(limit).collect(Collectors.toList());
        }
        
        // 计算相关商品
        List<ProductRecommendation> recommendations = 
            itemBasedCF.getRelatedProducts(productId, limit);
        
        // 缓存结果
        redisTemplate.opsForValue().set(cacheKey, recommendations, Duration.ofHours(6));
        
        return recommendations;
    }
    
    @Override
    public List<ProductRecommendation> getRealTimeRecommendations(
            String sessionId, Long currentProductId, int limit) {
        
        return realTimeRecommender.recommendBySession(sessionId, currentProductId);
    }
    
    @Override
    public void recordUserBehavior(UserBehaviorDTO behaviorDTO) {
        // 保存用户行为
        UserBehavior behavior = convertToEntity(behaviorDTO);
        behaviorMapper.insert(behavior);
        
        // 实时更新会话数据
        if (behaviorDTO.getSessionId() != null) {
            realTimeRecommender.recordSessionBehavior(
                behaviorDTO.getSessionId(), 
                behaviorDTO.getProductId(), 
                behaviorDTO.getBehaviorType()
            );
        }
        
        // 异步更新用户画像
        updateUserProfileAsync(behaviorDTO.getUserId());
    }
    
    @Override
    public void recordFeedback(RecommendationFeedback feedback) {
        // 更新推荐结果的反馈
        recommendationMapper.updateFeedback(
            feedback.getRecommendationId(),
            feedback.isClicked(),
            feedback.getClickTime()
        );
        
        // 用于算法优化
        optimizeAlgorithmAsync(feedback);
    }
    
    private boolean isNewUser(Long userId) {
        long behaviorCount = behaviorMapper.selectCount(
            new LambdaQueryWrapper<UserBehavior>()
                .eq(UserBehavior::getUserId, userId)
        );
        return behaviorCount < 5; // 行为少于5次认为是新用户
    }
    
    private void saveRecommendations(Long userId, List<ProductRecommendation> recommendations) {
        // 批量保存推荐结果
        List<ProductRecommendationEntity> entities = recommendations.stream()
            .map(rec -> {
                ProductRecommendationEntity entity = new ProductRecommendationEntity();
                entity.setUserId(userId);
                entity.setProductId(rec.getProductId());
                entity.setRecommendationType(rec.getRecommendationType());
                entity.setScore(BigDecimal.valueOf(rec.getScore()));
                entity.setReason(rec.getRecommendationReason());
                entity.setAlgorithmVersion(rec.getAlgorithmVersion());
                entity.setCreatedAt(LocalDateTime.now());
                entity.setExpiredAt(LocalDateTime.now().plusDays(7));
                return entity;
            })
            .collect(Collectors.toList());
        
        recommendationMapper.batchInsert(entities);
    }
    
    @Async
    private void updateUserProfileAsync(Long userId) {
        // 异步更新用户画像
        userProfileService.updateProfile(userId);
    }
    
    @Async
    private void optimizeAlgorithmAsync(RecommendationFeedback feedback) {
        // 异步优化算法参数
        algorithmOptimizer.optimize(feedback);
    }
}
```

## 4. 算法评估与优化

### 4.1 评估指标

#### 4.1.1 准确性指标
- **点击率 (CTR)**: 推荐商品的点击次数 / 推荐展示次数
- **转化率 (CVR)**: 推荐商品的购买次数 / 推荐点击次数
- **精确率 (Precision)**: 相关推荐商品数 / 总推荐商品数
- **召回率 (Recall)**: 相关推荐商品数 / 用户感兴趣商品总数

#### 4.1.2 多样性指标
- **类别覆盖率**: 推荐商品涉及的类别数 / 总类别数
- **商品覆盖率**: 被推荐的商品数 / 总商品数
- **新颖性**: 推荐商品的平均流行度倒数

#### 4.1.3 业务指标
- **用户留存率**: 使用推荐功能后的用户留存情况
- **平均订单价值**: 通过推荐产生的订单平均金额
- **推荐贡献率**: 推荐带来的GMV / 总GMV

### 4.2 A/B测试框架

```java
@Component
public class ABTestManager {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    public String getUserGroup(Long userId) {
        String cacheKey = "ab_test:user:" + userId;
        String group = (String) redisTemplate.opsForValue().get(cacheKey);
        
        if (group == null) {
            // 根据用户ID分组
            group = userId % 2 == 0 ? "A" : "B";
            redisTemplate.opsForValue().set(cacheKey, group, Duration.ofDays(30));
        }
        
        return group;
    }
    
    public RecommendationStrategy getStrategy(String group) {
        switch (group) {
            case "A":
                return RecommendationStrategy.COLLABORATIVE_FILTERING;
            case "B":
                return RecommendationStrategy.HYBRID;
            default:
                return RecommendationStrategy.HYBRID;
        }
    }
}
```

### 4.3 算法优化策略

#### 4.3.1 参数调优
```java
@Component
public class AlgorithmOptimizer {
    
    public void optimizeWeights() {
        // 基于历史数据优化算法权重
        Map<String, Double> currentWeights = getCurrentWeights();
        Map<String, Double> performance = calculatePerformance();
        
        // 使用梯度下降优化权重
        Map<String, Double> newWeights = gradientDescent(currentWeights, performance);
        
        // 更新权重配置
        updateWeights(newWeights);
    }
    
    private Map<String, Double> gradientDescent(
            Map<String, Double> weights, 
            Map<String, Double> performance) {
        
        double learningRate = 0.01;
        Map<String, Double> newWeights = new HashMap<>();
        
        for (String algorithm : weights.keySet()) {
            double currentWeight = weights.get(algorithm);
            double gradient = calculateGradient(algorithm, performance);
            double newWeight = currentWeight + learningRate * gradient;
            
            // 权重约束
            newWeight = Math.max(0.0, Math.min(1.0, newWeight));
            newWeights.put(algorithm, newWeight);
        }
        
        // 归一化权重
        return normalizeWeights(newWeights);
    }
}
```

## 5. 冷启动解决方案

### 5.1 新用户冷启动

#### 5.1.1 基于人口统计学的推荐
```java
@Component
public class ColdStartRecommender {
    
    public List<ProductRecommendation> recommendForNewUser(UserProfile profile) {
        List<ProductRecommendation> recommendations = new ArrayList<>();
        
        // 基于地理位置推荐本地特产
        if (profile.getLocation() != null) {
            recommendations.addAll(getLocalProducts(profile.getLocation()));
        }
        
        // 基于年龄推荐
        if (profile.getAge() != null) {
            recommendations.addAll(getAgeBasedProducts(profile.getAge()));
        }
        
        // 基于性别推荐
        if (profile.getGender() != null) {
            recommendations.addAll(getGenderBasedProducts(profile.getGender()));
        }
        
        // 补充热门商品
        recommendations.addAll(getPopularProducts());
        
        return recommendations.stream()
            .distinct()
            .limit(20)
            .collect(Collectors.toList());
    }
}
```

#### 5.1.2 引导式推荐
```java
@Component
public class OnboardingRecommender {
    
    public List<Category> getOnboardingCategories() {
        // 返回用于新用户选择偏好的分类
        return categoryMapper.selectList(
            new LambdaQueryWrapper<Category>()
                .eq(Category::getLevel, 1)
                .eq(Category::getStatus, 1)
                .orderByAsc(Category::getSortOrder)
        );
    }
    
    public void saveUserPreferences(Long userId, List<Long> categoryIds) {
        // 保存用户偏好
        List<UserPreferenceEntity> preferences = categoryIds.stream()
            .map(categoryId -> {
                UserPreferenceEntity preference = new UserPreferenceEntity();
                preference.setUserId(userId);
                preference.setCategoryId(categoryId);
                preference.setWeight(1.0);
                preference.setCreatedAt(LocalDateTime.now());
                return preference;
            })
            .collect(Collectors.toList());
        
        userPreferenceMapper.batchInsert(preferences);
    }
}
```

### 5.2 新商品冷启动

#### 5.2.1 基于内容的推荐
```java
@Component
public class NewProductRecommender {
    
    public List<Long> findTargetUsers(Product newProduct) {
        List<Long> targetUsers = new ArrayList<>();
        
        // 找到对该分类感兴趣的用户
        List<Long> categoryUsers = userBehaviorMapper.selectUsersByCategory(
            newProduct.getCategoryId(), 30); // 最近30天
        targetUsers.addAll(categoryUsers);
        
        // 找到购买过相似价格商品的用户
        List<Long> priceUsers = userBehaviorMapper.selectUsersByPriceRange(
            newProduct.getPrice().multiply(BigDecimal.valueOf(0.8)),
            newProduct.getPrice().multiply(BigDecimal.valueOf(1.2))
        );
        targetUsers.addAll(priceUsers);
        
        // 找到购买过同产地商品的用户
        if (newProduct.getOrigin() != null) {
            List<Long> originUsers = userBehaviorMapper.selectUsersByOrigin(
                newProduct.getOrigin());
            targetUsers.addAll(originUsers);
        }
        
        return targetUsers.stream().distinct().collect(Collectors.toList());
    }
}
```

## 6. 性能优化

### 6.1 计算优化

#### 6.1.1 离线计算
```java
@Component
@Slf4j
public class OfflineRecommendationJob {
    
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void calculateUserSimilarity() {
        log.info("开始计算用户相似度矩阵");
        
        List<Long> activeUsers = getActiveUsers(); // 获取活跃用户
        
        for (Long userId : activeUsers) {
            try {
                Map<Long, Double> similarities = userBasedCF.calculateUserSimilarity(
                    userId, getAllBehaviors());
                
                // 保存到Redis
                String key = "user_similarity:" + userId;
                redisTemplate.opsForHash().putAll(key, 
                    similarities.entrySet().stream().collect(
                        Collectors.toMap(
                            entry -> entry.getKey().toString(),
                            Map.Entry::getValue
                        )
                    ));
                redisTemplate.expire(key, Duration.ofDays(1));
                
            } catch (Exception e) {
                log.error("计算用户{}相似度失败", userId, e);
            }
        }
        
        log.info("用户相似度矩阵计算完成");
    }
    
    @Scheduled(cron = "0 30 2 * * ?") // 每天凌晨2点30分执行
    public void calculateItemSimilarity() {
        log.info("开始计算商品相似度矩阵");
        
        List<Long> activeProducts = getActiveProducts();
        
        activeProducts.parallelStream().forEach(productId -> {
            try {
                Map<Long, Double> similarities = itemBasedCF.calculateItemSimilarity(
                    productId, getAllBehaviors());
                
                String key = "item_similarity:" + productId;
                redisTemplate.opsForHash().putAll(key,
                    similarities.entrySet().stream().collect(
                        Collectors.toMap(
                            entry -> entry.getKey().toString(),
                            Map.Entry::getValue
                        )
                    ));
                redisTemplate.expire(key, Duration.ofDays(1));
                
            } catch (Exception e) {
                log.error("计算商品{}相似度失败", productId, e);
            }
        });
        
        log.info("商品相似度矩阵计算完成");
    }
}
```

#### 6.1.2 缓存策略
```java
@Configuration
public class RecommendationCacheConfig {
    
    @Bean
    public CacheManager recommendationCacheManager() {
        RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofHours(2)) // 推荐结果缓存2小时
            .serializeKeysWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new StringRedisSerializer()))
            .serializeValuesWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new GenericJackson2JsonRedisSerializer()));
        
        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();
        
        // 个性化推荐缓存2小时
        cacheConfigurations.put("personal_recommendations", 
            config.entryTtl(Duration.ofHours(2)));
        
        // 相关商品推荐缓存6小时
        cacheConfigurations.put("related_products", 
            config.entryTtl(Duration.ofHours(6)));
        
        // 热门商品缓存1小时
        cacheConfigurations.put("popular_products", 
            config.entryTtl(Duration.ofHours(1)));
        
        return RedisCacheManager.builder(redisConnectionFactory)
            .cacheDefaults(config)
            .withInitialCacheConfigurations(cacheConfigurations)
            .build();
    }
}
```

### 6.2 数据优化

#### 6.2.1 数据预处理
```java
@Component
public class DataPreprocessor {
    
    @Scheduled(cron = "0 0 1 * * ?") // 每天凌晨1点执行
    public void preprocessUserBehavior() {
        // 清理过期行为数据
        userBehaviorMapper.deleteExpiredBehaviors(LocalDateTime.now().minusDays(90));
        
        // 聚合用户行为
        aggregateUserBehavior();
        
        // 更新用户画像
        updateUserProfiles();
    }
    
    private void aggregateUserBehavior() {
        // 按用户和商品聚合行为数据
        List<UserBehaviorAggregate> aggregates = userBehaviorMapper.aggregateBehaviors();
        
        // 批量更新聚合表
        userBehaviorAggregateMapper.batchInsertOrUpdate(aggregates);
    }
}
```

## 7. 监控与运维

### 7.1 推荐效果监控

```java
@Component
public class RecommendationMonitor {
    
    @Autowired
    private MeterRegistry meterRegistry;
    
    public void recordRecommendationMetrics(String algorithm, boolean clicked) {
        // 记录推荐点击率
        Counter.builder("recommendation.clicks")
            .tag("algorithm", algorithm)
            .register(meterRegistry)
            .increment();
        
        if (clicked) {
            Counter.builder("recommendation.clicks.success")
                .tag("algorithm", algorithm)
                .register(meterRegistry)
                .increment();
        }
    }
    
    @Scheduled(fixedRate = 60000) // 每分钟执行
    public void calculateRealTimeMetrics() {
        // 计算实时CTR
        double ctr = calculateCTR();
        Gauge.builder("recommendation.ctr")
            .register(meterRegistry, () -> ctr);
        
        // 计算推荐覆盖率
        double coverage = calculateCoverage();
        Gauge.builder("recommendation.coverage")
            .register(meterRegistry, () -> coverage);
    }
}
```

### 7.2 算法性能监控

```java
@Aspect
@Component
public class RecommendationPerformanceAspect {
    
    @Autowired
    private MeterRegistry meterRegistry;
    
    @Around("@annotation(Timed)")
    public Object timeRecommendationMethod(ProceedingJoinPoint joinPoint) throws Throwable {
        Timer.Sample sample = Timer.start(meterRegistry);
        
        try {
            Object result = joinPoint.proceed();
            sample.stop(Timer.builder("recommendation.method.duration")
                .tag("method", joinPoint.getSignature().getName())
                .tag("status", "success")
                .register(meterRegistry));
            return result;
        } catch (Exception e) {
            sample.stop(Timer.builder("recommendation.method.duration")
                .tag("method", joinPoint.getSignature().getName())
                .tag("status", "error")
                .register(meterRegistry));
            throw e;
        }
    }
}
```

## 8. 部署与配置

### 8.1 配置文件

```yaml
# application-recommendation.yml
recommendation:
  algorithms:
    hybrid:
      weights:
        collaborative: 0.4
        content: 0.3
        popularity: 0.2
        seasonal: 0.1
    
    collaborative:
      user-similarity-threshold: 0.1
      item-similarity-threshold: 0.1
      max-similar-users: 50
      max-similar-items: 20
    
    content:
      category-weight: 0.3
      price-weight: 0.2
      origin-weight: 0.2
      brand-weight: 0.1
      rating-weight: 0.2
  
  cache:
    personal-recommendations-ttl: 7200 # 2小时
    related-products-ttl: 21600 # 6小时
    popular-products-ttl: 3600 # 1小时
  
  cold-start:
    new-user-behavior-threshold: 5
    new-product-days: 7
  
  performance:
    batch-size: 1000
    max-recommendations: 100
    calculation-timeout: 30000 # 30秒
```

### 8.2 Docker部署

```dockerfile
# Dockerfile
FROM openjdk:11-jre-slim

VOLUME /tmp

COPY target/recommendation-service.jar app.jar

EXPOSE 8080

ENTRYPOINT ["java", "-jar", "/app.jar"]
```

```yaml
# docker-compose.yml
version: '3.8'
services:
  recommendation-service:
    build: .
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - REDIS_HOST=redis
      - MYSQL_HOST=mysql
    depends_on:
      - redis
      - mysql
  
  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"
  
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: agriculture
    ports:
      - "3306:3306"
```

---

**文档版本**: v1.0  
**创建日期**: 2024年12月  
**最后更新**: 2024年12月  
**维护人员**: 算法团队