# 农品汇模块架构设计文档

## 1. 需求分析与核心功能点

### 1.1 核心需求
- 构建农产品专业电商平台
- 连接生产者与消费者，减少中间环节
- 提供丰富的农产品信息与价格走势
- 支持普通用户成为销售者并上传自产农产品
- 与溯源系统无缝集成，提升农产品附加值

### 1.2 核心功能点
1. **农产品展示与搜索**：分类展示，多维度搜索
2. **农产品详情展示**：图文详情，产地信息，溯源信息
3. **农产品价格走势**：历史价格，市场分析
4. **农产品评价系统**：用户评价，评分机制
5. **农产品供应商管理**：供应商信息，认证体系
6. **农产品推荐系统**：相关产品，个性化推荐
7. **销售者管理系统**：申请成为销售者，管理自己的产品
8. **数据统计与分析**：销量统计，趋势分析

## 2. 农品汇模块与系统集成方案

### 2.1 系统集成架构
![农品汇模块与系统集成架构](../架构文档/images/marketplace_integration_architecture.png)

### 2.2 核心集成点
1. **用户体系集成**：
   - 复用现有用户体系，扩展销售者权限模型
   - 集成现有认证授权机制
   
2. **溯源系统集成**：
   - 产品详情页面集成溯源信息入口
   - 产品与溯源记录自动关联

3. **订单系统集成**：
   - 农产品订单流程与现有系统整合
   - 库存与订单状态实时同步

4. **支付系统集成**：
   - 复用现有支付方式与流程
   - 农产品特有优惠策略支持

## 3. 农品汇数据流转图

```mermaid
flowchart TD
    A[生产者/销售者] -->|上传产品| B(农品汇管理系统)
    B -->|存储| C[(农品汇数据库)]
    D[消费者] -->|浏览/搜索| E[农产品列表页]
    E -->|查询| C
    D -->|查看| F[农产品详情页]
    F -->|查询| C
    D -->|下单| G[订单系统]
    G -->|更新| C
    G -->|通知| A
    H[价格分析系统] -->|分析| C
    H -->|展示| I[价格走势图]
    I -->|查看| D
    J[评价系统] <-->|交互| D
    J -->|存储| C
    K[推荐引擎] -->|读取| C
    K -->|推荐| D
```

## 4. 农品汇模块组件结构

### 4.1 后端组件结构
```mermaid
flowchart TD
    A[ProductController] -->|调用| B[ProductService]
    B -->|调用| C[ProductMapper]
    C -->|操作| D[(数据库)]
    E[CategoryController] -->|调用| F[CategoryService]
    F -->|调用| G[CategoryMapper]
    G -->|操作| D
    H[PriceController] -->|调用| I[PriceService]
    I -->|调用| J[PriceMapper]
    J -->|操作| D
    K[SupplierController] -->|调用| L[SupplierService]
    L -->|调用| M[SupplierMapper]
    M -->|操作| D
    N[ReviewController] -->|调用| O[ReviewService]
    O -->|调用| P[ReviewMapper]
    P -->|操作| D
    Q[RecommendController] -->|调用| R[RecommendService]
    R -->|调用| S[RecommendMapper]
    S -->|操作| D
    T[SearchController] -->|调用| U[SearchService]
    U -->|调用| V[SearchMapper]
    V -->|操作| D
```

### 4.2 前端组件结构
```mermaid
flowchart TD
    A[农品汇主页] -->|包含| B[分类导航组件]
    A -->|包含| C[产品列表组件]
    A -->|包含| D[搜索组件]
    A -->|包含| E[价格走势卡片]
    A -->|包含| F[推荐产品组件]
    G[产品详情页] -->|包含| H[产品图片轮播]
    G -->|包含| I[产品详情描述]
    G -->|包含| J[价格历史图表]
    G -->|包含| K[评价列表组件]
    G -->|包含| L[相关产品推荐]
    G -->|包含| M[溯源信息入口]
    N[销售者中心] -->|包含| O[产品管理]
    N -->|包含| P[订单管理]
    N -->|包含| Q[销售统计]
```

## 5. 前后端交互接口规范

### 5.1 RESTful API设计原则
- 采用标准HTTP方法（GET, POST, PUT, DELETE）
- 使用资源导向的URL设计
- 统一的响应格式和状态码
- API版本控制（/api/v1/...）

### 5.2 核心API接口定义

#### 5.2.1 农产品信息接口
- **获取产品列表**: GET /api/v1/products
- **获取产品详情**: GET /api/v1/products/{id}
- **创建产品**: POST /api/v1/products
- **更新产品**: PUT /api/v1/products/{id}
- **删除产品**: DELETE /api/v1/products/{id}

#### 5.2.2 农产品分类接口
- **获取分类列表**: GET /api/v1/categories
- **获取分类详情**: GET /api/v1/categories/{id}
- **创建分类**: POST /api/v1/categories
- **更新分类**: PUT /api/v1/categories/{id}
- **删除分类**: DELETE /api/v1/categories/{id}

#### 5.2.3 农产品价格接口
- **获取产品价格历史**: GET /api/v1/prices/{productId}/history
- **获取价格指数**: GET /api/v1/prices/index
- **获取价格分析**: GET /api/v1/prices/analysis
- **创建价格记录**: POST /api/v1/prices

#### 5.2.4 农产品供应商接口
- **获取供应商列表**: GET /api/v1/suppliers
- **获取供应商详情**: GET /api/v1/suppliers/{id}
- **申请成为供应商**: POST /api/v1/suppliers/apply
- **审核供应商申请**: PUT /api/v1/suppliers/{id}/audit

#### 5.2.5 农产品评价接口
- **获取产品评价列表**: GET /api/v1/reviews/product/{productId}
- **创建评价**: POST /api/v1/reviews
- **回复评价**: POST /api/v1/reviews/{id}/reply

#### 5.2.6 农产品搜索接口
- **搜索产品**: GET /api/v1/search?keyword={keyword}
- **高级搜索**: POST /api/v1/search/advanced

#### 5.2.7 农产品推荐接口
- **获取推荐产品**: GET /api/v1/recommendations?userId={userId}
- **获取相关产品**: GET /api/v1/recommendations/related/{productId}

### 5.3 请求/响应格式
- 请求格式：JSON格式，UTF-8编码
- 响应格式：统一采用以下结构
  ```json
  {
    "code": 200,       // 业务状态码，0表示成功
    "message": "success",  // 状态描述
    "data": { ... }    // 业务数据
  }
  ```

## 6. 农品汇数据存储结构

### 6.1 核心数据表设计
1. **products（农产品信息表）**
   - 产品ID、名称、描述、分类ID、价格、库存、图片URL列表、产地、上架状态等

2. **categories（农产品分类表）**
   - 分类ID、分类名称、父分类ID、层级、排序、图标URL等

3. **product_prices（农产品价格表）**
   - 价格ID、产品ID、价格、记录日期、价格类型（批发/零售）等

4. **suppliers（农产品供应商表）**
   - 供应商ID、用户ID、供应商名称、联系方式、地址、认证状态、评级等

5. **product_reviews（农产品评价表）**
   - 评价ID、产品ID、用户ID、评分、评价内容、评价时间、图片URL列表等

6. **product_tags（农产品标签表）**
   - 标签ID、产品ID、标签名称

7. **product_images（农产品图片表）**
   - 图片ID、产品ID、图片URL、排序、类型（主图/详情图）等

### 6.2 主要表结构详细设计
请参考数据库设计文档中的详细表结构设计。

## 7. 未来拓展计划

### 7.1 技术拓展
1. **AI推荐系统优化**：
   - 基于用户行为的个性化推荐
   - 季节性和地域性推荐优化

2. **大数据分析能力**：
   - 农产品市场趋势分析
   - 消费者行为分析

3. **实时价格预警**：
   - 价格异常波动检测
   - 价格预测模型

### 7.2 业务拓展
1. **预售模式**：
   - 农产品预售功能
   - 众筹种植模式

2. **社区功能**：
   - 农产品知识分享
   - 产地直播功能

3. **会员体系**：
   - 农产品订阅服务
   - 会员专享价格

## 8. 附录

### 8.1 相关技术标准
- 农产品分类国家标准
- 农产品质量等级标准
- 电子商务产品信息规范

### 8.2 参考文献
- 《农产品电子商务发展报告》
- 《农产品流通与价格形成机制研究》