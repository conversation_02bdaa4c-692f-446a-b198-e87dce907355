# 农品汇模块后端开发文档

## 1. 模块概述

### 1.1 技术栈
- **框架**: Spring Boot 2.7+
- **数据库**: MySQL 8.0+
- **ORM**: MyBatis Plus
- **缓存**: Redis
- **消息队列**: RabbitMQ
- **搜索引擎**: Elasticsearch (可选)
- **文件存储**: 阿里云OSS/本地存储
- **API文档**: Swagger/OpenAPI 3.0

### 1.2 架构设计
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Controller    │───▶│     Service     │───▶│     Mapper      │
│   (接口层)       │    │    (业务层)      │    │   (数据访问层)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│      DTO        │    │     Entity      │    │    Database     │
│   (数据传输)     │    │   (实体模型)     │    │    (MySQL)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 2. API接口设计

### 2.1 商品管理接口

#### 2.1.1 获取商品列表
```http
GET /api/products
```

**请求参数**:
```json
{
  "page": 1,
  "size": 20,
  "categoryId": 1,
  "keyword": "苹果",
  "minPrice": 10.00,
  "maxPrice": 100.00,
  "sortBy": "sales",
  "sortOrder": "desc",
  "isHot": true,
  "isNew": false,
  "origin": "山东",
  "sellerId": 123
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 100,
    "pages": 5,
    "current": 1,
    "size": 20,
    "records": [
      {
        "id": 1,
        "name": "红富士苹果",
        "description": "新鲜红富士苹果，口感脆甜",
        "image": "https://example.com/apple.jpg",
        "price": 25.80,
        "originalPrice": 30.00,
        "stock": 100,
        "salesCount": 500,
        "rating": 4.8,
        "reviewCount": 120,
        "categoryId": 2,
        "categoryName": "新鲜水果",
        "sellerId": 123,
        "sellerName": "果农老王",
        "brand": "富士",
        "origin": "山东烟台",
        "unit": "斤",
        "tags": ["新鲜", "有机", "包邮"],
        "isHot": true,
        "isNew": false,
        "isFeatured": true,
        "viewCount": 1500,
        "createdAt": "2024-12-01T10:00:00"
      }
    ]
  }
}
```

#### 2.1.2 获取商品详情
```http
GET /api/products/{id}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "name": "红富士苹果",
    "description": "新鲜红富士苹果，口感脆甜，产自山东烟台优质果园",
    "images": [
      {
        "id": 1,
        "url": "https://example.com/apple1.jpg",
        "type": 1,
        "altText": "苹果主图"
      },
      {
        "id": 2,
        "url": "https://example.com/apple2.jpg",
        "type": 2,
        "altText": "苹果详情图"
      }
    ],
    "price": 25.80,
    "originalPrice": 30.00,
    "stock": 100,
    "salesCount": 500,
    "rating": 4.8,
    "reviewCount": 120,
    "categoryId": 2,
    "categoryName": "新鲜水果",
    "sellerId": 123,
    "sellerInfo": {
      "id": 123,
      "name": "果农老王",
      "avatar": "https://example.com/avatar.jpg",
      "rating": 4.9,
      "productCount": 50
    },
    "specifications": {
      "weight": "500g/个",
      "size": "80-85mm",
      "sweetness": "14-16度"
    },
    "brand": "富士",
    "origin": "山东烟台",
    "unit": "斤",
    "shelfLife": "7-10天",
    "storageMethod": "冷藏保存",
    "tags": ["新鲜", "有机", "包邮"],
    "priceHistory": [
      {
        "price": 25.80,
        "date": "2024-12-01"
      },
      {
        "price": 28.00,
        "date": "2024-11-01"
      }
    ],
    "relatedProducts": [
      {
        "id": 2,
        "name": "青苹果",
        "image": "https://example.com/green-apple.jpg",
        "price": 22.00
      }
    ],
    "isFavorited": false,
    "viewCount": 1500,
    "createdAt": "2024-12-01T10:00:00",
    "updatedAt": "2024-12-01T15:30:00"
  }
}
```

#### 2.1.3 创建商品
```http
POST /api/products
```

**请求数据**:
```json
{
  "name": "红富士苹果",
  "description": "新鲜红富士苹果，口感脆甜",
  "categoryId": 2,
  "price": 25.80,
  "originalPrice": 30.00,
  "stock": 100,
  "brand": "富士",
  "origin": "山东烟台",
  "unit": "斤",
  "shelfLife": "7-10天",
  "storageMethod": "冷藏保存",
  "tags": ["新鲜", "有机", "包邮"],
  "specifications": {
    "weight": "500g/个",
    "size": "80-85mm",
    "sweetness": "14-16度"
  },
  "images": [
    {
      "url": "https://example.com/apple1.jpg",
      "type": 1,
      "altText": "苹果主图"
    }
  ]
}
```

#### 2.1.4 更新商品
```http
PUT /api/products/{id}
```

#### 2.1.5 删除商品
```http
DELETE /api/products/{id}
```

#### 2.1.6 获取热门商品
```http
GET /api/products/hot
```

#### 2.1.7 获取新品推荐
```http
GET /api/products/new
```

#### 2.1.8 获取精选商品
```http
GET /api/products/featured
```

### 2.2 分类管理接口

#### 2.2.1 获取所有分类
```http
GET /api/categories
```

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "name": "新鲜蔬菜",
      "parentId": 0,
      "level": 1,
      "icon": "vegetable-icon",
      "image": "https://example.com/vegetable.jpg",
      "description": "新鲜时令蔬菜",
      "isHot": true,
      "productCount": 150,
      "children": [
        {
          "id": 11,
          "name": "叶菜类",
          "parentId": 1,
          "level": 2,
          "productCount": 50,
          "children": []
        }
      ]
    }
  ]
}
```

#### 2.2.2 获取主分类
```http
GET /api/categories/main
```

#### 2.2.3 获取子分类
```http
GET /api/categories/{parentId}/children
```

#### 2.2.4 获取分类详情
```http
GET /api/categories/{id}
```

### 2.3 购物车接口

#### 2.3.1 获取购物车列表
```http
GET /api/cart
```

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "items": [
      {
        "id": 1,
        "productId": 1,
        "productName": "红富士苹果",
        "productImage": "https://example.com/apple.jpg",
        "price": 25.80,
        "quantity": 2,
        "selected": true,
        "stock": 100,
        "sellerId": 123,
        "sellerName": "果农老王",
        "specifications": "500g/个",
        "totalPrice": 51.60,
        "createdAt": "2024-12-01T10:00:00"
      }
    ],
    "summary": {
      "totalItems": 3,
      "selectedItems": 2,
      "totalAmount": 76.40,
      "discountAmount": 5.00,
      "finalAmount": 71.40
    }
  }
}
```

#### 2.3.2 添加到购物车
```http
POST /api/cart/items
```

**请求数据**:
```json
{
  "productId": 1,
  "quantity": 2
}
```

#### 2.3.3 更新购物车商品数量
```http
PUT /api/cart/items/{id}
```

#### 2.3.4 删除购物车商品
```http
DELETE /api/cart/items/{id}
```

#### 2.3.5 清空购物车
```http
DELETE /api/cart/clear
```

### 2.4 订单管理接口

#### 2.4.1 创建订单
```http
POST /api/orders
```

**请求数据**:
```json
{
  "items": [
    {
      "productId": 1,
      "quantity": 2,
      "price": 25.80
    }
  ],
  "shippingAddress": {
    "receiverName": "张三",
    "phone": "13800138000",
    "province": "北京市",
    "city": "北京市",
    "district": "朝阳区",
    "detail": "某某街道123号",
    "zipCode": "100000"
  },
  "paymentMethod": "alipay",
  "remark": "请尽快发货"
}
```

#### 2.4.2 获取订单列表
```http
GET /api/orders
```

#### 2.4.3 获取订单详情
```http
GET /api/orders/{id}
```

#### 2.4.4 取消订单
```http
PUT /api/orders/{id}/cancel
```

#### 2.4.5 确认收货
```http
PUT /api/orders/{id}/receive
```

### 2.5 评价管理接口

#### 2.5.1 获取商品评价
```http
GET /api/products/{productId}/reviews
```

#### 2.5.2 创建评价
```http
POST /api/reviews
```

#### 2.5.3 点赞评价
```http
POST /api/reviews/{id}/like
```

### 2.6 收藏管理接口

#### 2.6.1 获取收藏列表
```http
GET /api/favorites
```

#### 2.6.2 添加收藏
```http
POST /api/favorites
```

#### 2.6.3 取消收藏
```http
DELETE /api/favorites/{productId}
```

### 2.7 搜索接口

#### 2.7.1 商品搜索
```http
GET /api/search
```

**请求参数**:
```json
{
  "keyword": "苹果",
  "categoryId": 2,
  "minPrice": 10.00,
  "maxPrice": 100.00,
  "origin": "山东",
  "brand": "富士",
  "sortBy": "sales",
  "sortOrder": "desc",
  "page": 1,
  "size": 20
}
```

#### 2.7.2 获取热门搜索
```http
GET /api/search/hot-keywords
```

#### 2.7.3 获取搜索建议
```http
GET /api/search/suggestions
```

### 2.8 统计接口

#### 2.8.1 获取首页统计
```http
GET /api/statistics/dashboard
```

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "totalProducts": 1500,
    "totalSuppliers": 200,
    "totalTransactions": 5000,
    "totalUsers": 10000,
    "todayOrders": 150,
    "todayRevenue": 25000.00
  }
}
```

## 3. 核心业务逻辑实现

### 3.1 商品服务实现

```java
@Service
@Transactional
public class ProductServiceImpl implements ProductService {
    
    @Autowired
    private ProductMapper productMapper;
    
    @Autowired
    private ProductImageMapper productImageMapper;
    
    @Autowired
    private CategoryMapper categoryMapper;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Override
    public PageResult<ProductVO> getProducts(ProductQueryDTO queryDTO) {
        // 构建查询条件
        LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Product::getDeleted, 0)
               .eq(Product::getStatus, 1);
        
        // 分类筛选
        if (queryDTO.getCategoryId() != null) {
            wrapper.eq(Product::getCategoryId, queryDTO.getCategoryId());
        }
        
        // 关键词搜索
        if (StringUtils.hasText(queryDTO.getKeyword())) {
            wrapper.and(w -> w.like(Product::getName, queryDTO.getKeyword())
                           .or().like(Product::getDescription, queryDTO.getKeyword()));
        }
        
        // 价格区间
        if (queryDTO.getMinPrice() != null) {
            wrapper.ge(Product::getPrice, queryDTO.getMinPrice());
        }
        if (queryDTO.getMaxPrice() != null) {
            wrapper.le(Product::getPrice, queryDTO.getMaxPrice());
        }
        
        // 产地筛选
        if (StringUtils.hasText(queryDTO.getOrigin())) {
            wrapper.eq(Product::getOrigin, queryDTO.getOrigin());
        }
        
        // 卖家筛选
        if (queryDTO.getSellerId() != null) {
            wrapper.eq(Product::getSellerId, queryDTO.getSellerId());
        }
        
        // 特殊标记筛选
        if (queryDTO.getIsHot() != null && queryDTO.getIsHot()) {
            wrapper.eq(Product::getIsHot, 1);
        }
        if (queryDTO.getIsNew() != null && queryDTO.getIsNew()) {
            wrapper.eq(Product::getIsNew, 1);
        }
        
        // 排序
        String sortBy = queryDTO.getSortBy();
        String sortOrder = queryDTO.getSortOrder();
        if ("price".equals(sortBy)) {
            wrapper.orderBy(true, "asc".equals(sortOrder), Product::getPrice);
        } else if ("sales".equals(sortBy)) {
            wrapper.orderBy(true, "asc".equals(sortOrder), Product::getSalesCount);
        } else if ("rating".equals(sortBy)) {
            wrapper.orderBy(true, "asc".equals(sortOrder), Product::getRating);
        } else {
            wrapper.orderByDesc(Product::getCreatedAt);
        }
        
        // 分页查询
        Page<Product> page = new Page<>(queryDTO.getPage(), queryDTO.getSize());
        Page<Product> result = productMapper.selectPage(page, wrapper);
        
        // 转换为VO
        List<ProductVO> productVOs = result.getRecords().stream()
            .map(this::convertToVO)
            .collect(Collectors.toList());
        
        return PageResult.<ProductVO>builder()
            .records(productVOs)
            .total(result.getTotal())
            .pages(result.getPages())
            .current(result.getCurrent())
            .size(result.getSize())
            .build();
    }
    
    @Override
    @Cacheable(value = "product", key = "#id", unless = "#result == null")
    public ProductDetailVO getProductDetail(Long id) {
        // 获取商品基本信息
        Product product = productMapper.selectById(id);
        if (product == null || product.getDeleted() == 1) {
            throw new BusinessException("商品不存在");
        }
        
        // 增加浏览次数
        productMapper.incrementViewCount(id);
        
        // 获取商品图片
        List<ProductImage> images = productImageMapper.selectList(
            new LambdaQueryWrapper<ProductImage>()
                .eq(ProductImage::getProductId, id)
                .orderByAsc(ProductImage::getSortOrder)
        );
        
        // 获取分类信息
        Category category = categoryMapper.selectById(product.getCategoryId());
        
        // 获取相关商品
        List<Product> relatedProducts = getRelatedProducts(id, product.getCategoryId());
        
        // 转换为详情VO
        return convertToDetailVO(product, images, category, relatedProducts);
    }
    
    @Override
    public Long createProduct(ProductCreateDTO createDTO) {
        // 参数验证
        validateProductData(createDTO);
        
        // 转换为实体
        Product product = convertToEntity(createDTO);
        product.setCreatedAt(LocalDateTime.now());
        product.setUpdatedAt(LocalDateTime.now());
        
        // 保存商品
        productMapper.insert(product);
        
        // 保存商品图片
        if (CollectionUtils.isNotEmpty(createDTO.getImages())) {
            saveProductImages(product.getId(), createDTO.getImages());
        }
        
        // 清除相关缓存
        clearProductCache(product.getCategoryId());
        
        return product.getId();
    }
    
    @Override
    @CacheEvict(value = "product", key = "#id")
    public void updateProduct(Long id, ProductUpdateDTO updateDTO) {
        Product existingProduct = productMapper.selectById(id);
        if (existingProduct == null) {
            throw new BusinessException("商品不存在");
        }
        
        // 更新商品信息
        Product product = convertToEntity(updateDTO);
        product.setId(id);
        product.setUpdatedAt(LocalDateTime.now());
        productMapper.updateById(product);
        
        // 更新商品图片
        if (updateDTO.getImages() != null) {
            // 删除原有图片
            productImageMapper.delete(
                new LambdaQueryWrapper<ProductImage>()
                    .eq(ProductImage::getProductId, id)
            );
            // 保存新图片
            saveProductImages(id, updateDTO.getImages());
        }
        
        // 清除相关缓存
        clearProductCache(existingProduct.getCategoryId());
    }
    
    @Override
    @CacheEvict(value = "product", key = "#id")
    public void deleteProduct(Long id) {
        Product product = productMapper.selectById(id);
        if (product == null) {
            throw new BusinessException("商品不存在");
        }
        
        // 逻辑删除
        product.setDeleted(1);
        product.setUpdatedAt(LocalDateTime.now());
        productMapper.updateById(product);
        
        // 清除相关缓存
        clearProductCache(product.getCategoryId());
    }
    
    @Override
    @Cacheable(value = "hot_products", unless = "#result.isEmpty()")
    public List<ProductVO> getHotProducts(int limit) {
        List<Product> products = productMapper.selectList(
            new LambdaQueryWrapper<Product>()
                .eq(Product::getDeleted, 0)
                .eq(Product::getStatus, 1)
                .eq(Product::getIsHot, 1)
                .orderByDesc(Product::getSalesCount)
                .last("LIMIT " + limit)
        );
        
        return products.stream()
            .map(this::convertToVO)
            .collect(Collectors.toList());
    }
    
    private void validateProductData(ProductCreateDTO createDTO) {
        if (!StringUtils.hasText(createDTO.getName())) {
            throw new BusinessException("商品名称不能为空");
        }
        if (createDTO.getPrice() == null || createDTO.getPrice().compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException("商品价格必须大于0");
        }
        if (createDTO.getStock() == null || createDTO.getStock() < 0) {
            throw new BusinessException("商品库存不能为负数");
        }
        if (createDTO.getCategoryId() == null) {
            throw new BusinessException("商品分类不能为空");
        }
        
        // 验证分类是否存在
        Category category = categoryMapper.selectById(createDTO.getCategoryId());
        if (category == null || category.getDeleted() == 1) {
            throw new BusinessException("商品分类不存在");
        }
    }
    
    private void saveProductImages(Long productId, List<ProductImageDTO> imageDTOs) {
        for (int i = 0; i < imageDTOs.size(); i++) {
            ProductImageDTO imageDTO = imageDTOs.get(i);
            ProductImage image = new ProductImage();
            image.setProductId(productId);
            image.setImageUrl(imageDTO.getUrl());
            image.setImageType(imageDTO.getType());
            image.setAltText(imageDTO.getAltText());
            image.setSortOrder(i);
            image.setCreatedAt(LocalDateTime.now());
            productImageMapper.insert(image);
        }
    }
    
    private void clearProductCache(Long categoryId) {
        redisTemplate.delete("hot_products");
        redisTemplate.delete("new_products");
        redisTemplate.delete("featured_products");
        redisTemplate.delete("category_products:" + categoryId);
    }
}
```

### 3.2 购物车服务实现

```java
@Service
@Transactional
public class CartServiceImpl implements CartService {
    
    @Autowired
    private CartItemMapper cartItemMapper;
    
    @Autowired
    private ProductMapper productMapper;
    
    @Override
    public CartVO getCart(Long userId) {
        List<CartItem> cartItems = cartItemMapper.selectList(
            new LambdaQueryWrapper<CartItem>()
                .eq(CartItem::getUserId, userId)
                .orderByDesc(CartItem::getCreatedAt)
        );
        
        List<CartItemVO> itemVOs = cartItems.stream()
            .map(this::convertToVO)
            .collect(Collectors.toList());
        
        // 计算购物车汇总信息
        CartSummaryVO summary = calculateCartSummary(itemVOs);
        
        return CartVO.builder()
            .items(itemVOs)
            .summary(summary)
            .build();
    }
    
    @Override
    public void addToCart(Long userId, CartAddDTO addDTO) {
        // 验证商品是否存在且可购买
        Product product = productMapper.selectById(addDTO.getProductId());
        if (product == null || product.getDeleted() == 1 || product.getStatus() != 1) {
            throw new BusinessException("商品不存在或已下架");
        }
        
        // 检查库存
        if (product.getStock() < addDTO.getQuantity()) {
            throw new BusinessException("商品库存不足");
        }
        
        // 检查是否已在购物车中
        CartItem existingItem = cartItemMapper.selectOne(
            new LambdaQueryWrapper<CartItem>()
                .eq(CartItem::getUserId, userId)
                .eq(CartItem::getProductId, addDTO.getProductId())
        );
        
        if (existingItem != null) {
            // 更新数量
            int newQuantity = existingItem.getQuantity() + addDTO.getQuantity();
            if (newQuantity > product.getStock()) {
                throw new BusinessException("商品库存不足");
            }
            existingItem.setQuantity(newQuantity);
            existingItem.setUpdatedAt(LocalDateTime.now());
            cartItemMapper.updateById(existingItem);
        } else {
            // 新增购物车项
            CartItem cartItem = new CartItem();
            cartItem.setUserId(userId);
            cartItem.setProductId(addDTO.getProductId());
            cartItem.setQuantity(addDTO.getQuantity());
            cartItem.setSelected(true);
            cartItem.setCreatedAt(LocalDateTime.now());
            cartItem.setUpdatedAt(LocalDateTime.now());
            cartItemMapper.insert(cartItem);
        }
    }
    
    private CartSummaryVO calculateCartSummary(List<CartItemVO> items) {
        int totalItems = items.size();
        int selectedItems = (int) items.stream().filter(CartItemVO::getSelected).count();
        
        BigDecimal totalAmount = items.stream()
            .filter(CartItemVO::getSelected)
            .map(item -> item.getPrice().multiply(BigDecimal.valueOf(item.getQuantity())))
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        // 计算优惠金额（这里可以根据具体业务逻辑实现）
        BigDecimal discountAmount = BigDecimal.ZERO;
        BigDecimal finalAmount = totalAmount.subtract(discountAmount);
        
        return CartSummaryVO.builder()
            .totalItems(totalItems)
            .selectedItems(selectedItems)
            .totalAmount(totalAmount)
            .discountAmount(discountAmount)
            .finalAmount(finalAmount)
            .build();
    }
}
```

## 4. 异常处理

### 4.1 全局异常处理器

```java
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {
    
    @ExceptionHandler(BusinessException.class)
    public Result<Void> handleBusinessException(BusinessException e) {
        log.warn("业务异常: {}", e.getMessage());
        return Result.error(e.getCode(), e.getMessage());
    }
    
    @ExceptionHandler(ValidationException.class)
    public Result<Void> handleValidationException(ValidationException e) {
        log.warn("参数验证异常: {}", e.getMessage());
        return Result.error(400, "参数验证失败: " + e.getMessage());
    }
    
    @ExceptionHandler(Exception.class)
    public Result<Void> handleException(Exception e) {
        log.error("系统异常", e);
        return Result.error(500, "系统内部错误");
    }
}
```

### 4.2 业务异常定义

```java
public class BusinessException extends RuntimeException {
    private int code;
    
    public BusinessException(String message) {
        super(message);
        this.code = 400;
    }
    
    public BusinessException(int code, String message) {
        super(message);
        this.code = code;
    }
    
    // getter/setter
}
```

## 5. 缓存策略

### 5.1 Redis缓存配置

```java
@Configuration
@EnableCaching
public class CacheConfig {
    
    @Bean
    public CacheManager cacheManager(RedisConnectionFactory factory) {
        RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofHours(1))
            .serializeKeysWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new StringRedisSerializer()))
            .serializeValuesWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new GenericJackson2JsonRedisSerializer()));
        
        return RedisCacheManager.builder(factory)
            .cacheDefaults(config)
            .build();
    }
}
```

### 5.2 缓存使用示例

```java
// 商品详情缓存1小时
@Cacheable(value = "product", key = "#id", unless = "#result == null")
public ProductDetailVO getProductDetail(Long id) {
    // 实现逻辑
}

// 热门商品缓存30分钟
@Cacheable(value = "hot_products", unless = "#result.isEmpty()")
public List<ProductVO> getHotProducts(int limit) {
    // 实现逻辑
}

// 更新商品时清除缓存
@CacheEvict(value = "product", key = "#id")
public void updateProduct(Long id, ProductUpdateDTO updateDTO) {
    // 实现逻辑
}
```

## 6. 性能优化

### 6.1 数据库优化
- 合理使用索引
- 分页查询避免深度分页
- 读写分离
- 连接池优化

### 6.2 缓存优化
- 热点数据缓存
- 缓存预热
- 缓存穿透防护
- 缓存雪崩防护

### 6.3 接口优化
- 异步处理
- 批量操作
- 数据压缩
- CDN加速

## 7. 安全措施

### 7.1 接口安全
- JWT身份认证
- 接口权限控制
- 请求频率限制
- 参数验证

### 7.2 数据安全
- SQL注入防护
- XSS攻击防护
- 敏感数据加密
- 操作日志记录

## 8. 监控与日志

### 8.1 日志配置

```yaml
logging:
  level:
    com.agriculture.mall: DEBUG
    org.springframework.web: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/mall.log
    max-size: 100MB
    max-history: 30
```

### 8.2 性能监控
- 接口响应时间监控
- 数据库查询性能监控
- 缓存命中率监控
- 系统资源使用监控

---

**文档版本**: v1.0  
**创建日期**: 2024年12月  
**最后更新**: 2024年12月  
**维护人员**: 后端开发团队