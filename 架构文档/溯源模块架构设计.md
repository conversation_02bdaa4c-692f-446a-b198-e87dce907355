# 农产品溯源模块架构设计文档

## 1. 需求分析与核心功能点

### 1.1 核心需求
- 实现农产品"一物一码"全程溯源体系
- 记录农产品从种植、加工到销售的全生命周期数据
- 提供消费者便捷的溯源信息查询体验
- 提升农产品品质信任度与市场竞争力
- 支持生产者自助上传溯源信息

### 1.2 核心功能点
1. **溯源码生成与管理**：为每个农产品批次生成唯一溯源码和二维码
2. **生产环节信息记录**：包括种植、施肥、用药、采收等环节
3. **加工信息记录**：包括清洗、分拣、包装等环节
4. **检验认证信息**：质量检测报告、有机认证等信息
5. **物流运输记录**：运输方式、时间、温度监控等
6. **溯源信息查询**：通过扫码或手动输入溯源码查询
7. **溯源信息可视化**：以时间轴、图表等形式直观呈现溯源数据
8. **溯源报告生成**：生成可打印的溯源报告
9. **溯源数据统计分析**：为生产者提供数据分析

## 2. 溯源模块与系统集成方案

### 2.1 系统集成架构
![溯源模块与系统集成架构](../架构文档/images/traceability_integration_architecture.png)

### 2.2 核心集成点
1. **用户体系集成**：
   - 复用现有用户体系，扩展销售者权限模型
   - 集成现有认证授权机制
   
2. **产品数据集成**：
   - 溯源模块与产品目录关联，确保产品信息一致性
   - 产品详情页面集成溯源信息展示组件
   - 在ProductDetail.vue中新增"溯源信息"标签页
   - 集成溯源码展示、溯源时间线、质检报告、认证信息等组件
   - 支持溯源数据的可视化展示（ECharts图表）
   - 提供溯源操作功能（复制、分享、下载报告等）

3. **交易数据关联**：
   - 订单系统与溯源系统关联，便于购买者快速查看溯源信息
   - 支持在订单详情中展示溯源二维码

4. **消息通知集成**：
   - 利用现有消息推送系统发送溯源相关通知
   - 溯源状态更新触发相关消息推送

## 3. 溯源数据流转图

```mermaid
flowchart TD
    A[生产者] -->|上传生产信息| B(溯源信息管理系统)
    B -->|存储| C[(溯源数据库)]
    A -->|申请| D[溯源码生成]
    D -->|生成| E[溯源二维码]
    E -->|绑定| B
    F[消费者] -->|扫描| E
    F -->|输入溯源码| G[溯源查询接口]
    G -->|查询| C
    C -->|返回数据| H[溯源信息展示]
    H -->|展示| F
    B -->|触发| I[数据验证]
    I -->|更新| B
    J[管理员] -->|审核| B
```

## 4. 溯源模块组件结构

### 4.1 后端组件结构
```mermaid
flowchart TD
    A[TraceController] -->|调用| B[TraceService]
    B -->|调用| C[TraceRecordMapper]
    B -->|调用| D[TraceEventMapper]
    C -->|操作| E[(数据库)]
    D -->|操作| E
    B -->|调用| F[QRCodeGenerator]
    B -->|调用| G[TraceReportGenerator]
    H[UserService] -->|权限校验| B
    I[ProductService] -->|产品信息| B
```

### 4.2 前端组件结构
```mermaid
flowchart TD
    A[溯源中心页面] -->|包含| B[溯源查询组件]
    A -->|包含| C[溯源结果展示]
    C -->|包含| D[溯源时间轴组件]
    C -->|包含| E[溯源详情组件]
    C -->|包含| F[溯源报告组件]
    G[生产者溯源管理] -->|包含| H[溯源记录列表]
    G -->|包含| I[溯源信息录入]
    G -->|包含| J[溯源码管理]
    K[系统导航] -->|链接| A
    K -->|链接| G
    L[产品详情页面] -->|包含| M[溯源信息标签页]
    M -->|包含| N[溯源码展示组件]
    M -->|包含| O[溯源时间线组件]
    M -->|包含| P[质检报告组件]
    M -->|包含| Q[认证信息组件]
    M -->|包含| R[统计图表组件]
    M -->|包含| S[溯源操作组件]
```

## 5. 前后端交互接口规范

### 5.1 RESTful API设计原则
- 采用标准HTTP方法（GET, POST, PUT, DELETE）
- 使用资源导向的URL设计
- 统一的响应格式和状态码
- API版本控制（/api/v1/...）

### 5.2 核心API接口定义

#### 5.2.1 溯源记录管理接口
- **创建溯源记录**: POST /api/v1/trace/records
- **更新溯源记录**: PUT /api/v1/trace/records/{id}
- **获取溯源记录**: GET /api/v1/trace/records/{id}
- **获取生产者溯源记录列表**: GET /api/v1/trace/records/producer/{producerId}

#### 5.2.2 溯源事件管理接口
- **添加溯源事件**: POST /api/v1/trace/events
- **更新溯源事件**: PUT /api/v1/trace/events/{id}
- **删除溯源事件**: DELETE /api/v1/trace/events/{id}
- **获取溯源记录的所有事件**: GET /api/v1/trace/records/{recordId}/events

#### 5.2.3 溯源码管理接口
- **生成溯源码**: POST /api/v1/trace/codes/generate
- **验证溯源码**: GET /api/v1/trace/codes/verify/{code}
- **查询溯源信息**: GET /api/v1/trace/query?code={traceCode}

#### 5.2.4 溯源报告接口
- **生成溯源报告**: GET /api/v1/trace/reports/{recordId}

#### 5.2.5 产品详情页面溯源集成接口
- **通过产品ID获取溯源信息**: GET /api/v1/trace/product/{productId}
- **获取产品溯源步骤**: GET /api/v1/trace/product/{productId}/steps
- **获取产品质检报告**: GET /api/v1/trace/product/{productId}/quality-reports
- **获取产品认证信息**: GET /api/v1/trace/product/{productId}/certifications
- **获取产品溯源统计**: GET /api/v1/trace/product/{productId}/stats
- **生成产品分享链接**: POST /api/v1/trace/product/{productId}/share
- **下载产品溯源报告**: GET /api/v1/trace/product/{productId}/report/download

### 5.3 请求/响应格式
- 请求格式：JSON格式，UTF-8编码
- 响应格式：统一采用以下结构
  ```json
  {
    "code": 200,       // 业务状态码，0表示成功
    "message": "success",  // 状态描述
    "data": { ... }    // 业务数据
  }
  ```

## 6. 溯源数据存储结构

### 6.1 核心数据表设计
1. **traceability_records（溯源主记录表）**
   - 记录ID、产品ID、溯源码、农场信息、生产者信息、状态等

2. **traceability_events（溯源事件表）**
   - 事件ID、溯源记录ID、事件类型、时间、地点、描述、附件信息等

3. **trace_certificates（认证信息表）**
   - 认证ID、溯源记录ID、认证类型、认证机构、证书编号、有效期等

4. **trace_logistics（物流信息表）**
   - 物流ID、溯源记录ID、运输方式、承运商、出发地、目的地、状态等

5. **trace_codes（溯源码管理表）**
   - 溯源码、生成时间、状态、关联产品ID、二维码URL等

### 6.2 主要表结构详细设计
请参考数据库设计文档中的详细表结构设计。

## 7. 溯源信息安全策略

### 7.1 数据安全措施
1. **权限控制**：
   - 严格的角色权限管理
   - 生产者只能操作自己的溯源数据
   - 管理员有审核和管理权限

2. **数据验证**：
   - 输入数据严格验证
   - 事件时间的合理性验证
   - 溯源事件的逻辑顺序验证

3. **溯源码安全**：
   - 溯源码生成采用安全算法
   - 防止溯源码被伪造和篡改

4. **数据存储安全**：
   - 关键数据加密存储
   - 数据库访问权限控制
   - 定期数据备份

5. **操作审计**：
   - 记录所有关键操作日志
   - 溯源数据修改历史记录

## 8. 未来拓展计划

### 8.1 技术拓展
1. **区块链技术集成**：
   - 利用区块链确保溯源数据不可篡改
   - 提升溯源数据公信力

2. **IoT设备数据接入**：
   - 集成农田监测设备数据
   - 自动记录温度、湿度、光照等环境数据

3. **AI分析能力**：
   - 智能识别农产品质量
   - 预测产量和品质

4. **产品详情页面溯源优化**：
   - 实现溯源数据实时更新
   - 增强溯源可视化效果
   - 支持AR/VR溯源体验
   - 集成更多第三方认证数据
   - 优化移动端溯源展示体验

### 8.2 业务拓展
1. **溯源认证体系**：
   - 建立溯源标准认证
   - 发展溯源评级制度

2. **供应链协作**：
   - 上下游企业溯源数据共享
   - 构建完整供应链溯源网络

## 9. 附录

### 9.1 相关技术标准
- GS1全球统一标识系统
- 农产品质量安全追溯编码规则
- 农产品质量安全追溯信息采集规范

### 9.2 参考文献
- 《农产品质量安全可追溯管理办法》
- 《食品安全国家标准》