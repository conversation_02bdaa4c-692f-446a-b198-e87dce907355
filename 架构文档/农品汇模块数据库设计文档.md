# 农品汇模块数据库设计文档

## 1. 模块概述

### 1.1 模块名称
农品汇 (Agricultural Product Mall)

### 1.2 模块描述
农品汇是一个专业的农产品交易平台，连接农产品生产者与消费者，提供商品展示、搜索、分类、推荐、购买等功能。支持多种商品类型，包括新鲜蔬菜、水果、粮食、畜牧产品等。

### 1.3 核心功能
- 商品管理：商品发布、编辑、删除、上下架
- 商品展示：商品列表、详情、图片展示
- 分类管理：多级分类体系
- 搜索功能：关键词搜索、高级筛选
- 推荐系统：基于用户行为的个性化推荐
- 购物车：商品加入购物车、数量管理
- 订单管理：下单、支付、发货、收货
- 用户评价：商品评分、评论
- 价格管理：价格历史、价格走势

## 2. 数据库表设计

### 2.1 商品相关表

#### 2.1.1 商品主表 (product)
```sql
CREATE TABLE `product` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '商品ID',
  `name` varchar(255) NOT NULL COMMENT '商品名称',
  `description` text COMMENT '商品描述',
  `image` varchar(500) COMMENT '商品主图',
  `price` decimal(10,2) NOT NULL COMMENT '商品价格',
  `original_price` decimal(10,2) COMMENT '原价',
  `stock` int NOT NULL DEFAULT 0 COMMENT '库存数量',
  `sales_count` int DEFAULT 0 COMMENT '销售数量',
  `rating` decimal(3,2) DEFAULT 0.00 COMMENT '平均评分',
  `review_count` int DEFAULT 0 COMMENT '评价数量',
  `category_id` bigint NOT NULL COMMENT '分类ID',
  `seller_id` bigint NOT NULL COMMENT '卖家ID',
  `brand` varchar(100) COMMENT '品牌',
  `origin` varchar(100) COMMENT '产地',
  `unit` varchar(20) COMMENT '单位(斤/公斤/箱等)',
  `shelf_life` varchar(50) COMMENT '保质期',
  `storage_method` varchar(100) COMMENT '储存方式',
  `tags` varchar(500) COMMENT '标签(JSON格式)',
  `specifications` text COMMENT '规格参数(JSON格式)',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态(0:下架 1:上架 2:审核中)',
  `is_featured` tinyint DEFAULT 0 COMMENT '是否精选(0:否 1:是)',
  `is_hot` tinyint DEFAULT 0 COMMENT '是否热门(0:否 1:是)',
  `is_new` tinyint DEFAULT 0 COMMENT '是否新品(0:否 1:是)',
  `sort_order` int DEFAULT 0 COMMENT '排序权重',
  `view_count` int DEFAULT 0 COMMENT '浏览次数',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint DEFAULT 0 COMMENT '逻辑删除(0:未删除 1:已删除)',
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_seller_id` (`seller_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_sales_count` (`sales_count`),
  KEY `idx_rating` (`rating`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品主表';
```

#### 2.1.2 商品图片表 (product_image)
```sql
CREATE TABLE `product_image` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '图片ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `image_url` varchar(500) NOT NULL COMMENT '图片URL',
  `image_type` tinyint NOT NULL DEFAULT 1 COMMENT '图片类型(1:主图 2:详情图 3:规格图)',
  `sort_order` int DEFAULT 0 COMMENT '排序',
  `alt_text` varchar(255) COMMENT '图片描述',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_image_type` (`image_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品图片表';
```

#### 2.1.3 商品价格历史表 (product_price_history)
```sql
CREATE TABLE `product_price_history` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '价格历史ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `price` decimal(10,2) NOT NULL COMMENT '价格',
  `original_price` decimal(10,2) COMMENT '原价',
  `change_reason` varchar(100) COMMENT '变价原因',
  `effective_date` datetime NOT NULL COMMENT '生效时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_effective_date` (`effective_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品价格历史表';
```

### 2.2 分类相关表

#### 2.2.1 商品分类表 (category)
```sql
CREATE TABLE `category` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `parent_id` bigint DEFAULT 0 COMMENT '父分类ID(0表示顶级分类)',
  `level` tinyint NOT NULL DEFAULT 1 COMMENT '分类层级',
  `icon` varchar(255) COMMENT '分类图标',
  `image` varchar(255) COMMENT '分类图片',
  `description` text COMMENT '分类描述',
  `sort_order` int DEFAULT 0 COMMENT '排序',
  `is_hot` tinyint DEFAULT 0 COMMENT '是否热门(0:否 1:是)',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint DEFAULT 0 COMMENT '逻辑删除(0:未删除 1:已删除)',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_level` (`level`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品分类表';
```
我现在需要你对于农品汇中的分类以及高级分类农产品进行优化与完善请你完成这个任务
### 2.3 购物车相关表

#### 2.3.1 购物车表 (cart_item)
```sql
CREATE TABLE `cart_item` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '购物车项ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `quantity` int NOT NULL DEFAULT 1 COMMENT '数量',
  `selected` tinyint DEFAULT 1 COMMENT '是否选中(0:否 1:是)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_product` (`user_id`, `product_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='购物车表';
```

### 2.4 订单相关表

#### 2.4.1 订单主表 (order)
```sql
CREATE TABLE `order` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(32) NOT NULL COMMENT '订单号',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `seller_id` bigint NOT NULL COMMENT '卖家ID',
  `total_amount` decimal(10,2) NOT NULL COMMENT '订单总金额',
  `discount_amount` decimal(10,2) DEFAULT 0.00 COMMENT '优惠金额',
  `shipping_fee` decimal(10,2) DEFAULT 0.00 COMMENT '运费',
  `actual_amount` decimal(10,2) NOT NULL COMMENT '实付金额',
  `payment_method` varchar(20) COMMENT '支付方式',
  `payment_status` tinyint NOT NULL DEFAULT 0 COMMENT '支付状态(0:未支付 1:已支付 2:已退款)',
  `order_status` tinyint NOT NULL DEFAULT 0 COMMENT '订单状态(0:待支付 1:待发货 2:待收货 3:已完成 4:已取消)',
  `shipping_address` text COMMENT '收货地址(JSON格式)',
  `remark` varchar(500) COMMENT '订单备注',
  `payment_time` datetime COMMENT '支付时间',
  `shipping_time` datetime COMMENT '发货时间',
  `receive_time` datetime COMMENT '收货时间',
  `cancel_time` datetime COMMENT '取消时间',
  `cancel_reason` varchar(255) COMMENT '取消原因',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_seller_id` (`seller_id`),
  KEY `idx_order_status` (`order_status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单主表';
```

#### 2.4.2 订单商品表 (order_item)
```sql
CREATE TABLE `order_item` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '订单商品ID',
  `order_id` bigint NOT NULL COMMENT '订单ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `product_name` varchar(255) NOT NULL COMMENT '商品名称',
  `product_image` varchar(500) COMMENT '商品图片',
  `product_price` decimal(10,2) NOT NULL COMMENT '商品单价',
  `quantity` int NOT NULL COMMENT '购买数量',
  `total_price` decimal(10,2) NOT NULL COMMENT '小计金额',
  `specifications` varchar(500) COMMENT '商品规格',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单商品表';
```

### 2.5 评价相关表

#### 2.5.1 商品评价表 (product_review)
```sql
CREATE TABLE `product_review` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '评价ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `order_id` bigint NOT NULL COMMENT '订单ID',
  `rating` tinyint NOT NULL COMMENT '评分(1-5)',
  `content` text COMMENT '评价内容',
  `images` varchar(1000) COMMENT '评价图片(JSON格式)',
  `reply_content` text COMMENT '商家回复',
  `reply_time` datetime COMMENT '回复时间',
  `is_anonymous` tinyint DEFAULT 0 COMMENT '是否匿名(0:否 1:是)',
  `like_count` int DEFAULT 0 COMMENT '点赞数',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态(0:隐藏 1:显示)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_rating` (`rating`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品评价表';
```

### 2.6 收藏相关表

#### 2.6.1 商品收藏表 (product_favorite)
```sql
CREATE TABLE `product_favorite` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '收藏ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_product` (`user_id`, `product_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品收藏表';
```

### 2.7 推荐相关表

#### 2.7.1 用户行为表 (user_behavior)
```sql
CREATE TABLE `user_behavior` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '行为ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `behavior_type` tinyint NOT NULL COMMENT '行为类型(1:浏览 2:收藏 3:加购物车 4:购买)',
  `behavior_value` decimal(5,2) DEFAULT 1.00 COMMENT '行为权重值',
  `session_id` varchar(64) COMMENT '会话ID',
  `ip_address` varchar(45) COMMENT 'IP地址',
  `user_agent` varchar(500) COMMENT '用户代理',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_behavior_type` (`behavior_type`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户行为表';
```

#### 2.7.2 商品推荐表 (product_recommendation)
```sql
CREATE TABLE `product_recommendation` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '推荐ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `recommendation_type` tinyint NOT NULL COMMENT '推荐类型(1:协同过滤 2:内容推荐 3:热门推荐 4:新品推荐)',
  `score` decimal(5,4) NOT NULL COMMENT '推荐分数',
  `reason` varchar(255) COMMENT '推荐理由',
  `algorithm_version` varchar(20) COMMENT '算法版本',
  `is_clicked` tinyint DEFAULT 0 COMMENT '是否点击(0:否 1:是)',
  `click_time` datetime COMMENT '点击时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `expired_at` datetime NOT NULL COMMENT '过期时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_recommendation_type` (`recommendation_type`),
  KEY `idx_score` (`score`),
  KEY `idx_expired_at` (`expired_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品推荐表';
```

### 2.8 搜索相关表

#### 2.8.1 搜索关键词表 (search_keyword)
```sql
CREATE TABLE `search_keyword` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '关键词ID',
  `keyword` varchar(100) NOT NULL COMMENT '搜索关键词',
  `search_count` int DEFAULT 1 COMMENT '搜索次数',
  `result_count` int DEFAULT 0 COMMENT '结果数量',
  `is_hot` tinyint DEFAULT 0 COMMENT '是否热门(0:否 1:是)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_keyword` (`keyword`),
  KEY `idx_search_count` (`search_count`),
  KEY `idx_is_hot` (`is_hot`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='搜索关键词表';
```

#### 2.8.2 用户搜索历史表 (user_search_history)
```sql
CREATE TABLE `user_search_history` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '搜索历史ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `keyword` varchar(100) NOT NULL COMMENT '搜索关键词',
  `result_count` int DEFAULT 0 COMMENT '结果数量',
  `search_filters` text COMMENT '搜索筛选条件(JSON格式)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_keyword` (`keyword`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户搜索历史表';
```

### 2.9 溯源相关表

#### 2.9.1 溯源主记录表 (traceability_record)
```sql
CREATE TABLE `traceability_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '溯源记录ID',
  `product_id` bigint NOT NULL COMMENT '关联商品ID',
  `trace_code` varchar(64) NOT NULL COMMENT '溯源码',
  `qrcode_url` varchar(500) COMMENT '二维码URL',
  `farm_name` varchar(200) COMMENT '农场/生产基地名称',
  `producer_id` bigint NOT NULL COMMENT '生产者ID',
  `production_date` date COMMENT '生产日期',
  `harvest_date` date COMMENT '采收日期',
  `processing_date` date COMMENT '加工日期',
  `packaging_date` date COMMENT '包装日期',
  `shelf_life` int COMMENT '保质期(天)',
  `storage_condition` varchar(200) COMMENT '存储条件',
  `description` text COMMENT '产品描述',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态(0:未审核 1:已审核 2:已撤销)',
  `view_count` int DEFAULT 0 COMMENT '查看次数',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_trace_code` (`trace_code`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_producer_id` (`producer_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='溯源主记录表';
```

#### 2.9.2 溯源事件表 (traceability_event)
```sql
CREATE TABLE `traceability_event` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '事件ID',
  `record_id` bigint NOT NULL COMMENT '溯源记录ID',
  `event_type` tinyint NOT NULL COMMENT '事件类型(1:种植 2:施肥 3:用药 4:采收 5:加工 6:检测 7:包装 8:运输 9:入库)',
  `event_time` datetime NOT NULL COMMENT '事件时间',
  `location` varchar(255) COMMENT '事件地点',
  `operator` varchar(100) COMMENT '操作人',
  `description` text COMMENT '事件描述',
  `attachments` text COMMENT '附件信息(JSON格式)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_record_id` (`record_id`),
  KEY `idx_event_type` (`event_type`),
  KEY `idx_event_time` (`event_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='溯源事件表';
```

#### 2.9.3 溯源认证信息表 (trace_certificate)
```sql
CREATE TABLE `trace_certificate` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '认证ID',
  `record_id` bigint NOT NULL COMMENT '溯源记录ID',
  `cert_type` varchar(50) NOT NULL COMMENT '认证类型',
  `cert_name` varchar(200) NOT NULL COMMENT '认证名称',
  `cert_no` varchar(100) COMMENT '证书编号',
  `issuing_authority` varchar(200) COMMENT '发证机构',
  `issue_date` date COMMENT '发证日期',
  `valid_until` date COMMENT '有效期至',
  `cert_image` varchar(500) COMMENT '证书图片URL',
  `description` text COMMENT '认证描述',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_record_id` (`record_id`),
  KEY `idx_cert_type` (`cert_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='溯源认证信息表';
```

#### 2.9.4 溯源物流信息表 (trace_logistics)
```sql
CREATE TABLE `trace_logistics` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '物流ID',
  `record_id` bigint NOT NULL COMMENT '溯源记录ID',
  `carrier` varchar(100) COMMENT '承运商',
  `transport_type` varchar(50) COMMENT '运输方式',
  `tracking_no` varchar(100) COMMENT '运单号',
  `departure` varchar(255) COMMENT '出发地',
  `destination` varchar(255) COMMENT '目的地',
  `departure_time` datetime COMMENT '出发时间',
  `arrival_time` datetime COMMENT '到达时间',
  `temperature` decimal(5,2) COMMENT '运输温度(℃)',
  `humidity` decimal(5,2) COMMENT '运输湿度(%)',
  `status` tinyint DEFAULT 1 COMMENT '状态(1:运输中 2:已送达)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_record_id` (`record_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='溯源物流信息表';
```

#### 2.9.5 溯源质检报告表 (trace_quality_report)
```sql
CREATE TABLE `trace_quality_report` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '质检报告ID',
  `record_id` bigint NOT NULL COMMENT '溯源记录ID',
  `report_no` varchar(100) COMMENT '报告编号',
  `testing_org` varchar(200) COMMENT '检测机构',
  `testing_date` date COMMENT '检测日期',
  `report_url` varchar(500) COMMENT '报告URL',
  `result` tinyint DEFAULT 1 COMMENT '检测结果(0:不合格 1:合格)',
  `indicators` text COMMENT '检测指标(JSON格式)',
  `description` text COMMENT '报告描述',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_record_id` (`record_id`),
  KEY `idx_result` (`result`),
  KEY `idx_testing_date` (`testing_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='溯源质检报告表';
```

#### 2.9.6 溯源查询记录表 (trace_query_log)
```sql
CREATE TABLE `trace_query_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '查询记录ID',
  `trace_code` varchar(64) NOT NULL COMMENT '溯源码',
  `user_id` bigint COMMENT '用户ID(未登录为空)',
  `ip_address` varchar(45) COMMENT 'IP地址',
  `device_info` varchar(500) COMMENT '设备信息',
  `query_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '查询时间',
  `location` varchar(255) COMMENT '查询地点',
  PRIMARY KEY (`id`),
  KEY `idx_trace_code` (`trace_code`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_query_time` (`query_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='溯源查询记录表';
```

## 3. 索引设计

### 3.1 主要索引
- 商品表：分类ID、卖家ID、状态、创建时间、销量、评分
- 分类表：父分类ID、层级、排序
- 购物车表：用户ID、商品ID的联合唯一索引
- 订单表：订单号唯一索引、用户ID、卖家ID、订单状态
- 评价表：商品ID、用户ID、评分、创建时间
- 行为表：用户ID、商品ID、行为类型、创建时间
- 推荐表：用户ID、商品ID、推荐类型、分数、过期时间
- 溯源表：溯源码唯一索引、商品ID、生产者ID、状态
- 溯源事件表：溯源记录ID、事件类型、事件时间

### 3.2 复合索引
- 商品表：(category_id, status, created_at)
- 商品表：(status, is_hot, sales_count)
- 行为表：(user_id, behavior_type, created_at)
- 推荐表：(user_id, recommendation_type, score)
- 溯源事件表：(record_id, event_type, event_time)
- 溯源质检报告表：(record_id, result)

## 4. 数据字典

### 4.1 状态码定义

#### 商品状态 (product.status)
- 0: 下架
- 1: 上架
- 2: 审核中

#### 订单状态 (order.order_status)
- 0: 待支付
- 1: 待发货
- 2: 待收货
- 3: 已完成
- 4: 已取消

#### 支付状态 (order.payment_status)
- 0: 未支付
- 1: 已支付
- 2: 已退款

#### 用户行为类型 (user_behavior.behavior_type)
- 1: 浏览
- 2: 收藏
- 3: 加购物车
- 4: 购买

#### 推荐类型 (product_recommendation.recommendation_type)
- 1: 协同过滤推荐
- 2: 内容相似推荐
- 3: 热门商品推荐
- 4: 新品推荐

#### 溯源记录状态 (traceability_record.status)
- 0: 未审核
- 1: 已审核
- 2: 已撤销

#### 溯源事件类型 (traceability_event.event_type)
- 1: 种植
- 2: 施肥
- 3: 用药
- 4: 采收
- 5: 加工
- 6: 检测
- 7: 包装
- 8: 运输
- 9: 入库

#### 溯源物流状态 (trace_logistics.status)
- 1: 运输中
- 2: 已送达

#### 质检报告结果 (trace_quality_report.result)
- 0: 不合格
- 1: 合格

## 5. 数据初始化

### 5.1 分类数据初始化
```sql
-- 一级分类
INSERT INTO category (id, name, parent_id, level, sort_order) VALUES
(1, '新鲜蔬菜', 0, 1, 1),
(2, '新鲜水果', 0, 1, 2),
(3, '粮油调料', 0, 1, 3),
(4, '肉禽蛋奶', 0, 1, 4),
(5, '水产海鲜', 0, 1, 5),
(6, '农用工具', 0, 1, 6);

-- 二级分类
INSERT INTO category (id, name, parent_id, level, sort_order) VALUES
(11, '叶菜类', 1, 2, 1),
(12, '根茎类', 1, 2, 2),
(13, '瓜果类', 1, 2, 3),
(21, '热带水果', 2, 2, 1),
(22, '温带水果', 2, 2, 2),
(23, '浆果类', 2, 2, 3);
```

### 5.2 热门搜索关键词初始化
```sql
INSERT INTO search_keyword (keyword, search_count, is_hot) VALUES
('新鲜蔬菜', 1000, 1),
('有机水果', 800, 1),
('时令特产', 600, 1),
('绿色食品', 500, 1),
('农家自产', 400, 1);
```

## 6. 性能优化建议

### 6.1 查询优化
- 商品列表查询使用分页，避免全表扫描
- 热门商品、新品推荐使用缓存
- 搜索功能考虑使用Elasticsearch
- 用户行为数据定期归档

### 6.2 存储优化
- 商品图片使用CDN存储
- 大文本字段(description)考虑分离存储
- 历史数据定期清理或归档

### 6.3 缓存策略
- 商品详情缓存1小时
- 分类数据缓存24小时
- 热门商品缓存30分钟
- 推荐结果缓存2小时

## 7. 数据安全

### 7.1 敏感数据保护
- 用户地址信息加密存储
- 支付相关信息遵循PCI DSS标准
- 个人信息访问日志记录

### 7.2 数据备份
- 每日全量备份
- 实时增量备份
- 异地容灾备份

### 7.3 权限控制
- 数据库用户权限最小化
- 敏感操作审计日志
- 定期权限审查

---

**文档版本**: v1.0  
**创建日期**: 2024年12月  
**最后更新**: 2024年12月  
**维护人员**: 开发团队

## 8. 溯源模块与农品汇模块集成

### 8.1 数据关联关系
- **商品与溯源记录**：通过product_id字段，每个商品可以关联一个溯源记录，实现"一品一码"溯源体系
- **卖家与生产者**：卖家(seller_id)与溯源记录中的生产者(producer_id)建立关联，确保数据一致性
- **订单与溯源**：消费者购买商品后，可通过订单中的product_id查询相关溯源信息
- **商品详情与溯源展示**：在商品详情页面集成溯源信息展示组件，提供溯源码、溯源时间线等信息

### 8.2 业务流程集成
1. **商品发布流程**：
   - 卖家发布商品时可选择是否启用溯源功能
   - 启用溯源功能后，系统自动生成唯一溯源码
   - 卖家填写溯源相关信息（生产环节、加工环节等）

2. **商品展示流程**：
   - 商品详情页面显示溯源标识和溯源码
   - 消费者可扫码或点击查看完整溯源信息
   - 溯源信息以时间轴形式展示生产全过程

3. **订单流程**：
   - 订单确认后，溯源信息不可修改，确保数据真实性
   - 订单详情中展示溯源二维码，便于消费者查询
   - 消费者收货后可查看完整溯源报告

### 8.3 数据安全与权限控制
- 溯源数据的写入权限仅对应商品卖家和系统管理员开放
- 溯源数据一旦审核通过，关键信息不可修改，确保数据可信度
- 溯源查询记录完整保存，便于追踪异常查询行为
- 敏感溯源数据（如具体农药用量）可设置访问权限级别

### 8.4 技术实现要点
- 溯源码生成采用安全算法，确保唯一性和防伪性
- 溯源数据与商品数据实时同步，确保信息一致性
- 溯源查询接口支持高并发访问，采用缓存优化
- 溯源数据展示采用前端可视化组件，提升用户体验