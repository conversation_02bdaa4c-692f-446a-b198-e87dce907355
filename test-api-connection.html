<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SFAP农品汇API连接测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        
        .test-container {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .test-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .test-section {
            margin-bottom: 24px;
            padding: 16px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
        }
        
        .test-section h3 {
            margin: 0 0 12px 0;
            color: #374151;
        }
        
        .test-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            background: #5a67d8;
            transform: translateY(-1px);
        }
        
        .test-result {
            margin-top: 12px;
            padding: 12px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .success {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            color: #0c4a6e;
        }
        
        .error {
            background: #fef2f2;
            border: 1px solid #ef4444;
            color: #991b1b;
        }
        
        .loading {
            background: #fffbeb;
            border: 1px solid #f59e0b;
            color: #92400e;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success { background: #10b981; }
        .status-error { background: #ef4444; }
        .status-loading { background: #f59e0b; animation: pulse 1.5s infinite; }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">
            <span class="status-indicator status-loading"></span>
            SFAP农品汇API连接测试
        </h1>
        
        <div class="test-section">
            <h3>🛍️ 商品管理API测试</h3>
            <button class="test-button" onclick="testProductsAPI()">测试商品列表API</button>
            <button class="test-button" onclick="testProductStatsAPI()">测试商品统计API</button>
            <button class="test-button" onclick="testProductDetailAPI()">测试商品详情API</button>
            <div id="products-result" class="test-result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>📂 分类管理API测试</h3>
            <button class="test-button" onclick="testCategoriesAPI()">测试分类列表API</button>
            <button class="test-button" onclick="testCategoriesWithCountAPI()">测试带数量分类API</button>
            <button class="test-button" onclick="testCategoryTreeAPI()">测试分类树API</button>
            <div id="categories-result" class="test-result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>🔍 搜索功能测试</h3>
            <button class="test-button" onclick="testSearchAPI()">测试商品搜索API</button>
            <button class="test-button" onclick="testAdvancedSearchAPI()">测试高级搜索API</button>
            <div id="search-result" class="test-result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>📊 系统状态检查</h3>
            <button class="test-button" onclick="checkSystemStatus()">检查系统状态</button>
            <button class="test-button" onclick="testAllAPIs()">运行所有测试</button>
            <div id="system-result" class="test-result" style="display: none;"></div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8081';
        
        function showResult(elementId, content, type = 'success') {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `test-result ${type}`;
            element.textContent = content;
        }
        
        function showLoading(elementId) {
            showResult(elementId, '正在测试API连接...', 'loading');
        }
        
        async function makeAPIRequest(url, options = {}) {
            try {
                const response = await fetch(API_BASE_URL + url, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                
                const data = await response.json();
                return {
                    success: response.ok,
                    status: response.status,
                    data: data
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        }
        
        async function testProductsAPI() {
            showLoading('products-result');

            const result = await makeAPIRequest('/api/mall/products?page=1&size=5');

            if (result.success && result.data.code === 200) {
                const data = result.data.data;
                const message = `✅ 商品列表API测试成功！
状态码: ${result.status}
响应代码: ${result.data.code}
响应消息: ${result.data.message}
商品数量: ${data.records ? data.records.length : (Array.isArray(data) ? data.length : 0)}
总数: ${data.total || 'N/A'}
响应格式: ${JSON.stringify(result.data, null, 2)}`;
                showResult('products-result', message, 'success');
            } else {
                const message = `❌ 商品列表API测试失败！
状态码: ${result.status || 'N/A'}
响应代码: ${result.data?.code || 'N/A'}
错误信息: ${result.data?.message || result.error || '未知错误'}`;
                showResult('products-result', message, 'error');
            }
        }
        
        async function testProductStatsAPI() {
            showLoading('products-result');

            const result = await makeAPIRequest('/api/mall/products/stats/home');

            if (result.success && result.data.code === 200) {
                const message = `✅ 商品统计API测试成功！
状态码: ${result.status}
响应代码: ${result.data.code}
响应消息: ${result.data.message}
统计数据: ${JSON.stringify(result.data.data, null, 2)}`;
                showResult('products-result', message, 'success');
            } else {
                const message = `❌ 商品统计API测试失败！
状态码: ${result.status || 'N/A'}
响应代码: ${result.data?.code || 'N/A'}
错误信息: ${result.data?.message || result.error || '未知错误'}`;
                showResult('products-result', message, 'error');
            }
        }
        
        async function testProductDetailAPI() {
            showLoading('products-result');
            
            const result = await makeAPIRequest('/api/mall/products/1');
            
            if (result.success) {
                const message = `✅ 商品详情API测试成功！
状态码: ${result.status}
商品信息: ${JSON.stringify(result.data, null, 2)}`;
                showResult('products-result', message, 'success');
            } else {
                const message = `❌ 商品详情API测试失败！
错误信息: ${result.error || '未知错误'}`;
                showResult('products-result', message, 'error');
            }
        }
        
        async function testCategoriesAPI() {
            showLoading('categories-result');

            const result = await makeAPIRequest('/api/mall/categories');

            if (result.success && result.data.code === 200) {
                const data = result.data.data;
                const message = `✅ 分类列表API测试成功！
状态码: ${result.status}
响应代码: ${result.data.code}
响应消息: ${result.data.message}
分类数量: ${Array.isArray(data) ? data.length : 0}
响应格式: ${JSON.stringify(result.data, null, 2)}`;
                showResult('categories-result', message, 'success');
            } else {
                const message = `❌ 分类列表API测试失败！
状态码: ${result.status || 'N/A'}
响应代码: ${result.data?.code || 'N/A'}
错误信息: ${result.data?.message || result.error || '未知错误'}`;
                showResult('categories-result', message, 'error');
            }
        }
        
        async function testCategoriesWithCountAPI() {
            showLoading('categories-result');
            
            const result = await makeAPIRequest('/api/mall/categories/with-count');
            
            if (result.success) {
                const message = `✅ 带数量分类API测试成功！
状态码: ${result.status}
响应格式: ${JSON.stringify(result.data, null, 2)}`;
                showResult('categories-result', message, 'success');
            } else {
                const message = `❌ 带数量分类API测试失败！
错误信息: ${result.error || '未知错误'}`;
                showResult('categories-result', message, 'error');
            }
        }
        
        async function testCategoryTreeAPI() {
            showLoading('categories-result');
            
            const result = await makeAPIRequest('/api/mall/categories/tree');
            
            if (result.success) {
                const message = `✅ 分类树API测试成功！
状态码: ${result.status}
响应格式: ${JSON.stringify(result.data, null, 2)}`;
                showResult('categories-result', message, 'success');
            } else {
                const message = `❌ 分类树API测试失败！
错误信息: ${result.error || '未知错误'}`;
                showResult('categories-result', message, 'error');
            }
        }
        
        async function testSearchAPI() {
            showLoading('search-result');
            
            const result = await makeAPIRequest('/api/mall/products?keyword=苹果&page=1&size=5');
            
            if (result.success) {
                const message = `✅ 搜索API测试成功！
状态码: ${result.status}
搜索结果: ${JSON.stringify(result.data, null, 2)}`;
                showResult('search-result', message, 'success');
            } else {
                const message = `❌ 搜索API测试失败！
错误信息: ${result.error || '未知错误'}`;
                showResult('search-result', message, 'error');
            }
        }
        
        async function testAdvancedSearchAPI() {
            showLoading('search-result');
            
            const result = await makeAPIRequest('/api/mall/products?keyword=苹果&categoryId=3&minPrice=10&maxPrice=100&page=1&size=5');
            
            if (result.success) {
                const message = `✅ 高级搜索API测试成功！
状态码: ${result.status}
搜索结果: ${JSON.stringify(result.data, null, 2)}`;
                showResult('search-result', message, 'success');
            } else {
                const message = `❌ 高级搜索API测试失败！
错误信息: ${result.error || '未知错误'}`;
                showResult('search-result', message, 'error');
            }
        }
        
        async function checkSystemStatus() {
            showLoading('system-result');
            
            const tests = [
                { name: '商品列表API', url: '/api/mall/products?page=1&size=1' },
                { name: '分类列表API', url: '/api/mall/categories' },
                { name: '商品统计API', url: '/api/mall/products/stats/home' }
            ];
            
            let results = [];
            
            for (const test of tests) {
                const result = await makeAPIRequest(test.url);
                results.push({
                    name: test.name,
                    success: result.success,
                    status: result.status || 'N/A',
                    error: result.error
                });
            }
            
            const successCount = results.filter(r => r.success).length;
            const totalCount = results.length;
            
            let message = `📊 系统状态检查完成！
成功: ${successCount}/${totalCount} 个API

详细结果:
${results.map(r => 
    `${r.success ? '✅' : '❌'} ${r.name}: ${r.success ? `状态码 ${r.status}` : `错误 - ${r.error}`}`
).join('\n')}`;
            
            showResult('system-result', message, successCount === totalCount ? 'success' : 'error');
        }
        
        async function testAllAPIs() {
            showLoading('system-result');
            
            const message = `🚀 开始运行所有API测试...
这可能需要几秒钟时间，请稍候...`;
            showResult('system-result', message, 'loading');
            
            // 依次运行所有测试
            await testProductsAPI();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testCategoriesAPI();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testSearchAPI();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await checkSystemStatus();
        }
        
        // 页面加载完成后自动检查系统状态
        window.addEventListener('load', () => {
            setTimeout(checkSystemStatus, 1000);
        });
    </script>
</body>
</html>
