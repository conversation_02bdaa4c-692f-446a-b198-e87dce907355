# RESTful API设计规范

> **设计原则**: RESTful风格、统一响应格式、完善的错误处理  
> **技术栈**: Node.js + Express + MongoDB  
> **认证方式**: JWT Token  

## 📋 API设计概述

### 🎯 设计目标
- **统一性**: 所有接口遵循统一的设计规范
- **可扩展性**: 支持版本控制和功能扩展
- **安全性**: 完善的认证授权机制
- **易用性**: 清晰的接口文档和错误提示
- **高性能**: 优化的查询和缓存策略

### 📊 API架构设计
```
┌─────────────────────────────────────────────────────────────────┐
│                    API服务架构                                  │
├─────────────────────────────────────────────────────────────────┤
│  客户端层                                                       │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐               │
│  │ Web前端     │ │ 移动端APP   │ │ 微信小程序  │               │
│  │ Vue.js      │ │ uni-app     │ │ uni-app     │               │
│  └─────────────┘ └─────────────┘ └─────────────┘               │
│                              ↕                                  │
├─────────────────────────────────────────────────────────────────┤
│  API网关层                                                      │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │ Nginx反向代理 + 负载均衡                                   │ │
│  │ • 路由分发  • SSL终端  • 限流控制  • 日志记录             │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                              ↕                                  │
├─────────────────────────────────────────────────────────────────┤
│  业务API层                                                      │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐               │
│  │ 用户API     │ │ 设备API     │ │ 数据API     │               │
│  │ /api/users  │ │ /api/devices│ │ /api/data   │               │
│  └─────────────┘ └─────────────┘ └─────────────┘               │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐               │
│  │ 告警API     │ │ 分析API     │ │ 文件API     │               │
│  │ /api/alerts │ │ /api/analysis│ │ /api/files  │               │
│  └─────────────┘ └─────────────┘ └─────────────┘               │
│                              ↕                                  │
├─────────────────────────────────────────────────────────────────┤
│  数据访问层                                                     │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐               │
│  │ MongoDB     │ │ InfluxDB    │ │ Redis       │               │
│  │ 业务数据    │ │ 时序数据    │ │ 缓存数据    │               │
│  └─────────────┘ └─────────────┘ └─────────────┘               │
└─────────────────────────────────────────────────────────────────┘
```

## 🔧 API基础规范

### 📝 URL设计规范
```yaml
基础URL结构:
  https://api.sfap.com/v1/{resource}

资源命名规则:
  - 使用复数名词: /users, /devices, /alerts
  - 使用小写字母和连字符: /sensor-data
  - 避免动词: 用HTTP方法表示操作

版本控制:
  - URL版本: /v1/, /v2/
  - Header版本: API-Version: v1

示例URL:
  GET /v1/users              # 获取用户列表
  GET /v1/users/123          # 获取特定用户
  POST /v1/users             # 创建用户
  PUT /v1/users/123          # 更新用户
  DELETE /v1/users/123       # 删除用户
```

### 🔐 认证授权
```yaml
认证方式: JWT Token
Token位置: Authorization: Bearer <token>

认证流程:
  1. 用户登录获取Token
  2. 客户端在请求头中携带Token
  3. 服务端验证Token有效性
  4. 返回相应的数据或错误

Token结构:
  Header: 算法和类型
  Payload: 用户信息和权限
  Signature: 签名验证

权限级别:
  - admin: 系统管理员
  - user: 普通用户
  - device: 设备端
  - readonly: 只读权限
```

### 📊 统一响应格式
```json
// 成功响应格式
{
  "code": 200,
  "message": "success",
  "data": {
    // 具体数据内容
  },
  "timestamp": "2025-01-31T10:00:00Z",
  "requestId": "req_123456789"
}

// 错误响应格式
{
  "code": 400,
  "message": "参数错误",
  "error": {
    "type": "ValidationError",
    "details": [
      {
        "field": "email",
        "message": "邮箱格式不正确"
      }
    ]
  },
  "timestamp": "2025-01-31T10:00:00Z",
  "requestId": "req_123456789"
}

// 分页响应格式
{
  "code": 200,
  "message": "success",
  "data": {
    "items": [...],
    "pagination": {
      "page": 1,
      "pageSize": 20,
      "total": 100,
      "totalPages": 5
    }
  },
  "timestamp": "2025-01-31T10:00:00Z",
  "requestId": "req_123456789"
}
```

## 👥 用户管理API

### 🔑 用户认证接口
```yaml
POST /v1/auth/login
描述: 用户登录
请求体:
  {
    "email": "<EMAIL>",
    "password": "password123"
  }
响应:
  {
    "code": 200,
    "message": "登录成功",
    "data": {
      "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "user": {
        "id": "user_123",
        "email": "<EMAIL>",
        "name": "张三",
        "role": "user",
        "avatar": "https://example.com/avatar.jpg"
      },
      "expiresIn": 86400
    }
  }

POST /v1/auth/register
描述: 用户注册
请求体:
  {
    "email": "<EMAIL>",
    "password": "password123",
    "name": "张三",
    "phone": "13800138000"
  }

POST /v1/auth/logout
描述: 用户登出
Headers: Authorization: Bearer <token>

POST /v1/auth/refresh
描述: 刷新Token
Headers: Authorization: Bearer <token>
```

### 👤 用户信息接口
```yaml
GET /v1/users/profile
描述: 获取当前用户信息
Headers: Authorization: Bearer <token>
响应:
  {
    "code": 200,
    "data": {
      "id": "user_123",
      "email": "<EMAIL>",
      "name": "张三",
      "phone": "13800138000",
      "avatar": "https://example.com/avatar.jpg",
      "role": "user",
      "createdAt": "2025-01-01T00:00:00Z",
      "lastLoginAt": "2025-01-31T10:00:00Z"
    }
  }

PUT /v1/users/profile
描述: 更新用户信息
请求体:
  {
    "name": "李四",
    "phone": "13900139000",
    "avatar": "https://example.com/new-avatar.jpg"
  }

PUT /v1/users/password
描述: 修改密码
请求体:
  {
    "oldPassword": "oldpassword123",
    "newPassword": "newpassword123"
  }
```

## 🔌 设备管理API

### 📱 设备基础接口
```yaml
GET /v1/devices
描述: 获取设备列表
参数:
  - page: 页码 (默认1)
  - pageSize: 每页数量 (默认20)
  - status: 设备状态 (online/offline)
  - type: 设备类型
响应:
  {
    "code": 200,
    "data": {
      "items": [
        {
          "id": "device_001",
          "name": "环境监测站1号",
          "type": "environment_monitor",
          "status": "online",
          "location": {
            "latitude": 39.9042,
            "longitude": 116.4074,
            "address": "北京市朝阳区"
          },
          "lastHeartbeat": "2025-01-31T10:00:00Z",
          "sensors": [
            {
              "type": "temperature",
              "name": "温度传感器",
              "unit": "°C",
              "status": "normal"
            }
          ]
        }
      ],
      "pagination": {
        "page": 1,
        "pageSize": 20,
        "total": 5,
        "totalPages": 1
      }
    }
  }

POST /v1/devices
描述: 添加新设备
请求体:
  {
    "name": "环境监测站2号",
    "type": "environment_monitor",
    "location": {
      "latitude": 39.9042,
      "longitude": 116.4074,
      "address": "北京市朝阳区"
    },
    "sensors": [
      {
        "type": "temperature",
        "name": "温度传感器",
        "unit": "°C"
      }
    ]
  }

GET /v1/devices/{deviceId}
描述: 获取设备详情

PUT /v1/devices/{deviceId}
描述: 更新设备信息

DELETE /v1/devices/{deviceId}
描述: 删除设备
```

### ⚙️ 设备控制接口
```yaml
POST /v1/devices/{deviceId}/control
描述: 设备控制命令
请求体:
  {
    "command": "turn_on",
    "target": "irrigation_pump",
    "parameters": {
      "duration": 300,
      "intensity": "medium"
    }
  }
响应:
  {
    "code": 200,
    "data": {
      "commandId": "cmd_123456",
      "status": "sent",
      "timestamp": "2025-01-31T10:00:00Z"
    }
  }

GET /v1/devices/{deviceId}/status
描述: 获取设备实时状态
响应:
  {
    "code": 200,
    "data": {
      "deviceId": "device_001",
      "status": "online",
      "sensors": [
        {
          "type": "temperature",
          "value": 25.6,
          "unit": "°C",
          "timestamp": "2025-01-31T10:00:00Z",
          "quality": "good"
        }
      ],
      "actuators": [
        {
          "type": "irrigation_pump",
          "status": "off",
          "lastOperation": "2025-01-31T09:30:00Z"
        }
      ]
    }
  }
```

## 📊 数据管理API

### 📈 传感器数据接口
```yaml
GET /v1/sensor-data
描述: 获取传感器数据
参数:
  - deviceId: 设备ID
  - sensorType: 传感器类型
  - startTime: 开始时间
  - endTime: 结束时间
  - interval: 数据间隔 (1m/5m/1h/1d)
  - limit: 数据条数限制
响应:
  {
    "code": 200,
    "data": {
      "deviceId": "device_001",
      "sensorType": "temperature",
      "interval": "5m",
      "data": [
        {
          "timestamp": "2025-01-31T10:00:00Z",
          "value": 25.6,
          "unit": "°C",
          "quality": "good"
        }
      ],
      "statistics": {
        "count": 288,
        "min": 18.2,
        "max": 32.1,
        "avg": 25.8,
        "latest": 25.6
      }
    }
  }

POST /v1/sensor-data
描述: 上传传感器数据 (设备端使用)
Headers: 
  Authorization: Bearer <device_token>
  Content-Type: application/json
请求体:
  {
    "deviceId": "device_001",
    "timestamp": "2025-01-31T10:00:00Z",
    "data": [
      {
        "sensorType": "temperature",
        "value": 25.6,
        "unit": "°C",
        "quality": "good"
      },
      {
        "sensorType": "humidity",
        "value": 65.2,
        "unit": "%",
        "quality": "good"
      }
    ]
  }

GET /v1/sensor-data/latest
描述: 获取最新传感器数据
参数:
  - deviceId: 设备ID (可选)
  - sensorType: 传感器类型 (可选)
```

### 📋 数据分析接口
```yaml
GET /v1/analysis/trends
描述: 获取数据趋势分析
参数:
  - deviceId: 设备ID
  - sensorType: 传感器类型
  - period: 分析周期 (day/week/month)
响应:
  {
    "code": 200,
    "data": {
      "deviceId": "device_001",
      "sensorType": "temperature",
      "period": "week",
      "trend": {
        "direction": "increasing",
        "slope": 0.2,
        "confidence": 0.85
      },
      "patterns": [
        {
          "type": "daily_cycle",
          "description": "每日温度周期性变化",
          "strength": 0.9
        }
      ],
      "anomalies": [
        {
          "timestamp": "2025-01-30T14:00:00Z",
          "value": 35.2,
          "severity": "medium",
          "description": "温度异常偏高"
        }
      ]
    }
  }

GET /v1/analysis/predictions
描述: 获取预测数据
参数:
  - deviceId: 设备ID
  - sensorType: 传感器类型
  - horizon: 预测时长 (hours)
响应:
  {
    "code": 200,
    "data": {
      "deviceId": "device_001",
      "sensorType": "temperature",
      "horizon": 24,
      "predictions": [
        {
          "timestamp": "2025-01-31T11:00:00Z",
          "predicted_value": 26.2,
          "confidence_interval": {
            "lower": 24.8,
            "upper": 27.6
          },
          "confidence": 0.8
        }
      ],
      "model_info": {
        "algorithm": "LSTM",
        "accuracy": 0.92,
        "last_trained": "2025-01-31T00:00:00Z"
      }
    }
  }
```

## 🚨 告警管理API

### ⚠️ 告警配置接口
```yaml
GET /v1/alerts/rules
描述: 获取告警规则列表
响应:
  {
    "code": 200,
    "data": [
      {
        "id": "rule_001",
        "name": "温度过高告警",
        "deviceId": "device_001",
        "sensorType": "temperature",
        "condition": {
          "operator": "greater_than",
          "threshold": 35,
          "duration": 300
        },
        "severity": "high",
        "enabled": true,
        "notifications": [
          {
            "type": "email",
            "target": "<EMAIL>"
          }
        ]
      }
    ]
  }

POST /v1/alerts/rules
描述: 创建告警规则
请求体:
  {
    "name": "湿度过低告警",
    "deviceId": "device_001",
    "sensorType": "humidity",
    "condition": {
      "operator": "less_than",
      "threshold": 30,
      "duration": 600
    },
    "severity": "medium",
    "notifications": [
      {
        "type": "sms",
        "target": "13800138000"
      }
    ]
  }

PUT /v1/alerts/rules/{ruleId}
描述: 更新告警规则

DELETE /v1/alerts/rules/{ruleId}
描述: 删除告警规则
```

### 📢 告警记录接口
```yaml
GET /v1/alerts
描述: 获取告警记录
参数:
  - status: 告警状态 (active/resolved/acknowledged)
  - severity: 严重程度 (low/medium/high/critical)
  - startTime: 开始时间
  - endTime: 结束时间
  - page: 页码
  - pageSize: 每页数量
响应:
  {
    "code": 200,
    "data": {
      "items": [
        {
          "id": "alert_001",
          "ruleId": "rule_001",
          "ruleName": "温度过高告警",
          "deviceId": "device_001",
          "deviceName": "环境监测站1号",
          "severity": "high",
          "status": "active",
          "message": "温度达到36.5°C，超过阈值35°C",
          "triggeredAt": "2025-01-31T10:00:00Z",
          "acknowledgedAt": null,
          "resolvedAt": null,
          "data": {
            "sensorType": "temperature",
            "value": 36.5,
            "threshold": 35
          }
        }
      ],
      "pagination": {
        "page": 1,
        "pageSize": 20,
        "total": 15,
        "totalPages": 1
      }
    }
  }

PUT /v1/alerts/{alertId}/acknowledge
描述: 确认告警
请求体:
  {
    "comment": "已知晓，正在处理"
  }

PUT /v1/alerts/{alertId}/resolve
描述: 解决告警
请求体:
  {
    "comment": "问题已解决，温度恢复正常"
  }
```

## 📁 文件管理API

### 🖼️ 图片上传接口
```yaml
POST /v1/files/upload
描述: 文件上传
Content-Type: multipart/form-data
请求体:
  file: 文件内容
  type: 文件类型 (image/video/document)
  category: 文件分类 (avatar/device_photo/report)
响应:
  {
    "code": 200,
    "data": {
      "fileId": "file_123456",
      "filename": "device_photo_001.jpg",
      "url": "https://cdn.sfap.com/files/device_photo_001.jpg",
      "size": 1024000,
      "mimeType": "image/jpeg",
      "uploadedAt": "2025-01-31T10:00:00Z"
    }
  }

GET /v1/files/{fileId}
描述: 获取文件信息

DELETE /v1/files/{fileId}
描述: 删除文件
```

## 🔧 错误处理和状态码

### 📊 HTTP状态码规范
```yaml
成功响应:
  200 OK: 请求成功
  201 Created: 资源创建成功
  204 No Content: 请求成功但无返回内容

客户端错误:
  400 Bad Request: 请求参数错误
  401 Unauthorized: 未认证
  403 Forbidden: 无权限
  404 Not Found: 资源不存在
  409 Conflict: 资源冲突
  422 Unprocessable Entity: 参数验证失败
  429 Too Many Requests: 请求频率限制

服务端错误:
  500 Internal Server Error: 服务器内部错误
  502 Bad Gateway: 网关错误
  503 Service Unavailable: 服务不可用
```

### ⚠️ 错误码定义
```yaml
业务错误码:
  10001: 用户不存在
  10002: 密码错误
  10003: 邮箱已存在
  10004: Token已过期
  10005: Token无效

  20001: 设备不存在
  20002: 设备离线
  20003: 设备忙碌
  20004: 控制命令失败

  30001: 数据格式错误
  30002: 数据超出范围
  30003: 数据缺失
  30004: 数据过期

  40001: 告警规则不存在
  40002: 告警规则冲突
  40003: 通知发送失败

  50001: 文件格式不支持
  50002: 文件大小超限
  50003: 存储空间不足
```

---

**文档版本**: v1.0  
**创建时间**: 2025-01-31  
**API版本**: v1  
**维护状态**: 持续更新
