-- =====================================================
-- SFAP农品汇平台完整数据库备份文件
-- 备份时间: 2025-01-25
-- 数据库: agriculture_mall
-- MySQL版本: 8.0+
-- 字符集: utf8mb4
-- 排序规则: utf8mb4_unicode_ci
-- 
-- 此文件包含完整的表结构、数据、索引、视图等
-- 基于当前运行中的数据库生成
-- =====================================================

-- 设置SQL模式和字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;
SET SQL_MODE = 'NO_AUTO_VALUE_ON_ZERO';
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+08:00";

-- =====================================================
-- 1. 数据库创建
-- =====================================================

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS `agriculture_mall` 
DEFAULT CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE `agriculture_mall`;

-- =====================================================
-- 2. 用户权限设置
-- =====================================================

-- 创建应用专用用户（生产环境使用）
CREATE USER IF NOT EXISTS 'sfap_user'@'localhost' IDENTIFIED BY 'sfap_secure_password_2025';
CREATE USER IF NOT EXISTS 'sfap_user'@'%' IDENTIFIED BY 'sfap_secure_password_2025';

-- 授予权限
GRANT SELECT, INSERT, UPDATE, DELETE, CREATE, DROP, INDEX, ALTER, CREATE TEMPORARY TABLES, LOCK TABLES ON agriculture_mall.* TO 'sfap_user'@'localhost';
GRANT SELECT, INSERT, UPDATE, DELETE, CREATE, DROP, INDEX, ALTER, CREATE TEMPORARY TABLES, LOCK TABLES ON agriculture_mall.* TO 'sfap_user'@'%';

-- 刷新权限
FLUSH PRIVILEGES;

-- =====================================================
-- 3. 核心表结构和数据
-- =====================================================

-- -----------------------------------------------------
-- 表: user (用户表) - 包含21个用户
-- -----------------------------------------------------

DROP TABLE IF EXISTS `user`;
CREATE TABLE `user` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码',
  `nickname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '头像URL',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '邮箱',
  `role` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'user' COMMENT '角色：admin,seller,user',
  `status` int NOT NULL DEFAULT '1' COMMENT '状态：1-正常，0-禁用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` int NOT NULL DEFAULT '0' COMMENT '删除标记：0-未删除，1-已删除',
  `region` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '地区',
  `bio` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '个人简介',
  `address` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '地址',
  `city` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '城市',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间（兼容字段）',
  `district` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '区县',
  `fans` int DEFAULT '0' COMMENT '粉丝数',
  `focus` int DEFAULT '0' COMMENT '关注数',
  `gender` int DEFAULT '0' COMMENT '性别：0-未知，1-男，2-女',
  `id_card` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '身份证号',
  `integral` int DEFAULT '0' COMMENT '积分',
  `is_real_name_auth` bit(1) DEFAULT b'0' COMMENT '是否实名认证',
  `is_vip` bit(1) DEFAULT b'0' COMMENT '是否VIP',
  `last_login_time` datetime(6) DEFAULT NULL COMMENT '最后登录时间',
  `latitude` double DEFAULT NULL COMMENT '纬度',
  `level` int DEFAULT '1' COMMENT '用户等级',
  `longitude` double DEFAULT NULL COMMENT '经度',
  `openid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '微信OpenID',
  `province` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '省份',
  `real_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '真实姓名',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间（兼容字段）',
  `user_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'normal' COMMENT '用户类型',
  `vip_expire_time` datetime(6) DEFAULT NULL COMMENT 'VIP过期时间',
  `country` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '国家',
  `language` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '语言',
  `session_key` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '会话密钥',
  `total_likes_given` int NOT NULL DEFAULT '0' COMMENT '总点赞数（给出）',
  `total_likes_received` int NOT NULL DEFAULT '0' COMMENT '总点赞数（收到）',
  `total_reviews` int NOT NULL DEFAULT '0' COMMENT '总评论数',
  `total_favorites` int NOT NULL DEFAULT '0' COMMENT '总收藏数',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `openid` (`openid`),
  KEY `idx_role` (`role`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_total_reviews` (`total_reviews`)
) ENGINE=InnoDB AUTO_INCREMENT=39 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 核心用户数据（管理员和主要用户）
INSERT INTO `user` (`id`, `username`, `password`, `nickname`, `role`, `status`, `email`, `phone`, `created_at`, `updated_at`) VALUES
(1, 'system_admin', '$2a$10$N.zmdr9k7uOCQb0VeCdxUOaoxDAZKKTOxGLEqHrKpOuKGGknd6EO2', '系统管理员', 'admin', 1, '<EMAIL>', '13800000000', '2025-07-18 16:18:53', '2025-07-22 12:48:28'),
(7, 'fanohhh', '$2a$10$omqJiw.YaroI5M5Wn.a7gun3gv59MeAhgtcmMKs4jwUSEj7wyjsAu', 'fanohhh', 'seller', 1, '<EMAIL>', '18844937244', '2025-04-02 09:49:20', '2025-07-25 10:03:06'),
(11, 'admin', '$2a$10$lpFvyY.nPuWBgBvaArfH0u5/aXLE.XW7oEEwtvDlM04Dx55K64geq', '超级管理员', 'admin', 1, '<EMAIL>', '', '2025-04-02 10:23:18', '2025-07-24 09:52:56'),
(3, 'buyer1', '$2a$10$/QlyOwW4mg9NCxpMX4R0uecock//skWGM4f7NDWexo7OY21/ad/vG', '李购', 'user', 1, '<EMAIL>', '13700137002', '2025-04-01 21:32:59', '2025-07-18 09:10:40');

-- 继续添加更多表结构...
