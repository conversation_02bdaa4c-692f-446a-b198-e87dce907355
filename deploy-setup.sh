#!/bin/bash

# 智慧农业平台生产环境部署脚本
# 服务器IP: **************

echo "=== 智慧农业平台生产环境部署开始 ==="

# 1. 创建必要的目录
echo "1. 创建必要的目录..."
mkdir -p /www/wwwroot/agriculture/uploads
mkdir -p /www/wwwroot/agriculture/uploads/avatars
mkdir -p /www/wwwroot/agriculture/uploads/products
mkdir -p /www/wwwroot/agriculture/uploads/qrcodes
mkdir -p /www/wwwroot/agriculture/uploads/seller
mkdir -p /www/wwwroot/agriculture/logs

# 2. 设置目录权限
echo "2. 设置目录权限..."
chown -R www:www /www/wwwroot/agriculture/
chmod -R 755 /www/wwwroot/agriculture/uploads/
chmod -R 755 /www/wwwroot/agriculture/logs/

# 3. 检查Java环境
echo "3. 检查Java环境..."
java -version
if [ $? -ne 0 ]; then
    echo "错误: Java环境未正确配置"
    exit 1
fi

# 4. 检查MySQL服务
echo "4. 检查MySQL服务..."
systemctl status mysql
if [ $? -ne 0 ]; then
    echo "警告: MySQL服务可能未运行"
    systemctl start mysql
fi

# 5. 检查Redis服务
echo "5. 检查Redis服务..."
systemctl status redis
if [ $? -ne 0 ]; then
    echo "警告: Redis服务可能未运行"
    systemctl start redis
fi

# 6. 检查端口占用
echo "6. 检查端口占用..."
netstat -tlnp | grep :8081
if [ $? -eq 0 ]; then
    echo "警告: 端口8081已被占用"
fi

# 7. 创建数据库用户（如果需要）
echo "7. 配置数据库权限..."
mysql -u root -pfan13965711955 -e "
GRANT ALL PRIVILEGES ON agriculture_mall.* TO 'root'@'localhost';
GRANT ALL PRIVILEGES ON agriculture_mall.* TO 'root'@'%';
FLUSH PRIVILEGES;
" 2>/dev/null

# 8. 测试数据库连接
echo "8. 测试数据库连接..."
mysql -u root -pfan13965711955 -e "USE agriculture_mall; SELECT 'Database connection successful!' as status;" 2>/dev/null
if [ $? -eq 0 ]; then
    echo "✅ 数据库连接成功"
else
    echo "❌ 数据库连接失败"
fi

# 9. 创建systemd服务文件（可选）
echo "9. 创建systemd服务文件..."
cat > /etc/systemd/system/agriculture-mall.service << EOF
[Unit]
Description=Agriculture Mall Application
After=network.target

[Service]
Type=simple
User=www
WorkingDirectory=/www/wwwroot/test.com/backend/target
ExecStart=/www/server/java/jdk-17.0.8/bin/java -jar -Xmx1024M -Xms256M agriculture-mall-1.0.0.jar --server.port=8081 --spring.profiles.active=prod
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# 10. 重新加载systemd
systemctl daemon-reload

echo "=== 部署准备完成 ==="
echo ""
echo "接下来的步骤："
echo "1. 在宝塔面板中重启Java项目"
echo "2. 检查日志: tail -f /www/wwwroot/agriculture/logs/agriculture-mall.log"
echo "3. 测试API: curl http://**************:8081/"
echo "4. 访问Swagger文档: http://**************:8081/swagger-ui.html"
echo ""
echo "如果使用systemd管理服务:"
echo "- 启动服务: systemctl start agriculture-mall"
echo "- 查看状态: systemctl status agriculture-mall"
echo "- 开机自启: systemctl enable agriculture-mall"
