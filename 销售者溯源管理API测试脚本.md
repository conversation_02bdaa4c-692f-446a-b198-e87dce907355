# SFAP销售者溯源管理API测试脚本

## 📋 测试环境配置

**后端服务**: http://localhost:8081  
**测试用户**: 销售者用户 (ID: 18)  
**认证方式**: X-User-Id Header  

---

## 🧪 API测试用例

### 1. 获取销售者产品列表

```bash
# 测试获取销售者产品列表
curl -X GET "http://localhost:8081/api/traceability/seller/products" \
  -H "Content-Type: application/json" \
  -H "X-User-Id: 18"
```

**期望结果**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "有机菠菜",
      "categoryId": 1,
      "price": 15.00,
      "stock": 100
    }
  ]
}
```

### 2. 获取销售者溯源记录列表

```bash
# 测试获取溯源记录列表（分页）
curl -X GET "http://localhost:8081/api/traceability/seller/records?page=1&size=10" \
  -H "Content-Type: application/json" \
  -H "X-User-Id: 18"

# 测试搜索功能
curl -X GET "http://localhost:8081/api/traceability/seller/records?keyword=菠菜&status=draft" \
  -H "Content-Type: application/json" \
  -H "X-User-Id: 18"
```

**期望结果**:
```json
{
  "success": true,
  "data": {
    "records": [],
    "total": 0,
    "size": 10,
    "current": 1
  }
}
```

### 3. 创建溯源记录

```bash
# 测试创建溯源记录
curl -X POST "http://localhost:8081/api/traceability/seller/records" \
  -H "Content-Type: application/json" \
  -H "X-User-Id: 18" \
  -d '{
    "productId": 1,
    "productName": "有机菠菜",
    "productionDate": "2025-07-10",
    "expiryDate": "2025-07-20",
    "batchNumber": "BATCH001",
    "productionLocation": "北京市昌平区有机农场",
    "productionEnvironment": "greenhouse",
    "cultivationMethod": "有机种植，无农药化肥",
    "pesticidesUsed": "无",
    "qualityGrade": "premium",
    "testingOrganization": "北京农产品检测中心",
    "qualityReport": "各项指标均符合有机食品标准",
    "status": "draft",
    "remarks": "首批有机菠菜，品质优良"
  }'
```

**期望结果**:
```json
{
  "success": true,
  "message": "创建成功",
  "data": {
    "id": 1,
    "traceCode": "SFAP25071510001001A1B2",
    "productId": 1,
    "status": "draft"
  }
}
```

### 4. 更新溯源记录

```bash
# 测试更新溯源记录
curl -X PUT "http://localhost:8081/api/traceability/seller/records/1" \
  -H "Content-Type: application/json" \
  -H "X-User-Id: 18" \
  -d '{
    "productId": 1,
    "productName": "有机菠菜",
    "productionDate": "2025-07-10",
    "expiryDate": "2025-07-20",
    "batchNumber": "BATCH001",
    "productionLocation": "北京市昌平区有机农场",
    "productionEnvironment": "greenhouse",
    "cultivationMethod": "有机种植，无农药化肥，更新版本",
    "pesticidesUsed": "无",
    "qualityGrade": "premium",
    "testingOrganization": "北京农产品检测中心",
    "qualityReport": "各项指标均符合有机食品标准",
    "status": "pending",
    "remarks": "首批有机菠菜，品质优良，已提交审核"
  }'
```

### 5. 删除溯源记录

```bash
# 测试删除溯源记录
curl -X DELETE "http://localhost:8081/api/traceability/seller/records/1" \
  -H "Content-Type: application/json" \
  -H "X-User-Id: 18"
```

### 6. 批量删除溯源记录

```bash
# 测试批量删除
curl -X POST "http://localhost:8081/api/traceability/seller/records/batch-delete" \
  -H "Content-Type: application/json" \
  -H "X-User-Id: 18" \
  -d '[1, 2, 3]'
```

### 7. 批量更新状态

```bash
# 测试批量更新状态
curl -X POST "http://localhost:8081/api/traceability/seller/records/batch-status" \
  -H "Content-Type: application/json" \
  -H "X-User-Id: 18" \
  -d '{
    "ids": [1, 2, 3],
    "status": "pending"
  }'
```

---

## 🔒 权限测试用例

### 1. 未登录用户测试

```bash
# 测试未登录用户访问
curl -X GET "http://localhost:8081/api/traceability/seller/records" \
  -H "Content-Type: application/json"
```

**期望结果**: 401 Unauthorized

### 2. 跨用户权限测试

```bash
# 用户A创建记录
curl -X POST "http://localhost:8081/api/traceability/seller/records" \
  -H "Content-Type: application/json" \
  -H "X-User-Id: 18" \
  -d '{"productId": 1, "productName": "测试产品", "status": "draft"}'

# 用户B尝试访问用户A的记录
curl -X PUT "http://localhost:8081/api/traceability/seller/records/1" \
  -H "Content-Type: application/json" \
  -H "X-User-Id: 19" \
  -d '{"status": "published"}'
```

**期望结果**: 403 Forbidden

---

## 📊 性能测试用例

### 1. 分页性能测试

```bash
# 测试大页面数据加载
curl -X GET "http://localhost:8081/api/traceability/seller/records?page=1&size=100" \
  -H "Content-Type: application/json" \
  -H "X-User-Id: 18"
```

### 2. 搜索性能测试

```bash
# 测试复杂搜索条件
curl -X GET "http://localhost:8081/api/traceability/seller/records?keyword=菠菜&status=published&productId=1" \
  -H "Content-Type: application/json" \
  -H "X-User-Id: 18"
```

### 3. 批量操作性能测试

```bash
# 测试批量操作大量数据
curl -X POST "http://localhost:8081/api/traceability/seller/records/batch-status" \
  -H "Content-Type: application/json" \
  -H "X-User-Id: 18" \
  -d '{
    "ids": [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20],
    "status": "pending"
  }'
```

---

## 🧪 数据验证测试

### 1. 必填字段验证

```bash
# 测试缺少必填字段
curl -X POST "http://localhost:8081/api/traceability/seller/records" \
  -H "Content-Type: application/json" \
  -H "X-User-Id: 18" \
  -d '{
    "productName": "测试产品"
  }'
```

**期望结果**: 400 Bad Request with validation errors

### 2. 数据格式验证

```bash
# 测试错误的日期格式
curl -X POST "http://localhost:8081/api/traceability/seller/records" \
  -H "Content-Type: application/json" \
  -H "X-User-Id: 18" \
  -d '{
    "productId": 1,
    "productName": "测试产品",
    "productionDate": "invalid-date",
    "status": "draft"
  }'
```

### 3. 业务逻辑验证

```bash
# 测试重复溯源码
curl -X POST "http://localhost:8081/api/traceability/seller/records" \
  -H "Content-Type: application/json" \
  -H "X-User-Id: 18" \
  -d '{
    "productId": 1,
    "productName": "测试产品",
    "traceCode": "SFAP25071410001001A1B2",
    "status": "draft"
  }'
```

---

## 📋 测试检查清单

### ✅ 功能测试
- [ ] 获取产品列表
- [ ] 获取溯源记录列表
- [ ] 创建溯源记录
- [ ] 更新溯源记录
- [ ] 删除溯源记录
- [ ] 批量删除记录
- [ ] 批量更新状态

### ✅ 权限测试
- [ ] 未登录用户拦截
- [ ] 跨用户权限验证
- [ ] 角色权限验证

### ✅ 数据验证测试
- [ ] 必填字段验证
- [ ] 数据格式验证
- [ ] 业务逻辑验证
- [ ] 溯源码唯一性验证

### ✅ 性能测试
- [ ] 分页查询性能
- [ ] 搜索功能性能
- [ ] 批量操作性能

### ✅ 错误处理测试
- [ ] 网络错误处理
- [ ] 服务器错误处理
- [ ] 数据库错误处理
- [ ] 业务异常处理

---

## 🔧 测试工具推荐

### 1. API测试工具
- **Postman**: 图形化API测试
- **curl**: 命令行测试
- **Insomnia**: 轻量级API客户端

### 2. 自动化测试
- **Jest**: JavaScript单元测试
- **Cypress**: 端到端测试
- **JUnit**: Java单元测试

### 3. 性能测试
- **Apache Bench (ab)**: 简单性能测试
- **JMeter**: 复杂性能测试
- **Artillery**: 现代负载测试

---

## 📈 测试报告模板

### 测试结果记录
```
测试用例: [用例名称]
测试时间: [执行时间]
测试结果: [通过/失败]
响应时间: [毫秒]
错误信息: [如有]
备注: [其他说明]
```

### 问题跟踪
```
问题ID: [编号]
问题描述: [详细描述]
重现步骤: [步骤说明]
期望结果: [预期行为]
实际结果: [实际行为]
优先级: [高/中/低]
状态: [待修复/已修复/已验证]
```

---

**测试脚本版本**: v1.0  
**最后更新**: 2025-07-15  
**维护人员**: 开发团队
