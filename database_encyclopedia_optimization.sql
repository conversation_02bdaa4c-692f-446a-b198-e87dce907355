-- =====================================================
-- 农业百科系统数据库优化脚本
-- 功能：清理冗余表、优化索引、修复分页功能
-- 创建时间：2025-07-26
-- =====================================================

-- 1. 备份重要数据（可选，建议在生产环境执行前先备份）
-- CREATE TABLE encyclopedia_backup_20250726 AS SELECT * FROM encyclopedia;
-- CREATE TABLE encyclopedia_category_backup_20250726 AS SELECT * FROM encyclopedia_category;

-- 2. 清理冗余的备份表（谨慎操作）
-- 注意：这些表可能包含重要的历史数据，请在确认不需要后再删除
-- DROP TABLE IF EXISTS encyclopedia_backup;
-- DROP TABLE IF EXISTS encyclopedia_category_backup;
-- DROP TABLE IF EXISTS favorite_backup;

-- 3. 优化索引 - 确保分页查询性能
-- 为encyclopedia表添加复合索引，优化分页查询
CREATE INDEX IF NOT EXISTS idx_encyclopedia_pagination 
ON encyclopedia (deleted, status, is_featured DESC, is_hot DESC, publish_date DESC);

-- 为分类查询添加索引
CREATE INDEX IF NOT EXISTS idx_encyclopedia_category_query 
ON encyclopedia (category_id, deleted, status);

-- 为关键词搜索添加全文索引（MySQL 5.7+）
-- ALTER TABLE encyclopedia ADD FULLTEXT(title, keywords);

-- 为encyclopedia_category表优化索引
CREATE INDEX IF NOT EXISTS idx_category_hierarchy 
ON encyclopedia_category (parent_id, level, sort_order);

CREATE INDEX IF NOT EXISTS idx_category_status 
ON encyclopedia_category (deleted, status);

-- 4. 数据一致性检查和修复
-- 检查并修复分类计数
UPDATE encyclopedia_category ec 
SET count = (
    SELECT COUNT(*) 
    FROM encyclopedia e 
    WHERE e.category_id = ec.id 
    AND e.deleted = 0 
    AND e.status = 1
) 
WHERE ec.deleted = 0;

-- 5. 性能优化建议
-- 为经常查询的字段添加索引
CREATE INDEX IF NOT EXISTS idx_encyclopedia_author ON encyclopedia (author_id);
CREATE INDEX IF NOT EXISTS idx_encyclopedia_views ON encyclopedia (views_count DESC);
CREATE INDEX IF NOT EXISTS idx_encyclopedia_likes ON encyclopedia (likes_count DESC);

-- 为评论表添加索引
CREATE INDEX IF NOT EXISTS idx_comment_encyclopedia ON encyclopedia_comment (encyclopedia_id, deleted, status);
CREATE INDEX IF NOT EXISTS idx_comment_user ON encyclopedia_comment (user_id);
CREATE INDEX IF NOT EXISTS idx_comment_created ON encyclopedia_comment (created_at DESC);

-- 为收藏表添加索引
CREATE INDEX IF NOT EXISTS idx_favorite_user_encyclopedia ON encyclopedia_favorite (user_id, encyclopedia_id);
CREATE INDEX IF NOT EXISTS idx_favorite_created ON encyclopedia_favorite (created_at DESC);

-- 6. 数据库表统计信息
SELECT 
    'encyclopedia' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN deleted = 0 AND status = 1 THEN 1 END) as active_records,
    COUNT(CASE WHEN is_featured = 1 THEN 1 END) as featured_records,
    COUNT(CASE WHEN is_hot = 1 THEN 1 END) as hot_records
FROM encyclopedia

UNION ALL

SELECT 
    'encyclopedia_category' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN deleted = 0 AND status = 1 THEN 1 END) as active_records,
    COUNT(CASE WHEN level = 1 THEN 1 END) as level1_categories,
    COUNT(CASE WHEN level = 2 THEN 1 END) as level2_categories
FROM encyclopedia_category

UNION ALL

SELECT 
    'encyclopedia_comment' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN deleted = 0 AND status = 1 THEN 1 END) as active_records,
    COUNT(CASE WHEN is_approved = 1 THEN 1 END) as approved_records,
    0 as extra_info
FROM encyclopedia_comment

UNION ALL

SELECT 
    'encyclopedia_favorite' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN deleted = 0 THEN 1 END) as active_records,
    0 as extra_info1,
    0 as extra_info2
FROM encyclopedia_favorite;

-- 7. 分页性能测试查询
-- 测试第一页数据获取性能
EXPLAIN SELECT * FROM encyclopedia 
WHERE deleted = 0 AND status = 1 
ORDER BY is_featured DESC, is_hot DESC, publish_date DESC 
LIMIT 0, 10;

-- 测试分类筛选分页性能
EXPLAIN SELECT * FROM encyclopedia 
WHERE deleted = 0 AND status = 1 AND category_id = 1
ORDER BY is_featured DESC, is_hot DESC, publish_date DESC 
LIMIT 0, 10;

-- 测试关键词搜索分页性能
EXPLAIN SELECT * FROM encyclopedia 
WHERE deleted = 0 AND status = 1 
AND (title LIKE '%水稻%' OR keywords LIKE '%水稻%')
ORDER BY is_featured DESC, is_hot DESC, publish_date DESC 
LIMIT 0, 10;

-- 8. 数据完整性检查
-- 检查孤立的百科文章（分类不存在）
SELECT e.id, e.title, e.category_id, e.category_name
FROM encyclopedia e
LEFT JOIN encyclopedia_category ec ON e.category_id = ec.id
WHERE e.deleted = 0 AND e.status = 1 
AND (ec.id IS NULL OR ec.deleted = 1 OR ec.status = 0);

-- 检查分类计数是否准确
SELECT 
    ec.id,
    ec.name,
    ec.count as recorded_count,
    COUNT(e.id) as actual_count,
    (ec.count - COUNT(e.id)) as difference
FROM encyclopedia_category ec
LEFT JOIN encyclopedia e ON ec.id = e.category_id AND e.deleted = 0 AND e.status = 1
WHERE ec.deleted = 0
GROUP BY ec.id, ec.name, ec.count
HAVING difference != 0;

-- 9. 清理无效数据和修复分类问题
-- 标记没有文章的空分类（可选操作，谨慎执行）
-- UPDATE encyclopedia_category SET status = 0
-- WHERE id IN (
--   SELECT category_id FROM (
--     SELECT ec.id as category_id
--     FROM encyclopedia_category ec
--     LEFT JOIN encyclopedia e ON ec.id = e.category_id AND e.deleted = 0 AND e.status = 1
--     WHERE ec.deleted = 0 AND ec.level = 1
--     GROUP BY ec.id
--     HAVING COUNT(e.id) = 0
--   ) empty_categories
-- );

-- 删除没有内容的百科文章
-- DELETE FROM encyclopedia WHERE (content IS NULL OR content = '') AND deleted = 0;

-- 删除没有标题的百科文章
-- DELETE FROM encyclopedia WHERE (title IS NULL OR title = '') AND deleted = 0;

-- 修复图片路径问题 - 确保所有图片路径都是完整的
UPDATE encyclopedia
SET cover_image = CONCAT('http://localhost:8081', cover_image)
WHERE cover_image IS NOT NULL
  AND cover_image != ''
  AND cover_image NOT LIKE 'http%'
  AND cover_image LIKE '/%'
  AND deleted = 0;

-- 10. 优化建议输出
SELECT 
    '数据库优化完成' as status,
    NOW() as completion_time,
    '建议定期执行ANALYZE TABLE和OPTIMIZE TABLE命令' as recommendation;
