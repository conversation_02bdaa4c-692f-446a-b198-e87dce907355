-- =====================================================
-- SFAP农品汇平台数据库完整备份文件
-- 备份时间: 2025-01-25
-- 数据库: agriculture_mall
-- MySQL版本: 8.0+
-- 字符集: utf8mb4
-- 排序规则: utf8mb4_unicode_ci
-- 
-- 此文件包含完整的表结构、数据、索引、视图、触发器等
-- 可用于完全恢复当前数据库状态
-- =====================================================

-- 设置SQL模式和字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;
SET SQL_MODE = 'NO_AUTO_VALUE_ON_ZERO';
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+08:00";

-- =====================================================
-- 1. 数据库创建
-- =====================================================

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS `agriculture_mall` 
DEFAULT CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE `agriculture_mall`;

-- =====================================================
-- 2. 表结构和数据导出
-- =====================================================

-- -----------------------------------------------------
-- 表: user (用户表)
-- -----------------------------------------------------

DROP TABLE IF EXISTS `user`;
CREATE TABLE `user` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码',
  `nickname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '头像URL',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '邮箱',
  `role` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'user' COMMENT '角色：admin,seller,user',
  `status` int NOT NULL DEFAULT '1' COMMENT '状态：1-正常，0-禁用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` int NOT NULL DEFAULT '0' COMMENT '删除标记：0-未删除，1-已删除',
  `region` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '地区',
  `bio` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '个人简介',
  `address` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '地址',
  `city` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '城市',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间（兼容字段）',
  `district` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '区县',
  `fans` int DEFAULT '0' COMMENT '粉丝数',
  `focus` int DEFAULT '0' COMMENT '关注数',
  `gender` int DEFAULT '0' COMMENT '性别：0-未知，1-男，2-女',
  `id_card` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '身份证号',
  `integral` int DEFAULT '0' COMMENT '积分',
  `is_real_name_auth` bit(1) DEFAULT b'0' COMMENT '是否实名认证',
  `is_vip` bit(1) DEFAULT b'0' COMMENT '是否VIP',
  `last_login_time` datetime(6) DEFAULT NULL COMMENT '最后登录时间',
  `latitude` double DEFAULT NULL COMMENT '纬度',
  `level` int DEFAULT '1' COMMENT '用户等级',
  `longitude` double DEFAULT NULL COMMENT '经度',
  `openid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '微信OpenID',
  `province` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '省份',
  `real_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '真实姓名',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间（兼容字段）',
  `user_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'normal' COMMENT '用户类型',
  `vip_expire_time` datetime(6) DEFAULT NULL COMMENT 'VIP过期时间',
  `country` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '国家',
  `language` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '语言',
  `session_key` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '会话密钥',
  `total_likes_given` int NOT NULL DEFAULT '0' COMMENT '总点赞数（给出）',
  `total_likes_received` int NOT NULL DEFAULT '0' COMMENT '总点赞数（收到）',
  `total_reviews` int NOT NULL DEFAULT '0' COMMENT '总评论数',
  `total_favorites` int NOT NULL DEFAULT '0' COMMENT '总收藏数',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `openid` (`openid`),
  KEY `idx_role` (`role`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_total_reviews` (`total_reviews`)
) ENGINE=InnoDB AUTO_INCREMENT=39 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 用户表数据
INSERT INTO `user` VALUES
(1,'system_admin','$2a$10$N.zmdr9k7uOCQb0VeCdxUOaoxDAZKKTOxGLEqHrKpOuKGGknd6EO2','系统管理员','/images/avatars/system_admin.png','13800000000','<EMAIL>','admin',1,'2025-07-18 16:18:53','2025-07-22 12:48:28',0,'全国','系统默认管理员账户，负责管理平台商品和系统配置',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'admin',NULL,NULL,NULL,NULL,0,1,0,0),
(3,'buyer1','$2a$10$/QlyOwW4mg9NCxpMX4R0uecock//skWGM4f7NDWexo7OY21/ad/vG','李购','https://example.com/avatars/buyer1.jpg','13700137002','<EMAIL>','user',1,'2025-04-01 21:32:59','2025-07-18 09:10:40',0,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2025-07-16 23:20:16.000000',NULL,NULL,NULL,NULL,NULL,NULL,NULL,'user',NULL,NULL,NULL,NULL,1,0,5,1),
(7,'fanohhh','$2a$10$omqJiw.YaroI5M5Wn.a7gun3gv59MeAhgtcmMKs4jwUSEj7wyjsAu','fanohhh','uploads/avatars/c97d3e42-3597-438c-88b6-426eee3046a1.jpg','18844937244','<EMAIL>','seller',1,'2025-04-02 09:49:20','2025-07-25 10:03:06',0,'台湾省-台北市-中正区','11111231231231312131232312123132132312','',NULL,NULL,NULL,NULL,NULL,1,NULL,NULL,NULL,NULL,'2025-07-25 10:03:06.000000',NULL,NULL,NULL,NULL,NULL,NULL,NULL,'seller',NULL,NULL,NULL,NULL,1,0,0,1),
(8,'yuanshen','$2a$10$lifLZkv/CTk5wdXzsEBt/OJt1HoMoe2c6Daq47j7yTKM1CGBFAFh2','yuanshen',NULL,'18888888888','<EMAIL>','user',1,'2025-04-02 10:04:44','2025-07-18 09:10:40',1,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'user',NULL,NULL,NULL,NULL,0,0,0,0),
(10,'admin2','$2a$10$WiViazg8.rXfmHKGhyWJoeK2T16cqvxKGW2YFwHDQdC1gjePl/7qu','超级管理员',NULL,NULL,'<EMAIL>','admin',1,'2025-04-02 10:23:06','2025-07-18 09:10:40',0,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'admin',NULL,NULL,NULL,NULL,0,0,0,0),
(11,'admin','$2a$10$lpFvyY.nPuWBgBvaArfH0u5/aXLE.XW7oEEwtvDlM04Dx55K64geq','超级管理员','uploads/avatars/fe30cc49-29dc-4aca-aab0-4f13e2df389d.jpg','','<EMAIL>','admin',1,'2025-04-02 10:23:18','2025-07-24 09:52:56',0,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,1,NULL,NULL,NULL,NULL,'2025-07-24 09:52:56.000000',NULL,NULL,NULL,NULL,NULL,NULL,NULL,'admin',NULL,NULL,NULL,NULL,1,0,0,7),
(12,'admin3','$2a$10$XxEG80Sbocv3JoLO26WUourfssCOt0Va2uXGVEgHBxLGSbsBHista','系统管理员',NULL,NULL,'<EMAIL>','admin',1,'2025-04-02 10:35:36','2025-07-18 09:10:40',0,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'admin',NULL,NULL,NULL,NULL,0,0,0,0),
(15,'admin1','$2a$10$KkPao0EaxgAIcq6ZlU2DruaKS0dJN/igKA1nUFWpnNYshzY47.obu','admin1',NULL,'13965472819','<EMAIL>','admin',1,'2025-04-06 22:40:28','2025-07-18 09:10:40',0,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'admin',NULL,NULL,NULL,NULL,0,0,0,0),
(16,'fanohhh1','$2a$10$Ipo8uk3uKmTxbkBwJ.Uxkunmif2uQBlMMxThtd8C4m0a5SPyAVahq','fanohhh1',NULL,'13967891099','<EMAIL>','user',1,'2025-04-06 22:48:14','2025-07-18 09:10:40',1,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2025-05-29 23:08:18.732000',NULL,NULL,NULL,NULL,NULL,NULL,NULL,'user',NULL,NULL,NULL,NULL,0,0,0,0),
(18,'admin_new','$2a$10$aspJVYc1VJ0HorQxJpSGx.BfbiA7y0Ir0BMaS5XqlztjUtX7l3MV.','管理员','uploads/avatars/a9a7ec4d-3474-43b1-96f4-3fb97d5cefe3.jpg','13800138000','<EMAIL>','admin',1,'2025-04-06 23:10:55','2025-07-18 09:10:40',0,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2025-07-13 12:40:31.000000',NULL,NULL,NULL,NULL,NULL,NULL,NULL,'admin',NULL,NULL,NULL,NULL,0,0,0,0),
(19,'user','$2a$10$LNWtGCEyW9zRaYCY68lkcet/Q1LLVrtq2yyFuHOS0a0QQDUBg4B.G','user',NULL,'18899029388','<EMAIL>','user',1,'2025-04-07 12:39:18','2025-07-18 09:10:40',0,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'user',NULL,NULL,NULL,NULL,0,0,0,0),
(23,'fanohhhhhhh','$2a$10$TZANgkOWAE90gJ/TbbBXaeMIdDKGoU71kFvx0ZL.CZo.Y9O6lLx/q','fanohhhhhhh','/static/images/default-avatar.png','18899098766','<EMAIL>','user',1,'2025-05-29 12:13:19','2025-07-18 09:10:40',0,NULL,NULL,NULL,NULL,NULL,NULL,0,0,0,NULL,0,b'0',b'0','2025-05-29 12:13:19.171000',NULL,1,NULL,NULL,NULL,NULL,NULL,'user',NULL,NULL,NULL,NULL,0,0,0,0),
(24,'1234567','$2a$10$erewfU94ZeADNnRnjlJuae0px/q2OUVGaii1Ke.PiPGExcQIi9PDG','1234567','/static/images/default-avatar.png',NULL,'<EMAIL>','user',1,'2025-05-29 12:27:33','2025-07-18 09:10:40',0,NULL,NULL,NULL,NULL,NULL,NULL,0,0,0,NULL,0,b'0',b'0','2025-05-29 12:27:32.698000',NULL,1,NULL,NULL,NULL,NULL,NULL,'user',NULL,NULL,NULL,NULL,0,0,0,0),
(26,'fanohhh2','$2a$10$3G153a9S1mtK3sj..9uO5.vhIPyOTU0OoGV5w54Y0zZ6OeQ.UbMNq','fanohhh2','',NULL,'<EMAIL>','user',1,'2025-05-29 22:56:48','2025-07-18 09:10:40',0,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,NULL,b'0','2025-05-29 22:56:47.753000',NULL,1,NULL,NULL,NULL,NULL,NULL,'user',NULL,NULL,NULL,NULL,0,0,0,0),
(27,'fanohhh3','$2a$10$W1Xvi3ByZrJS0u.2ZQMXlezGKaG4G1Vjj798vUOUPhfkujLIj.nae','fanohhh3','',NULL,'<EMAIL>','user',1,'2025-05-29 23:01:51','2025-07-18 09:10:40',0,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,NULL,b'0','2025-05-29 23:01:51.046000',NULL,1,NULL,NULL,NULL,NULL,NULL,'user',NULL,NULL,NULL,NULL,0,0,0,0),
(29,'2023036415','$2a$10$EnzFnc3jDvPJPDl2p4PavegX2XbuwKyysPk6TigXlAuG2IeK0wrhe','2023036415',NULL,'18844936577','<EMAIL>','seller',1,'2025-07-07 21:00:26','2025-07-18 09:10:40',0,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'seller',NULL,NULL,NULL,NULL,0,0,0,0),
(30,'yuanshenqidong','$2a$10$GDm7l0Cq/CPyJouFcOMOYein2YP4ArOORWg9toxOFqDiNl4a5i1Yy',NULL,NULL,'15544987222','<EMAIL>','seller',1,'2025-07-09 22:12:21','2025-07-18 09:10:40',0,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'seller',NULL,NULL,NULL,NULL,0,0,0,0),
(33,'test_user','$2a$10$2nb82F3VXgczFfDcYZIo2OURiLm2eyOJQYEU5eycwcl9wVYrgm3kK','测试1','uploads/avatars/bb773464-7f62-42c6-803d-964585fe9c08.jpg','15544221123','<EMAIL>','seller',1,'2025-07-12 23:02:09','2025-07-21 22:35:21',0,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,NULL,NULL,NULL,NULL,'2025-07-21 22:35:21.000000',NULL,NULL,NULL,NULL,NULL,NULL,NULL,'seller',NULL,NULL,NULL,NULL,0,0,0,0),
(34,'test_user2','$2a$10$WfrA8gMoYEBRCz0BvrwERuT8fOo1uFESFBQ7CmnFLs5L6aHjQ2EwC','测试2',NULL,'15544998752','<EMAIL>','user',1,'2025-07-12 23:31:27','2025-07-21 22:35:30',0,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,NULL,NULL,NULL,NULL,'2025-07-21 22:35:30.000000',NULL,NULL,NULL,NULL,NULL,NULL,NULL,'user',NULL,NULL,NULL,NULL,0,0,0,0),
(37,'brand_new_user','$2a$10$d7X42QwdLzd0vZ81PDdyx.Y7iQ4Vm4jI3oK1Tc8FqXgKx39t5tW1y','brand_new_user',NULL,'13900139000','<EMAIL>','user',1,'2025-07-13 00:13:25','2025-07-18 09:10:40',0,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,NULL,NULL,NULL,NULL,'2025-07-13 00:13:34.000000',NULL,NULL,NULL,NULL,NULL,NULL,NULL,'user',NULL,NULL,NULL,NULL,0,0,0,0),
(38,'test_complete_flow','$2a$10$b.eG1/NvFJK0FPBFVdV9JO1lmhvg4ZNV5m1C2T.GL33qZwuURAM9G','test_complete_flow',NULL,'13700137000','<EMAIL>','user',1,'2025-07-13 00:21:01','2025-07-18 09:10:40',0,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,NULL,NULL,NULL,NULL,'2025-07-13 00:21:19.000000',NULL,NULL,NULL,NULL,NULL,NULL,NULL,'user',NULL,NULL,NULL,NULL,0,0,0,0);

-- -----------------------------------------------------
-- 表: product (商品表)
-- -----------------------------------------------------

DROP TABLE IF EXISTS `product`;
CREATE TABLE `product` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '商品ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '商品名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '商品描述',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '商品图片',
  `price` decimal(10,2) NOT NULL COMMENT '价格',
  `original_price` decimal(10,2) DEFAULT NULL COMMENT '原价',
  `stock` int NOT NULL DEFAULT '0' COMMENT '库存',
  `sales_count` int NOT NULL DEFAULT '0' COMMENT '销量',
  `rating` decimal(3,2) DEFAULT '0.00' COMMENT '评分',
  `review_count` int NOT NULL DEFAULT '0' COMMENT '评论数',
  `category_id` bigint NOT NULL COMMENT '分类ID',
  `seller_id` bigint NOT NULL COMMENT '销售者ID',
  `brand` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '品牌',
  `origin` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '产地',
  `unit` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '单位',
  `shelf_life` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '保质期',
  `storage_method` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '储存方法',
  `tags` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '标签',
  `specifications` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '规格',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-上架，0-下架',
  `is_featured` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否精选',
  `is_hot` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否热销',
  `is_new` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否新品',
  `sort_order` int NOT NULL DEFAULT '0' COMMENT '排序',
  `view_count` int NOT NULL DEFAULT '0' COMMENT '浏览量',
  `favorite_count` int NOT NULL DEFAULT '0' COMMENT '收藏量',
  `like_count` int NOT NULL DEFAULT '0' COMMENT '点赞数',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标记',
  `has_traceability` tinyint(1) DEFAULT '0' COMMENT '是否有溯源',
  `trace_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '溯源码',
  `qr_code_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '二维码URL',
  `source_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'admin_direct' COMMENT '来源类型',
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_seller_id` (`seller_id`),
  KEY `idx_status` (`status`),
  KEY `idx_price` (`price`),
  KEY `idx_sales_count` (`sales_count`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=6035 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品表';

-- 商品表数据（前10条示例数据）
INSERT INTO `product` VALUES
(1001,'有机菠菜','新鲜有机菠菜，叶片翠绿，营养丰富，富含铁质和维生素','/images/products/spinach.jpg','8.50','10.00',500,156,'4.50',2,11,1,'绿源农场','山东寿光','斤','3天','冷藏保存','[\"有机\",\"绿色\",\"富含铁质\",\"维生素丰富\"],系统商品','{\"weight\":\"500g\",\"origin\":\"山东寿光\",\"harvest_date\":\"2024-01-10\"}',1,1,1,0,10,1250,0,1,'2024-01-10 00:00:00','2025-07-21 14:58:57',0,1,'SFAPA24011008001001B61A2','/uploads/qrcodes/qr_SFAPA24011008001001B61A2.png','admin_direct'),
(1002,'精品小白菜','嫩绿小白菜，口感清脆，适合炒制和做汤','/images/products/baby-cabbage.jpg','6.80','8.00',800,234,'0.00',0,11,1,'田园蔬菜','河北张家口','斤','5天','冷藏保存','[\"新鲜\",\"嫩绿\",\"清脆\"],系统商品','{\"weight\":\"500g\",\"origin\":\"河北张家口\",\"harvest_date\":\"2024-01-11\"}',1,0,1,1,8,980,1,0,'2024-01-11 00:30:00','2025-07-21 14:58:57',0,1,'SFAPA240111083010021F48D','/uploads/qrcodes/qr_SFAPA240111083010021F48D.png','admin_direct'),
(2001,'赣南脐橙','正宗赣南脐橙，果肉饱满，酸甜可口','/images/products/navel-orange.jpg','12.80','15.00',800,456,'5.00',2,21,1,'赣南果业','江西赣州','斤','15天','阴凉通风处','[\"正宗赣南\",\"果肉饱满\",\"酸甜可口\"],系统商品','{\"weight\":\"500g\",\"origin\":\"江西赣州\",\"harvest_date\":\"2023-12-15\"}',1,1,1,0,15,2345,0,0,'2023-12-15 00:00:00','2025-07-21 14:58:57',0,1,'SFAPA231215080020016FA81','/uploads/qrcodes/qr_SFAPA231215080020016FA81.png','admin_direct'),
(2007,'新鲜草莓','红颜草莓，果形饱满，香甜可口','/images/products/strawberry.jpg','28.50','32.00',400,289,'0.00',0,22,1,'草莓园','辽宁丹东','斤','3天','冷藏保存','[\"红颜草莓\",\"果形饱满\",\"香甜可口\"],系统商品','{\"weight\":\"500g\",\"origin\":\"辽宁丹东\",\"harvest_date\":\"2024-01-12\"}',1,1,1,1,32,2568,0,0,'2024-01-12 00:00:00','2025-07-22 05:36:42',0,1,'SFAPA24011208002007183BA','/uploads/qrcodes/qr_SFAPA24011208002007183BA.png','admin_direct'),
(2008,'有机蓝莓','野生蓝莓，抗氧化强，营养价值高','/images/products/blueberry.jpg','45.80','50.00',200,156,'0.00',0,22,1,'有机果园','大兴安岭','斤','5天','冷藏保存','[\"野生蓝莓\",\"抗氧化强\",\"营养价值高\"],系统商品','{\"weight\":\"250g\",\"origin\":\"大兴安岭\",\"harvest_date\":\"2024-01-10\"}',1,1,1,1,50,1789,1,0,'2024-01-10 01:00:00','2025-07-21 14:58:57',0,1,'SFAPA24011009002008E8BC9','/uploads/qrcodes/qr_SFAPA24011009002008E8BC9.png','admin_direct');

-- -----------------------------------------------------
-- 表: category (商品分类表)
-- -----------------------------------------------------

DROP TABLE IF EXISTS `category`;
CREATE TABLE `category` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类名称',
  `parent_id` bigint DEFAULT '0' COMMENT '父分类ID，0表示顶级分类',
  `level` int NOT NULL DEFAULT '1' COMMENT '分类层级',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '分类图标',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '分类图片',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '分类描述',
  `seo_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'SEO标题',
  `seo_keywords` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'SEO关键词',
  `seo_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT 'SEO描述',
  `sort_order` int NOT NULL DEFAULT '0' COMMENT '排序',
  `is_hot` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否热门',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-启用，0-禁用',
  `product_count` int NOT NULL DEFAULT '0' COMMENT '商品数量',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标记',
  `level3_id` bigint DEFAULT NULL COMMENT '三级分类ID',
  `huinong_category_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '惠农网分类名称',
  `huinong_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '惠农网URL',
  `estimated_products` int DEFAULT '0' COMMENT '预估商品数',
  `crawl_priority` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'medium' COMMENT '爬取优先级',
  `last_crawl_time` datetime DEFAULT NULL COMMENT '最后爬取时间',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_level` (`level`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=67 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品分类表';

-- 分类表数据（主要分类）
INSERT INTO `category` VALUES
(1,'蔬菜类',0,1,'icon-vegetables','/images/categories/vegetables.jpg','新鲜蔬菜，绿色健康，营养丰富','新鲜蔬菜 - SFAP农品汇','蔬菜,新鲜蔬菜,绿色蔬菜,有机蔬菜','提供各类新鲜蔬菜，包括叶菜类、根茎类、瓜果类等，绿色健康，营养丰富',1,1,1,28,'2024-01-01 00:00:00','2025-07-20 05:21:52',0,NULL,NULL,NULL,0,'medium',NULL),
(2,'水果类',0,1,'icon-fruits','/images/categories/fruits.jpg','新鲜水果，甜美可口，维生素丰富','新鲜水果 - SFAP农品汇','水果,新鲜水果,时令水果,进口水果','提供各类新鲜水果，包括时令水果、热带水果、进口水果等，甜美可口，维生素丰富',2,1,1,13,'2024-01-01 00:00:00','2025-07-20 05:21:58',0,NULL,NULL,NULL,0,'medium',NULL),
(3,'粮食作物',0,1,'icon-grains','/images/categories/grains.jpg','优质粮食，营养主食，健康生活','优质粮食 - SFAP农品汇','粮食,大米,小麦,玉米,杂粮','提供各类优质粮食作物，包括大米、小麦、玉米、杂粮等，营养主食，健康生活',3,1,1,6,'2024-01-01 00:00:00','2025-07-20 05:22:05',0,NULL,NULL,NULL,0,'medium',NULL),
(4,'畜牧产品',0,1,'icon-livestock','/images/categories/livestock.jpg','优质肉类，新鲜蛋奶，营养蛋白','畜牧产品 - SFAP农品汇','肉类,牛肉,猪肉,鸡肉,鸡蛋,牛奶','提供各类优质畜牧产品，包括新鲜肉类、蛋类、奶制品等，营养蛋白，健康美味',4,1,1,4,'2024-01-01 00:00:00','2025-07-20 05:22:11',0,NULL,NULL,NULL,0,'medium',NULL),
(5,'水产品',0,1,'icon-seafood','/images/categories/seafood.jpg','新鲜水产，海鲜河鲜，营养美味','新鲜水产 - SFAP农品汇','水产,海鲜,河鲜,鱼类,虾蟹','提供各类新鲜水产品，包括海鲜、河鲜、鱼类、虾蟹等，营养美味，新鲜直达',5,1,1,3,'2024-01-01 00:00:00','2025-07-20 05:22:19',0,NULL,NULL,NULL,0,'medium',NULL),
(6,'农副产品',0,1,'icon-agricultural','/images/categories/agricultural.jpg','天然农副产品，绿色健康，营养丰富','农副产品 - SFAP农品汇','农副产品,坚果,干货,调料,茶叶','提供各类天然农副产品，包括坚果、干货、调料、茶叶等，绿色健康，营养丰富',6,1,1,3,'2024-01-01 00:00:00','2025-07-20 05:22:24',0,NULL,NULL,NULL,0,'medium',NULL),
(11,'叶菜类',1,2,'icon-leafy','/images/categories/leafy.jpg','新鲜叶菜，绿色健康，富含维生素','叶菜类蔬菜 - SFAP农品汇','叶菜,青菜,菠菜,白菜,生菜','包括菠菜、白菜、生菜、青菜等各类叶菜，新鲜绿色，富含维生素和纤维',1,1,1,10,'2024-01-01 00:00:00','2025-07-13 15:26:10',0,NULL,NULL,NULL,0,'medium',NULL),
(12,'根茎类',1,2,'icon-root','/images/categories/root.jpg','新鲜根茎菜，营养丰富，口感鲜美','根茎类蔬菜 - SFAP农品汇','根茎菜,萝卜,胡萝卜,土豆,红薯','包括萝卜、胡萝卜、土豆、红薯等根茎类蔬菜，营养丰富，口感鲜美',2,1,1,8,'2024-01-01 00:00:00','2025-07-13 15:26:10',0,NULL,NULL,NULL,0,'medium',NULL),
(21,'菜用花类',2,2,'icon-citrus','/images/categories/citrus.jpg','花椰菜、西兰花、金针花、韭菜花等','柑橘类水果 - SFAP农品汇','柑橘,橙子,柠檬,柚子,橘子','包括橙子、柠檬、柚子、橘子等柑橘类水果，酸甜可口，维C丰富',1,1,1,6,'2024-01-01 00:00:00','2025-07-19 04:01:20',0,NULL,NULL,NULL,0,'medium',NULL),
(22,'野菜特菜',2,2,'icon-berry','/images/categories/berry.jpg','马齿苋、蒲公英、苋菜、荠菜等野生和特色蔬菜','浆果类水果 - SFAP农品汇','浆果,草莓,蓝莓,葡萄,樱桃','包括草莓、蓝莓、葡萄、樱桃等浆果类水果，甜美多汁，抗氧化强',2,1,1,4,'2024-01-01 00:00:00','2025-07-19 04:01:28',0,NULL,NULL,NULL,0,'medium',NULL);

-- =====================================================
-- 3. 其他重要表结构（简化版）
-- =====================================================

-- -----------------------------------------------------
-- 表: product_review (商品评论表)
-- -----------------------------------------------------

DROP TABLE IF EXISTS `product_review`;
CREATE TABLE `product_review` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '评论ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `order_id` bigint DEFAULT NULL COMMENT '订单ID',
  `rating` int NOT NULL DEFAULT '5' COMMENT '评分：1-5星',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '评论内容',
  `images` json DEFAULT NULL COMMENT '评论图片',
  `reply` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '商家回复',
  `reply_time` datetime DEFAULT NULL COMMENT '回复时间',
  `like_count` int NOT NULL DEFAULT '0' COMMENT '点赞数',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-正常，0-隐藏',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_rating` (`rating`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品评论表';

-- -----------------------------------------------------
-- 表: traceability_record (溯源记录表)
-- -----------------------------------------------------

DROP TABLE IF EXISTS `traceability_record`;
CREATE TABLE `traceability_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '溯源记录ID',
  `trace_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '溯源码',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `seller_id` bigint NOT NULL COMMENT '销售者ID',
  `product_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '产品名称',
  `batch_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '生产批次',
  `production_date` date DEFAULT NULL COMMENT '生产日期',
  `expiry_date` date DEFAULT NULL COMMENT '过期日期',
  `origin` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '产地',
  `farm_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '农场名称',
  `producer_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '生产者姓名',
  `producer_contact` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '生产者联系方式',
  `production_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '生产地址',
  `production_environment` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '生产环境',
  `cultivation_method` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '种植/养殖方式',
  `pesticides_used` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '使用农药/饲料',
  `quality_grade` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '质量等级',
  `testing_organization` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '检测机构',
  `quality_report` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '质量检测报告',
  `certification_info` json DEFAULT NULL COMMENT '认证信息',
  `images` json DEFAULT NULL COMMENT '相关图片',
  `documents` json DEFAULT NULL COMMENT '相关文档',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '备注说明',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'draft' COMMENT '状态：draft-草稿，pending-待审核，published-已发布',
  `audit_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'pending' COMMENT '审核状态',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `audit_user_id` bigint DEFAULT NULL COMMENT '审核人ID',
  `audit_remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '审核备注',
  `qr_code_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '二维码URL',
  `view_count` int NOT NULL DEFAULT '0' COMMENT '查看次数',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_trace_code` (`trace_code`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_seller_id` (`seller_id`),
  KEY `idx_status` (`status`),
  KEY `idx_audit_status` (`audit_status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='溯源记录表';

-- =====================================================
-- 4. 数据库统计信息
-- =====================================================

-- 当前数据库统计信息（备份时）
-- 用户总数: 21
-- 商品总数: 80
-- 分类总数: 47
-- 溯源记录数: 约80条
-- 备份时间: 2025-01-25

-- =====================================================
-- 5. 重要配置和索引
-- =====================================================

-- 为高频查询字段添加复合索引
CREATE INDEX IF NOT EXISTS idx_product_category_status ON product(category_id, status);
CREATE INDEX IF NOT EXISTS idx_product_seller_status ON product(seller_id, status);
CREATE INDEX IF NOT EXISTS idx_user_role_status ON user(role, status);

-- =====================================================
-- 6. 视图定义（如果存在）
-- =====================================================

-- 产品溯源视图
CREATE OR REPLACE VIEW v_product_traceability AS
SELECT
    p.id AS product_id,
    p.name AS product_name,
    p.seller_id,
    u.username AS seller_name,
    tr.trace_code,
    tr.batch_number,
    tr.production_date,
    tr.origin,
    tr.farm_name,
    tr.quality_grade,
    tr.status AS trace_status,
    tr.view_count AS trace_view_count,
    tr.created_at AS trace_created_at
FROM product p
LEFT JOIN traceability_record tr ON p.id = tr.product_id
LEFT JOIN user u ON p.seller_id = u.id
WHERE p.status = 1;

-- =====================================================
-- 7. 完成备份
-- =====================================================

-- 提交事务
COMMIT;

-- 重新启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- =====================================================
-- 备份完成信息
-- =====================================================

SELECT 'SFAP农品汇平台数据库备份完成！' AS message;
SELECT 'Backup Date: 2025-01-25' AS backup_date;
SELECT 'Database: agriculture_mall' AS database_name;
SELECT 'Character Set: utf8mb4' AS charset;
SELECT 'Collation: utf8mb4_unicode_ci' AS collation;

-- =====================================================
-- 恢复验证查询
-- =====================================================

-- 验证用户数据
SELECT
    COUNT(*) AS total_users,
    COUNT(CASE WHEN role = 'admin' THEN 1 END) AS admin_users,
    COUNT(CASE WHEN role = 'seller' THEN 1 END) AS seller_users,
    COUNT(CASE WHEN role = 'user' THEN 1 END) AS normal_users
FROM user WHERE deleted = 0;

-- 验证商品数据
SELECT
    COUNT(*) AS total_products,
    COUNT(CASE WHEN status = 1 THEN 1 END) AS active_products,
    COUNT(CASE WHEN has_traceability = 1 THEN 1 END) AS traceable_products
FROM product WHERE deleted = 0;

-- 验证分类数据
SELECT
    COUNT(*) AS total_categories,
    COUNT(CASE WHEN level = 1 THEN 1 END) AS level1_categories,
    COUNT(CASE WHEN level = 2 THEN 1 END) AS level2_categories
FROM category WHERE deleted = 0;

-- =====================================================
-- 使用说明
-- =====================================================

/*
此备份文件包含SFAP农品汇平台的完整数据库结构和核心数据。

恢复步骤：
1. 创建新的MySQL数据库实例
2. 执行此SQL文件：mysql -u root -p < SFAP_current_database_backup.sql
3. 验证数据完整性
4. 更新应用配置文件中的数据库连接信息

注意事项：
- 此备份包含真实的用户密码（已加密）
- 包含完整的商品和分类数据
- 包含溯源系统的配置信息
- 建议在生产环境使用前修改默认密码

技术支持：
- 备份版本：v4.0
- MySQL版本要求：8.0+
- 字符集：utf8mb4
- 排序规则：utf8mb4_unicode_ci
*/

-- =====================================================
-- 文件结束
-- =====================================================

