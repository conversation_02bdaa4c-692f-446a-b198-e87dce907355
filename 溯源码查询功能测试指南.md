# SFAP平台溯源码查询功能测试指南

## 🎯 测试目标

验证SFAP平台普通用户溯源查询功能的完整性和准确性，确保用户能够通过溯源码获取完整的农产品生产、认证和物流信息。

## 📋 测试准备

### 测试环境
- **前端地址**: http://localhost:8080
- **后端地址**: http://localhost:8081
- **数据库**: agriculture_mall
- **测试浏览器**: Chrome/Firefox/Safari

### 推荐测试溯源码

#### 🌟 完整数据测试码 (强烈推荐)
```
溯源码: SFAP25071410001001A1B2
产品: 有机菠菜
数据完整性: ⭐⭐⭐⭐⭐
- ✅ 基本信息: 完整
- ✅ 生产事件: 5条记录
- ✅ 认证信息: 2条记录
- ✅ 物流轨迹: 2条记录
- ✅ 二维码: 已生成
```

## 🔍 详细测试步骤

### 测试1: 基础查询功能

#### 步骤1.1: 访问查询页面
```
1. 打开浏览器
2. 访问: http://localhost:8080/trace
3. 验证页面正常加载
4. 确认看到查询界面
```

#### 步骤1.2: 手动输入查询
```
1. 在输入框中输入: SFAP25071410001001A1B2
2. 点击"查询"按钮
3. 等待查询结果加载
4. 验证返回完整的溯源信息
```

#### 预期结果1.2
```
✅ 产品基本信息:
   - 产品名称: 有机菠菜
   - 农场名称: 绿源有机农场
   - 生产者: fanohhh
   - 批次号: BO20250701001
   - 质量等级: A级
   - 种植日期: 2025-06-15
   - 采收日期: 2025-07-01
   - 包装日期: 2025-07-02

✅ 生产时间轴 (5个事件):
   1. 播种 (2025-06-15) - 选用优质有机菠菜种子进行播种
   2. 施肥 (2025-06-20) - 施用有机肥料，促进幼苗生长
   3. 浇水 (2025-06-25) - 定期浇水，保持土壤湿润
   4. 采收 (2025-07-01) - 菠菜叶片饱满，达到采收标准
   5. 包装 (2025-07-02) - 清洗后进行真空包装

✅ 认证信息 (2个证书):
   1. 有机产品认证 (ORG2025001001)
      - 颁发机构: 中国有机产品认证中心
      - 有效期: 2025-01-15 至 2026-01-14
      - 状态: 有效
   
   2. 质量检测报告 (QC2025070101)
      - 颁发机构: 农产品质量检测中心
      - 有效期: 2025-07-01 至 2025-09-30
      - 状态: 有效

✅ 物流轨迹 (2个阶段):
   1. 农场到配送中心
      - 承运商: 绿色物流配送
      - 运输方式: 冷链运输
      - 出发时间: 2025-07-02 16:00
      - 到达时间: 2025-07-03 08:00
      - 运输温度: 2.5°C
      - 运输湿度: 85%
      - 状态: 已到达
   
   2. 配送中心到零售终端
      - 承运商: 最后一公里配送
      - 运输方式: 冷藏车配送
      - 出发时间: 2025-07-03 09:00
      - 到达时间: 2025-07-03 18:00
      - 运输温度: 3.0°C
      - 运输湿度: 80%
      - 状态: 已到达

✅ 查询统计:
   - 总查询次数: 动态更新
   - 今日查询: 动态更新
   - 本周查询: 动态更新
   - 本月查询: 动态更新
```

### 测试2: URL直达查询

#### 步骤2.1: 直接URL访问
```
1. 在浏览器地址栏输入:
   http://localhost:8080/trace/SFAP25071410001001A1B2
2. 按回车键访问
3. 验证自动加载查询结果
```

#### 预期结果2.1
```
✅ 页面自动加载溯源码查询结果
✅ 显示与手动查询相同的完整信息
✅ 输入框自动填充溯源码
```

### 测试3: 扫码查询功能

#### 步骤3.1: 启动扫码功能
```
1. 在查询页面点击"扫码查询"按钮
2. 允许浏览器访问摄像头权限
3. 验证扫码界面正常显示
```

#### 步骤3.2: 扫描二维码
```
1. 准备二维码图片 (如果有的话)
2. 将二维码对准摄像头
3. 等待自动识别
4. 验证自动跳转到查询结果
```

### 测试4: API接口测试

#### 步骤4.1: 基础查询API
```bash
curl -X GET "http://localhost:8081/api/traceability/query/SFAP25071410001001A1B2"
```

#### 步骤4.2: 详情查询API
```bash
curl -X GET "http://localhost:8081/api/traceability/detail/SFAP25071410001001A1B2"
```

#### 步骤4.3: 验证API
```bash
curl -X POST "http://localhost:8081/api/traceability/validate" \
  -H "Content-Type: application/json" \
  -d '{"traceCode":"SFAP25071410001001A1B2"}'
```

### 测试5: 错误处理测试

#### 步骤5.1: 无效溯源码测试
```
1. 输入无效溯源码: INVALID_CODE_123
2. 点击查询
3. 验证错误提示正确显示
```

#### 步骤5.2: 空输入测试
```
1. 不输入任何内容
2. 点击查询
3. 验证提示用户输入溯源码
```

#### 步骤5.3: 格式错误测试
```
1. 输入格式错误的码: SFAP123
2. 点击查询
3. 验证格式错误提示
```

## ✅ 测试验证清单

### 基础功能验证
- [ ] 查询页面正常加载
- [ ] 输入框接受26位溯源码
- [ ] 查询按钮响应正常
- [ ] 查询结果正确显示
- [ ] 错误提示正确显示

### 数据完整性验证
- [ ] 产品基本信息完整显示
- [ ] 生产时间轴正确渲染 (5个事件)
- [ ] 认证信息卡片正常展示 (2个证书)
- [ ] 物流轨迹时间线显示 (2个阶段)
- [ ] 查询统计数据更新

### 交互功能验证
- [ ] URL直达查询正常工作
- [ ] 扫码功能可用 (如果支持)
- [ ] 查询历史记录保存
- [ ] 分享功能可用
- [ ] 二维码下载正常

### 响应式设计验证
- [ ] 桌面端显示正常
- [ ] 平板端适配良好
- [ ] 移动端响应式布局
- [ ] 不同浏览器兼容性

### 性能验证
- [ ] 查询响应时间 < 3秒
- [ ] 页面加载流畅
- [ ] 图片加载正常
- [ ] 无明显卡顿

## 🐛 常见问题排查

### 问题1: 查询无结果
```
可能原因:
1. 溯源码输入错误
2. 数据库连接问题
3. 后端服务未启动

解决方案:
1. 检查溯源码格式 (26位，以SFAP开头)
2. 确认后端服务运行在8081端口
3. 检查数据库连接配置
```

### 问题2: 页面加载失败
```
可能原因:
1. 前端服务未启动
2. 端口冲突
3. 路由配置错误

解决方案:
1. 确认前端服务运行在8080端口
2. 检查npm run serve是否正常
3. 验证路由配置
```

### 问题3: 扫码功能不可用
```
可能原因:
1. 浏览器不支持摄像头
2. 权限被拒绝
3. HTTPS要求

解决方案:
1. 使用支持摄像头的现代浏览器
2. 允许摄像头权限
3. 在HTTPS环境下测试
```

## 📊 测试报告模板

### 测试结果记录
```
测试时间: ___________
测试人员: ___________
测试环境: ___________

基础功能: ✅/❌
数据完整性: ✅/❌
交互功能: ✅/❌
响应式设计: ✅/❌
性能表现: ✅/❌

发现问题:
1. ________________
2. ________________
3. ________________

改进建议:
1. ________________
2. ________________
3. ________________
```

---

**测试指南版本**: v1.0  
**最后更新**: 2025-07-14  
**维护人员**: AI Assistant
