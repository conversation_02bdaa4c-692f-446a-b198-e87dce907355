/*
 Navicat MySQL Dump SQL

 Source Server         : agriculture_mall
 Source Server Type    : MySQL
 Source Server Version : 80041 (8.0.41)
 Source Host           : localhost:3306
 Source Schema         : agriculture_mall

 Target Server Type    : MySQL
 Target Server Version : 80041 (8.0.41)
 File Encoding         : 65001

 Date: 25/07/2025 14:02:09
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for address
-- ----------------------------
DROP TABLE IF EXISTS `address`;
CREATE TABLE `address`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '地址ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '收货人姓名',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '联系电话',
  `province` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '省份',
  `city` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '城市',
  `district` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '区县',
  `detail` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '详细地址',
  `is_default` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否默认地址：0-否，1-是',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE COMMENT '用户ID索引'
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '收货地址表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for agriculture_news
-- ----------------------------
DROP TABLE IF EXISTS `agriculture_news`;
CREATE TABLE `agriculture_news`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '新闻标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '新闻正文内容',
  `summary` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '新闻摘要',
  `source` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '新闻来源',
  `publish_time` datetime NULL DEFAULT NULL COMMENT '发布时间',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '原文链接',
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '新闻分类',
  `views` int NULL DEFAULT 0 COMMENT '阅读量',
  `crawl_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '爬取时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_url`(`url` ASC) USING BTREE,
  INDEX `idx_publish_time`(`publish_time` ASC) USING BTREE,
  INDEX `idx_category`(`category` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 78 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '农业新闻表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ai_conversations
-- ----------------------------
DROP TABLE IF EXISTS `ai_conversations`;
CREATE TABLE `ai_conversations`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `ai_response` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `created_at` datetime(6) NULL DEFAULT NULL,
  `session_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_input` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 26 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for api_access_log
-- ----------------------------
DROP TABLE IF EXISTS `api_access_log`;
CREATE TABLE `api_access_log`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` bigint NULL DEFAULT NULL COMMENT '用户ID',
  `api_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'API路径',
  `http_method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'HTTP方法',
  `request_params` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '请求参数',
  `response_code` int NOT NULL COMMENT '响应状态码',
  `response_time` int NOT NULL COMMENT '响应时间(毫秒)',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '用户代理',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '错误信息',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_api_path`(`api_path` ASC) USING BTREE,
  INDEX `idx_response_code`(`response_code` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'API访问日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for cart_item
-- ----------------------------
DROP TABLE IF EXISTS `cart_item`;
CREATE TABLE `cart_item`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '购物车项ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `quantity` int NOT NULL DEFAULT 1 COMMENT '数量',
  `price` decimal(10, 2) NULL DEFAULT NULL COMMENT '商品价格(下单时的价格)',
  `selected` tinyint NULL DEFAULT 1 COMMENT '是否选中(0:否 1:是)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint NULL DEFAULT 0 COMMENT '逻辑删除(0:未删除 1:已删除)',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_product`(`user_id` ASC, `product_id` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_product_id`(`product_id` ASC) USING BTREE,
  INDEX `idx_deleted`(`deleted` ASC) USING BTREE,
  INDEX `idx_user_selected`(`user_id` ASC, `selected` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 36 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '购物车表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for categories
-- ----------------------------
DROP TABLE IF EXISTS `categories`;
CREATE TABLE `categories`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类名称',
  `pinyin` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类拼音',
  `category_id` int NOT NULL COMMENT '惠农网分类ID',
  `parent_id` int NULL DEFAULT NULL COMMENT '父分类ID',
  `level` tinyint NOT NULL DEFAULT 1 COMMENT '分类层级',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_category_id`(`category_id` ASC) USING BTREE,
  UNIQUE INDEX `uk_pinyin`(`pinyin` ASC) USING BTREE,
  INDEX `idx_parent_id`(`parent_id` ASC) USING BTREE,
  INDEX `idx_level`(`level` ASC) USING BTREE,
  INDEX `idx_name`(`name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 19 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '产品分类表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for category
-- ----------------------------
DROP TABLE IF EXISTS `category`;
CREATE TABLE `category`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类名称',
  `parent_id` bigint NULL DEFAULT 0 COMMENT '父分类ID(0表示顶级分类)',
  `level` tinyint NOT NULL DEFAULT 1 COMMENT '分类层级',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '分类图标',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '分类图片',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '分类描述',
  `seo_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'SEO标题',
  `seo_keywords` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'SEO关键词',
  `seo_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT 'SEO描述',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序',
  `is_hot` tinyint NULL DEFAULT 0 COMMENT '是否热门(0:否 1:是)',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
  `product_count` int NULL DEFAULT 0 COMMENT '该分类下的商品数量',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint NULL DEFAULT 0 COMMENT '逻辑删除(0:未删除 1:已删除)',
  `level3_id` bigint NULL DEFAULT NULL COMMENT '三级分类ID',
  `huinong_category_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '惠农网分类名称',
  `huinong_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '惠农网分类URL',
  `estimated_products` int NULL DEFAULT 0 COMMENT '预估产品数量',
  `crawl_priority` enum('high','medium','low') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'medium' COMMENT '爬取优先级',
  `last_crawl_time` datetime NULL DEFAULT NULL COMMENT '最后爬取时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_parent_id`(`parent_id` ASC) USING BTREE,
  INDEX `idx_level`(`level` ASC) USING BTREE,
  INDEX `idx_sort_order`(`sort_order` ASC) USING BTREE,
  INDEX `idx_is_hot`(`is_hot` ASC) USING BTREE,
  INDEX `idx_status_deleted`(`status` ASC, `deleted` ASC) USING BTREE,
  INDEX `idx_huinong_category_name`(`huinong_category_name` ASC) USING BTREE,
  INDEX `idx_crawl_priority`(`crawl_priority` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 67 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '商品分类表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for consultation
-- ----------------------------
DROP TABLE IF EXISTS `consultation`;
CREATE TABLE `consultation`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '咨询ID',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '标题',
  `summary` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '摘要',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '内容',
  `cover_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '封面图片URL',
  `category_id` bigint NOT NULL COMMENT '分类ID',
  `author_id` bigint NOT NULL COMMENT '作者ID',
  `author_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '作者名称',
  `views_count` int NULL DEFAULT 0 COMMENT '浏览量',
  `likes_count` int NULL DEFAULT 0 COMMENT '点赞数',
  `comments_count` int NULL DEFAULT 0 COMMENT '评论数',
  `is_featured` tinyint(1) NULL DEFAULT 0 COMMENT '是否精选：0-否，1-是',
  `is_hot` tinyint(1) NULL DEFAULT 0 COMMENT '是否热门：0-否，1-是',
  `is_new` tinyint(1) NULL DEFAULT 0 COMMENT '是否新发布：0-否，1-是',
  `keywords` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '关键词，多个关键词用逗号分隔',
  `publish_date` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发布时间',
  `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` tinyint NULL DEFAULT 1 COMMENT '状态：0-草稿，1-已发布，2-已下架',
  `deleted` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_category_id`(`category_id` ASC) USING BTREE COMMENT '分类索引',
  INDEX `idx_author_id`(`author_id` ASC) USING BTREE COMMENT '作者索引',
  INDEX `idx_publish_date`(`publish_date` ASC) USING BTREE COMMENT '发布时间索引',
  INDEX `idx_status`(`status` ASC) USING BTREE COMMENT '状态索引'
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '农业咨询表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for consultation_attachment
-- ----------------------------
DROP TABLE IF EXISTS `consultation_attachment`;
CREATE TABLE `consultation_attachment`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '附件ID',
  `consultation_id` bigint NOT NULL COMMENT '咨询ID',
  `file_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件名称',
  `file_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件路径',
  `file_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '文件类型',
  `file_size` bigint NULL DEFAULT NULL COMMENT '文件大小(字节)',
  `upload_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
  `uploader_id` bigint NULL DEFAULT NULL COMMENT '上传者ID',
  `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_consultation_id`(`consultation_id` ASC) USING BTREE COMMENT '咨询ID索引'
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '农业咨询附件表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for consultation_category
-- ----------------------------
DROP TABLE IF EXISTS `consultation_category`;
CREATE TABLE `consultation_category`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '分类描述',
  `parent_id` bigint NULL DEFAULT 0 COMMENT '父分类ID，0表示一级分类',
  `level` int NULL DEFAULT 1 COMMENT '分类层级，1为一级分类',
  `count` int NULL DEFAULT 0 COMMENT '该分类下的咨询数量',
  `sort` int NULL DEFAULT 0 COMMENT '排序',
  `status` int NULL DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
  `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_parent_id`(`parent_id` ASC) USING BTREE COMMENT '父分类索引'
) ENGINE = InnoDB AUTO_INCREMENT = 23 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '农业咨询分类表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for crawl_logs
-- ----------------------------
DROP TABLE IF EXISTS `crawl_logs`;
CREATE TABLE `crawl_logs`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `task_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '任务ID',
  `spider_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '爬虫名称',
  `start_time` timestamp NOT NULL COMMENT '开始时间',
  `end_time` timestamp NULL DEFAULT NULL COMMENT '结束时间',
  `status` enum('running','success','failed','stopped') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '状态',
  `total_requests` int NULL DEFAULT 0 COMMENT '总请求数',
  `success_requests` int NULL DEFAULT 0 COMMENT '成功请求数',
  `failed_requests` int NULL DEFAULT 0 COMMENT '失败请求数',
  `items_scraped` int NULL DEFAULT 0 COMMENT '采集条目数',
  `items_saved` int NULL DEFAULT 0 COMMENT '保存条目数',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '错误信息',
  `config_snapshot` json NULL COMMENT '配置快照',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_task_id`(`task_id` ASC) USING BTREE,
  INDEX `idx_spider_name`(`spider_name` ASC) USING BTREE,
  INDEX `idx_start_time`(`start_time` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '爬取日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for crawl_task_queue
-- ----------------------------
DROP TABLE IF EXISTS `crawl_task_queue`;
CREATE TABLE `crawl_task_queue`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_type` enum('category','subcategory','product') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '任务类型',
  `category_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类名称',
  `subcategory_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '子分类名称',
  `target_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '目标URL',
  `priority` enum('high','medium','low') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'medium' COMMENT '优先级',
  `max_pages` int NULL DEFAULT 5 COMMENT '最大爬取页数',
  `status` enum('pending','running','completed','failed','paused') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'pending' COMMENT '任务状态',
  `progress` int NULL DEFAULT 0 COMMENT '进度百分比',
  `items_crawled` int NULL DEFAULT 0 COMMENT '已爬取项目数',
  `items_saved` int NULL DEFAULT 0 COMMENT '已保存项目数',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '错误信息',
  `started_at` datetime NULL DEFAULT NULL COMMENT '开始时间',
  `completed_at` datetime NULL DEFAULT NULL COMMENT '完成时间',
  `retry_count` int NULL DEFAULT 0 COMMENT '重试次数',
  `max_retries` int NULL DEFAULT 3 COMMENT '最大重试次数',
  `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '爬取任务队列管理表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for data_quality_logs
-- ----------------------------
DROP TABLE IF EXISTS `data_quality_logs`;
CREATE TABLE `data_quality_logs`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '质量日志ID',
  `check_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '检查时间',
  `check_type` enum('completeness','accuracy','consistency','timeliness') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '检查类型',
  `table_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '表名',
  `total_records` int NOT NULL COMMENT '总记录数',
  `valid_records` int NOT NULL COMMENT '有效记录数',
  `invalid_records` int NOT NULL COMMENT '无效记录数',
  `quality_score` decimal(5, 2) NOT NULL COMMENT '质量分数',
  `details` json NULL COMMENT '详细信息',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_check_time`(`check_time` ASC) USING BTREE,
  INDEX `idx_check_type`(`check_type` ASC) USING BTREE,
  INDEX `idx_table_name`(`table_name` ASC) USING BTREE,
  INDEX `idx_quality_score`(`quality_score` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '数据质量监控表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for encyclopedia
-- ----------------------------
DROP TABLE IF EXISTS `encyclopedia`;
CREATE TABLE `encyclopedia`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '百科ID',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '标题',
  `summary` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '摘要',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '内容',
  `cover_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '封面图片URL',
  `category_id` bigint NOT NULL COMMENT '分类ID',
  `category_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '分类名称',
  `author_id` bigint NOT NULL COMMENT '作者ID',
  `author_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '作者名称',
  `views_count` int NULL DEFAULT 0 COMMENT '浏览量',
  `likes_count` int NULL DEFAULT 0 COMMENT '点赞数',
  `favorites_count` int NULL DEFAULT 0 COMMENT '收藏数',
  `is_featured` tinyint(1) NULL DEFAULT 0 COMMENT '是否精选：0-否，1-是',
  `is_hot` tinyint(1) NULL DEFAULT 0 COMMENT '是否热门：0-否，1-是',
  `keywords` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '关键词，多个关键词用逗号分隔',
  `publish_date` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发布时间',
  `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` tinyint NULL DEFAULT 1 COMMENT '状态：0-草稿，1-已发布，2-已下架',
  `deleted` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_category_id`(`category_id` ASC) USING BTREE COMMENT '分类索引',
  INDEX `idx_author_id`(`author_id` ASC) USING BTREE COMMENT '作者索引',
  INDEX `idx_publish_date`(`publish_date` ASC) USING BTREE COMMENT '发布时间索引',
  INDEX `idx_status`(`status` ASC) USING BTREE COMMENT '状态索引'
) ENGINE = InnoDB AUTO_INCREMENT = 48 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '农业百科表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for encyclopedia_backup
-- ----------------------------
DROP TABLE IF EXISTS `encyclopedia_backup`;
CREATE TABLE `encyclopedia_backup`  (
  `id` bigint NOT NULL DEFAULT 0 COMMENT '百科ID',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '标题',
  `summary` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '摘要',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '内容',
  `cover_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '封面图片URL',
  `category_id` bigint NOT NULL COMMENT '分类ID',
  `category_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '分类名称',
  `author_id` bigint NOT NULL COMMENT '作者ID',
  `author_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '作者名称',
  `views_count` int NULL DEFAULT 0 COMMENT '浏览量',
  `likes_count` int NULL DEFAULT 0 COMMENT '点赞数',
  `favorites_count` int NULL DEFAULT 0 COMMENT '收藏数',
  `is_featured` tinyint(1) NULL DEFAULT 0 COMMENT '是否精选：0-否，1-是',
  `is_hot` tinyint(1) NULL DEFAULT 0 COMMENT '是否热门：0-否，1-是',
  `keywords` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '关键词，多个关键词用逗号分隔',
  `publish_date` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发布时间',
  `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` tinyint NULL DEFAULT 1 COMMENT '状态：0-草稿，1-已发布，2-已下架',
  `deleted` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for encyclopedia_category
-- ----------------------------
DROP TABLE IF EXISTS `encyclopedia_category`;
CREATE TABLE `encyclopedia_category`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分类名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '分类描述',
  `parent_id` bigint NULL DEFAULT 0 COMMENT '父分类ID，0表示一级分类',
  `level` int NULL DEFAULT 1 COMMENT '分类层级，1为一级分类',
  `count` int NULL DEFAULT 0 COMMENT '该分类下的百科数量',
  `sort` int NULL DEFAULT 0 COMMENT '排序',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图标或封面',
  `status` int NULL DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
  `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `cover_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  `sort_order` int NULL DEFAULT 0,
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  INDEX `idx_parent_id`(`parent_id`) COMMENT '父分类索引'
) ENGINE = InnoDB AUTO_INCREMENT = 65 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '农业百科分类表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for encyclopedia_category_backup
-- ----------------------------
DROP TABLE IF EXISTS `encyclopedia_category_backup`;
CREATE TABLE `encyclopedia_category_backup`  (
  `id` bigint NOT NULL DEFAULT 0 COMMENT '分类ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分类名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '分类描述',
  `parent_id` bigint NULL DEFAULT 0 COMMENT '父分类ID，0表示一级分类',
  `level` int NULL DEFAULT 1 COMMENT '分类层级，1为一级分类',
  `count` int NULL DEFAULT 0 COMMENT '该分类下的百科数量',
  `sort` int NULL DEFAULT 0 COMMENT '排序',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图标或封面',
  `status` int NULL DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
  `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `cover_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  `sort_order` int NULL DEFAULT 0,
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for encyclopedia_comment
-- ----------------------------
DROP TABLE IF EXISTS `encyclopedia_comment`;
CREATE TABLE `encyclopedia_comment`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '评论ID',
  `encyclopedia_id` bigint NOT NULL COMMENT '百科ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '用户名称',
  `user_avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '用户头像URL',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '评论内容',
  `parent_id` bigint NULL DEFAULT 0 COMMENT '父评论ID，0表示一级评论',
  `level` int NULL DEFAULT 1 COMMENT '评论层级，1为一级评论',
  `likes_count` int NULL DEFAULT 0 COMMENT '点赞数',
  `is_approved` tinyint(1) NULL DEFAULT 1 COMMENT '是否通过审核：0-未审核，1-已审核',
  `reply_count` int NULL DEFAULT 0 COMMENT '回复数量',
  `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` tinyint NULL DEFAULT 1 COMMENT '状态：0-已屏蔽，1-正常',
  `deleted` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_encyclopedia_id`(`encyclopedia_id` ASC) USING BTREE COMMENT '百科索引',
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE COMMENT '用户索引',
  INDEX `idx_parent_id`(`parent_id` ASC) USING BTREE COMMENT '父评论索引',
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE COMMENT '创建时间索引'
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '农业百科评论表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for encyclopedia_favorite
-- ----------------------------
DROP TABLE IF EXISTS `encyclopedia_favorite`;
CREATE TABLE `encyclopedia_favorite`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '收藏ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `encyclopedia_id` bigint NOT NULL COMMENT '百科ID',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '百科标题',
  `favorite_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '收藏时间',
  `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_encyclopedia`(`user_id` ASC, `encyclopedia_id` ASC) USING BTREE COMMENT '用户百科唯一索引',
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE COMMENT '用户索引',
  INDEX `idx_encyclopedia_id`(`encyclopedia_id` ASC) USING BTREE COMMENT '百科索引'
) ENGINE = InnoDB AUTO_INCREMENT = 312 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '农业百科收藏表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for error_log
-- ----------------------------
DROP TABLE IF EXISTS `error_log`;
CREATE TABLE `error_log`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '错误ID',
  `error_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '错误类型',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '错误信息',
  `stack_trace` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '堆栈跟踪',
  `user_id` bigint NULL DEFAULT NULL COMMENT '用户ID',
  `request_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '请求URL',
  `request_method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '请求方法',
  `request_params` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '请求参数',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '用户代理',
  `severity` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'ERROR' COMMENT '严重程度(DEBUG,INFO,WARN,ERROR,FATAL)',
  `resolved` tinyint NULL DEFAULT 0 COMMENT '是否已解决(0:未解决 1:已解决)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `resolved_at` datetime NULL DEFAULT NULL COMMENT '解决时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_error_type`(`error_type` ASC) USING BTREE,
  INDEX `idx_severity`(`severity` ASC) USING BTREE,
  INDEX `idx_resolved`(`resolved` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '错误日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for favorite_backup
-- ----------------------------
DROP TABLE IF EXISTS `favorite_backup`;
CREATE TABLE `favorite_backup`  (
  `id` bigint NOT NULL DEFAULT 0,
  `user_id` bigint NOT NULL,
  `product_id` bigint NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `deleted` int NOT NULL DEFAULT 0
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for model_evaluations
-- ----------------------------
DROP TABLE IF EXISTS `model_evaluations`;
CREATE TABLE `model_evaluations`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `model_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模型ID',
  `evaluation_date` date NOT NULL COMMENT '评估日期',
  `mape` decimal(8, 4) NULL DEFAULT NULL COMMENT '平均绝对百分比误差',
  `rmse` decimal(10, 4) NULL DEFAULT NULL COMMENT '均方根误差',
  `mae` decimal(10, 4) NULL DEFAULT NULL COMMENT '平均绝对误差',
  `r2_score` decimal(8, 4) NULL DEFAULT NULL COMMENT 'R²决定系数',
  `trend_accuracy` decimal(5, 2) NULL DEFAULT NULL COMMENT '趋势预测准确率',
  `evaluation_details` json NULL COMMENT '详细评估信息',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_model_id`(`model_id` ASC) USING BTREE,
  INDEX `idx_evaluation_date`(`evaluation_date` ASC) USING BTREE,
  CONSTRAINT `model_evaluations_ibfk_1` FOREIGN KEY (`model_id`) REFERENCES `prediction_models` (`model_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '模型评估表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for news_images
-- ----------------------------
DROP TABLE IF EXISTS `news_images`;
CREATE TABLE `news_images`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `news_id` int NOT NULL COMMENT '关联的新闻ID',
  `image_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '原始图片URL',
  `image_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '本地存储路径',
  `is_cover` tinyint(1) NULL DEFAULT 0 COMMENT '是否为封面图',
  `width` int NULL DEFAULT NULL COMMENT '图片宽度',
  `height` int NULL DEFAULT NULL COMMENT '图片高度',
  `file_size` int NULL DEFAULT NULL COMMENT '文件大小(KB)',
  `crawl_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '爬取时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_news_id`(`news_id` ASC) USING BTREE,
  CONSTRAINT `fk_news_images_news` FOREIGN KEY (`news_id`) REFERENCES `agriculture_news` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '新闻图片表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for notification
-- ----------------------------
DROP TABLE IF EXISTS `notification`;
CREATE TABLE `notification`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '通知ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '通知标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '通知内容',
  `type` tinyint NOT NULL COMMENT '通知类型：1-系统通知，2-订单通知，3-商品通知',
  `is_read` tinyint NOT NULL DEFAULT 0 COMMENT '是否已读：0-未读，1-已读',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 18 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '消息通知表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for order
-- ----------------------------
DROP TABLE IF EXISTS `order`;
CREATE TABLE `order`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单号(系统生成唯一标识)',
  `user_id` bigint NOT NULL COMMENT '买家用户ID',
  `seller_id` bigint NOT NULL COMMENT '卖家用户ID',
  `total_amount` decimal(10, 2) NOT NULL COMMENT '订单总金额(商品总价+运费-优惠)',
  `discount_amount` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '优惠金额(优惠券/满减等)',
  `shipping_fee` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '运费',
  `actual_amount` decimal(10, 2) NOT NULL COMMENT '实付金额(总金额-优惠)',
  `payment_method` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '支付方式(ALIPAY/WECHAT/BANK_CARD/BALANCE)',
  `payment_status` tinyint NOT NULL DEFAULT 0 COMMENT '支付状态(0:未支付 1:已支付 2:支付中 3:支付失败 4:已退款)',
  `order_status` tinyint NOT NULL DEFAULT 0 COMMENT '订单状态(0:待支付 1:待发货 2:待收货 3:已完成 4:已取消 5:已关闭 6:无效订单)',
  `shipping_address` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '收货地址(JSON格式)',
  `buyer_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '买家备注',
  `seller_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '卖家备注',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '订单备注(系统/客服备注)',
  `delivery_company` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '物流公司',
  `tracking_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '物流单号',
  `payment_time` datetime NULL DEFAULT NULL COMMENT '支付时间',
  `shipping_time` datetime NULL DEFAULT NULL COMMENT '发货时间',
  `delivery_time` datetime NULL DEFAULT NULL COMMENT '物流送达时间',
  `completion_time` datetime NULL DEFAULT NULL COMMENT '订单完成时间(确认收货时间)',
  `receive_time` datetime NULL DEFAULT NULL COMMENT '收货时间',
  `cancel_time` datetime NULL DEFAULT NULL COMMENT '取消时间',
  `cancel_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '取消原因',
  `ip_address` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '下单IP地址',
  `device_info` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '设备信息(浏览器/APP版本等)',
  `deleted` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除(0:否 1:是)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_order_no`(`order_no` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_seller_id`(`seller_id` ASC) USING BTREE,
  INDEX `idx_order_status`(`order_status` ASC) USING BTREE,
  INDEX `idx_payment_status`(`payment_status` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  INDEX `idx_user_seller`(`user_id` ASC, `seller_id` ASC) USING BTREE,
  INDEX `idx_order_seller_status`(`seller_id` ASC, `order_status` ASC, `deleted` ASC) USING BTREE,
  INDEX `idx_order_date_status`(`created_at` ASC, `order_status` ASC, `deleted` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 40015 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '订单主表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for order_item
-- ----------------------------
DROP TABLE IF EXISTS `order_item`;
CREATE TABLE `order_item`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '订单商品ID',
  `order_id` bigint NOT NULL COMMENT '订单ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `product_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '商品名称',
  `product_image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '商品图片',
  `product_price` decimal(10, 2) NOT NULL COMMENT '商品单价',
  `quantity` int NOT NULL COMMENT '购买数量',
  `total_price` decimal(10, 2) NOT NULL COMMENT '小计金额',
  `specifications` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '商品规格',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_order_id`(`order_id` ASC) USING BTREE,
  INDEX `idx_product_id`(`product_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '订单商品表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for prediction_models
-- ----------------------------
DROP TABLE IF EXISTS `prediction_models`;
CREATE TABLE `prediction_models`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `model_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模型唯一标识',
  `model_type` enum('RNN','ARIMA') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模型类型',
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '农产品类别',
  `region` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '地区',
  `model_params` json NULL COMMENT '模型参数',
  `training_metrics` json NULL COMMENT '训练指标',
  `model_file_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '模型文件路径',
  `model_size` bigint NULL DEFAULT NULL COMMENT '模型文件大小(字节)',
  `accuracy` decimal(5, 2) NULL DEFAULT NULL COMMENT '模型准确率',
  `status` enum('training','active','deprecated') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'training',
  `training_start_time` timestamp NULL DEFAULT NULL COMMENT '训练开始时间',
  `training_end_time` timestamp NULL DEFAULT NULL COMMENT '训练结束时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `model_id`(`model_id` ASC) USING BTREE,
  INDEX `idx_category_region`(`category` ASC, `region` ASC) USING BTREE,
  INDEX `idx_model_type`(`model_type` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '预测模型信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for prediction_results
-- ----------------------------
DROP TABLE IF EXISTS `prediction_results`;
CREATE TABLE `prediction_results`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `prediction_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '预测任务ID',
  `model_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '使用的模型ID',
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '农产品类别',
  `region` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '地区',
  `prediction_date` date NOT NULL COMMENT '预测日期',
  `predicted_price` decimal(10, 2) NOT NULL COMMENT '预测价格',
  `confidence` decimal(5, 4) NULL DEFAULT NULL COMMENT '置信度',
  `upper_bound` decimal(10, 2) NULL DEFAULT NULL COMMENT '置信区间上界',
  `lower_bound` decimal(10, 2) NULL DEFAULT NULL COMMENT '置信区间下界',
  `trend` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '趋势(up/down/stable)',
  `factors` json NULL COMMENT '影响因素分析',
  `actual_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '实际价格(用于后续验证)',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `prediction_id`(`prediction_id` ASC) USING BTREE,
  INDEX `idx_model_id`(`model_id` ASC) USING BTREE,
  INDEX `idx_category_region_date`(`category` ASC, `region` ASC, `prediction_date` ASC) USING BTREE,
  INDEX `idx_prediction_date`(`prediction_date` ASC) USING BTREE,
  CONSTRAINT `prediction_results_ibfk_1` FOREIGN KEY (`model_id`) REFERENCES `prediction_models` (`model_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '预测结果表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for price_data
-- ----------------------------
DROP TABLE IF EXISTS `price_data`;
CREATE TABLE `price_data`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '价格数据ID',
  `product_id` int NOT NULL COMMENT '产品ID',
  `region_id` int NOT NULL COMMENT '地区ID',
  `price` decimal(10, 2) NOT NULL COMMENT '价格',
  `unit` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '斤' COMMENT '计量单位',
  `price_change` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '价格变化',
  `price_trend` tinyint NULL DEFAULT NULL COMMENT '价格趋势(1上涨0持平-1下跌)',
  `source_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '数据源URL',
  `source_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '源数据ID',
  `data_date` date NOT NULL COMMENT '数据日期',
  `crawl_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '采集时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_product_region_date`(`product_id` ASC, `region_id` ASC, `data_date` ASC) USING BTREE,
  INDEX `idx_data_date`(`data_date` ASC) USING BTREE,
  INDEX `idx_crawl_time`(`crawl_time` ASC) USING BTREE,
  INDEX `idx_price`(`price` ASC) USING BTREE,
  INDEX `idx_source_id`(`source_id` ASC) USING BTREE,
  INDEX `idx_product_date`(`product_id` ASC, `data_date` DESC) USING BTREE,
  INDEX `idx_region_date`(`region_id` ASC, `data_date` DESC) USING BTREE,
  CONSTRAINT `price_data_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `price_data_ibfk_2` FOREIGN KEY (`region_id`) REFERENCES `regions` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 521 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '价格数据表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for price_history
-- ----------------------------
DROP TABLE IF EXISTS `price_history`;
CREATE TABLE `price_history`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '农产品类别',
  `product_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '具体产品名称',
  `region` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '地区',
  `market_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '市场名称',
  `price` decimal(10, 2) NOT NULL COMMENT '价格(元/kg)',
  `volume` int NULL DEFAULT NULL COMMENT '交易量(kg)',
  `quality_grade` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '质量等级',
  `date` date NOT NULL COMMENT '日期',
  `data_source` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '数据来源',
  `weather` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '天气情况',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_category_region_date`(`category` ASC, `region` ASC, `date` ASC, `market_name` ASC) USING BTREE,
  INDEX `idx_category_region_date`(`category` ASC, `region` ASC, `date` ASC) USING BTREE,
  INDEX `idx_product_date`(`product_name` ASC, `date` ASC) USING BTREE,
  INDEX `idx_date`(`date` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '历史价格数据表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for product
-- ----------------------------
DROP TABLE IF EXISTS `product`;
CREATE TABLE `product`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '商品ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '商品名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '商品描述',
  `image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '商品主图',
  `price` decimal(10, 2) NOT NULL COMMENT '商品价格',
  `original_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '原价',
  `stock` int NOT NULL DEFAULT 0 COMMENT '库存数量',
  `sales_count` int NULL DEFAULT 0 COMMENT '销售数量',
  `rating` decimal(3, 2) NULL DEFAULT 0.00 COMMENT '平均评分',
  `review_count` int NULL DEFAULT 0 COMMENT '评价数量',
  `category_id` bigint NOT NULL COMMENT '分类ID',
  `seller_id` bigint NOT NULL DEFAULT 1,
  `brand` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '品牌',
  `origin` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '产地',
  `unit` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '单位(斤/公斤/箱等)',
  `shelf_life` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '保质期',
  `storage_method` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '储存方式',
  `tags` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '标签(JSON格式)',
  `specifications` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '规格参数(JSON格式)',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态(0:下架 1:上架 2:审核中)',
  `is_featured` tinyint NULL DEFAULT 0 COMMENT '是否精选(0:否 1:是)',
  `is_hot` tinyint NULL DEFAULT 0 COMMENT '是否热门(0:否 1:是)',
  `is_new` tinyint NULL DEFAULT 0 COMMENT '是否新品(0:否 1:是)',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序权重',
  `view_count` int NULL DEFAULT 0 COMMENT '浏览次数',
  `favorite_count` int NULL DEFAULT 0 COMMENT '收藏次数',
  `like_count` int NOT NULL DEFAULT 0 COMMENT '点赞数量',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint NULL DEFAULT 0 COMMENT '逻辑删除(0:未删除 1:已删除)',
  `has_traceability` tinyint(1) NULL DEFAULT 0 COMMENT '是否有溯源信息(0:无 1:有)',
  `trace_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '关联的溯源码',
  `qr_code_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '二维码图片URL',
  `source_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'seller_upload' COMMENT '产品来源类型(admin_direct:产品直购 seller_upload:销售者上传)',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_category_id`(`category_id` ASC) USING BTREE,
  INDEX `idx_seller_id`(`seller_id` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  INDEX `idx_sales_count`(`sales_count` ASC) USING BTREE,
  INDEX `idx_rating`(`rating` ASC) USING BTREE,
  INDEX `idx_product_seller_status`(`seller_id` ASC, `status` ASC, `deleted` ASC) USING BTREE,
  INDEX `idx_product_trace_code`(`trace_code` ASC) USING BTREE,
  INDEX `idx_product_has_traceability`(`has_traceability` ASC) USING BTREE,
  INDEX `idx_like_count`(`like_count` ASC) USING BTREE,
  INDEX `idx_rating_sales`(`rating` ASC, `sales_count` ASC) USING BTREE,
  INDEX `idx_status_created`(`status` ASC, `created_at` ASC) USING BTREE,
  INDEX `idx_product_stats`(`like_count` ASC, `favorite_count` ASC, `review_count` ASC) USING BTREE,
  INDEX `idx_product_rating_sales`(`rating` ASC, `sales_count` ASC) USING BTREE,
  INDEX `idx_source_type`(`source_type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6035 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '商品主表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for product_attribute
-- ----------------------------
DROP TABLE IF EXISTS `product_attribute`;
CREATE TABLE `product_attribute`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '属性ID',
  `category_id` bigint NOT NULL COMMENT '分类ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '属性名称',
  `type` tinyint NOT NULL DEFAULT 1 COMMENT '属性类型(1:文本 2:数字 3:选择 4:多选 5:日期)',
  `options` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '选项值(JSON格式)',
  `is_required` tinyint NULL DEFAULT 0 COMMENT '是否必填(0:否 1:是)',
  `is_searchable` tinyint NULL DEFAULT 0 COMMENT '是否可搜索(0:否 1:是)',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_category_id`(`category_id` ASC) USING BTREE,
  INDEX `idx_sort_order`(`sort_order` ASC) USING BTREE,
  CONSTRAINT `fk_product_attribute_category` FOREIGN KEY (`category_id`) REFERENCES `category` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '产品属性定义表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for product_attribute_value
-- ----------------------------
DROP TABLE IF EXISTS `product_attribute_value`;
CREATE TABLE `product_attribute_value`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '属性值ID',
  `product_id` bigint NOT NULL COMMENT '产品ID',
  `attribute_id` bigint NOT NULL COMMENT '属性ID',
  `value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '属性值',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_product_attribute`(`product_id` ASC, `attribute_id` ASC) USING BTREE,
  INDEX `idx_product_id`(`product_id` ASC) USING BTREE,
  INDEX `idx_attribute_id`(`attribute_id` ASC) USING BTREE,
  CONSTRAINT `fk_product_attr_value_attribute` FOREIGN KEY (`attribute_id`) REFERENCES `product_attribute` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_product_attr_value_product` FOREIGN KEY (`product_id`) REFERENCES `product` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '产品属性值表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for product_favorite
-- ----------------------------
DROP TABLE IF EXISTS `product_favorite`;
CREATE TABLE `product_favorite`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  `deleted` int NOT NULL DEFAULT 0 COMMENT '删除标记:0-未删除,1-已删除',
  `folder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '默认收藏夹' COMMENT '收藏夹名称',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_product`(`user_id` ASC, `product_id` ASC) USING BTREE,
  INDEX `idx_user_created`(`user_id` ASC, `created_at` ASC) USING BTREE,
  INDEX `idx_product_created`(`product_id` ASC, `created_at` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_product_id`(`product_id` ASC) USING BTREE,
  INDEX `idx_user_folder`(`user_id` ASC, `folder_name` ASC) USING BTREE,
  INDEX `idx_folder_created`(`folder_name` ASC, `created_at` ASC) USING BTREE,
  CONSTRAINT `fk_product_favorite_product` FOREIGN KEY (`product_id`) REFERENCES `product` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_product_favorite_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '商品收藏表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for product_image
-- ----------------------------
DROP TABLE IF EXISTS `product_image`;
CREATE TABLE `product_image`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '图片ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `image_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '图片URL',
  `image_type` tinyint NOT NULL DEFAULT 1 COMMENT '图片类型(1:主图 2:详情图 3:规格图)',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序',
  `alt_text` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '图片描述',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_product_id`(`product_id` ASC) USING BTREE,
  INDEX `idx_image_type`(`image_type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '商品图片表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for product_likes
-- ----------------------------
DROP TABLE IF EXISTS `product_likes`;
CREATE TABLE `product_likes`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_product`(`user_id` ASC, `product_id` ASC) USING BTREE,
  INDEX `idx_product_id`(`product_id` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  CONSTRAINT `fk_product_likes_product` FOREIGN KEY (`product_id`) REFERENCES `product` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_product_likes_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '商品点赞表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for product_price_history
-- ----------------------------
DROP TABLE IF EXISTS `product_price_history`;
CREATE TABLE `product_price_history`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '价格历史ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `price` decimal(10, 2) NOT NULL COMMENT '价格',
  `original_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '原价',
  `change_reason` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '变价原因',
  `effective_date` datetime NOT NULL COMMENT '生效时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `source` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'manual' COMMENT '数据来源：manual-手动，huinong-惠农网，user-用户上报',
  `market_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '市场名称',
  `region` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '地区信息',
  `unit` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '斤' COMMENT '价格单位',
  `quality_score` decimal(3, 2) NULL DEFAULT 1.00 COMMENT '数据质量评分(0-1)',
  `crawl_time` datetime NULL DEFAULT NULL COMMENT '数据爬取时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_product_id`(`product_id` ASC) USING BTREE,
  INDEX `idx_effective_date`(`effective_date` ASC) USING BTREE,
  INDEX `idx_product_time`(`product_id` ASC, `effective_date` DESC) USING BTREE,
  INDEX `idx_source_time`(`source` ASC, `created_at` DESC) USING BTREE,
  INDEX `idx_region_time`(`region` ASC, `effective_date` DESC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '商品价格历史表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for product_recommendation
-- ----------------------------
DROP TABLE IF EXISTS `product_recommendation`;
CREATE TABLE `product_recommendation`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '推荐ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `recommendation_type` tinyint NOT NULL COMMENT '推荐类型(1:协同过滤 2:内容推荐 3:热门推荐 4:新品推荐)',
  `score` decimal(5, 4) NOT NULL COMMENT '推荐分数',
  `reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '推荐理由',
  `algorithm_version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '算法版本',
  `is_clicked` tinyint NULL DEFAULT 0 COMMENT '是否点击(0:否 1:是)',
  `click_time` datetime NULL DEFAULT NULL COMMENT '点击时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `expired_at` datetime NOT NULL COMMENT '过期时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_product_id`(`product_id` ASC) USING BTREE,
  INDEX `idx_recommendation_type`(`recommendation_type` ASC) USING BTREE,
  INDEX `idx_score`(`score` ASC) USING BTREE,
  INDEX `idx_expired_at`(`expired_at` ASC) USING BTREE,
  INDEX `idx_user_product`(`user_id` ASC, `product_id` ASC) USING BTREE,
  INDEX `idx_type_score`(`recommendation_type` ASC, `score` DESC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '商品推荐表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for product_review
-- ----------------------------
DROP TABLE IF EXISTS `product_review`;
CREATE TABLE `product_review`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '评价ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `order_id` bigint NOT NULL COMMENT '订单ID',
  `rating` tinyint NOT NULL COMMENT '评分(1-5)',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '评价内容',
  `images` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '评价图片(JSON格式)',
  `reply_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '商家回复',
  `reply_time` datetime NULL DEFAULT NULL COMMENT '回复时间',
  `is_anonymous` tinyint NULL DEFAULT 0 COMMENT '是否匿名(0:否 1:是)',
  `like_count` int NULL DEFAULT 0 COMMENT '点赞数',
  `reply_count` int NOT NULL DEFAULT 0 COMMENT '回复数量',
  `is_helpful` tinyint NOT NULL DEFAULT 0 COMMENT '是否有用标记',
  `helpful_count` int NOT NULL DEFAULT 0 COMMENT '有用标记数量',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态(0:隐藏 1:显示)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `review_type` tinyint NULL DEFAULT 1 COMMENT '评价类型：1-商品评价，2-服务评价，3-物流评价',
  `is_additional` tinyint NULL DEFAULT 0 COMMENT '是否追加评价：0-否，1-是',
  `parent_review_id` bigint NULL DEFAULT NULL COMMENT '父评价ID（追加评价时使用）',
  `additional_days` int NULL DEFAULT 0 COMMENT '追加评价距离首次评价天数',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_product_id`(`product_id` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_order_id`(`order_id` ASC) USING BTREE,
  INDEX `idx_rating`(`rating` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  INDEX `idx_product_rating`(`product_id` ASC, `rating` ASC) USING BTREE,
  INDEX `idx_user_created`(`user_id` ASC, `created_at` ASC) USING BTREE,
  INDEX `idx_like_count`(`like_count` ASC) USING BTREE,
  INDEX `idx_helpful_count`(`helpful_count` ASC) USING BTREE,
  INDEX `idx_review_type`(`review_type` ASC, `created_at` ASC) USING BTREE,
  INDEX `idx_parent_review`(`parent_review_id` ASC) USING BTREE,
  INDEX `idx_additional`(`is_additional` ASC, `created_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '商品评价表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for product_view_history
-- ----------------------------
DROP TABLE IF EXISTS `product_view_history`;
CREATE TABLE `product_view_history`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '浏览记录ID',
  `user_id` bigint NULL DEFAULT NULL COMMENT '用户ID(可为空，支持匿名浏览)',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `session_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '会话ID',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '用户代理',
  `view_duration` int NULL DEFAULT 0 COMMENT '浏览时长(秒)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '浏览时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_product_id`(`product_id` ASC) USING BTREE,
  INDEX `idx_session_id`(`session_id` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '商品浏览记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for products
-- ----------------------------
DROP TABLE IF EXISTS `products`;
CREATE TABLE `products`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '产品ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '产品名称',
  `category_id` int NOT NULL COMMENT '分类ID',
  `subcategory` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '子分类名称',
  `product_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '产品编码',
  `unit` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '斤' COMMENT '计量单位',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '产品描述',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_category_id`(`category_id` ASC) USING BTREE,
  INDEX `idx_name`(`name` ASC) USING BTREE,
  INDEX `idx_subcategory`(`subcategory` ASC) USING BTREE,
  CONSTRAINT `products_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 229 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '产品表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for qr_code_generation_task
-- ----------------------------
DROP TABLE IF EXISTS `qr_code_generation_task`;
CREATE TABLE `qr_code_generation_task`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `trace_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '溯源码',
  `product_id` bigint NOT NULL COMMENT '产品ID',
  `status` tinyint NULL DEFAULT 0 COMMENT '生成状态(0:待生成 1:生成中 2:已完成 3:失败)',
  `qr_code_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '生成的二维码URL',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '错误信息',
  `retry_count` int NULL DEFAULT 0 COMMENT '重试次数',
  `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_trace_code`(`trace_code` ASC) USING BTREE,
  INDEX `idx_product_id`(`product_id` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '二维码生成任务表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for quality_report
-- ----------------------------
DROP TABLE IF EXISTS `quality_report`;
CREATE TABLE `quality_report`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `trace_record_id` bigint NOT NULL COMMENT '溯源记录ID',
  `report_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '报告编号',
  `report_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '报告名称',
  `test_organization` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '检测机构',
  `test_date` datetime NOT NULL COMMENT '检测日期',
  `test_items` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '检测项目',
  `test_result` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '检测结果',
  `test_standard` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '检测标准',
  `is_qualified` tinyint(1) NULL DEFAULT 1 COMMENT '是否合格 (0-不合格, 1-合格)',
  `report_file_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '报告文件URL',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '备注',
  `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除 (0-未删除, 1-已删除)',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_trace_record_id`(`trace_record_id` ASC) USING BTREE,
  INDEX `idx_report_number`(`report_number` ASC) USING BTREE,
  INDEX `idx_test_date`(`test_date` ASC) USING BTREE,
  INDEX `idx_is_qualified`(`is_qualified` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  CONSTRAINT `quality_report_ibfk_1` FOREIGN KEY (`trace_record_id`) REFERENCES `traceability_record` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '质检报告表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for recommendation_config
-- ----------------------------
DROP TABLE IF EXISTS `recommendation_config`;
CREATE TABLE `recommendation_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `algorithm_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '算法类型(collaborative_filtering, content_based, hot_products, new_products)',
  `config_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置名称',
  `config_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置值(JSON格式)',
  `weight` decimal(5, 4) NULL DEFAULT 1.0000 COMMENT '算法权重',
  `is_active` tinyint NULL DEFAULT 1 COMMENT '是否启用(0:否 1:是)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_algorithm_config`(`algorithm_type` ASC, `config_name` ASC) USING BTREE,
  INDEX `idx_algorithm_type`(`algorithm_type` ASC) USING BTREE,
  INDEX `idx_is_active`(`is_active` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '推荐算法配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for regions
-- ----------------------------
DROP TABLE IF EXISTS `regions`;
CREATE TABLE `regions`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '地区ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '地区名称',
  `region_code` int NOT NULL COMMENT '惠农网地区编码',
  `parent_id` int NULL DEFAULT NULL COMMENT '父地区ID',
  `level` tinyint NOT NULL COMMENT '地区层级(1省2市3县)',
  `full_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '完整地区名称',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_region_code`(`region_code` ASC) USING BTREE,
  INDEX `idx_parent_id`(`parent_id` ASC) USING BTREE,
  INDEX `idx_level`(`level` ASC) USING BTREE,
  INDEX `idx_name`(`name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 36 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '地区表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for review
-- ----------------------------
DROP TABLE IF EXISTS `review`;
CREATE TABLE `review`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '评价ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `order_id` bigint NOT NULL COMMENT '订单ID',
  `rating` tinyint NOT NULL COMMENT '评分：1-5星',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '评价内容',
  `images` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '评价图片，多个图片用逗号分隔',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态：0-隐藏，1-显示',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_product_id`(`product_id` ASC) USING BTREE,
  INDEX `idx_order_id`(`order_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '评价表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for review_images
-- ----------------------------
DROP TABLE IF EXISTS `review_images`;
CREATE TABLE `review_images`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `review_id` bigint NOT NULL COMMENT '评价ID',
  `image_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '图片URL',
  `image_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '图片名称',
  `image_size` int NULL DEFAULT NULL COMMENT '图片大小(字节)',
  `sort_order` tinyint NOT NULL DEFAULT 0 COMMENT '排序顺序',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_review_id`(`review_id` ASC) USING BTREE,
  INDEX `idx_sort_order`(`review_id` ASC, `sort_order` ASC) USING BTREE,
  CONSTRAINT `fk_review_images_review` FOREIGN KEY (`review_id`) REFERENCES `product_review` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '评价图片表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for review_likes
-- ----------------------------
DROP TABLE IF EXISTS `review_likes`;
CREATE TABLE `review_likes`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `review_id` bigint NOT NULL COMMENT '评价ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_review`(`user_id` ASC, `review_id` ASC) USING BTREE,
  INDEX `idx_review_id`(`review_id` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  CONSTRAINT `fk_review_likes_review` FOREIGN KEY (`review_id`) REFERENCES `product_review` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_review_likes_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '评价点赞表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for review_replies
-- ----------------------------
DROP TABLE IF EXISTS `review_replies`;
CREATE TABLE `review_replies`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `review_id` bigint NOT NULL COMMENT '评价ID',
  `user_id` bigint NOT NULL COMMENT '回复用户ID',
  `parent_id` bigint NULL DEFAULT NULL COMMENT '父回复ID(支持多级回复)',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '回复内容',
  `reply_type` tinyint NOT NULL DEFAULT 1 COMMENT '回复类型:1-用户回复,2-商家回复',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态:0-隐藏,1-正常',
  `like_count` int NOT NULL DEFAULT 0 COMMENT '点赞数量',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_review_id`(`review_id` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_parent_id`(`parent_id` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  CONSTRAINT `fk_review_replies_parent` FOREIGN KEY (`parent_id`) REFERENCES `review_replies` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_review_replies_review` FOREIGN KEY (`review_id`) REFERENCES `product_review` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_review_replies_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '评价回复表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for search_keyword
-- ----------------------------
DROP TABLE IF EXISTS `search_keyword`;
CREATE TABLE `search_keyword`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '关键词ID',
  `keyword` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '搜索关键词',
  `search_count` int NULL DEFAULT 0 COMMENT '搜索次数',
  `result_count` int NULL DEFAULT 0 COMMENT '平均结果数量',
  `click_rate` decimal(5, 4) NULL DEFAULT 0.0000 COMMENT '点击率',
  `category_id` bigint NULL DEFAULT NULL COMMENT '关联分类ID',
  `is_hot` tinyint NULL DEFAULT 0 COMMENT '是否热门关键词(0:否 1:是)',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
  `last_search_at` datetime NULL DEFAULT NULL COMMENT '最后搜索时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_keyword`(`keyword` ASC) USING BTREE,
  INDEX `idx_search_count`(`search_count` ASC) USING BTREE,
  INDEX `idx_is_hot`(`is_hot` ASC) USING BTREE,
  INDEX `idx_category_id`(`category_id` ASC) USING BTREE,
  INDEX `idx_last_search_at`(`last_search_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '搜索关键词统计表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for seller_application
-- ----------------------------
DROP TABLE IF EXISTS `seller_application`;
CREATE TABLE `seller_application`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '申请ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `applicant_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '申请者类型: personal(个人), business(企业)',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '姓名或企业名称',
  `id_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '身份证号或营业执照号',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '联系电话',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '联系邮箱',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '地址',
  `farm_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '农场名称',
  `contact_info` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系信息',
  `qualification_doc_urls` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '资质文档URL列表，以逗号分隔',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '销售描述',
  `certificate_urls` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '证书URL列表，以逗号分隔',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending' COMMENT '状态: pending(待审核), approved(已通过), rejected(已拒绝)',
  `reject_reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '拒绝原因',
  `approved_by` bigint NULL DEFAULT NULL COMMENT '审批人ID',
  `approved_time` datetime NULL DEFAULT NULL COMMENT '审批时间',
  `audit_comment` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '审核意见',
  `auditor_id` bigint NULL DEFAULT NULL COMMENT '审核员ID',
  `audit_date` datetime NULL DEFAULT NULL COMMENT '审核日期',
  `apply_date` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '申请日期',
  `expected_completion_time` datetime NULL DEFAULT NULL COMMENT '预期完成时间',
  `approved_at` datetime NULL DEFAULT NULL COMMENT '批准时间',
  `contact_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系人姓名',
  `contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系人电话',
  `contact_email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系人邮箱',
  `business_license` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '营业执照',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `contact_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `business_scope` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `admin_comment` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `deleted` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE,
  INDEX `idx_seller_application_status`(`status` ASC, `deleted` ASC, `create_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '销售者申请表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for seller_notification
-- ----------------------------
DROP TABLE IF EXISTS `seller_notification`;
CREATE TABLE `seller_notification`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '通知ID',
  `seller_id` bigint NOT NULL COMMENT '销售者用户ID',
  `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '通知类型(order:订单 product:商品 system:系统)',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '通知标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '通知内容',
  `related_id` bigint NULL DEFAULT NULL COMMENT '关联ID(订单ID、商品ID等)',
  `is_read` tinyint NOT NULL DEFAULT 0 COMMENT '是否已读(0:未读 1:已读)',
  `priority` tinyint NOT NULL DEFAULT 1 COMMENT '优先级(1:低 2:中 3:高)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `read_at` datetime NULL DEFAULT NULL COMMENT '阅读时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_seller_id`(`seller_id` ASC) USING BTREE,
  INDEX `idx_type`(`type` ASC) USING BTREE,
  INDEX `idx_is_read`(`is_read` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '销售者通知消息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for seller_product
-- ----------------------------
DROP TABLE IF EXISTS `seller_product`;
CREATE TABLE `seller_product`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `seller_id` bigint NOT NULL COMMENT '销售者ID',
  `product_id` bigint NOT NULL COMMENT '产品ID',
  `seller_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '销售者定价',
  `seller_stock` int NULL DEFAULT 0 COMMENT '销售者库存',
  `commission_rate` decimal(5, 4) NULL DEFAULT 0.0000 COMMENT '佣金比例',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态(0:停售 1:在售 2:审核中)',
  `priority` int NULL DEFAULT 0 COMMENT '优先级(数值越大优先级越高)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_seller_product`(`seller_id` ASC, `product_id` ASC) USING BTREE,
  INDEX `idx_seller_id`(`seller_id` ASC) USING BTREE,
  INDEX `idx_product_id`(`product_id` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  CONSTRAINT `fk_seller_product_product` FOREIGN KEY (`product_id`) REFERENCES `product` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_seller_product_seller` FOREIGN KEY (`seller_id`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '销售者产品关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for seller_shop
-- ----------------------------
DROP TABLE IF EXISTS `seller_shop`;
CREATE TABLE `seller_shop`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '店铺ID',
  `seller_id` bigint NOT NULL COMMENT '销售者用户ID',
  `shop_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '店铺名称',
  `shop_logo` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '店铺Logo',
  `shop_banner` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '店铺横幅',
  `shop_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '店铺描述',
  `business_hours` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '营业时间',
  `contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系电话',
  `contact_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系地址',
  `service_rating` decimal(3, 2) NULL DEFAULT 5.00 COMMENT '服务评分',
  `delivery_rating` decimal(3, 2) NULL DEFAULT 5.00 COMMENT '物流评分',
  `product_rating` decimal(3, 2) NULL DEFAULT 5.00 COMMENT '商品评分',
  `total_sales` decimal(12, 2) NULL DEFAULT 0.00 COMMENT '总销售额',
  `total_orders` int NULL DEFAULT 0 COMMENT '总订单数',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '店铺状态(0:关闭 1:营业)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_seller_id`(`seller_id` ASC) USING BTREE,
  INDEX `idx_shop_name`(`shop_name` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '销售者店铺信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for seller_statistics
-- ----------------------------
DROP TABLE IF EXISTS `seller_statistics`;
CREATE TABLE `seller_statistics`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `seller_id` bigint NOT NULL COMMENT '销售者用户ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `daily_sales` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '日销售额',
  `daily_orders` int NULL DEFAULT 0 COMMENT '日订单数',
  `daily_visitors` int NULL DEFAULT 0 COMMENT '日访客数',
  `daily_views` int NULL DEFAULT 0 COMMENT '日浏览量',
  `monthly_sales` decimal(12, 2) NULL DEFAULT 0.00 COMMENT '月销售额',
  `monthly_orders` int NULL DEFAULT 0 COMMENT '月订单数',
  `yearly_sales` decimal(15, 2) NULL DEFAULT 0.00 COMMENT '年销售额',
  `yearly_orders` int NULL DEFAULT 0 COMMENT '年订单数',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_seller_date`(`seller_id` ASC, `stat_date` ASC) USING BTREE,
  INDEX `idx_seller_id`(`seller_id` ASC) USING BTREE,
  INDEX `idx_stat_date`(`stat_date` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '销售者统计数据表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for shipping_template
-- ----------------------------
DROP TABLE IF EXISTS `shipping_template`;
CREATE TABLE `shipping_template`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模板名称',
  `type` tinyint NOT NULL DEFAULT 1 COMMENT '计费方式(1:按重量 2:按件数 3:按体积)',
  `free_shipping_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '包邮金额',
  `default_areas` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '默认配送区域(JSON格式)',
  `default_first_unit` decimal(8, 3) NOT NULL COMMENT '首重/首件',
  `default_first_fee` decimal(10, 2) NOT NULL COMMENT '首费',
  `default_additional_unit` decimal(8, 3) NOT NULL COMMENT '续重/续件',
  `default_additional_fee` decimal(10, 2) NOT NULL COMMENT '续费',
  `special_areas` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '特殊区域配置(JSON格式)',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_type`(`type` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '运费模板表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for system_configs
-- ----------------------------
DROP TABLE IF EXISTS `system_configs`;
CREATE TABLE `system_configs`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置键',
  `config_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置值',
  `config_type` enum('string','int','float','boolean','json') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'string' COMMENT '配置类型',
  `description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '配置描述',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_config_key`(`config_key` ASC) USING BTREE,
  INDEX `idx_is_active`(`is_active` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '系统配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for system_stats
-- ----------------------------
DROP TABLE IF EXISTS `system_stats`;
CREATE TABLE `system_stats`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `total_users` int NULL DEFAULT 0 COMMENT '总用户数',
  `active_users` int NULL DEFAULT 0 COMMENT '活跃用户数（当日登录）',
  `new_users` int NULL DEFAULT 0 COMMENT '新增用户数',
  `total_products` int NULL DEFAULT 0 COMMENT '总商品数',
  `online_products` int NULL DEFAULT 0 COMMENT '在售商品数',
  `new_products` int NULL DEFAULT 0 COMMENT '新增商品数',
  `total_orders` int NULL DEFAULT 0 COMMENT '总订单数',
  `new_orders` int NULL DEFAULT 0 COMMENT '新增订单数',
  `total_sales` decimal(15, 2) NULL DEFAULT 0.00 COMMENT '总销售额',
  `daily_sales` decimal(15, 2) NULL DEFAULT 0.00 COMMENT '当日销售额',
  `seller_applications` int NULL DEFAULT 0 COMMENT '销售者申请数',
  `pending_applications` int NULL DEFAULT 0 COMMENT '待审核申请数',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_stat_date`(`stat_date` ASC) USING BTREE,
  INDEX `idx_stat_date`(`stat_date` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '系统统计表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for trace_certificates
-- ----------------------------
DROP TABLE IF EXISTS `trace_certificates`;
CREATE TABLE `trace_certificates`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `trace_record_id` bigint NOT NULL COMMENT '关联的溯源记录ID',
  `certificate_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '证书类型：ORGANIC(有机认证)，GREEN(绿色食品)，QUALITY(质量检测)，ORIGIN(原产地认证)',
  `certificate_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '证书编号',
  `issuing_authority` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '颁发机构',
  `issue_date` date NULL DEFAULT NULL COMMENT '颁发日期',
  `valid_until` date NULL DEFAULT NULL COMMENT '有效期至',
  `certificate_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '证书图片URL',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '证书描述',
  `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_trace_record_id`(`trace_record_id` ASC) USING BTREE,
  INDEX `idx_certificate_no`(`certificate_no` ASC) USING BTREE,
  INDEX `idx_trace_certificates_type`(`certificate_type` ASC) USING BTREE,
  CONSTRAINT `fk_certificate_trace_record` FOREIGN KEY (`trace_record_id`) REFERENCES `traceability_record` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '认证信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for trace_code_sequence
-- ----------------------------
DROP TABLE IF EXISTS `trace_code_sequence`;
CREATE TABLE `trace_code_sequence`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '序列ID',
  `date_prefix` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '日期前缀(YYYYMMDD)',
  `sequence_number` int NOT NULL DEFAULT 1 COMMENT '当日序列号',
  `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_date_prefix`(`date_prefix` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '溯源码序列生成表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for trace_codes
-- ----------------------------
DROP TABLE IF EXISTS `trace_codes`;
CREATE TABLE `trace_codes`  (
  `code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '溯源码（主键）',
  `trace_record_id` bigint NOT NULL COMMENT '关联的溯源记录ID',
  `qr_code_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '二维码图片URL',
  `generated_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '生成时间',
  `status` tinyint NULL DEFAULT 1 COMMENT '状态：0-无效，1-有效',
  `scan_count` int NULL DEFAULT 0 COMMENT '扫描次数统计',
  `daily_scan_count` int NULL DEFAULT 0 COMMENT '今日扫描次数',
  `weekly_scan_count` int NULL DEFAULT 0 COMMENT '本周扫描次数',
  `monthly_scan_count` int NULL DEFAULT 0 COMMENT '本月扫描次数',
  `last_scan_time` datetime NULL DEFAULT NULL COMMENT '最后扫描时间',
  `first_scan_time` datetime NULL DEFAULT NULL COMMENT '首次扫描时间',
  PRIMARY KEY (`code`) USING BTREE,
  UNIQUE INDEX `idx_trace_record_id`(`trace_record_id` ASC) USING BTREE,
  CONSTRAINT `fk_trace_code_record` FOREIGN KEY (`trace_record_id`) REFERENCES `traceability_record` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '溯源码表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for trace_logistics
-- ----------------------------
DROP TABLE IF EXISTS `trace_logistics`;
CREATE TABLE `trace_logistics`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `trace_record_id` bigint NOT NULL COMMENT '关联的溯源记录ID',
  `carrier_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '承运商名称',
  `transport_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '运输方式：ROAD(公路)，RAILWAY(铁路)，AIR(空运)，SHIP(水运)',
  `departure_time` datetime NULL DEFAULT NULL COMMENT '出发时间',
  `arrival_time` datetime NULL DEFAULT NULL COMMENT '到达时间',
  `origin` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '始发地',
  `destination` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '目的地',
  `temperature` decimal(5, 2) NULL DEFAULT NULL COMMENT '运输温度（如冷链）',
  `humidity` decimal(5, 2) NULL DEFAULT NULL COMMENT '运输湿度',
  `status` tinyint NULL DEFAULT 0 COMMENT '物流状态：0-待发货，1-运输中，2-已到达，3-已签收',
  `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_trace_record_id`(`trace_record_id` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  CONSTRAINT `fk_logistics_trace_record` FOREIGN KEY (`trace_record_id`) REFERENCES `traceability_record` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '物流信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for traceability_event
-- ----------------------------
DROP TABLE IF EXISTS `traceability_event`;
CREATE TABLE `traceability_event`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '事件ID',
  `trace_record_id` bigint NOT NULL COMMENT '关联溯源记录ID',
  `event_sequence` int NULL DEFAULT NULL COMMENT '事件序号',
  `event_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '事件类型',
  `event_date` datetime NOT NULL COMMENT '事件发生的日期和时间',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '事件的详细描述',
  `location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '事件发生地点',
  `responsible_person` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '事件负责人',
  `attachments` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '附件信息（JSON格式，包含图片和视频URL）',
  `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后更新时间',
  `deleted` tinyint(1) NULL DEFAULT 0 COMMENT '逻辑删除标识',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_trace_record_id`(`trace_record_id` ASC) USING BTREE,
  INDEX `idx_event_date`(`event_date` ASC) USING BTREE,
  INDEX `idx_traceability_event_type_date`(`event_type` ASC, `event_date` ASC) USING BTREE,
  CONSTRAINT `fk_event_trace_record` FOREIGN KEY (`trace_record_id`) REFERENCES `traceability_record` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 275 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '溯源事件详情表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for traceability_query
-- ----------------------------
DROP TABLE IF EXISTS `traceability_query`;
CREATE TABLE `traceability_query`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '查询记录ID',
  `trace_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '查询的溯源码',
  `trace_record_id` bigint NOT NULL COMMENT '关联的溯源记录ID',
  `ip_address` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '查询IP地址',
  `device_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '查询设备信息',
  `location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '查询地理位置',
  `user_id` bigint NULL DEFAULT NULL COMMENT '查询用户ID（可为空，表示未登录用户）',
  `query_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '查询时间',
  `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `deleted` tinyint(1) NULL DEFAULT 0 COMMENT '逻辑删除标识',
  `query_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'user_query' COMMENT '查询类型(user_query:用户查询 admin_verify:管理员验证)',
  `product_id` bigint NULL DEFAULT NULL COMMENT '关联的产品ID（如果查询成功）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_trace_record_id`(`trace_record_id` ASC) USING BTREE,
  INDEX `idx_trace_code`(`trace_code` ASC) USING BTREE,
  INDEX `idx_query_time`(`query_time` ASC) USING BTREE,
  INDEX `idx_traceability_query_time_code`(`query_time` ASC, `trace_code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 66 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '溯源查询记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for traceability_record
-- ----------------------------
DROP TABLE IF EXISTS `traceability_record`;
CREATE TABLE `traceability_record`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '溯源记录ID',
  `product_id` bigint NOT NULL COMMENT '关联产品ID',
  `trace_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '唯一溯源码',
  `product_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '产品名称（冗余字段，方便查询）',
  `farm_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '生产基地/农场名称',
  `producer_id` bigint NOT NULL COMMENT '生产者ID（关联用户表）',
  `producer_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '生产者名称',
  `created_by` bigint NULL DEFAULT NULL COMMENT '记录创建者ID',
  `batch_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '批次号',
  `specification` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '产品规格',
  `quality_grade` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '质量等级',
  `creation_date` date NULL DEFAULT NULL COMMENT '溯源记录创建日期（通常是开始生产的日期）',
  `harvest_date` date NULL DEFAULT NULL COMMENT '采摘/收获日期',
  `packaging_date` date NULL DEFAULT NULL COMMENT '包装日期',
  `qr_code_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '二维码图片URL',
  `status` tinyint NULL DEFAULT 0 COMMENT '溯源记录状态（0:草稿/待完善, 1:待审核, 2:已发布, 3:已下架, 4:异常）',
  `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后更新时间',
  `deleted` tinyint(1) NULL DEFAULT 0 COMMENT '逻辑删除标识',
  `source_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'seller_upload' COMMENT '产品来源类型(admin_direct:产品直购 seller_upload:销售者上传)',
  `production_date` date NULL DEFAULT NULL COMMENT '生产日期',
  `processing_date` date NULL DEFAULT NULL COMMENT '加工日期',
  `certifications` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '认证信息（有机认证、绿色食品认证等）',
  `quality_test_results` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '质量检测结果',
  `pesticides_used` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '农药使用情况',
  `fertilizers_used` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '肥料使用情况',
  `irrigation_method` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '灌溉方式',
  `soil_condition` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '土壤条件',
  `weather_conditions` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '天气条件',
  `harvest_method` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '收获方式',
  `processing_method` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '加工方式',
  `packaging_material` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '包装材料',
  `storage_conditions` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '储存条件',
  `transportation_method` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '运输方式',
  `additional_notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '附加说明',
  `production_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '生产环节信息(JSON格式)',
  `processing_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '加工环节信息(JSON格式)',
  `circulation_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '流通环节信息(JSON格式)',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_trace_code`(`trace_code` ASC) USING BTREE,
  UNIQUE INDEX `uk_product_id`(`product_id` ASC) USING BTREE,
  INDEX `idx_producer_id`(`producer_id` ASC) USING BTREE,
  INDEX `idx_product_id`(`product_id` ASC) USING BTREE,
  INDEX `idx_source_type`(`source_type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 102 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '溯源主记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `nickname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `role` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` int NOT NULL DEFAULT 1,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `deleted` int NOT NULL DEFAULT 0,
  `region` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '所在地区',
  `bio` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '个人简介',
  `address` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `city` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `create_time` datetime(6) NULL DEFAULT NULL,
  `district` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `fans` int NULL DEFAULT NULL,
  `focus` int NULL DEFAULT NULL,
  `gender` int NULL DEFAULT NULL,
  `id_card` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `integral` int NULL DEFAULT NULL,
  `is_real_name_auth` bit(1) NULL DEFAULT NULL,
  `is_vip` bit(1) NULL DEFAULT NULL,
  `last_login_time` datetime(6) NULL DEFAULT NULL,
  `latitude` double NULL DEFAULT NULL,
  `level` int NULL DEFAULT NULL,
  `longitude` double NULL DEFAULT NULL,
  `openid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `province` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `real_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `update_time` datetime(6) NULL DEFAULT NULL,
  `user_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'normal' COMMENT '用户类型',
  `vip_expire_time` datetime(6) NULL DEFAULT NULL,
  `country` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `language` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `session_key` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `total_likes_given` int NOT NULL DEFAULT 0 COMMENT '总点赞数(给出)',
  `total_likes_received` int NOT NULL DEFAULT 0 COMMENT '总点赞数(收到)',
  `total_reviews` int NOT NULL DEFAULT 0 COMMENT '总评价数',
  `total_favorites` int NOT NULL DEFAULT 0 COMMENT '总收藏数',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `username`(`username` ASC) USING BTREE,
  UNIQUE INDEX `UK_bpouh13r5970grppm1u0a5lu`(`openid` ASC) USING BTREE,
  INDEX `idx_user_role_status`(`role` ASC, `user_type` ASC, `status` ASC, `deleted` ASC) USING BTREE,
  INDEX `idx_total_reviews`(`total_reviews` ASC) USING BTREE,
  INDEX `idx_role_status`(`role` ASC, `status` ASC) USING BTREE,
  INDEX `idx_user_stats`(`total_reviews` ASC, `total_likes_given` ASC, `total_favorites` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 40 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user_address
-- ----------------------------
DROP TABLE IF EXISTS `user_address`;
CREATE TABLE `user_address`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '地址ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `receiver_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '收货人姓名',
  `receiver_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '收货人电话',
  `province` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '省份',
  `city` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '城市',
  `district` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '区县',
  `detail_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '详细地址',
  `postal_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '邮政编码',
  `is_default` tinyint NOT NULL DEFAULT 0 COMMENT '是否默认地址(0:否 1:是)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_is_default`(`is_default` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户地址管理表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user_behavior
-- ----------------------------
DROP TABLE IF EXISTS `user_behavior`;
CREATE TABLE `user_behavior`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '行为ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `behavior_type` tinyint NOT NULL COMMENT '行为类型(1:浏览 2:收藏 3:加购物车 4:购买)',
  `behavior_value` decimal(5, 2) NULL DEFAULT 1.00 COMMENT '行为权重值',
  `behavior_duration` int NULL DEFAULT NULL COMMENT '行为持续时间(秒)',
  `session_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '会话ID',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '用户代理',
  `page_source` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '页面来源',
  `referrer` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '引用页面',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_product_id`(`product_id` ASC) USING BTREE,
  INDEX `idx_behavior_type`(`behavior_type` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  INDEX `idx_session_id`(`session_id` ASC) USING BTREE,
  INDEX `idx_user_behavior_time`(`user_id` ASC, `behavior_type` ASC, `created_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 91 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户行为表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user_favorite_folders
-- ----------------------------
DROP TABLE IF EXISTS `user_favorite_folders`;
CREATE TABLE `user_favorite_folders`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `folder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '收藏夹名称',
  `description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '收藏夹描述',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序顺序',
  `is_default` tinyint NULL DEFAULT 0 COMMENT '是否默认收藏夹：0-否，1-是',
  `product_count` int NULL DEFAULT 0 COMMENT '收藏商品数量',
  `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_folder`(`user_id` ASC, `folder_name` ASC) USING BTREE,
  INDEX `idx_user_sort`(`user_id` ASC, `sort_order` ASC) USING BTREE,
  INDEX `idx_user_default`(`user_id` ASC, `is_default` ASC) USING BTREE,
  CONSTRAINT `user_favorite_folders_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 19 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户收藏夹管理表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user_search_history
-- ----------------------------
DROP TABLE IF EXISTS `user_search_history`;
CREATE TABLE `user_search_history`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '搜索历史ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `keyword` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '搜索关键词',
  `result_count` int NULL DEFAULT 0 COMMENT '结果数量',
  `search_filters` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '搜索筛选条件(JSON格式)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_keyword`(`keyword` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 19 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户搜索历史表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- View structure for seller_order_stats
-- ----------------------------
DROP VIEW IF EXISTS `seller_order_stats`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `seller_order_stats` AS select `o`.`seller_id` AS `seller_id`,count(0) AS `total_orders`,count((case when (`o`.`order_status` = 3) then 1 end)) AS `completed_orders`,sum((case when (`o`.`order_status` = 3) then `o`.`actual_amount` else 0 end)) AS `total_revenue`,avg((case when (`o`.`order_status` = 3) then `o`.`actual_amount` end)) AS `avg_order_value` from `order` `o` where (`o`.`deleted` = 0) group by `o`.`seller_id`;

-- ----------------------------
-- View structure for seller_product_stats
-- ----------------------------
DROP VIEW IF EXISTS `seller_product_stats`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `seller_product_stats` AS select `p`.`seller_id` AS `seller_id`,count(0) AS `total_products`,count((case when (`p`.`status` = 1) then 1 end)) AS `active_products`,count((case when (`p`.`stock` > 0) then 1 end)) AS `in_stock_products`,avg(`p`.`rating`) AS `avg_rating`,sum(`p`.`sales_count`) AS `total_sales_count`,sum(`p`.`view_count`) AS `total_views`,sum(`p`.`favorite_count`) AS `total_favorites` from `product` `p` where (`p`.`deleted` = 0) group by `p`.`seller_id`;

-- ----------------------------
-- View structure for v_admin_dashboard_stats
-- ----------------------------
DROP VIEW IF EXISTS `v_admin_dashboard_stats`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_admin_dashboard_stats` AS select (select count(0) from `user` where (`user`.`deleted` = 0)) AS `total_users`,(select count(0) from `user` where (((`user`.`role` = 'seller') or (`user`.`user_type` = 'seller')) and (`user`.`deleted` = 0))) AS `total_sellers`,(select count(0) from `user` where ((`user`.`role` = 'admin') and (`user`.`deleted` = 0))) AS `total_admins`,(select count(0) from `product` where (`product`.`deleted` = 0)) AS `total_products`,(select count(0) from `product` where ((`product`.`status` = 1) and (`product`.`deleted` = 0))) AS `active_products`,(select count(0) from `order` where (`order`.`deleted` = 0)) AS `total_orders`,(select count(0) from `order` where ((`order`.`order_status` = 3) and (`order`.`deleted` = 0))) AS `completed_orders`,(select coalesce(sum(`order`.`actual_amount`),0) from `order` where ((`order`.`order_status` = 3) and (`order`.`deleted` = 0))) AS `total_revenue`,(select count(0) from `seller_application` where ((`seller_application`.`status` = 0) and (`seller_application`.`deleted` = 0))) AS `pending_applications`,(select count(0) from `seller_application` where ((`seller_application`.`status` = 1) and (`seller_application`.`deleted` = 0))) AS `approved_applications`,(select count(0) from `seller_application` where ((`seller_application`.`status` = 2) and (`seller_application`.`deleted` = 0))) AS `rejected_applications`,(select count(0) from `user` where ((cast(`user`.`created_at` as date) = curdate()) and (`user`.`deleted` = 0))) AS `today_new_users`,(select count(0) from `order` where ((cast(`order`.`created_at` as date) = curdate()) and (`order`.`deleted` = 0))) AS `today_new_orders`,(select coalesce(sum(`order`.`actual_amount`),0) from `order` where ((cast(`order`.`created_at` as date) = curdate()) and (`order`.`order_status` = 3) and (`order`.`deleted` = 0))) AS `today_revenue`;

-- ----------------------------
-- View structure for v_data_consistency_check
-- ----------------------------
DROP VIEW IF EXISTS `v_data_consistency_check`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_data_consistency_check` AS select 'user_role_inconsistency' AS `check_type`,'用户角色不一致' AS `check_description`,count(0) AS `issue_count`,json_arrayagg(json_object('user_id',`user`.`id`,'username',`user`.`username`,'role',`user`.`role`,'user_type',`user`.`user_type`)) AS `issue_details` from `user` where ((`user`.`role` <> coalesce(`user`.`user_type`,'user')) and (`user`.`deleted` = 0)) union all select 'orphaned_products' AS `check_type`,'孤立商品记录' AS `check_description`,count(0) AS `issue_count`,json_arrayagg(json_object('product_id',`p`.`id`,'product_name',`p`.`name`,'seller_id',`p`.`seller_id`)) AS `issue_details` from (`product` `p` left join `user` `u` on((`p`.`seller_id` = `u`.`id`))) where ((`u`.`id` is null) and (`p`.`deleted` = 0)) union all select 'invalid_orders' AS `check_type`,'无效订单记录' AS `check_description`,count(0) AS `issue_count`,json_arrayagg(json_object('order_id',`o`.`id`,'order_no',`o`.`order_no`,'user_id',`o`.`user_id`,'seller_id',`o`.`seller_id`)) AS `issue_details` from ((`order` `o` left join `user` `buyer` on((`o`.`user_id` = `buyer`.`id`))) left join `user` `seller` on((`o`.`seller_id` = `seller`.`id`))) where (((`buyer`.`id` is null) or (`seller`.`id` is null)) and (`o`.`deleted` = 0));

-- ----------------------------
-- View structure for v_latest_prices
-- ----------------------------
DROP VIEW IF EXISTS `v_latest_prices`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_latest_prices` AS select `pd`.`id` AS `id`,`p`.`name` AS `product_name`,`c`.`name` AS `category_name`,`r`.`full_name` AS `region_name`,`pd`.`price` AS `price`,`pd`.`unit` AS `unit`,`pd`.`price_change` AS `price_change`,`pd`.`price_trend` AS `price_trend`,`pd`.`data_date` AS `data_date`,`pd`.`crawl_time` AS `crawl_time` from (((`price_data` `pd` join `products` `p` on((`pd`.`product_id` = `p`.`id`))) join `categories` `c` on((`p`.`category_id` = `c`.`id`))) join `regions` `r` on((`pd`.`region_id` = `r`.`id`))) where (`pd`.`data_date` = (select max(`pd2`.`data_date`) from `price_data` `pd2` where ((`pd2`.`product_id` = `pd`.`product_id`) and (`pd2`.`region_id` = `pd`.`region_id`))));

-- ----------------------------
-- View structure for v_monthly_trends
-- ----------------------------
DROP VIEW IF EXISTS `v_monthly_trends`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_monthly_trends` AS select date_format(`combined_stats`.`stat_month`,'%Y-%m') AS `month_label`,`combined_stats`.`stat_month` AS `stat_month`,`combined_stats`.`new_users` AS `new_users`,`combined_stats`.`new_sellers` AS `new_sellers`,`combined_stats`.`new_products` AS `new_products`,`combined_stats`.`new_orders` AS `new_orders`,`combined_stats`.`monthly_revenue` AS `monthly_revenue`,lag(`combined_stats`.`new_users`) OVER (ORDER BY `combined_stats`.`stat_month` )  AS `prev_users`,lag(`combined_stats`.`monthly_revenue`) OVER (ORDER BY `combined_stats`.`stat_month` )  AS `prev_revenue` from (select date_format(`user`.`created_at`,'%Y-%m-01') AS `stat_month`,count((case when ((`user`.`role` <> 'seller') and (`user`.`user_type` <> 'seller')) then 1 end)) AS `new_users`,count((case when ((`user`.`role` = 'seller') or (`user`.`user_type` = 'seller')) then 1 end)) AS `new_sellers`,0 AS `new_products`,0 AS `new_orders`,0 AS `monthly_revenue` from `user` where ((`user`.`deleted` = 0) and (`user`.`created_at` >= (now() - interval 12 month))) group by date_format(`user`.`created_at`,'%Y-%m') union all select date_format(`product`.`created_at`,'%Y-%m-01') AS `stat_month`,0 AS `new_users`,0 AS `new_sellers`,count(0) AS `new_products`,0 AS `new_orders`,0 AS `monthly_revenue` from `product` where ((`product`.`deleted` = 0) and (`product`.`created_at` >= (now() - interval 12 month))) group by date_format(`product`.`created_at`,'%Y-%m') union all select date_format(`order`.`created_at`,'%Y-%m-01') AS `stat_month`,0 AS `new_users`,0 AS `new_sellers`,0 AS `new_products`,count(0) AS `new_orders`,sum((case when (`order`.`order_status` = 3) then `order`.`actual_amount` else 0 end)) AS `monthly_revenue` from `order` where ((`order`.`deleted` = 0) and (`order`.`created_at` >= (now() - interval 12 month))) group by date_format(`order`.`created_at`,'%Y-%m')) `combined_stats` group by `combined_stats`.`stat_month` order by `combined_stats`.`stat_month` desc;

-- ----------------------------
-- View structure for v_order_analysis
-- ----------------------------
DROP VIEW IF EXISTS `v_order_analysis`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_order_analysis` AS select `o`.`id` AS `order_id`,`o`.`order_no` AS `order_no`,`o`.`user_id` AS `buyer_id`,`buyer`.`username` AS `buyer_name`,`buyer`.`phone` AS `buyer_phone`,`o`.`seller_id` AS `seller_id`,`seller`.`username` AS `seller_name`,`ss`.`shop_name` AS `shop_name`,`o`.`total_amount` AS `total_amount`,`o`.`actual_amount` AS `actual_amount`,`o`.`order_status` AS `order_status`,(case `o`.`order_status` when 0 then '待付款' when 1 then '待发货' when 2 then '已发货' when 3 then '已完成' when 4 then '已取消' else '未知' end) AS `status_text`,`o`.`payment_status` AS `payment_status`,(case `o`.`payment_status` when 0 then '未付款' when 1 then '已付款' when 2 then '已退款' else '未知' end) AS `payment_text`,`o`.`created_at` AS `order_time`,`o`.`payment_time` AS `payment_time`,`o`.`delivery_time` AS `delivery_time`,`o`.`completion_time` AS `completion_time`,(case when ((`o`.`order_status` = 0) and (timestampdiff(HOUR,`o`.`created_at`,now()) > 24)) then 1 when ((`o`.`order_status` = 1) and (timestampdiff(HOUR,`o`.`created_at`,now()) > 72)) then 1 else 0 end) AS `is_delayed`,timestampdiff(HOUR,`o`.`created_at`,coalesce(`o`.`completion_time`,now())) AS `processing_hours`,`oi`.`item_count` AS `item_count`,`oi`.`product_names` AS `product_names` from ((((`order` `o` left join `user` `buyer` on((`o`.`user_id` = `buyer`.`id`))) left join `user` `seller` on((`o`.`seller_id` = `seller`.`id`))) left join `seller_shop` `ss` on((`o`.`seller_id` = `ss`.`seller_id`))) left join (select `order_item`.`order_id` AS `order_id`,count(0) AS `item_count`,group_concat(`order_item`.`product_name` separator ', ') AS `product_names` from `order_item` group by `order_item`.`order_id`) `oi` on((`o`.`id` = `oi`.`order_id`))) where (`o`.`deleted` = 0);

-- ----------------------------
-- View structure for v_performance_metrics
-- ----------------------------
DROP VIEW IF EXISTS `v_performance_metrics`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_performance_metrics` AS select 'database' AS `metric_category`,'table_sizes' AS `metric_name`,json_object('user_count',(select count(0) from `user` where (`user`.`deleted` = 0)),'product_count',(select count(0) from `product` where (`product`.`deleted` = 0)),'order_count',(select count(0) from `order` where (`order`.`deleted` = 0)),'seller_application_count',(select count(0) from `seller_application` where (`seller_application`.`deleted` = 0))) AS `metric_value`,now() AS `measured_at` union all select 'business' AS `metric_category`,'daily_metrics' AS `metric_name`,json_object('daily_orders',(select count(0) from `order` where ((cast(`order`.`created_at` as date) = curdate()) and (`order`.`deleted` = 0))),'daily_revenue',(select coalesce(sum(`order`.`actual_amount`),0) from `order` where ((cast(`order`.`created_at` as date) = curdate()) and (`order`.`order_status` = 3) and (`order`.`deleted` = 0))),'daily_new_users',(select count(0) from `user` where ((cast(`user`.`created_at` as date) = curdate()) and (`user`.`deleted` = 0))),'daily_new_products',(select count(0) from `product` where ((cast(`product`.`created_at` as date) = curdate()) and (`product`.`deleted` = 0)))) AS `metric_value`,now() AS `measured_at`;

-- ----------------------------
-- View structure for v_product_competitiveness
-- ----------------------------
DROP VIEW IF EXISTS `v_product_competitiveness`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_product_competitiveness` AS select `p`.`id` AS `product_id`,`p`.`name` AS `product_name`,`p`.`category_id` AS `category_id`,`c`.`name` AS `category_name`,`p`.`price` AS `price`,`p`.`sales_count` AS `sales_count`,`p`.`rating` AS `rating`,`p`.`view_count` AS `view_count`,`p`.`favorite_count` AS `favorite_count`,(case when (`p`.`price` <= (`cat_stats`.`avg_price` * 0.8)) then '价格优势' when (`p`.`price` <= (`cat_stats`.`avg_price` * 1.2)) then '价格适中' else '价格偏高' end) AS `price_competitiveness`,rank() OVER (PARTITION BY `p`.`category_id` ORDER BY `p`.`sales_count` desc )  AS `sales_rank_in_category`,rank() OVER (PARTITION BY `p`.`category_id` ORDER BY `p`.`rating` desc )  AS `rating_rank_in_category`,((((coalesce(`p`.`sales_count`,0) * 0.4) + ((coalesce(`p`.`rating`,0) * 20) * 0.3)) + (coalesce(`p`.`view_count`,0) * 0.2)) + (coalesce(`p`.`favorite_count`,0) * 0.1)) AS `competitiveness_score` from ((`product` `p` left join `category` `c` on((`p`.`category_id` = `c`.`id`))) left join (select `product`.`category_id` AS `category_id`,avg(`product`.`price`) AS `avg_price`,avg(`product`.`sales_count`) AS `avg_sales`,avg(`product`.`rating`) AS `avg_rating` from `product` where ((`product`.`deleted` = 0) and (`product`.`status` = 1)) group by `product`.`category_id`) `cat_stats` on((`p`.`category_id` = `cat_stats`.`category_id`))) where ((`p`.`deleted` = 0) and (`p`.`status` = 1));

-- ----------------------------
-- View structure for v_product_performance
-- ----------------------------
DROP VIEW IF EXISTS `v_product_performance`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_product_performance` AS select `p`.`id` AS `product_id`,`p`.`name` AS `product_name`,`p`.`seller_id` AS `seller_id`,`u`.`username` AS `seller_name`,`ss`.`shop_name` AS `shop_name`,`p`.`category_id` AS `category_id`,`c`.`name` AS `category_name`,`p`.`price` AS `price`,`p`.`original_price` AS `original_price`,`p`.`stock` AS `stock`,`p`.`sales_count` AS `sales_count`,`p`.`view_count` AS `view_count`,coalesce(`pf`.`favorite_count`,0) AS `favorite_count`,`p`.`rating` AS `rating`,`p`.`review_count` AS `review_count`,`p`.`status` AS `status`,(case `p`.`status` when 0 then '下架' when 1 then '上架' else '未知' end) AS `status_text`,`p`.`created_at` AS `created_at`,`p`.`updated_at` AS `updated_at`,coalesce(`oi`.`order_count`,0) AS `order_count`,coalesce(`oi`.`total_revenue`,0) AS `total_revenue`,(case when (`p`.`stock` <= 10) then '库存不足' when (`p`.`stock` <= 50) then '库存偏低' else '库存充足' end) AS `stock_status`,(case when (`p`.`sales_count` >= 100) then '热销' when (`p`.`sales_count` >= 50) then '畅销' when (`p`.`sales_count` >= 10) then '一般' else '滞销' end) AS `sales_level` from (((((`product` `p` left join `user` `u` on((`p`.`seller_id` = `u`.`id`))) left join `seller_shop` `ss` on((`p`.`seller_id` = `ss`.`seller_id`))) left join `category` `c` on((`p`.`category_id` = `c`.`id`))) left join (select `product_favorite`.`product_id` AS `product_id`,count(0) AS `favorite_count` from `product_favorite` group by `product_favorite`.`product_id`) `pf` on((`p`.`id` = `pf`.`product_id`))) left join (select `order_item`.`product_id` AS `product_id`,count(distinct `order_item`.`order_id`) AS `order_count`,sum((`order_item`.`quantity` * `order_item`.`product_price`)) AS `total_revenue` from `order_item` group by `order_item`.`product_id`) `oi` on((`p`.`id` = `oi`.`product_id`))) where (`p`.`deleted` = 0);

-- ----------------------------
-- View structure for v_product_traceability
-- ----------------------------
DROP VIEW IF EXISTS `v_product_traceability`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_product_traceability` AS select `p`.`id` AS `product_id`,`p`.`name` AS `product_name`,`p`.`seller_id` AS `seller_id`,`p`.`has_traceability` AS `has_traceability`,`p`.`trace_code` AS `trace_code`,`p`.`qr_code_url` AS `product_qr_url`,`tr`.`id` AS `trace_record_id`,`tr`.`farm_name` AS `farm_name`,`tr`.`producer_name` AS `producer_name`,`tr`.`batch_number` AS `batch_number`,`tr`.`specification` AS `specification`,`tr`.`quality_grade` AS `quality_grade`,`tr`.`creation_date` AS `creation_date`,`tr`.`harvest_date` AS `harvest_date`,`tr`.`packaging_date` AS `packaging_date`,`tr`.`status` AS `trace_status`,`tc`.`scan_count` AS `scan_count`,`tc`.`last_scan_time` AS `last_scan_time`,`tc`.`qr_code_url` AS `trace_qr_url`,count(`te`.`id`) AS `event_count`,count(`tcert`.`id`) AS `certificate_count` from ((((`product` `p` left join `traceability_record` `tr` on(((`p`.`id` = `tr`.`product_id`) and (`tr`.`deleted` = 0)))) left join `trace_codes` `tc` on((`tr`.`trace_code` = `tc`.`code`))) left join `traceability_event` `te` on(((`tr`.`id` = `te`.`trace_record_id`) and (`te`.`deleted` = 0)))) left join `trace_certificates` `tcert` on((`tr`.`id` = `tcert`.`trace_record_id`))) where (`p`.`deleted` = 0) group by `p`.`id`,`p`.`name`,`p`.`seller_id`,`p`.`has_traceability`,`p`.`trace_code`,`p`.`qr_code_url`,`tr`.`id`,`tr`.`farm_name`,`tr`.`producer_name`,`tr`.`batch_number`,`tr`.`specification`,`tr`.`quality_grade`,`tr`.`creation_date`,`tr`.`harvest_date`,`tr`.`packaging_date`,`tr`.`status`,`tc`.`scan_count`,`tc`.`last_scan_time`,`tc`.`qr_code_url`;

-- ----------------------------
-- View structure for v_seller_application_stats
-- ----------------------------
DROP VIEW IF EXISTS `v_seller_application_stats`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_seller_application_stats` AS select `sa`.`id` AS `application_id`,`sa`.`user_id` AS `user_id`,`u`.`username` AS `username`,`u`.`phone` AS `phone`,`u`.`email` AS `email`,`sa`.`applicant_type` AS `applicant_type`,`sa`.`name` AS `applicant_name`,`sa`.`id_number` AS `id_number`,`sa`.`business_license` AS `business_license`,`sa`.`contact_phone` AS `contact_phone`,`sa`.`contact_address` AS `contact_address`,`sa`.`business_scope` AS `business_scope`,`sa`.`status` AS `application_status`,(case `sa`.`status` when 0 then '待审核' when 1 then '已通过' when 2 then '已拒绝' else '未知' end) AS `status_text`,`sa`.`admin_comment` AS `admin_comment`,`sa`.`create_time` AS `application_time`,`sa`.`update_time` AS `last_update_time`,(to_days(now()) - to_days(`sa`.`create_time`)) AS `days_since_application`,(case when ((`sa`.`status` = 0) and ((to_days(now()) - to_days(`sa`.`create_time`)) > 7)) then 1 else 0 end) AS `is_overdue` from (`seller_application` `sa` left join `user` `u` on((`sa`.`user_id` = `u`.`id`))) where (`sa`.`deleted` = 0) order by `sa`.`create_time` desc;

-- ----------------------------
-- View structure for v_seller_basic_stats
-- ----------------------------
DROP VIEW IF EXISTS `v_seller_basic_stats`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_seller_basic_stats` AS select `u`.`id` AS `seller_id`,`u`.`username` AS `username`,`u`.`nickname` AS `nickname`,`u`.`phone` AS `phone`,`u`.`email` AS `email`,`u`.`role` AS `role`,`u`.`user_type` AS `user_type`,`u`.`status` AS `user_status`,`u`.`created_at` AS `register_time`,`ss`.`shop_name` AS `shop_name`,`ss`.`shop_description` AS `shop_description`,`ss`.`business_hours` AS `business_hours`,`ss`.`contact_phone` AS `shop_phone`,`ss`.`contact_address` AS `contact_address`,`ss`.`service_rating` AS `service_rating`,`ss`.`delivery_rating` AS `delivery_rating`,`ss`.`product_rating` AS `product_rating`,`ss`.`total_sales` AS `shop_total_sales`,`ss`.`total_orders` AS `shop_total_orders`,`ss`.`status` AS `shop_status`,`ss`.`created_at` AS `shop_created_at`,coalesce(`ps`.`total_products`,0) AS `total_products`,coalesce(`ps`.`active_products`,0) AS `active_products`,coalesce(`ps`.`avg_rating`,0) AS `avg_product_rating`,coalesce(`os`.`pending_orders`,0) AS `pending_orders`,coalesce(`os`.`completed_orders`,0) AS `completed_orders`,coalesce(`os`.`total_revenue`,0) AS `total_revenue`,coalesce(`os`.`avg_order_value`,0) AS `avg_order_value` from (((`user` `u` left join `seller_shop` `ss` on((`u`.`id` = `ss`.`seller_id`))) left join (select `product`.`seller_id` AS `seller_id`,count(0) AS `total_products`,count((case when (`product`.`status` = 1) then 1 end)) AS `active_products`,avg(`product`.`rating`) AS `avg_rating` from `product` where (`product`.`deleted` = 0) group by `product`.`seller_id`) `ps` on((`u`.`id` = `ps`.`seller_id`))) left join (select `order`.`seller_id` AS `seller_id`,count((case when (`order`.`order_status` in (0,1)) then 1 end)) AS `pending_orders`,count((case when (`order`.`order_status` = 3) then 1 end)) AS `completed_orders`,sum((case when (`order`.`order_status` = 3) then `order`.`actual_amount` else 0 end)) AS `total_revenue`,avg((case when (`order`.`order_status` = 3) then `order`.`actual_amount` end)) AS `avg_order_value` from `order` where (`order`.`deleted` = 0) group by `order`.`seller_id`) `os` on((`u`.`id` = `os`.`seller_id`))) where (((`u`.`role` = 'seller') or (`u`.`user_type` = 'seller')) and (`u`.`deleted` = 0));

-- ----------------------------
-- View structure for v_seller_performance_score
-- ----------------------------
DROP VIEW IF EXISTS `v_seller_performance_score`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_seller_performance_score` AS select `v_seller_basic_stats`.`seller_id` AS `seller_id`,`v_seller_basic_stats`.`username` AS `username`,`v_seller_basic_stats`.`shop_name` AS `shop_name`,coalesce((`v_seller_basic_stats`.`avg_product_rating` * 6),0) AS `product_quality_score`,coalesce(((`v_seller_basic_stats`.`service_rating` + `v_seller_basic_stats`.`delivery_rating`) * 2.5),0) AS `service_quality_score`,least(coalesce(((`v_seller_basic_stats`.`total_revenue` / 10000) * 25),0),25) AS `sales_performance_score`,coalesce((`v_seller_basic_stats`.`product_rating` * 4),0) AS `customer_satisfaction_score`,(((coalesce((`v_seller_basic_stats`.`avg_product_rating` * 6),0) + coalesce(((`v_seller_basic_stats`.`service_rating` + `v_seller_basic_stats`.`delivery_rating`) * 2.5),0)) + least(coalesce(((`v_seller_basic_stats`.`total_revenue` / 10000) * 25),0),25)) + coalesce((`v_seller_basic_stats`.`product_rating` * 4),0)) AS `total_score`,(case when ((((coalesce((`v_seller_basic_stats`.`avg_product_rating` * 6),0) + coalesce(((`v_seller_basic_stats`.`service_rating` + `v_seller_basic_stats`.`delivery_rating`) * 2.5),0)) + least(coalesce(((`v_seller_basic_stats`.`total_revenue` / 10000) * 25),0),25)) + coalesce((`v_seller_basic_stats`.`product_rating` * 4),0)) >= 90) then 'A+' when ((((coalesce((`v_seller_basic_stats`.`avg_product_rating` * 6),0) + coalesce(((`v_seller_basic_stats`.`service_rating` + `v_seller_basic_stats`.`delivery_rating`) * 2.5),0)) + least(coalesce(((`v_seller_basic_stats`.`total_revenue` / 10000) * 25),0),25)) + coalesce((`v_seller_basic_stats`.`product_rating` * 4),0)) >= 80) then 'A' when ((((coalesce((`v_seller_basic_stats`.`avg_product_rating` * 6),0) + coalesce(((`v_seller_basic_stats`.`service_rating` + `v_seller_basic_stats`.`delivery_rating`) * 2.5),0)) + least(coalesce(((`v_seller_basic_stats`.`total_revenue` / 10000) * 25),0),25)) + coalesce((`v_seller_basic_stats`.`product_rating` * 4),0)) >= 70) then 'B' when ((((coalesce((`v_seller_basic_stats`.`avg_product_rating` * 6),0) + coalesce(((`v_seller_basic_stats`.`service_rating` + `v_seller_basic_stats`.`delivery_rating`) * 2.5),0)) + least(coalesce(((`v_seller_basic_stats`.`total_revenue` / 10000) * 25),0),25)) + coalesce((`v_seller_basic_stats`.`product_rating` * 4),0)) >= 60) then 'C' else 'D' end) AS `performance_grade` from `v_seller_basic_stats` where (`v_seller_basic_stats`.`total_products` > 0);

-- ----------------------------
-- View structure for v_system_alerts
-- ----------------------------
DROP VIEW IF EXISTS `v_system_alerts`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_system_alerts` AS select 'low_stock' AS `alert_type`,'库存预警' AS `alert_title`,concat('商品 "',`p`.`name`,'" 库存不足，当前库存: ',`p`.`stock`) AS `alert_message`,'warning' AS `alert_level`,`p`.`seller_id` AS `related_user_id`,`p`.`id` AS `related_entity_id`,'product' AS `related_entity_type`,now() AS `alert_time` from `product` `p` where ((`p`.`stock` <= 10) and (`p`.`status` = 1) and (`p`.`deleted` = 0)) union all select 'pending_order' AS `alert_type`,'订单处理预警' AS `alert_title`,concat('订单 "',`o`.`order_no`,'" 待处理时间过长') AS `alert_message`,'error' AS `alert_level`,`o`.`seller_id` AS `related_user_id`,`o`.`id` AS `related_entity_id`,'order' AS `related_entity_type`,now() AS `alert_time` from `order` `o` where ((`o`.`order_status` = 1) and (timestampdiff(HOUR,`o`.`created_at`,now()) > 72) and (`o`.`deleted` = 0)) union all select 'pending_application' AS `alert_type`,'申请审核预警' AS `alert_title`,concat('销售者申请 "',`sa`.`name`,'" 待审核时间过长') AS `alert_message`,'info' AS `alert_level`,`sa`.`user_id` AS `related_user_id`,`sa`.`id` AS `related_entity_id`,'seller_application' AS `related_entity_type`,now() AS `alert_time` from `seller_application` `sa` where ((`sa`.`status` = 0) and ((to_days(now()) - to_days(`sa`.`create_time`)) > 7) and (`sa`.`deleted` = 0));

-- ----------------------------
-- View structure for v_system_realtime_status
-- ----------------------------
DROP VIEW IF EXISTS `v_system_realtime_status`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_system_realtime_status` AS select 'users' AS `metric_type`,'online_users' AS `metric_name`,count(0) AS `metric_value`,now() AS `last_updated` from `user` where ((`user`.`last_login_time` >= (now() - interval 30 minute)) and (`user`.`deleted` = 0)) union all select 'orders' AS `metric_type`,'pending_orders' AS `metric_name`,count(0) AS `metric_value`,now() AS `last_updated` from `order` where ((`order`.`order_status` in (0,1)) and (`order`.`deleted` = 0)) union all select 'products' AS `metric_type`,'low_stock_products' AS `metric_name`,count(0) AS `metric_value`,now() AS `last_updated` from `product` where ((`product`.`stock` <= 10) and (`product`.`status` = 1) and (`product`.`deleted` = 0)) union all select 'applications' AS `metric_type`,'pending_applications' AS `metric_name`,count(0) AS `metric_value`,now() AS `last_updated` from `seller_application` where ((`seller_application`.`status` = 0) and (`seller_application`.`deleted` = 0));

-- ----------------------------
-- View structure for v_traceability_stats
-- ----------------------------
DROP VIEW IF EXISTS `v_traceability_stats`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_traceability_stats` AS select `tr`.`id` AS `record_id`,`tr`.`product_id` AS `product_id`,`tr`.`trace_code` AS `trace_code`,`tr`.`product_name` AS `product_name`,`tr`.`producer_name` AS `producer_name`,`tr`.`farm_name` AS `farm_name`,`tc`.`scan_count` AS `scan_count`,`tc`.`daily_scan_count` AS `daily_scan_count`,`tc`.`weekly_scan_count` AS `weekly_scan_count`,`tc`.`monthly_scan_count` AS `monthly_scan_count`,`tc`.`first_scan_time` AS `first_scan_time`,`tc`.`last_scan_time` AS `last_scan_time`,`tc`.`qr_code_url` AS `qr_code_url`,count(`tq`.`id`) AS `total_queries`,count(distinct `tq`.`ip_address`) AS `unique_visitors`,`tr`.`status` AS `record_status`,`tr`.`created_at` AS `record_created_at` from ((`traceability_record` `tr` left join `trace_codes` `tc` on((`tr`.`trace_code` = `tc`.`code`))) left join `traceability_query` `tq` on(((`tr`.`trace_code` = `tq`.`trace_code`) and (`tq`.`deleted` = 0)))) where (`tr`.`deleted` = 0) group by `tr`.`id`,`tr`.`product_id`,`tr`.`trace_code`,`tr`.`product_name`,`tr`.`producer_name`,`tr`.`farm_name`,`tc`.`scan_count`,`tc`.`daily_scan_count`,`tc`.`weekly_scan_count`,`tc`.`monthly_scan_count`,`tc`.`first_scan_time`,`tc`.`last_scan_time`,`tc`.`qr_code_url`,`tr`.`status`,`tr`.`created_at`;

-- ----------------------------
-- View structure for v_user_behavior_analysis
-- ----------------------------
DROP VIEW IF EXISTS `v_user_behavior_analysis`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_user_behavior_analysis` AS select `u`.`id` AS `user_id`,`u`.`username` AS `username`,`u`.`role` AS `role`,coalesce(`order_stats`.`total_orders`,0) AS `total_orders`,coalesce(`order_stats`.`total_spent`,0) AS `total_spent`,coalesce(`order_stats`.`avg_order_value`,0) AS `avg_order_value`,coalesce(`fav_stats`.`favorite_count`,0) AS `favorite_count`,coalesce(`view_stats`.`view_count`,0) AS `view_count`,`u`.`last_login_time` AS `last_login_time`,(to_days(now()) - to_days(`u`.`last_login_time`)) AS `days_since_last_login`,(case when ((to_days(now()) - to_days(`u`.`last_login_time`)) <= 7) then '活跃用户' when ((to_days(now()) - to_days(`u`.`last_login_time`)) <= 30) then '一般用户' when ((to_days(now()) - to_days(`u`.`last_login_time`)) <= 90) then '不活跃用户' else '流失用户' end) AS `user_activity_level`,(case when (coalesce(`order_stats`.`total_spent`,0) >= 10000) then 'VIP客户' when (coalesce(`order_stats`.`total_spent`,0) >= 5000) then '高价值客户' when (coalesce(`order_stats`.`total_spent`,0) >= 1000) then '中等客户' when (coalesce(`order_stats`.`total_spent`,0) > 0) then '普通客户' else '潜在客户' end) AS `customer_value_level` from (((`user` `u` left join (select `order`.`user_id` AS `user_id`,count(0) AS `total_orders`,sum(`order`.`actual_amount`) AS `total_spent`,avg(`order`.`actual_amount`) AS `avg_order_value` from `order` where ((`order`.`order_status` = 3) and (`order`.`deleted` = 0)) group by `order`.`user_id`) `order_stats` on((`u`.`id` = `order_stats`.`user_id`))) left join (select `product_favorite`.`user_id` AS `user_id`,count(0) AS `favorite_count` from `product_favorite` group by `product_favorite`.`user_id`) `fav_stats` on((`u`.`id` = `fav_stats`.`user_id`))) left join (select `product_view_history`.`user_id` AS `user_id`,count(0) AS `view_count` from `product_view_history` where (`product_view_history`.`created_at` >= (now() - interval 30 day)) group by `product_view_history`.`user_id`) `view_stats` on((`u`.`id` = `view_stats`.`user_id`))) where ((`u`.`deleted` = 0) and (`u`.`role` <> 'admin'));

-- ----------------------------
-- View structure for v_user_permission_stats
-- ----------------------------
DROP VIEW IF EXISTS `v_user_permission_stats`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_user_permission_stats` AS select `u`.`id` AS `user_id`,`u`.`username` AS `username`,`u`.`nickname` AS `nickname`,`u`.`phone` AS `phone`,`u`.`email` AS `email`,`u`.`role` AS `role`,`u`.`user_type` AS `user_type`,`u`.`status` AS `status`,(case `u`.`status` when 0 then '禁用' when 1 then '正常' else '未知' end) AS `status_text`,`u`.`created_at` AS `register_time`,`u`.`last_login_time` AS `last_login_time`,(to_days(now()) - to_days(`u`.`last_login_time`)) AS `days_since_last_login`,(case when (`u`.`role` = 'admin') then '管理员' when ((`u`.`role` = 'seller') or (`u`.`user_type` = 'seller')) then '销售者' else '普通用户' end) AS `role_text`,(case when (`u`.`role` <> coalesce(`u`.`user_type`,'user')) then 1 else 0 end) AS `role_inconsistent`,coalesce(`sa`.`application_status`,-(1)) AS `seller_application_status`,(case coalesce(`sa`.`application_status`,-(1)) when -(1) then '未申请' when 0 then '申请中' when 1 then '已通过' when 2 then '已拒绝' else '未知' end) AS `application_status_text`,coalesce(`ub`.`behavior_score`,0) AS `user_activity_score` from ((`user` `u` left join (select `seller_application`.`user_id` AS `user_id`,`seller_application`.`status` AS `application_status` from `seller_application` where (`seller_application`.`deleted` = 0)) `sa` on((`u`.`id` = `sa`.`user_id`))) left join (select `user_behavior`.`user_id` AS `user_id`,((count(0) * 10) + sum(`user_behavior`.`behavior_value`)) AS `behavior_score` from `user_behavior` where (`user_behavior`.`created_at` >= (now() - interval 30 day)) group by `user_behavior`.`user_id`) `ub` on((`u`.`id` = `ub`.`user_id`))) where (`u`.`deleted` = 0);

-- ----------------------------
-- Procedure structure for CleanOldData
-- ----------------------------
DROP PROCEDURE IF EXISTS `CleanOldData`;
delimiter ;;
CREATE PROCEDURE `CleanOldData`()
BEGIN
    DECLARE price_retention_days INT DEFAULT 365;
    DECLARE log_retention_days INT DEFAULT 90;

    -- 获取配置的保留天数
    SELECT CAST(config_value AS UNSIGNED) INTO price_retention_days
    FROM system_configs
    WHERE config_key = 'data_retention.price_data_days' AND is_active = TRUE;

    SELECT CAST(config_value AS UNSIGNED) INTO log_retention_days
    FROM system_configs
    WHERE config_key = 'data_retention.log_data_days' AND is_active = TRUE;

    -- 清理过期的价格数据
    DELETE FROM price_data
    WHERE data_date < DATE_SUB(CURDATE(), INTERVAL price_retention_days DAY);

    -- 清理过期的日志数据
    DELETE FROM crawl_logs
    WHERE created_at < DATE_SUB(NOW(), INTERVAL log_retention_days DAY);

    DELETE FROM data_quality_logs
    WHERE check_time < DATE_SUB(NOW(), INTERVAL log_retention_days DAY);

END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for GenerateDailyStats
-- ----------------------------
DROP PROCEDURE IF EXISTS `GenerateDailyStats`;
delimiter ;;
CREATE PROCEDURE `GenerateDailyStats`(IN stat_date DATE)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
        BEGIN
            ROLLBACK;
            RESIGNAL;
        END;

    START TRANSACTION;

    -- 删除当日已存在的统计数据
    DELETE FROM `system_stats` WHERE `stat_date` = stat_date;

    -- 插入新的统计数据
    INSERT INTO `system_stats` (
        `stat_date`,
        `total_users`,
        `active_users`,
        `new_users`,
        `total_products`,
        `online_products`,
        `new_products`,
        `total_orders`,
        `new_orders`,
        `total_sales`,
        `daily_sales`,
        `seller_applications`,
        `pending_applications`
    )
    SELECT
        stat_date,
        (SELECT COUNT(*) FROM `user` WHERE `deleted` = 0) as total_users,
        (SELECT COUNT(*) FROM `user` WHERE DATE(`last_login_time`) = stat_date) as active_users,
        (SELECT COUNT(*) FROM `user` WHERE DATE(`created_at`) = stat_date) as new_users,
        (SELECT COUNT(*) FROM `product` WHERE `deleted` = 0) as total_products,
        (SELECT COUNT(*) FROM `product` WHERE `deleted` = 0 AND `status` = 1) as online_products,
        (SELECT COUNT(*) FROM `product` WHERE DATE(`created_at`) = stat_date) as new_products,
        (SELECT COUNT(*) FROM `order`) as total_orders,
        (SELECT COUNT(*) FROM `order` WHERE DATE(`create_time`) = stat_date) as new_orders,
        (SELECT COALESCE(SUM(`total_amount`), 0) FROM `order` WHERE `status` = 'completed') as total_sales,
        (SELECT COALESCE(SUM(`total_amount`), 0) FROM `order` WHERE DATE(`create_time`) = stat_date AND `status` = 'completed') as daily_sales,
        (SELECT COUNT(*) FROM `seller_application`) as seller_applications,
        (SELECT COUNT(*) FROM `seller_application` WHERE `status` = 'pending') as pending_applications;

    COMMIT;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for GenerateTraceCode
-- ----------------------------
DROP PROCEDURE IF EXISTS `GenerateTraceCode`;
delimiter ;;
CREATE PROCEDURE `GenerateTraceCode`(IN p_product_id BIGINT,
    OUT p_trace_code VARCHAR(128))
BEGIN
    DECLARE v_date_prefix VARCHAR(8);
    DECLARE v_sequence_number INT DEFAULT 1;
    DECLARE v_formatted_sequence VARCHAR(6);

    -- 获取当前日期前缀
    SET v_date_prefix = DATE_FORMAT(NOW(), '%Y%m%d');

    -- 获取或创建当日序列号
    INSERT INTO `trace_code_sequence` (`date_prefix`, `sequence_number`)
    VALUES (v_date_prefix, 1)
    ON DUPLICATE KEY UPDATE
                         `sequence_number` = `sequence_number` + 1,
                         `updated_at` = NOW();

    -- 获取序列号
    SELECT `sequence_number` INTO v_sequence_number
    FROM `trace_code_sequence`
    WHERE `date_prefix` = v_date_prefix;

    -- 格式化序列号为6位数字
    SET v_formatted_sequence = LPAD(v_sequence_number, 6, '0');

    -- 生成溯源码：SFAP + 日期 + 序列号
    SET p_trace_code = CONCAT('SFAP', v_date_prefix, v_formatted_sequence);

END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for sp_cleanup_view_history
-- ----------------------------
DROP PROCEDURE IF EXISTS `sp_cleanup_view_history`;
delimiter ;;
CREATE PROCEDURE `sp_cleanup_view_history`()
BEGIN
    DELETE FROM product_view_history
    WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY);

    SELECT ROW_COUNT() as deleted_records;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for UpdateScanStatistics
-- ----------------------------
DROP PROCEDURE IF EXISTS `UpdateScanStatistics`;
delimiter ;;
CREATE PROCEDURE `UpdateScanStatistics`(IN p_trace_code VARCHAR(128))
BEGIN
    DECLARE v_today DATE DEFAULT CURDATE();
    DECLARE v_week_start DATE;
    DECLARE v_month_start DATE;

    -- 计算本周开始日期（周一）
    SET v_week_start = DATE_SUB(v_today, INTERVAL WEEKDAY(v_today) DAY);

    -- 计算本月开始日期
    SET v_month_start = DATE_FORMAT(v_today, '%Y-%m-01');

    -- 更新扫码统计
    UPDATE `trace_codes`
    SET
        `scan_count` = `scan_count` + 1,
        `last_scan_time` = NOW(),
        `first_scan_time` = COALESCE(`first_scan_time`, NOW())
    WHERE `code` = p_trace_code;

    -- 重置每日统计（如果是新的一天）
    UPDATE `trace_codes`
    SET `daily_scan_count` = 0
    WHERE `code` = p_trace_code
      AND DATE(`last_scan_time`) < v_today;

    -- 更新今日扫码次数
    UPDATE `trace_codes`
    SET `daily_scan_count` = `daily_scan_count` + 1
    WHERE `code` = p_trace_code;

END
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table product_favorite
-- ----------------------------
DROP TRIGGER IF EXISTS `tr_product_favorite_insert`;
delimiter ;;
CREATE TRIGGER `tr_product_favorite_insert` AFTER INSERT ON `product_favorite` FOR EACH ROW BEGIN
    UPDATE `product` SET `favorite_count` = `favorite_count` + 1 WHERE `id` = NEW.product_id;
    UPDATE `user` SET `total_favorites` = `total_favorites` + 1 WHERE `id` = NEW.user_id;
END
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table product_favorite
-- ----------------------------
DROP TRIGGER IF EXISTS `tr_product_favorite_delete`;
delimiter ;;
CREATE TRIGGER `tr_product_favorite_delete` AFTER DELETE ON `product_favorite` FOR EACH ROW BEGIN
    UPDATE `product` SET `favorite_count` = GREATEST(`favorite_count` - 1, 0) WHERE `id` = OLD.product_id;
    UPDATE `user` SET `total_favorites` = GREATEST(`total_favorites` - 1, 0) WHERE `id` = OLD.user_id;
END
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table product_likes
-- ----------------------------
DROP TRIGGER IF EXISTS `tr_product_likes_insert`;
delimiter ;;
CREATE TRIGGER `tr_product_likes_insert` AFTER INSERT ON `product_likes` FOR EACH ROW BEGIN
    UPDATE `product` SET `like_count` = `like_count` + 1 WHERE `id` = NEW.product_id;
    UPDATE `user` SET `total_likes_given` = `total_likes_given` + 1 WHERE `id` = NEW.user_id;
    UPDATE `user` u 
    INNER JOIN `product` p ON p.seller_id = u.id 
    SET u.`total_likes_received` = u.`total_likes_received` + 1 
    WHERE p.id = NEW.product_id;
END
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table product_likes
-- ----------------------------
DROP TRIGGER IF EXISTS `tr_product_likes_delete`;
delimiter ;;
CREATE TRIGGER `tr_product_likes_delete` AFTER DELETE ON `product_likes` FOR EACH ROW BEGIN
    UPDATE `product` SET `like_count` = GREATEST(`like_count` - 1, 0) WHERE `id` = OLD.product_id;
    UPDATE `user` SET `total_likes_given` = GREATEST(`total_likes_given` - 1, 0) WHERE `id` = OLD.user_id;
    UPDATE `user` u 
    INNER JOIN `product` p ON p.seller_id = u.id 
    SET u.`total_likes_received` = GREATEST(u.`total_likes_received` - 1, 0) 
    WHERE p.id = OLD.product_id;
END
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table product_review
-- ----------------------------
DROP TRIGGER IF EXISTS `tr_product_review_insert`;
delimiter ;;
CREATE TRIGGER `tr_product_review_insert` AFTER INSERT ON `product_review` FOR EACH ROW BEGIN
    DECLARE avg_rating DECIMAL(3,2);
    
    UPDATE product SET review_count = review_count + 1 WHERE id = NEW.product_id;
    UPDATE user SET total_reviews = total_reviews + 1 WHERE id = NEW.user_id;
    
    SELECT AVG(rating) INTO avg_rating 
    FROM product_review 
    WHERE product_id = NEW.product_id AND status = 1;
    
    UPDATE product SET rating = COALESCE(avg_rating, 0) WHERE id = NEW.product_id;
END
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table product_review
-- ----------------------------
DROP TRIGGER IF EXISTS `tr_product_review_update`;
delimiter ;;
CREATE TRIGGER `tr_product_review_update` AFTER UPDATE ON `product_review` FOR EACH ROW BEGIN
    DECLARE avg_rating DECIMAL(3,2);
    
    IF OLD.rating != NEW.rating THEN
        SELECT AVG(rating) INTO avg_rating 
        FROM product_review 
        WHERE product_id = NEW.product_id AND status = 1;
        
        UPDATE product SET rating = COALESCE(avg_rating, 0) WHERE id = NEW.product_id;
    END IF;
END
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table product_review
-- ----------------------------
DROP TRIGGER IF EXISTS `tr_product_review_delete`;
delimiter ;;
CREATE TRIGGER `tr_product_review_delete` AFTER DELETE ON `product_review` FOR EACH ROW BEGIN
    DECLARE avg_rating DECIMAL(3,2);
    
    UPDATE product SET review_count = review_count - 1 WHERE id = OLD.product_id;
    UPDATE user SET total_reviews = total_reviews - 1 WHERE id = OLD.user_id;
    
    SELECT AVG(rating) INTO avg_rating 
    FROM product_review 
    WHERE product_id = OLD.product_id AND status = 1;
    
    UPDATE product SET rating = COALESCE(avg_rating, 0) WHERE id = OLD.product_id;
END
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table review_likes
-- ----------------------------
DROP TRIGGER IF EXISTS `tr_review_likes_insert`;
delimiter ;;
CREATE TRIGGER `tr_review_likes_insert` AFTER INSERT ON `review_likes` FOR EACH ROW BEGIN
    UPDATE `product_review` SET `like_count` = `like_count` + 1 WHERE `id` = NEW.review_id;
END
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table review_likes
-- ----------------------------
DROP TRIGGER IF EXISTS `tr_review_likes_delete`;
delimiter ;;
CREATE TRIGGER `tr_review_likes_delete` AFTER DELETE ON `review_likes` FOR EACH ROW BEGIN
    UPDATE `product_review` SET `like_count` = GREATEST(`like_count` - 1, 0) WHERE `id` = OLD.review_id;
END
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table review_replies
-- ----------------------------
DROP TRIGGER IF EXISTS `tr_review_replies_insert`;
delimiter ;;
CREATE TRIGGER `tr_review_replies_insert` AFTER INSERT ON `review_replies` FOR EACH ROW BEGIN
    UPDATE `product_review` SET `reply_count` = `reply_count` + 1 WHERE `id` = NEW.review_id;
END
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table review_replies
-- ----------------------------
DROP TRIGGER IF EXISTS `tr_review_replies_delete`;
delimiter ;;
CREATE TRIGGER `tr_review_replies_delete` AFTER DELETE ON `review_replies` FOR EACH ROW BEGIN
    UPDATE `product_review` SET `reply_count` = GREATEST(`reply_count` - 1, 0) WHERE `id` = OLD.review_id;
END
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table user
-- ----------------------------
DROP TRIGGER IF EXISTS `sync_user_role_on_update`;
delimiter ;;
CREATE TRIGGER `sync_user_role_on_update` BEFORE UPDATE ON `user` FOR EACH ROW BEGIN
    -- 如果更新了role字段，同步user_type
    IF NEW.role != OLD.role THEN
        IF NEW.role = 'admin' THEN
            SET NEW.user_type = 'admin';
        ELSEIF NEW.role = 'seller' THEN
            SET NEW.user_type = 'seller';
        ELSEIF NEW.role = 'user' THEN
            SET NEW.user_type = 'normal';
        END IF;
    END IF;

    -- 如果更新了user_type字段，同步role
    IF NEW.user_type != OLD.user_type THEN
        IF NEW.user_type = 'admin' THEN
            SET NEW.role = 'admin';
        ELSEIF NEW.user_type = 'seller' THEN
            SET NEW.role = 'seller';
        ELSEIF NEW.user_type = 'normal' THEN
            SET NEW.role = 'user';
        END IF;
    END IF;

    -- 更新时间戳
    SET NEW.updated_at = NOW();
END
;;
delimiter ;

SET FOREIGN_KEY_CHECKS = 1;
