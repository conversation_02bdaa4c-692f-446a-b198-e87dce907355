import requests
import json

# 测试前端修复后的请求格式
trace_code = "SFAPS250723230460290SF08"
url = "http://localhost:8081/api/traceability/query"

# 模拟前端发送的完整请求数据
data = {
    "traceCode": trace_code,
    "source": "web",
    "location": "未知位置",
    "ipAddress": "127.0.0.1",
    "deviceInfo": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "userId": None
}

print(f"Testing frontend request format")
print(f"Trace Code: {trace_code}")
print(f"API: {url}")
print(f"Request data: {json.dumps(data, indent=2, ensure_ascii=False)}")

try:
    response = requests.post(url, json=data, headers={'Content-Type': 'application/json'})
    print(f"\nStatus Code: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print("✅ Frontend request format works!")
        print(f"Product Name: {result['data']['productName']}")
        print(f"Producer: {result['data']['producerName']}")
        print(f"Status: {result['data']['status']}")
        print(f"QR Code URL: {result['data']['qrCodeUrl']}")
    else:
        print("❌ Request failed!")
        print(f"Response: {response.text}")
        
except Exception as e:
    print(f"❌ Request failed: {e}")

# 测试另一个溯源码
print("\n" + "="*50)
print("Testing another trace code...")

trace_code2 = "SFAPS250723225460285CFMK"
data2 = {
    "traceCode": trace_code2,
    "source": "web",
    "location": "未知位置",
    "ipAddress": "127.0.0.1",
    "deviceInfo": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "userId": None
}

try:
    response2 = requests.post(url, json=data2, headers={'Content-Type': 'application/json'})
    print(f"Status Code: {response2.status_code}")
    
    if response2.status_code == 200:
        result2 = response2.json()
        print("✅ Second trace code works!")
        print(f"Product Name: {result2['data']['productName']}")
    else:
        print("❌ Second request failed!")
        print(f"Response: {response2.text}")
        
except Exception as e:
    print(f"❌ Second request failed: {e}")